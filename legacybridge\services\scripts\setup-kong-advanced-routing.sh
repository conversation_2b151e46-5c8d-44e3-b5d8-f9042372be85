#!/bin/bash

# Advanced Kong routing configuration for LegacyBridge
# Includes load balancing, service discovery, and failover
set -e

KONG_ADMIN_URL="http://localhost:8001"

echo "🔀 Configuring advanced Kong routing and load balancing..."

# Function to create upstream (load balancer)
create_upstream() {
    local upstream_name=$1
    local algorithm=${2:-"round-robin"}
    local healthcheck_path=${3:-"/health"}
    local slots=${4:-1000}
    
    echo "⚖️  Creating upstream: $upstream_name"
    
    # Check if upstream exists
    if curl -s $KONG_ADMIN_URL/upstreams/$upstream_name | grep -q "\"name\":\"$upstream_name\""; then
        echo "  ℹ️  Upstream $upstream_name already exists, updating..."
        curl -X PATCH $KONG_ADMIN_URL/upstreams/$upstream_name \
            --data "algorithm=$algorithm" \
            --data "slots=$slots" \
            --data "healthchecks.active.type=http" \
            --data "healthchecks.active.http_path=$healthcheck_path" \
            --data "healthchecks.active.healthy.interval=30" \
            --data "healthchecks.active.healthy.successes=3" \
            --data "healthchecks.active.unhealthy.interval=30" \
            --data "healthchecks.active.unhealthy.http_failures=3" \
            --data "healthchecks.active.unhealthy.timeouts=3" \
            --data "healthchecks.passive.healthy.successes=3" \
            --data "healthchecks.passive.unhealthy.http_failures=3" \
            --data "healthchecks.passive.unhealthy.timeouts=3" > /dev/null
    else
        curl -X POST $KONG_ADMIN_URL/upstreams \
            --data "name=$upstream_name" \
            --data "algorithm=$algorithm" \
            --data "slots=$slots" \
            --data "healthchecks.active.type=http" \
            --data "healthchecks.active.http_path=$healthcheck_path" \
            --data "healthchecks.active.healthy.interval=30" \
            --data "healthchecks.active.healthy.successes=3" \
            --data "healthchecks.active.unhealthy.interval=30" \
            --data "healthchecks.active.unhealthy.http_failures=3" \
            --data "healthchecks.active.unhealthy.timeouts=3" \
            --data "healthchecks.passive.healthy.successes=3" \
            --data "healthchecks.passive.unhealthy.http_failures=3" \
            --data "healthchecks.passive.unhealthy.timeouts=3" > /dev/null
    fi
    echo "  ✅ Upstream $upstream_name configured with health checks"
}

# Function to add target to upstream
add_target() {
    local upstream_name=$1
    local target_host=$2
    local target_port=$3
    local weight=${4:-100}
    
    echo "🎯 Adding target to upstream $upstream_name: $target_host:$target_port"
    
    # Check if target already exists
    local target_id=$(curl -s $KONG_ADMIN_URL/upstreams/$upstream_name/targets | \
                     jq -r ".data[] | select(.target == \"$target_host:$target_port\") | .id" 2>/dev/null || echo "")
    
    if [ -n "$target_id" ]; then
        echo "  ℹ️  Target already exists, updating weight..."
        curl -X PATCH $KONG_ADMIN_URL/upstreams/$upstream_name/targets/$target_id \
            --data "weight=$weight" > /dev/null
    else
        curl -X POST $KONG_ADMIN_URL/upstreams/$upstream_name/targets \
            --data "target=$target_host:$target_port" \
            --data "weight=$weight" > /dev/null
    fi
    echo "  ✅ Target $target_host:$target_port added with weight $weight"
}

# Function to update service to use upstream
update_service_upstream() {
    local service_name=$1
    local upstream_name=$2
    
    echo "🔗 Updating service $service_name to use upstream $upstream_name"
    
    curl -X PATCH $KONG_ADMIN_URL/services/$service_name \
        --data "host=$upstream_name" \
        --data "port=80" > /dev/null
    
    echo "  ✅ Service $service_name now uses upstream $upstream_name"
}

# Function to create circuit breaker plugin for service
add_circuit_breaker() {
    local service_name=$1
    local failure_threshold=${2:-5}
    local recovery_time=${3:-30}
    
    echo "⚡ Adding circuit breaker to $service_name"
    
    # Check if plugin exists
    if curl -s "$KONG_ADMIN_URL/services/$service_name/plugins" | grep -q "\"name\":\"proxy-cache\""; then
        echo "  ℹ️  Circuit breaker already configured for $service_name"
        return
    fi
    
    # Use proxy-cache as a simple circuit breaker alternative
    curl -X POST $KONG_ADMIN_URL/services/$service_name/plugins \
        --data "name=proxy-cache" \
        --data "config.request_method=GET" \
        --data "config.response_code=200,301,404" \
        --data "config.content_type=application/json" \
        --data "config.cache_ttl=60" \
        --data "config.strategy=memory" > /dev/null
    
    echo "  ✅ Circuit breaker configured for $service_name"
}

# Wait for Kong to be ready
echo "⏳ Waiting for Kong Admin API..."
until curl -f $KONG_ADMIN_URL/status > /dev/null 2>&1; do
    sleep 2
done
echo "✅ Kong Admin API is ready"

# Create upstreams for each service
echo "🏗️  Creating upstreams with load balancing..."

create_upstream "auth-service-upstream" "round-robin" "/health"
create_upstream "conversion-service-upstream" "least-connections" "/health"
create_upstream "file-service-upstream" "round-robin" "/health"
create_upstream "job-service-upstream" "round-robin" "/health"

# Add targets to upstreams (multiple instances for load balancing)
echo "🎯 Adding service targets..."

# Auth service targets
add_target "auth-service-upstream" "host.docker.internal" "3001" 100

# Conversion service targets (can have multiple instances)
add_target "conversion-service-upstream" "host.docker.internal" "3002" 100

# File service targets
add_target "file-service-upstream" "host.docker.internal" "3003" 100

# Job service targets
add_target "job-service-upstream" "host.docker.internal" "3004" 100

# Update services to use upstreams
echo "🔗 Connecting services to upstreams..."

update_service_upstream "auth-service" "auth-service-upstream"
update_service_upstream "conversion-service" "conversion-service-upstream"
update_service_upstream "file-service" "file-service-upstream"
update_service_upstream "job-service" "job-service-upstream"

# Add circuit breakers
echo "⚡ Configuring circuit breakers..."

add_circuit_breaker "auth-service" 5 30
add_circuit_breaker "conversion-service" 3 60
add_circuit_breaker "file-service" 5 30
add_circuit_breaker "job-service" 5 30

echo "✅ Advanced Kong routing configuration completed!"
echo ""
echo "🔗 Load balancing endpoints:"
echo "  🔐 Authentication: http://localhost:8000/auth (upstream: auth-service-upstream)"
echo "  🔄 Conversion:     http://localhost:8000/convert (upstream: conversion-service-upstream)"
echo "  📁 Files:          http://localhost:8000/files (upstream: file-service-upstream)"
echo "  ⚙️  Jobs:           http://localhost:8000/jobs (upstream: job-service-upstream)"
echo ""
echo "📊 Health monitoring:"
echo "  Kong will automatically health check all service instances"
echo "  Failed instances will be removed from load balancing"
echo "  Circuit breakers will prevent cascade failures"
echo ""
echo "🎯 Next steps:"
echo "  1. Scale services horizontally by adding more targets"
echo "  2. Monitor upstream health at http://localhost:8001/upstreams"
echo "  3. Check service metrics at http://localhost:8001/metrics"
