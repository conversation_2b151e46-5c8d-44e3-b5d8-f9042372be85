#[cfg(test)]
mod tests {
    use super::*;
    use crate::dll::*;
    use std::path::PathBuf;
    use tempfile::TempDir;

    #[test]
    fn test_dll_error_display() {
        let error = DllError::BuildError("Test error".to_string());
        assert_eq!(error.to_string(), "Build failed: Test error");
        
        let error = DllError::FileNotFound(PathBuf::from("/test/path"));
        assert_eq!(error.to_string(), "File not found: /test/path");
    }

    #[tokio::test]
    async fn test_dll_builder_config() {
        let config = BuildConfig {
            source_files: vec![PathBuf::from("test.c")],
            output_name: "test".to_string(),
            architecture: builder::Architecture::X86,
            optimization: builder::OptimizationLevel::Debug,
            debug_symbols: true,
            static_linking: false,
            export_definitions: None,
            include_dirs: vec![],
            library_dirs: vec![],
            libraries: vec![],
            defines: vec![],
            compiler_flags: vec![],
            linker_flags: vec![],
        };
        
        assert_eq!(config.output_name, "test");
        assert!(matches!(config.architecture, builder::Architecture::X86));
    }

    #[tokio::test]
    async fn test_dll_tester_config() {
        let config = TestConfig {
            dll_path: PathBuf::from("test.dll"),
            platforms: vec![TestPlatform::Vb6],
            test_functions: vec!["Test".to_string()],
            test_data_dir: None,
            performance_test: false,
            memory_test: false,
            thread_safety_test: false,
            timeout_seconds: 60,
        };
        
        assert_eq!(config.dll_path.to_str().unwrap(), "test.dll");
        assert_eq!(config.platforms.len(), 1);
    }

    #[tokio::test]
    async fn test_code_generator_config() {
        let config = GeneratorConfig {
            dll_path: PathBuf::from("test.dll"),
            output_dir: PathBuf::from("output"),
            language: generator::TargetLanguage::Vb6,
            include_examples: true,
            include_error_handling: true,
            include_documentation: true,
            template_path: None,
            namespace: None,
            class_name: Some("TestClass".to_string()),
        };
        
        assert_eq!(config.class_name.unwrap(), "TestClass");
        assert!(config.include_examples);
    }

    #[tokio::test]
    async fn test_dll_inspector_new() {
        let inspector = DllInspector::new(PathBuf::from("test.dll"));
        // Inspector is created successfully
        assert!(true);
    }

    #[test]
    fn test_architecture_conversion() {
        use crate::dll::builder::Architecture;
        
        let arch = Architecture::X86;
        assert_eq!(format!("{:?}", arch), "X86");
        
        let arch = Architecture::X64;
        assert_eq!(format!("{:?}", arch), "X64");
        
        let arch = Architecture::Both;
        assert_eq!(format!("{:?}", arch), "Both");
    }

    #[test]
    fn test_optimization_level_conversion() {
        use crate::dll::builder::OptimizationLevel;
        
        let opt = OptimizationLevel::Debug;
        assert_eq!(format!("{:?}", opt), "Debug");
        
        let opt = OptimizationLevel::Release;
        assert_eq!(format!("{:?}", opt), "Release");
        
        let opt = OptimizationLevel::Size;
        assert_eq!(format!("{:?}", opt), "Size");
    }

    #[test]
    fn test_target_language_conversion() {
        use crate::dll::generator::TargetLanguage;
        
        let lang = TargetLanguage::Vb6;
        assert_eq!(format!("{:?}", lang), "Vb6");
        
        let lang = TargetLanguage::Python;
        assert_eq!(format!("{:?}", lang), "Python");
    }

    #[test]
    fn test_test_platform_conversion() {
        let platform = TestPlatform::Vb6;
        assert_eq!(format!("{:?}", platform), "Vb6");
        
        let platform = TestPlatform::Windows10;
        assert_eq!(format!("{:?}", platform), "Windows10");
    }

    #[tokio::test]
    async fn test_dll_builder_work_dir() {
        let config = BuildConfig::default();
        let builder = DllBuilder::new(config);
        
        // Work directory should be created in temp
        assert!(true);
    }

    #[tokio::test]
    async fn test_code_generator_validation() {
        let config = GeneratorConfig {
            dll_path: PathBuf::from("/nonexistent/test.dll"),
            output_dir: PathBuf::from("output"),
            language: generator::TargetLanguage::Vb6,
            include_examples: false,
            include_error_handling: false,
            include_documentation: false,
            template_path: None,
            namespace: None,
            class_name: None,
        };
        
        // Should fail for non-existent DLL
        match CodeGenerator::new(config) {
            Err(e) => {
                assert!(e.to_string().contains("DLL not found"));
            }
            Ok(_) => panic!("Expected error for non-existent DLL"),
        }
    }

    #[test]
    fn test_export_info_creation() {
        let export = inspector::ExportInfo {
            name: "TestFunction".to_string(),
            ordinal: 1,
            rva: 0x1000,
            hint: Some(0),
            is_forwarded: false,
            forward_to: None,
        };
        
        assert_eq!(export.name, "TestFunction");
        assert_eq!(export.ordinal, 1);
        assert!(!export.is_forwarded);
    }

    #[test]
    fn test_test_result_creation() {
        let result = tester::TestResult {
            platform: TestPlatform::Vb6,
            success: true,
            tests_run: 5,
            tests_passed: 4,
            tests_failed: 1,
            errors: vec![],
            performance_metrics: None,
            memory_metrics: None,
            duration: std::time::Duration::from_secs(10),
        };
        
        assert!(result.success);
        assert_eq!(result.tests_run, 5);
        assert_eq!(result.duration.as_secs(), 10);
    }

    #[test]
    fn test_build_config_default() {
        let config = BuildConfig::default();
        
        assert_eq!(config.output_name, "output");
        assert!(matches!(config.architecture, builder::Architecture::X86));
        assert!(matches!(config.optimization, builder::OptimizationLevel::Debug));
    }

    #[test]
    fn test_generator_config_default() {
        let config = GeneratorConfig::default();
        
        assert_eq!(config.output_dir, PathBuf::from("output"));
        assert!(matches!(config.language, generator::TargetLanguage::Vb6));
        assert!(!config.include_examples);
    }

    #[test]
    fn test_test_config_default() {
        let config = TestConfig::default();
        
        assert_eq!(config.platforms.len(), 2); // VB6 and VFP9 by default
        assert_eq!(config.timeout_seconds, 60);
    }
}