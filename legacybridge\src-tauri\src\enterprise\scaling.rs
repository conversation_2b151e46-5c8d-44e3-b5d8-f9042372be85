// Auto-scaling Logic for LegacyBridge
use serde::{Serialize, Deserialize};
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tokio::sync::mpsc;
use std::collections::VecDeque;

use super::monitoring::{SystemMetrics, ResourceMetrics};

/// Auto-scaling system for enterprise deployments
pub struct AutoScaler {
    config: ScalingConfig,
    state: Arc<RwLock<ScalingState>>,
    metrics_receiver: Option<mpsc::Receiver<SystemMetrics>>,
    scaling_history: Arc<RwLock<VecDeque<ScalingEvent>>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScalingConfig {
    /// Minimum number of worker instances
    pub min_instances: u32,
    
    /// Maximum number of worker instances
    pub max_instances: u32,
    
    /// CPU threshold for scaling up (percentage)
    pub scale_up_cpu_threshold: f64,
    
    /// CPU threshold for scaling down (percentage)
    pub scale_down_cpu_threshold: f64,
    
    /// Memory threshold for scaling up (percentage)
    pub scale_up_memory_threshold: f64,
    
    /// Memory threshold for scaling down (percentage)
    pub scale_down_memory_threshold: f64,
    
    /// Request queue threshold for scaling up
    pub scale_up_queue_threshold: usize,
    
    /// Response time threshold for scaling up (ms)
    pub scale_up_response_time_threshold: f64,
    
    /// Cool-down period after scaling up
    pub scale_up_cooldown: Duration,
    
    /// Cool-down period after scaling down
    pub scale_down_cooldown: Duration,
    
    /// Evaluation window for metrics
    pub evaluation_window: Duration,
    
    /// Number of consecutive threshold breaches before scaling
    pub threshold_breach_count: u32,
    
    /// Enable predictive scaling
    pub enable_predictive_scaling: bool,
    
    /// Enable gradual scaling (one instance at a time)
    pub gradual_scaling: bool,
}

impl Default for ScalingConfig {
    fn default() -> Self {
        Self {
            min_instances: 1,
            max_instances: 10,
            scale_up_cpu_threshold: 75.0,
            scale_down_cpu_threshold: 30.0,
            scale_up_memory_threshold: 80.0,
            scale_down_memory_threshold: 40.0,
            scale_up_queue_threshold: 100,
            scale_up_response_time_threshold: 2000.0,
            scale_up_cooldown: Duration::from_secs(300), // 5 minutes
            scale_down_cooldown: Duration::from_secs(600), // 10 minutes
            evaluation_window: Duration::from_secs(120), // 2 minutes
            threshold_breach_count: 3,
            enable_predictive_scaling: true,
            gradual_scaling: true,
        }
    }
}

#[derive(Debug, Clone)]
pub struct ScalingState {
    /// Current number of active instances
    pub current_instances: u32,
    
    /// Last scaling operation timestamp
    pub last_scale_operation: Option<Instant>,
    
    /// Consecutive threshold breaches
    pub consecutive_breaches: u32,
    
    /// Current scaling status
    pub status: ScalingStatus,
    
    /// Predicted load for next period
    pub predicted_load: Option<PredictedLoad>,
    
    /// Resource utilization history
    pub utilization_history: VecDeque<ResourceUtilization>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ScalingStatus {
    Idle,
    ScalingUp,
    ScalingDown,
    CoolingDown,
    AtMaxCapacity,
    AtMinCapacity,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScalingEvent {
    pub timestamp: Instant,
    pub action: ScalingAction,
    pub from_instances: u32,
    pub to_instances: u32,
    pub reason: String,
    pub metrics_snapshot: ScalingMetrics,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ScalingAction {
    ScaleUp,
    ScaleDown,
    NoAction,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScalingMetrics {
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub queue_size: usize,
    pub response_time: f64,
    pub error_rate: f64,
}

#[derive(Debug, Clone)]
pub struct PredictedLoad {
    pub cpu_prediction: f64,
    pub memory_prediction: f64,
    pub request_rate_prediction: f64,
    pub confidence: f64,
}

#[derive(Debug, Clone)]
pub struct ResourceUtilization {
    pub timestamp: Instant,
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub queue_size: usize,
    pub response_time: f64,
}

impl AutoScaler {
    pub fn new(config: ScalingConfig) -> Self {
        let initial_state = ScalingState {
            current_instances: config.min_instances,
            last_scale_operation: None,
            consecutive_breaches: 0,
            status: ScalingStatus::Idle,
            predicted_load: None,
            utilization_history: VecDeque::with_capacity(100),
        };
        
        Self {
            config,
            state: Arc::new(RwLock::new(initial_state)),
            metrics_receiver: None,
            scaling_history: Arc::new(RwLock::new(VecDeque::with_capacity(1000))),
        }
    }
    
    /// Start the auto-scaling engine
    pub async fn start(&mut self, metrics_receiver: mpsc::Receiver<SystemMetrics>) -> Result<(), ScalingError> {
        self.metrics_receiver = Some(metrics_receiver);
        
        // Start scaling evaluation loop
        let state = self.state.clone();
        let config = self.config.clone();
        let history = self.scaling_history.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(30));
            
            loop {
                interval.tick().await;
                
                // Evaluate scaling needs
                if let Err(e) = Self::evaluate_and_scale(&state, &config, &history).await {
                    eprintln!("Scaling evaluation error: {}", e);
                }
            }
        });
        
        Ok(())
    }
    
    async fn evaluate_and_scale(
        state: &Arc<RwLock<ScalingState>>,
        config: &ScalingConfig,
        history: &Arc<RwLock<VecDeque<ScalingEvent>>>,
    ) -> Result<(), ScalingError> {
        let mut state_guard = state.write().unwrap();
        
        // Check if in cooldown period
        if let Some(last_operation) = state_guard.last_scale_operation {
            let cooldown = match state_guard.status {
                ScalingStatus::ScalingUp => config.scale_up_cooldown,
                ScalingStatus::ScalingDown => config.scale_down_cooldown,
                _ => Duration::from_secs(0),
            };
            
            if last_operation.elapsed() < cooldown {
                state_guard.status = ScalingStatus::CoolingDown;
                return Ok(());
            }
        }
        
        // Get current metrics
        let current_metrics = Self::get_current_metrics(&state_guard)?;
        
        // Update utilization history
        state_guard.utilization_history.push_back(ResourceUtilization {
            timestamp: Instant::now(),
            cpu_usage: current_metrics.cpu_usage,
            memory_usage: current_metrics.memory_usage,
            queue_size: current_metrics.queue_size,
            response_time: current_metrics.response_time,
        });
        
        // Maintain history size
        while state_guard.utilization_history.len() > 100 {
            state_guard.utilization_history.pop_front();
        }
        
        // Determine scaling action
        let action = Self::determine_scaling_action(&state_guard, config, &current_metrics)?;
        
        // Execute scaling action
        match action {
            ScalingAction::ScaleUp => {
                if state_guard.current_instances < config.max_instances {
                    let new_instances = if config.gradual_scaling {
                        state_guard.current_instances + 1
                    } else {
                        (state_guard.current_instances + 2).min(config.max_instances)
                    };
                    
                    Self::scale_to_instances(&mut state_guard, new_instances, action, current_metrics, history)?;
                } else {
                    state_guard.status = ScalingStatus::AtMaxCapacity;
                }
            }
            ScalingAction::ScaleDown => {
                if state_guard.current_instances > config.min_instances {
                    let new_instances = if config.gradual_scaling {
                        state_guard.current_instances - 1
                    } else {
                        (state_guard.current_instances - 1).max(config.min_instances)
                    };
                    
                    Self::scale_to_instances(&mut state_guard, new_instances, action, current_metrics, history)?;
                } else {
                    state_guard.status = ScalingStatus::AtMinCapacity;
                }
            }
            ScalingAction::NoAction => {
                state_guard.status = ScalingStatus::Idle;
                state_guard.consecutive_breaches = 0;
            }
        }
        
        Ok(())
    }
    
    fn determine_scaling_action(
        state: &ScalingState,
        config: &ScalingConfig,
        metrics: &ScalingMetrics,
    ) -> Result<ScalingAction, ScalingError> {
        // Check scale up conditions
        let scale_up_needed = 
            metrics.cpu_usage > config.scale_up_cpu_threshold ||
            metrics.memory_usage > config.scale_up_memory_threshold ||
            metrics.queue_size > config.scale_up_queue_threshold ||
            metrics.response_time > config.scale_up_response_time_threshold;
        
        // Check scale down conditions
        let scale_down_possible = 
            metrics.cpu_usage < config.scale_down_cpu_threshold &&
            metrics.memory_usage < config.scale_down_memory_threshold &&
            metrics.queue_size < 10 &&
            metrics.response_time < 500.0;
        
        if scale_up_needed {
            // Check for consecutive breaches
            if state.consecutive_breaches >= config.threshold_breach_count - 1 {
                Ok(ScalingAction::ScaleUp)
            } else {
                Ok(ScalingAction::NoAction)
            }
        } else if scale_down_possible {
            // Scale down is less aggressive
            if state.consecutive_breaches >= config.threshold_breach_count * 2 - 1 {
                Ok(ScalingAction::ScaleDown)
            } else {
                Ok(ScalingAction::NoAction)
            }
        } else {
            Ok(ScalingAction::NoAction)
        }
    }
    
    fn scale_to_instances(
        state: &mut ScalingState,
        new_instances: u32,
        action: ScalingAction,
        metrics: ScalingMetrics,
        history: &Arc<RwLock<VecDeque<ScalingEvent>>>,
    ) -> Result<(), ScalingError> {
        let event = ScalingEvent {
            timestamp: Instant::now(),
            action: action.clone(),
            from_instances: state.current_instances,
            to_instances: new_instances,
            reason: Self::format_scaling_reason(&metrics),
            metrics_snapshot: metrics,
        };
        
        // Update state
        state.current_instances = new_instances;
        state.last_scale_operation = Some(Instant::now());
        state.consecutive_breaches = 0;
        state.status = match action {
            ScalingAction::ScaleUp => ScalingStatus::ScalingUp,
            ScalingAction::ScaleDown => ScalingStatus::ScalingDown,
            ScalingAction::NoAction => ScalingStatus::Idle,
        };
        
        // Record event
        let mut history_guard = history.write().unwrap();
        history_guard.push_back(event);
        
        // Maintain history size
        while history_guard.len() > 1000 {
            history_guard.pop_front();
        }
        
        Ok(())
    }
    
    fn get_current_metrics(state: &ScalingState) -> Result<ScalingMetrics, ScalingError> {
        // In a real implementation, this would get metrics from the monitoring system
        // For now, return placeholder metrics based on recent history
        
        if let Some(recent) = state.utilization_history.back() {
            Ok(ScalingMetrics {
                cpu_usage: recent.cpu_usage,
                memory_usage: recent.memory_usage,
                queue_size: recent.queue_size,
                response_time: recent.response_time,
                error_rate: 0.0, // Placeholder
            })
        } else {
            Ok(ScalingMetrics {
                cpu_usage: 50.0,
                memory_usage: 60.0,
                queue_size: 10,
                response_time: 200.0,
                error_rate: 0.0,
            })
        }
    }
    
    fn format_scaling_reason(metrics: &ScalingMetrics) -> String {
        let mut reasons = Vec::new();
        
        if metrics.cpu_usage > 75.0 {
            reasons.push(format!("High CPU usage: {:.1}%", metrics.cpu_usage));
        }
        if metrics.memory_usage > 80.0 {
            reasons.push(format!("High memory usage: {:.1}%", metrics.memory_usage));
        }
        if metrics.queue_size > 100 {
            reasons.push(format!("Large queue size: {}", metrics.queue_size));
        }
        if metrics.response_time > 2000.0 {
            reasons.push(format!("High response time: {:.0}ms", metrics.response_time));
        }
        
        if reasons.is_empty() {
            "Resource optimization".to_string()
        } else {
            reasons.join(", ")
        }
    }
    
    /// Get current scaling status
    pub fn get_status(&self) -> ScalingStatus {
        self.state.read().unwrap().status.clone()
    }
    
    /// Get current instance count
    pub fn get_instance_count(&self) -> u32 {
        self.state.read().unwrap().current_instances
    }
    
    /// Get scaling history
    pub fn get_history(&self, limit: usize) -> Vec<ScalingEvent> {
        let history = self.scaling_history.read().unwrap();
        history.iter().rev().take(limit).cloned().collect()
    }
    
    /// Force scale to specific instance count (for manual intervention)
    pub async fn scale_to(&self, instances: u32) -> Result<(), ScalingError> {
        let mut state = self.state.write().unwrap();
        
        if instances < self.config.min_instances || instances > self.config.max_instances {
            return Err(ScalingError::InvalidInstanceCount {
                requested: instances,
                min: self.config.min_instances,
                max: self.config.max_instances,
            });
        }
        
        let current_metrics = Self::get_current_metrics(&state)?;
        let action = if instances > state.current_instances {
            ScalingAction::ScaleUp
        } else if instances < state.current_instances {
            ScalingAction::ScaleDown
        } else {
            ScalingAction::NoAction
        };
        
        Self::scale_to_instances(&mut state, instances, action, current_metrics, &self.scaling_history)?;
        
        Ok(())
    }
    
    /// Update configuration
    pub fn update_config(&mut self, config: ScalingConfig) {
        self.config = config;
    }
    
    /// Enable or disable auto-scaling
    pub fn set_enabled(&self, enabled: bool) {
        let mut state = self.state.write().unwrap();
        if !enabled {
            state.status = ScalingStatus::Idle;
        }
    }
}

/// Worker pool manager for horizontal scaling
pub struct WorkerPool {
    workers: Vec<WorkerInstance>,
    config: WorkerPoolConfig,
}

#[derive(Debug, Clone)]
pub struct WorkerInstance {
    pub id: String,
    pub status: WorkerStatus,
    pub created_at: Instant,
    pub last_heartbeat: Instant,
    pub metrics: WorkerMetrics,
}

#[derive(Debug, Clone, PartialEq)]
pub enum WorkerStatus {
    Starting,
    Running,
    Stopping,
    Stopped,
    Failed,
}

#[derive(Debug, Clone)]
pub struct WorkerMetrics {
    pub tasks_processed: u64,
    pub tasks_failed: u64,
    pub cpu_usage: f64,
    pub memory_usage: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkerPoolConfig {
    pub worker_timeout: Duration,
    pub health_check_interval: Duration,
    pub max_retries: u32,
}

impl Default for WorkerPoolConfig {
    fn default() -> Self {
        Self {
            worker_timeout: Duration::from_secs(300),
            health_check_interval: Duration::from_secs(30),
            max_retries: 3,
        }
    }
}

#[derive(Debug)]
pub enum ScalingError {
    MetricsUnavailable,
    ScalingOperationFailed(String),
    InvalidInstanceCount { requested: u32, min: u32, max: u32 },
    WorkerStartupFailed(String),
    ConfigurationError(String),
}

impl std::fmt::Display for ScalingError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ScalingError::MetricsUnavailable => write!(f, "Metrics unavailable for scaling decision"),
            ScalingError::ScalingOperationFailed(msg) => write!(f, "Scaling operation failed: {}", msg),
            ScalingError::InvalidInstanceCount { requested, min, max } => {
                write!(f, "Invalid instance count {}, must be between {} and {}", requested, min, max)
            }
            ScalingError::WorkerStartupFailed(msg) => write!(f, "Worker startup failed: {}", msg),
            ScalingError::ConfigurationError(msg) => write!(f, "Configuration error: {}", msg),
        }
    }
}

impl std::error::Error for ScalingError {}