-- Custom rate limiting plugin for LegacyBridge with user-based and service-based limits

local redis = require "resty.redis"
local cjson = require "cjson"

local LegacyBridgeRateLimitHandler = {}

LegacyBridgeRateLimitHandler.PRIORITY = 900
LegacyBridgeRateLimitHandler.VERSION = "1.0.0"

local function get_redis_connection(config)
    local red = redis:new()
    red:set_timeout(config.redis_timeout or 1000)
    
    local redis_host = config.redis_host or "redis"
    local redis_port = config.redis_port or 6379
    local redis_password = config.redis_password
    
    local ok, err = red:connect(redis_host, redis_port)
    if not ok then
        kong.log.err("Failed to connect to Redis: ", err)
        return nil, err
    end
    
    if redis_password then
        local res, err = red:auth(redis_password)
        if not res then
            kong.log.err("Failed to authenticate with Redis: ", err)
            return nil, err
        end
    end
    
    return red, nil
end

local function get_rate_limit_key(identifier, window, config)
    local service_name = kong.router.get_service().name or "unknown"
    return string.format("rate_limit:%s:%s:%s:%d", 
                        config.namespace or "legacybridge", 
                        service_name, 
                        identifier, 
                        window)
end

local function get_identifier(config)
    -- Try to get user ID from headers (set by auth plugin)
    local user_id = kong.request.get_header("X-User-ID")
    if user_id then
        return "user:" .. user_id
    end
    
    -- Fall back to IP address
    local forwarded_ip = kong.request.get_header("X-Forwarded-For")
    if forwarded_ip then
        local ip = string.match(forwarded_ip, "([^,]+)")
        return "ip:" .. ip
    end
    
    return "ip:" .. kong.client.get_ip()
end

local function get_current_window(window_size)
    return math.floor(ngx.time() / window_size) * window_size
end

local function check_rate_limit(identifier, limit, window_size, config)
    local red, err = get_redis_connection(config)
    if not red then
        -- If Redis is unavailable, allow the request (fail open)
        kong.log.warn("Redis unavailable for rate limiting, allowing request: ", err)
        return true, 0, 0
    end
    
    local current_window = get_current_window(window_size)
    local key = get_rate_limit_key(identifier, current_window, config)
    
    -- Use Redis pipeline for atomic operations
    red:init_pipeline()
    red:incr(key)
    red:expire(key, window_size * 2) -- Keep for 2 windows for safety
    local results, err = red:commit_pipeline()
    
    if not results then
        kong.log.err("Redis pipeline failed: ", err)
        -- Fail open if Redis operations fail
        return true, 0, 0
    end
    
    local current_count = results[1]
    local remaining = math.max(0, limit - current_count)
    local reset_time = current_window + window_size
    
    -- Close Redis connection
    red:set_keepalive(10000, 100)
    
    return current_count <= limit, current_count, remaining, reset_time
end

local function get_service_specific_limits(service_name, config)
    local service_limits = config.service_limits or {}
    local service_config = service_limits[service_name]
    
    if service_config then
        return service_config.requests_per_minute or config.default_requests_per_minute,
               service_config.requests_per_hour or config.default_requests_per_hour,
               service_config.requests_per_day or config.default_requests_per_day
    end
    
    return config.default_requests_per_minute,
           config.default_requests_per_hour,
           config.default_requests_per_day
end

local function check_user_tier_limits(user_roles, config)
    -- Check if user has premium/admin roles for higher limits
    if not user_roles then
        return 1 -- Default multiplier
    end
    
    local roles = {}
    for role in string.gmatch(user_roles, "([^,]+)") do
        roles[role] = true
    end
    
    if roles["admin"] then
        return config.admin_limit_multiplier or 10
    elseif roles["premium"] then
        return config.premium_limit_multiplier or 5
    elseif roles["pro"] then
        return config.pro_limit_multiplier or 3
    end
    
    return 1 -- Default user
end

function LegacyBridgeRateLimitHandler:access(config)
    local service_name = kong.router.get_service().name or "unknown"
    local identifier = get_identifier(config)
    local user_roles = kong.request.get_header("X-User-Roles")
    
    -- Get service-specific limits
    local minute_limit, hour_limit, day_limit = get_service_specific_limits(service_name, config)
    
    -- Apply user tier multipliers
    local tier_multiplier = check_user_tier_limits(user_roles, config)
    minute_limit = minute_limit * tier_multiplier
    hour_limit = hour_limit * tier_multiplier
    day_limit = day_limit * tier_multiplier
    
    -- Check minute limit
    local allowed, current, remaining, reset = check_rate_limit(identifier, minute_limit, 60, config)
    if not allowed then
        kong.log.warn("Rate limit exceeded for ", identifier, " on service ", service_name, 
                     " (", current, "/", minute_limit, " per minute)")
        
        kong.response.set_header("X-RateLimit-Limit-Minute", tostring(minute_limit))
        kong.response.set_header("X-RateLimit-Remaining-Minute", "0")
        kong.response.set_header("X-RateLimit-Reset-Minute", tostring(reset))
        
        return kong.response.exit(429, {
            error = "rate_limit_exceeded",
            message = "Too many requests per minute",
            limit = minute_limit,
            window = "minute",
            reset_time = reset
        })
    end
    
    -- Check hour limit
    local hour_allowed, hour_current, hour_remaining, hour_reset = check_rate_limit(identifier, hour_limit, 3600, config)
    if not hour_allowed then
        kong.log.warn("Hourly rate limit exceeded for ", identifier, " on service ", service_name)
        
        kong.response.set_header("X-RateLimit-Limit-Hour", tostring(hour_limit))
        kong.response.set_header("X-RateLimit-Remaining-Hour", "0")
        kong.response.set_header("X-RateLimit-Reset-Hour", tostring(hour_reset))
        
        return kong.response.exit(429, {
            error = "rate_limit_exceeded",
            message = "Too many requests per hour",
            limit = hour_limit,
            window = "hour",
            reset_time = hour_reset
        })
    end
    
    -- Check daily limit
    local day_allowed, day_current, day_remaining, day_reset = check_rate_limit(identifier, day_limit, 86400, config)
    if not day_allowed then
        kong.log.warn("Daily rate limit exceeded for ", identifier, " on service ", service_name)
        
        kong.response.set_header("X-RateLimit-Limit-Day", tostring(day_limit))
        kong.response.set_header("X-RateLimit-Remaining-Day", "0")
        kong.response.set_header("X-RateLimit-Reset-Day", tostring(day_reset))
        
        return kong.response.exit(429, {
            error = "rate_limit_exceeded",
            message = "Too many requests per day",
            limit = day_limit,
            window = "day",
            reset_time = day_reset
        })
    end
    
    -- Set rate limit headers for successful requests
    kong.response.set_header("X-RateLimit-Limit-Minute", tostring(minute_limit))
    kong.response.set_header("X-RateLimit-Remaining-Minute", tostring(remaining))
    kong.response.set_header("X-RateLimit-Reset-Minute", tostring(reset))
    
    kong.response.set_header("X-RateLimit-Limit-Hour", tostring(hour_limit))
    kong.response.set_header("X-RateLimit-Remaining-Hour", tostring(hour_remaining))
    kong.response.set_header("X-RateLimit-Reset-Hour", tostring(hour_reset))
    
    kong.log.debug("Rate limit check passed for ", identifier, " on service ", service_name,
                  " (", current, "/", minute_limit, " per minute)")
end

return LegacyBridgeRateLimitHandler
