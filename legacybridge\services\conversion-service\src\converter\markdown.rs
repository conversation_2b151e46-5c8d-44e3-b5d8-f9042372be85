// Markdown parsing and generation
use super::{ParsedDocument, DocumentElement};
use legacybridge_shared::{ServiceError, ServiceResult};
use pulldown_cmark::{Parser, Event, Tag, CodeBlockKind, CowStr};
use serde_json::Value;
use std::collections::HashMap;

/// Parse markdown content into structured document
pub fn parse_markdown(content: &str) -> ServiceResult<ParsedDocument> {
    let parser = Parser::new(content);
    let mut elements = Vec::new();
    let mut current_text = String::new();
    let mut in_heading = false;
    let mut heading_level = 1;
    let mut in_paragraph = false;
    let mut in_code_block = false;
    let mut code_language = None;
    let mut list_items = Vec::new();
    let mut in_list = false;
    let mut list_ordered = false;

    for event in parser {
        match event {
            Event::Start(tag) => {
                match tag {
                    Tag::Heading(level, _, _) => {
                        in_heading = true;
                        heading_level = level as u8;
                        current_text.clear();
                    }
                    Tag::Paragraph => {
                        in_paragraph = true;
                        current_text.clear();
                    }
                    Tag::CodeBlock(kind) => {
                        in_code_block = true;
                        code_language = match kind {
                            CodeBlockKind::Fenced(lang) => Some(lang.to_string()),
                            _ => None,
                        };
                        current_text.clear();
                    }
                    Tag::List(start_num) => {
                        in_list = true;
                        list_ordered = start_num.is_some();
                        list_items.clear();
                    }
                    Tag::Item => {
                        current_text.clear();
                    }
                    _ => {}
                }
            }
            Event::End(tag) => {
                match tag {
                    Tag::Heading(_, _, _) => {
                        if in_heading {
                            elements.push(DocumentElement::Heading {
                                level: heading_level,
                                text: current_text.trim().to_string(),
                            });
                            in_heading = false;
                            current_text.clear();
                        }
                    }
                    Tag::Paragraph => {
                        if in_paragraph && !current_text.trim().is_empty() {
                            elements.push(DocumentElement::Paragraph {
                                text: current_text.trim().to_string(),
                            });
                            in_paragraph = false;
                            current_text.clear();
                        }
                    }
                    Tag::CodeBlock(_) => {
                        if in_code_block {
                            elements.push(DocumentElement::Code {
                                text: current_text.clone(),
                                language: code_language.clone(),
                            });
                            in_code_block = false;
                            current_text.clear();
                            code_language = None;
                        }
                    }
                    Tag::List(_) => {
                        if in_list && !list_items.is_empty() {
                            elements.push(DocumentElement::List {
                                items: list_items.clone(),
                                ordered: list_ordered,
                            });
                            in_list = false;
                            list_items.clear();
                        }
                    }
                    Tag::Item => {
                        if in_list && !current_text.trim().is_empty() {
                            list_items.push(current_text.trim().to_string());
                            current_text.clear();
                        }
                    }
                    _ => {}
                }
            }
            Event::Text(text) => {
                current_text.push_str(&text);
            }
            Event::Code(code) => {
                current_text.push('`');
                current_text.push_str(&code);
                current_text.push('`');
            }
            Event::SoftBreak | Event::HardBreak => {
                current_text.push('\n');
            }
            _ => {}
        }
    }

    // Handle any remaining content
    if !current_text.trim().is_empty() {
        if in_paragraph {
            elements.push(DocumentElement::Paragraph {
                text: current_text.trim().to_string(),
            });
        }
    }

    // Extract title from first heading
    let title = elements.iter()
        .find_map(|element| {
            if let DocumentElement::Heading { level: 1, text } = element {
                Some(text.clone())
            } else {
                None
            }
        });

    let mut metadata = HashMap::new();
    metadata.insert("format".to_string(), Value::String("markdown".to_string()));
    metadata.insert("element_count".to_string(), Value::Number(elements.len().into()));

    Ok(ParsedDocument {
        title,
        content: content.to_string(),
        metadata,
        structure: elements,
    })
}

/// Generate markdown from structured document
pub fn generate_markdown(document: &ParsedDocument, _options: Option<&Value>) -> ServiceResult<String> {
    let mut output = String::new();

    for element in &document.structure {
        match element {
            DocumentElement::Heading { level, text } => {
                output.push_str(&"#".repeat(*level as usize));
                output.push(' ');
                output.push_str(text);
                output.push_str("\n\n");
            }
            DocumentElement::Paragraph { text } => {
                output.push_str(text);
                output.push_str("\n\n");
            }
            DocumentElement::List { items, ordered } => {
                for (i, item) in items.iter().enumerate() {
                    if *ordered {
                        output.push_str(&format!("{}. ", i + 1));
                    } else {
                        output.push_str("- ");
                    }
                    output.push_str(item);
                    output.push('\n');
                }
                output.push('\n');
            }
            DocumentElement::Code { text, language } => {
                output.push_str("```");
                if let Some(lang) = language {
                    output.push_str(lang);
                }
                output.push('\n');
                output.push_str(text);
                if !text.ends_with('\n') {
                    output.push('\n');
                }
                output.push_str("```\n\n");
            }
            DocumentElement::Quote { text } => {
                for line in text.lines() {
                    output.push_str("> ");
                    output.push_str(line);
                    output.push('\n');
                }
                output.push('\n');
            }
            DocumentElement::Table { headers, rows } => {
                // Generate markdown table
                if !headers.is_empty() {
                    output.push('|');
                    for header in headers {
                        output.push(' ');
                        output.push_str(header);
                        output.push_str(" |");
                    }
                    output.push('\n');

                    // Header separator
                    output.push('|');
                    for _ in headers {
                        output.push_str(" --- |");
                    }
                    output.push('\n');

                    // Rows
                    for row in rows {
                        output.push('|');
                        for (i, cell) in row.iter().enumerate() {
                            if i < headers.len() {
                                output.push(' ');
                                output.push_str(cell);
                                output.push_str(" |");
                            }
                        }
                        output.push('\n');
                    }
                    output.push('\n');
                }
            }
            DocumentElement::Image { alt, url } => {
                output.push_str(&format!("![{}]({})\n\n", alt, url));
            }
            DocumentElement::Link { text, url } => {
                output.push_str(&format!("[{}]({})", text, url));
            }
        }
    }

    Ok(output)
}
