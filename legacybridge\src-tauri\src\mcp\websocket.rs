// WebSocket Support for LegacyBridge MCP Server
// Provides real-time updates and progress tracking

use tokio_tungstenite::{
    accept_async,
    tungstenite::{Message, Error as WsError},
    WebSocketStream,
};
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::{broadcast, RwLock};
use futures_util::{StreamExt, SinkExt};
use serde::{Serialize, Deserialize};
use serde_json::json;
use std::sync::Arc;
use std::collections::HashMap;
use uuid::Uuid;
use std::net::SocketAddr;
use crate::mcp::types::{ConversionJob, JobStatus, ConversionResult};

/// WebSocket server for real-time MCP updates
pub struct WebSocketServer {
    /// Broadcast channel for progress updates
    progress_tx: broadcast::Sender<ProgressUpdate>,
    
    /// Active WebSocket connections
    connections: Arc<RwLock<HashMap<String, WebSocketConnection>>>,
    
    /// Server configuration
    config: WebSocketConfig,
}

/// WebSocket connection details
struct WebSocketConnection {
    id: String,
    addr: SocketAddr,
    subscriptions: Vec<String>,
    authenticated: bool,
}

/// Configuration for WebSocket server
#[derive(Debug, Clone)]
pub struct WebSocketConfig {
    pub bind_address: String,
    pub port: u16,
    pub max_connections: usize,
    pub heartbeat_interval: u64,
    pub enable_compression: bool,
}

impl Default for WebSocketConfig {
    fn default() -> Self {
        Self {
            bind_address: "127.0.0.1".to_string(),
            port: 8766,
            max_connections: 100,
            heartbeat_interval: 30,
            enable_compression: true,
        }
    }
}

/// Progress update message
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgressUpdate {
    pub job_id: String,
    pub update_type: UpdateType,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub data: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum UpdateType {
    JobStarted,
    JobProgress,
    JobCompleted,
    JobFailed,
    FileStarted,
    FileCompleted,
    FileFailed,
    StatusChange,
    MetricsUpdate,
}

impl WebSocketServer {
    /// Create a new WebSocket server
    pub fn new(config: WebSocketConfig) -> Self {
        let (progress_tx, _) = broadcast::channel(1000);
        
        Self {
            progress_tx,
            connections: Arc::new(RwLock::new(HashMap::new())),
            config,
        }
    }
    
    /// Start the WebSocket server
    pub async fn start(&self) -> Result<(), Box<dyn std::error::Error>> {
        let addr = format!("{}:{}", self.config.bind_address, self.config.port);
        let listener = TcpListener::bind(&addr).await?;
        
        println!("WebSocket server listening on: {}", addr);
        
        // Clone for the accept loop
        let connections = self.connections.clone();
        let progress_tx = self.progress_tx.clone();
        let config = self.config.clone();
        
        tokio::spawn(async move {
            while let Ok((stream, addr)) = listener.accept().await {
                if connections.read().await.len() >= config.max_connections {
                    println!("Maximum connections reached, rejecting: {}", addr);
                    continue;
                }
                
                tokio::spawn(Self::handle_connection(
                    stream,
                    addr,
                    connections.clone(),
                    progress_tx.clone(),
                ));
            }
        });
        
        Ok(())
    }
    
    /// Handle a new WebSocket connection
    async fn handle_connection(
        stream: TcpStream,
        addr: SocketAddr,
        connections: Arc<RwLock<HashMap<String, WebSocketConnection>>>,
        progress_tx: broadcast::Sender<ProgressUpdate>,
    ) {
        let ws_stream = match accept_async(stream).await {
            Ok(ws) => ws,
            Err(e) => {
                println!("WebSocket handshake failed for {}: {}", addr, e);
                return;
            }
        };
        
        let conn_id = Uuid::new_v4().to_string();
        let connection = WebSocketConnection {
            id: conn_id.clone(),
            addr,
            subscriptions: Vec::new(),
            authenticated: false,
        };
        
        // Add connection
        connections.write().await.insert(conn_id.clone(), connection);
        
        println!("New WebSocket connection: {} from {}", conn_id, addr);
        
        // Handle messages
        if let Err(e) = Self::handle_messages(
            ws_stream,
            conn_id.clone(),
            connections.clone(),
            progress_tx,
        ).await {
            println!("WebSocket error for {}: {}", conn_id, e);
        }
        
        // Remove connection
        connections.write().await.remove(&conn_id);
        println!("WebSocket disconnected: {}", conn_id);
    }
    
    /// Handle messages from a WebSocket connection
    async fn handle_messages(
        ws_stream: WebSocketStream<TcpStream>,
        conn_id: String,
        connections: Arc<RwLock<HashMap<String, WebSocketConnection>>>,
        progress_tx: broadcast::Sender<ProgressUpdate>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let (mut ws_sender, mut ws_receiver) = ws_stream.split();
        let mut progress_rx = progress_tx.subscribe();
        
        // Send welcome message
        let welcome = json!({
            "type": "welcome",
            "connection_id": conn_id,
            "protocol_version": "1.0",
            "capabilities": ["job_tracking", "real_time_progress", "metrics", "subscriptions"]
        });
        
        ws_sender.send(Message::Text(serde_json::to_string(&welcome)?)).await?;
        
        // Message handling loop
        loop {
            tokio::select! {
                // Handle incoming messages
                msg = ws_receiver.next() => {
                    match msg {
                        Some(Ok(Message::Text(text))) => {
                            if let Ok(request) = serde_json::from_str::<WebSocketRequest>(&text) {
                                Self::handle_request(
                                    request,
                                    &conn_id,
                                    &mut ws_sender,
                                    &connections,
                                ).await?;
                            }
                        }
                        Some(Ok(Message::Ping(data))) => {
                            ws_sender.send(Message::Pong(data)).await?;
                        }
                        Some(Ok(Message::Close(_))) => break,
                        Some(Err(e)) => {
                            println!("WebSocket receive error: {}", e);
                            break;
                        }
                        None => break,
                        _ => {}
                    }
                }
                
                // Handle progress updates
                update = progress_rx.recv() => {
                    if let Ok(update) = update {
                        // Check if this connection is subscribed to this job
                        let conns = connections.read().await;
                        if let Some(conn) = conns.get(&conn_id) {
                            if conn.subscriptions.contains(&update.job_id) || 
                               conn.subscriptions.contains(&"*".to_string()) {
                                let msg = json!({
                                    "type": "progress_update",
                                    "data": update
                                });
                                ws_sender.send(Message::Text(serde_json::to_string(&msg)?)).await?;
                            }
                        }
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// Handle a WebSocket request
    async fn handle_request(
        request: WebSocketRequest,
        conn_id: &str,
        ws_sender: &mut futures_util::stream::SplitSink<WebSocketStream<TcpStream>, Message>,
        connections: &Arc<RwLock<HashMap<String, WebSocketConnection>>>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let response = match request.method.as_str() {
            "subscribe" => {
                if let Some(job_id) = request.params.get("job_id").and_then(|v| v.as_str()) {
                    let mut conns = connections.write().await;
                    if let Some(conn) = conns.get_mut(conn_id) {
                        if !conn.subscriptions.contains(&job_id.to_string()) {
                            conn.subscriptions.push(job_id.to_string());
                        }
                    }
                    
                    json!({
                        "type": "subscribe_response",
                        "success": true,
                        "job_id": job_id,
                        "message": "Subscribed to job updates"
                    })
                } else {
                    json!({
                        "type": "error",
                        "message": "Missing job_id parameter"
                    })
                }
            }
            
            "unsubscribe" => {
                if let Some(job_id) = request.params.get("job_id").and_then(|v| v.as_str()) {
                    let mut conns = connections.write().await;
                    if let Some(conn) = conns.get_mut(conn_id) {
                        conn.subscriptions.retain(|id| id != job_id);
                    }
                    
                    json!({
                        "type": "unsubscribe_response",
                        "success": true,
                        "job_id": job_id,
                        "message": "Unsubscribed from job updates"
                    })
                } else {
                    json!({
                        "type": "error",
                        "message": "Missing job_id parameter"
                    })
                }
            }
            
            "subscribe_all" => {
                let mut conns = connections.write().await;
                if let Some(conn) = conns.get_mut(conn_id) {
                    if !conn.subscriptions.contains(&"*".to_string()) {
                        conn.subscriptions.push("*".to_string());
                    }
                }
                
                json!({
                    "type": "subscribe_all_response",
                    "success": true,
                    "message": "Subscribed to all job updates"
                })
            }
            
            "ping" => {
                json!({
                    "type": "pong",
                    "timestamp": chrono::Utc::now()
                })
            }
            
            _ => {
                json!({
                    "type": "error",
                    "message": format!("Unknown method: {}", request.method)
                })
            }
        };
        
        ws_sender.send(Message::Text(serde_json::to_string(&response)?)).await?;
        Ok(())
    }
    
    /// Send a progress update to all subscribed clients
    pub async fn send_progress_update(&self, update: ProgressUpdate) {
        let _ = self.progress_tx.send(update);
    }
    
    /// Create a progress update for a job
    pub fn create_job_progress(
        job: &ConversionJob,
        update_type: UpdateType,
    ) -> ProgressUpdate {
        ProgressUpdate {
            job_id: job.id.clone(),
            update_type,
            timestamp: chrono::Utc::now(),
            data: json!({
                "status": job.status,
                "total_files": job.total_files,
                "processed_files": job.processed_files,
                "successful_conversions": job.successful_conversions,
                "failed_conversions": job.failed_conversions,
                "progress_percentage": (job.processed_files as f64 / job.total_files as f64) * 100.0,
            }),
        }
    }
    
    /// Create a progress update for a file
    pub fn create_file_progress(
        job_id: &str,
        file_name: &str,
        status: &str,
        details: Option<serde_json::Value>,
    ) -> ProgressUpdate {
        let update_type = match status {
            "started" => UpdateType::FileStarted,
            "completed" => UpdateType::FileCompleted,
            "failed" => UpdateType::FileFailed,
            _ => UpdateType::StatusChange,
        };
        
        ProgressUpdate {
            job_id: job_id.to_string(),
            update_type,
            timestamp: chrono::Utc::now(),
            data: json!({
                "file_name": file_name,
                "status": status,
                "details": details
            }),
        }
    }
    
    /// Get connection statistics
    pub async fn get_stats(&self) -> WebSocketStats {
        let connections = self.connections.read().await;
        let total_subscriptions: usize = connections.values()
            .map(|c| c.subscriptions.len())
            .sum();
        
        WebSocketStats {
            active_connections: connections.len(),
            total_subscriptions,
            authenticated_connections: connections.values()
                .filter(|c| c.authenticated)
                .count(),
        }
    }
}

#[derive(Debug, Deserialize)]
struct WebSocketRequest {
    method: String,
    params: serde_json::Value,
}

#[derive(Debug, Serialize)]
pub struct WebSocketStats {
    pub active_connections: usize,
    pub total_subscriptions: usize,
    pub authenticated_connections: usize,
}

// Integration with the main MCP server
impl crate::mcp::server::LegacyBridgeMcpServer {
    /// Enable WebSocket support
    pub async fn enable_websocket(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if self.config.enable_websocket {
            let ws_config = WebSocketConfig::default();
            let ws_server = WebSocketServer::new(ws_config);
            ws_server.start().await?;
            
            // Store WebSocket server reference (would need to add field to struct)
            // self.websocket_server = Some(ws_server);
        }
        Ok(())
    }
    
    /// Send progress update via WebSocket
    pub async fn send_ws_progress(&self, update: ProgressUpdate) {
        // if let Some(ws_server) = &self.websocket_server {
        //     ws_server.send_progress_update(update).await;
        // }
    }
}