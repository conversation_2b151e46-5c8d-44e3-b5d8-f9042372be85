terraform {
  required_version = ">= 1.0"
  
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.0"
    }
  }
}

# Cloud Provider Selection
variable "cloud_provider" {
  description = "Cloud provider to deploy to (aws, azure, gcp)"
  type        = string
  validation {
    condition     = contains(["aws", "azure", "gcp"], var.cloud_provider)
    error_message = "Cloud provider must be one of: aws, azure, gcp"
  }
}

# Common Variables
variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "region" {
  description = "Cloud region"
  type        = string
}

variable "project_name" {
  description = "Project name"
  type        = string
  default     = "legacybridge"
}

# Kubernetes Configuration
variable "kubernetes_version" {
  description = "Kubernetes version"
  type        = string
  default     = "1.28"
}

variable "node_count" {
  description = "Initial number of nodes"
  type        = number
  default     = 3
}

variable "min_node_count" {
  description = "Minimum number of nodes"
  type        = number
  default     = 2
}

variable "max_node_count" {
  description = "Maximum number of nodes"
  type        = number
  default     = 20
}

variable "node_instance_type" {
  description = "Instance type for nodes"
  type        = string
  default     = "t3.large" # AWS default
}

# Database Configuration
variable "db_instance_type" {
  description = "Database instance type"
  type        = string
  default     = "db.t3.medium" # AWS default
}

variable "db_storage_size" {
  description = "Database storage size in GB"
  type        = number
  default     = 100
}

variable "db_version" {
  description = "PostgreSQL version"
  type        = string
  default     = "15"
}

# Redis Configuration
variable "redis_instance_type" {
  description = "Redis instance type"
  type        = string
  default     = "cache.t3.micro" # AWS default
}

# Tags
variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default = {
    Project     = "LegacyBridge"
    ManagedBy   = "Terraform"
  }
}

# Local values
locals {
  name_prefix = "${var.project_name}-${var.environment}"
  
  # Provider-specific instance type mapping
  instance_types = {
    aws = {
      node     = var.node_instance_type
      database = var.db_instance_type
      redis    = var.redis_instance_type
    }
    azure = {
      node     = "Standard_DS2_v2"
      database = "GP_Gen5_2"
      redis    = "Standard"
    }
    gcp = {
      node     = "n1-standard-2"
      database = "db-n1-standard-2"
      redis    = "STANDARD_HA"
    }
  }
}

# Provider Configuration
provider "aws" {
  region = var.cloud_provider == "aws" ? var.region : "us-east-1"
  skip_region_validation = var.cloud_provider != "aws"
}

provider "azurerm" {
  features {}
  skip_provider_registration = var.cloud_provider != "azure"
}

provider "google" {
  project = var.cloud_provider == "gcp" ? var.gcp_project_id : ""
  region  = var.cloud_provider == "gcp" ? var.region : "us-central1"
}

# GCP-specific variables
variable "gcp_project_id" {
  description = "GCP Project ID"
  type        = string
  default     = ""
}

# Deploy based on selected cloud provider
module "aws_infrastructure" {
  source = "./modules/aws"
  count  = var.cloud_provider == "aws" ? 1 : 0
  
  environment         = var.environment
  region             = var.region
  name_prefix        = local.name_prefix
  kubernetes_version = var.kubernetes_version
  node_count         = var.node_count
  min_node_count     = var.min_node_count
  max_node_count     = var.max_node_count
  node_instance_type = local.instance_types.aws.node
  db_instance_type   = local.instance_types.aws.database
  db_storage_size    = var.db_storage_size
  db_version         = var.db_version
  redis_instance_type = local.instance_types.aws.redis
  tags               = var.tags
}

module "azure_infrastructure" {
  source = "./modules/azure"
  count  = var.cloud_provider == "azure" ? 1 : 0
  
  environment         = var.environment
  location           = var.region
  name_prefix        = local.name_prefix
  kubernetes_version = var.kubernetes_version
  node_count         = var.node_count
  min_node_count     = var.min_node_count
  max_node_count     = var.max_node_count
  node_vm_size       = local.instance_types.azure.node
  db_sku_name        = local.instance_types.azure.database
  db_storage_size    = var.db_storage_size
  db_version         = var.db_version
  redis_sku          = local.instance_types.azure.redis
  tags               = var.tags
}

module "gcp_infrastructure" {
  source = "./modules/gcp"
  count  = var.cloud_provider == "gcp" ? 1 : 0
  
  project_id         = var.gcp_project_id
  environment        = var.environment
  region             = var.region
  zone               = "${var.region}-a"
  name_prefix        = local.name_prefix
  kubernetes_version = var.kubernetes_version
  node_count         = var.node_count
  min_node_count     = var.min_node_count
  max_node_count     = var.max_node_count
  node_machine_type  = local.instance_types.gcp.node
  db_tier            = local.instance_types.gcp.database
  db_disk_size       = var.db_storage_size
  db_version         = "POSTGRES_${var.db_version}"
  redis_tier         = local.instance_types.gcp.redis
  labels             = var.tags
}

# Outputs
output "kubernetes_cluster_name" {
  description = "Kubernetes cluster name"
  value = coalesce(
    try(module.aws_infrastructure[0].cluster_name, ""),
    try(module.azure_infrastructure[0].cluster_name, ""),
    try(module.gcp_infrastructure[0].cluster_name, "")
  )
}

output "kubernetes_cluster_endpoint" {
  description = "Kubernetes cluster endpoint"
  value = coalesce(
    try(module.aws_infrastructure[0].cluster_endpoint, ""),
    try(module.azure_infrastructure[0].cluster_endpoint, ""),
    try(module.gcp_infrastructure[0].cluster_endpoint, "")
  )
}

output "database_endpoint" {
  description = "Database endpoint"
  value = coalesce(
    try(module.aws_infrastructure[0].database_endpoint, ""),
    try(module.azure_infrastructure[0].database_endpoint, ""),
    try(module.gcp_infrastructure[0].database_endpoint, "")
  )
}

output "redis_endpoint" {
  description = "Redis endpoint"
  value = coalesce(
    try(module.aws_infrastructure[0].redis_endpoint, ""),
    try(module.azure_infrastructure[0].redis_endpoint, ""),
    try(module.gcp_infrastructure[0].redis_endpoint, "")
  )
}

output "storage_bucket" {
  description = "Storage bucket name"
  value = coalesce(
    try(module.aws_infrastructure[0].s3_bucket, ""),
    try(module.azure_infrastructure[0].storage_account, ""),
    try(module.gcp_infrastructure[0].storage_bucket, "")
  )
}

output "container_registry" {
  description = "Container registry URL"
  value = coalesce(
    try(module.aws_infrastructure[0].ecr_repository_url, ""),
    try(module.azure_infrastructure[0].acr_login_server, ""),
    try(module.gcp_infrastructure[0].gcr_url, "")
  )
}