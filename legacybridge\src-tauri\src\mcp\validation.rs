// Comprehensive Error Handling and Validation for LegacyBridge MCP Server
// Provides input validation, error recovery, and detailed error reporting

use serde::{Serialize, Deserialize};
use serde_json::{json, Value as JsonValue};
use std::fmt;
use std::collections::HashMap;
use crate::mcp::types::IntegrationError;

/// Validation result with detailed error information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationResult {
    pub valid: bool,
    pub errors: Vec<ValidationError>,
    pub warnings: Vec<ValidationWarning>,
    pub metadata: Option<JsonValue>,
}

impl ValidationResult {
    pub fn ok() -> Self {
        Self {
            valid: true,
            errors: Vec::new(),
            warnings: Vec::new(),
            metadata: None,
        }
    }
    
    pub fn error(error: ValidationError) -> Self {
        Self {
            valid: false,
            errors: vec![error],
            warnings: Vec::new(),
            metadata: None,
        }
    }
    
    pub fn with_warning(mut self, warning: ValidationWarning) -> Self {
        self.warnings.push(warning);
        self
    }
    
    pub fn with_metadata(mut self, metadata: JsonValue) -> Self {
        self.metadata = Some(metadata);
        self
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationError {
    pub code: String,
    pub message: String,
    pub field: Option<String>,
    pub context: Option<JsonValue>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationWarning {
    pub code: String,
    pub message: String,
    pub suggestion: Option<String>,
}

/// Input validator for MCP requests
pub struct InputValidator {
    max_file_size: usize,
    max_batch_size: usize,
    supported_formats: HashMap<String, FormatValidation>,
}

#[derive(Clone)]
struct FormatValidation {
    input_formats: Vec<String>,
    output_formats: Vec<String>,
    max_size: Option<usize>,
    validation_rules: Vec<ValidationRule>,
}

#[derive(Clone)]
enum ValidationRule {
    FileExtension(Vec<String>),
    MagicBytes(Vec<u8>),
    ContentPattern(String),
    Custom(fn(&[u8]) -> bool),
}

impl InputValidator {
    pub fn new(max_file_size: usize, max_batch_size: usize) -> Self {
        let mut supported_formats = HashMap::new();
        
        // RTF format validation
        supported_formats.insert("rtf".to_string(), FormatValidation {
            input_formats: vec!["rtf".to_string()],
            output_formats: vec!["md".to_string(), "html".to_string(), "txt".to_string(), "docx".to_string()],
            max_size: Some(100 * 1024 * 1024), // 100MB
            validation_rules: vec![
                ValidationRule::FileExtension(vec![".rtf".to_string()]),
                ValidationRule::MagicBytes(b"{\\rtf".to_vec()),
            ],
        });
        
        // Markdown format validation
        supported_formats.insert("md".to_string(), FormatValidation {
            input_formats: vec!["md".to_string(), "markdown".to_string()],
            output_formats: vec!["rtf".to_string(), "html".to_string(), "pdf".to_string(), "docx".to_string()],
            max_size: Some(50 * 1024 * 1024), // 50MB
            validation_rules: vec![
                ValidationRule::FileExtension(vec![".md".to_string(), ".markdown".to_string()]),
            ],
        });
        
        // DOC format validation
        supported_formats.insert("doc".to_string(), FormatValidation {
            input_formats: vec!["doc".to_string()],
            output_formats: vec!["md".to_string(), "rtf".to_string(), "html".to_string(), "docx".to_string()],
            max_size: Some(50 * 1024 * 1024), // 50MB
            validation_rules: vec![
                ValidationRule::FileExtension(vec![".doc".to_string()]),
                ValidationRule::MagicBytes(b"\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1".to_vec()),
            ],
        });
        
        // Add more format validations...
        
        Self {
            max_file_size,
            max_batch_size,
            supported_formats,
        }
    }
    
    /// Validate file conversion request
    pub fn validate_convert_file(&self, args: &JsonValue) -> ValidationResult {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        
        // Validate input_content
        if let Some(content) = args["input_content"].as_str() {
            // Check if base64 encoded
            if args["input_format"].as_str() != Some("auto") {
                if let Err(e) = base64::decode(content) {
                    if content.len() > 1000 {
                        warnings.push(ValidationWarning {
                            code: "BASE64_DECODE_WARNING".to_string(),
                            message: "Content appears to not be base64 encoded for binary format".to_string(),
                            suggestion: Some("Encode binary content as base64".to_string()),
                        });
                    }
                }
            }
            
            // Check size
            let size = content.len();
            if size > self.max_file_size {
                errors.push(ValidationError {
                    code: "FILE_TOO_LARGE".to_string(),
                    message: format!("File size {} exceeds maximum allowed size {}", size, self.max_file_size),
                    field: Some("input_content".to_string()),
                    context: Some(json!({
                        "actual_size": size,
                        "max_size": self.max_file_size
                    })),
                });
            }
        } else {
            errors.push(ValidationError {
                code: "MISSING_FIELD".to_string(),
                message: "Missing required field: input_content".to_string(),
                field: Some("input_content".to_string()),
                context: None,
            });
        }
        
        // Validate output_format
        if let Some(format) = args["output_format"].as_str() {
            if !self.is_valid_output_format(format) {
                errors.push(ValidationError {
                    code: "INVALID_OUTPUT_FORMAT".to_string(),
                    message: format!("Unsupported output format: {}", format),
                    field: Some("output_format".to_string()),
                    context: Some(json!({
                        "valid_formats": self.get_valid_output_formats()
                    })),
                });
            }
        } else {
            errors.push(ValidationError {
                code: "MISSING_FIELD".to_string(),
                message: "Missing required field: output_format".to_string(),
                field: Some("output_format".to_string()),
                context: None,
            });
        }
        
        // Validate options
        if let Some(options) = args["options"].as_object() {
            if let Some(quality) = options.get("quality") {
                if let Some(q) = quality.as_u64() {
                    if q < 1 || q > 10 {
                        errors.push(ValidationError {
                            code: "INVALID_OPTION_VALUE".to_string(),
                            message: "Quality must be between 1 and 10".to_string(),
                            field: Some("options.quality".to_string()),
                            context: Some(json!({"value": q})),
                        });
                    }
                }
            }
        }
        
        ValidationResult {
            valid: errors.is_empty(),
            errors,
            warnings,
            metadata: None,
        }
    }
    
    /// Validate batch conversion request
    pub fn validate_batch_convert(&self, args: &JsonValue) -> ValidationResult {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        
        // Validate files array
        if let Some(files) = args["files"].as_array() {
            if files.is_empty() {
                errors.push(ValidationError {
                    code: "EMPTY_BATCH".to_string(),
                    message: "Files array cannot be empty".to_string(),
                    field: Some("files".to_string()),
                    context: None,
                });
            } else if files.len() > self.max_batch_size {
                errors.push(ValidationError {
                    code: "BATCH_TOO_LARGE".to_string(),
                    message: format!("Batch size {} exceeds maximum {}", files.len(), self.max_batch_size),
                    field: Some("files".to_string()),
                    context: Some(json!({
                        "actual_size": files.len(),
                        "max_size": self.max_batch_size
                    })),
                });
            }
            
            // Validate each file
            for (index, file) in files.iter().enumerate() {
                if file["content"].is_null() {
                    errors.push(ValidationError {
                        code: "MISSING_FILE_CONTENT".to_string(),
                        message: format!("File at index {} missing content", index),
                        field: Some(format!("files[{}].content", index)),
                        context: None,
                    });
                }
                
                if file["filename"].is_null() {
                    errors.push(ValidationError {
                        code: "MISSING_FILENAME".to_string(),
                        message: format!("File at index {} missing filename", index),
                        field: Some(format!("files[{}].filename", index)),
                        context: None,
                    });
                }
            }
        } else {
            errors.push(ValidationError {
                code: "MISSING_FIELD".to_string(),
                message: "Missing required field: files".to_string(),
                field: Some("files".to_string()),
                context: None,
            });
        }
        
        // Validate parallel_jobs
        if let Some(jobs) = args["parallel_jobs"].as_u64() {
            if jobs < 1 || jobs > 16 {
                warnings.push(ValidationWarning {
                    code: "SUBOPTIMAL_PARALLEL_JOBS".to_string(),
                    message: format!("Parallel jobs {} may not be optimal", jobs),
                    suggestion: Some("Use 1-16 parallel jobs for best performance".to_string()),
                });
            }
        }
        
        ValidationResult {
            valid: errors.is_empty(),
            errors,
            warnings,
            metadata: None,
        }
    }
    
    /// Validate format detection request
    pub fn validate_detect_format(&self, args: &JsonValue) -> ValidationResult {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        
        if args["file_content"].is_null() {
            errors.push(ValidationError {
                code: "MISSING_FIELD".to_string(),
                message: "Missing required field: file_content".to_string(),
                field: Some("file_content".to_string()),
                context: None,
            });
        }
        
        ValidationResult {
            valid: errors.is_empty(),
            errors,
            warnings,
            metadata: None,
        }
    }
    
    fn is_valid_output_format(&self, format: &str) -> bool {
        matches!(format, "md" | "rtf" | "html" | "pdf" | "txt" | "csv" | "json" | "xml" | "docx")
    }
    
    fn get_valid_output_formats(&self) -> Vec<&str> {
        vec!["md", "rtf", "html", "pdf", "txt", "csv", "json", "xml", "docx"]
    }
}

/// Error recovery strategies
pub struct ErrorRecovery {
    recovery_strategies: HashMap<String, RecoveryStrategy>,
}

#[derive(Clone)]
enum RecoveryStrategy {
    Retry { max_attempts: u32, backoff_ms: u64 },
    Fallback { alternative_format: String },
    PartialSuccess { continue_on_error: bool },
    Transform { transformation: fn(&mut JsonValue) },
}

impl ErrorRecovery {
    pub fn new() -> Self {
        let mut strategies = HashMap::new();
        
        // Network errors - retry with backoff
        strategies.insert("NETWORK_ERROR".to_string(), RecoveryStrategy::Retry {
            max_attempts: 3,
            backoff_ms: 1000,
        });
        
        // Format detection failure - try alternative
        strategies.insert("FORMAT_DETECTION_FAILED".to_string(), RecoveryStrategy::Fallback {
            alternative_format: "txt".to_string(),
        });
        
        // Batch processing - continue on error
        strategies.insert("BATCH_ITEM_FAILED".to_string(), RecoveryStrategy::PartialSuccess {
            continue_on_error: true,
        });
        
        Self {
            recovery_strategies: strategies,
        }
    }
    
    /// Attempt to recover from an error
    pub async fn recover(&self, error_code: &str, context: &mut JsonValue) -> Result<JsonValue, IntegrationError> {
        match self.recovery_strategies.get(error_code) {
            Some(RecoveryStrategy::Retry { max_attempts, backoff_ms }) => {
                let attempt = context["attempt"].as_u64().unwrap_or(0) + 1;
                
                if attempt <= *max_attempts as u64 {
                    tokio::time::sleep(tokio::time::Duration::from_millis(*backoff_ms * attempt)).await;
                    context["attempt"] = json!(attempt);
                    Ok(json!({
                        "action": "retry",
                        "attempt": attempt,
                        "max_attempts": max_attempts
                    }))
                } else {
                    Err(IntegrationError::InternalError("Max retry attempts exceeded".to_string()))
                }
            }
            
            Some(RecoveryStrategy::Fallback { alternative_format }) => {
                Ok(json!({
                    "action": "fallback",
                    "alternative_format": alternative_format
                }))
            }
            
            Some(RecoveryStrategy::PartialSuccess { continue_on_error }) => {
                Ok(json!({
                    "action": "continue",
                    "continue_on_error": continue_on_error
                }))
            }
            
            Some(RecoveryStrategy::Transform { transformation }) => {
                transformation(context);
                Ok(json!({
                    "action": "transform",
                    "transformed": true
                }))
            }
            
            None => Err(IntegrationError::InternalError(format!("No recovery strategy for error: {}", error_code)))
        }
    }
}

/// Error handler with detailed reporting
pub struct ErrorHandler {
    error_mappings: HashMap<String, ErrorInfo>,
}

struct ErrorInfo {
    user_message: String,
    developer_message: String,
    http_status: u16,
    error_code: String,
    recoverable: bool,
}

impl ErrorHandler {
    pub fn new() -> Self {
        let mut mappings = HashMap::new();
        
        // File too large
        mappings.insert("FILE_TOO_LARGE".to_string(), ErrorInfo {
            user_message: "The file is too large to process. Please reduce the file size or split it into smaller files.".to_string(),
            developer_message: "File size exceeds configured maximum. Consider increasing max_file_size or implementing chunked processing.".to_string(),
            http_status: 413,
            error_code: "E001".to_string(),
            recoverable: false,
        });
        
        // Unsupported format
        mappings.insert("UNSUPPORTED_FORMAT".to_string(), ErrorInfo {
            user_message: "The file format is not supported. Please check the list of supported formats.".to_string(),
            developer_message: "Format not in supported formats list. Add format support or update validation rules.".to_string(),
            http_status: 400,
            error_code: "E002".to_string(),
            recoverable: false,
        });
        
        // Conversion failure
        mappings.insert("CONVERSION_FAILED".to_string(), ErrorInfo {
            user_message: "The conversion failed. The file may be corrupted or in an unexpected format.".to_string(),
            developer_message: "Conversion engine error. Check logs for detailed error information.".to_string(),
            http_status: 500,
            error_code: "E003".to_string(),
            recoverable: true,
        });
        
        Self {
            error_mappings: mappings,
        }
    }
    
    /// Create detailed error response
    pub fn create_error_response(&self, error_code: &str, context: Option<JsonValue>) -> JsonValue {
        let error_info = self.error_mappings.get(error_code)
            .cloned()
            .unwrap_or(ErrorInfo {
                user_message: "An unexpected error occurred".to_string(),
                developer_message: format!("Unknown error code: {}", error_code),
                http_status: 500,
                error_code: "E999".to_string(),
                recoverable: false,
            });
        
        json!({
            "error": {
                "code": error_info.error_code,
                "message": error_info.user_message,
                "developer_message": error_info.developer_message,
                "http_status": error_info.http_status,
                "recoverable": error_info.recoverable,
                "context": context,
                "timestamp": chrono::Utc::now(),
            }
        })
    }
}

// Extension for the MCP server
impl crate::mcp::server::LegacyBridgeMcpServer {
    /// Validate request before processing
    pub fn validate_request(&self, tool_name: &str, args: &JsonValue) -> ValidationResult {
        let validator = InputValidator::new(self.config.max_file_size, self.config.max_batch_size);
        
        match tool_name {
            "convert_file" => validator.validate_convert_file(args),
            "batch_convert" => validator.validate_batch_convert(args),
            "detect_file_format" => validator.validate_detect_format(args),
            _ => ValidationResult::ok(),
        }
    }
}