# AWS Infrastructure Deployment Guide

## Overview

This directory contains AWS CloudFormation templates for deploying LegacyBridge on AWS with enterprise-grade infrastructure.

## Architecture

The infrastructure includes:
- **VPC**: Multi-AZ setup with public/private subnets
- **EKS**: Managed Kubernetes cluster with auto-scaling node groups
- **RDS PostgreSQL**: Multi-AZ deployment with encryption
- **ElastiCache Redis**: Managed Redis for caching
- **S3**: Object storage for files
- **ECR**: Container registry for Docker images
- **Secrets Manager**: Secure credential storage

## Prerequisites

1. AWS CLI installed and configured
2. kubectl installed
3. eksctl (optional, for easier EKS management)
4. Sufficient IAM permissions

## Deployment Steps

### 1. Deploy the CloudFormation Stack

```bash
# Set your AWS region
export AWS_REGION=us-west-2

# Deploy the stack
aws cloudformation create-stack \
  --stack-name legacybridge-production \
  --template-body file://legacybridge-infrastructure.yaml \
  --parameters \
    ParameterKey=Environment,ParameterValue=production \
    ParameterKey=InstanceType,ParameterValue=t3.large \
    ParameterKey=MinSize,ParameterValue=3 \
    ParameterKey=MaxSize,ParameterValue=20 \
  --capabilities CAPABILITY_IAM \
  --region $AWS_REGION
```

### 2. Wait for Stack Creation

```bash
# Monitor stack creation
aws cloudformation wait stack-create-complete \
  --stack-name legacybridge-production \
  --region $AWS_REGION
```

### 3. Configure kubectl for EKS

```bash
# Get the cluster name
CLUSTER_NAME=$(aws cloudformation describe-stacks \
  --stack-name legacybridge-production \
  --query 'Stacks[0].Outputs[?OutputKey==`EKSClusterName`].OutputValue' \
  --output text \
  --region $AWS_REGION)

# Update kubeconfig
aws eks update-kubeconfig \
  --region $AWS_REGION \
  --name $CLUSTER_NAME
```

### 4. Deploy Kubernetes Resources

```bash
# Apply Kubernetes manifests
kubectl apply -f ../../k8s/
```

### 5. Configure Application Secrets

```bash
# Get database endpoint and secret
DB_ENDPOINT=$(aws cloudformation describe-stacks \
  --stack-name legacybridge-production \
  --query 'Stacks[0].Outputs[?OutputKey==`RDSEndpoint`].OutputValue' \
  --output text \
  --region $AWS_REGION)

DB_SECRET_ARN=$(aws cloudformation describe-stacks \
  --stack-name legacybridge-production \
  --query 'Stacks[0].Outputs[?OutputKey==`DBSecretARN`].OutputValue' \
  --output text \
  --region $AWS_REGION)

# Get database password
DB_PASSWORD=$(aws secretsmanager get-secret-value \
  --secret-id $DB_SECRET_ARN \
  --query 'SecretString' \
  --output text \
  --region $AWS_REGION | jq -r '.password')

# Create Kubernetes secret
kubectl create secret generic legacybridge-secrets \
  --from-literal=database-url="postgresql://postgres:${DB_PASSWORD}@${DB_ENDPOINT}:5432/legacybridge" \
  --namespace legacybridge
```

## Stack Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| Environment | production | Environment name (development/staging/production) |
| InstanceType | t3.large | EC2 instance type for worker nodes |
| MinSize | 3 | Minimum number of worker nodes |
| MaxSize | 20 | Maximum number of worker nodes |
| VpcCidr | 10.0.0.0/16 | CIDR block for VPC |
| DBInstanceClass | db.t3.medium | RDS instance type |
| CacheNodeType | cache.t3.micro | ElastiCache node type |

## Outputs

The CloudFormation stack exports the following outputs:

- `VPC-ID`: VPC identifier
- `EKS-CLUSTER`: EKS cluster name
- `EKS-ENDPOINT`: EKS cluster endpoint
- `RDS-ENDPOINT`: PostgreSQL database endpoint
- `REDIS-ENDPOINT`: Redis cache endpoint
- `S3-BUCKET`: S3 bucket name
- `ECR-URI`: ECR repository URI
- `DB-SECRET`: Database secret ARN

## Cost Optimization

1. **Use Spot Instances**: Modify the node group to use spot instances for cost savings
2. **Right-sizing**: Monitor usage and adjust instance types accordingly
3. **Reserved Instances**: Purchase RIs for predictable workloads
4. **Auto-scaling**: Configure HPA and cluster autoscaler for efficient resource usage

## Security Best Practices

1. **Network Security**: 
   - Private subnets for workloads
   - NAT gateways for outbound traffic
   - Security groups with least privilege

2. **Data Security**:
   - Encryption at rest for RDS and S3
   - SSL/TLS for data in transit
   - Secrets Manager for credentials

3. **Access Control**:
   - IAM roles for service accounts
   - RBAC for Kubernetes access
   - MFA for AWS console access

## Monitoring

1. **CloudWatch**: Logs and metrics are sent to CloudWatch
2. **Container Insights**: Enable for EKS monitoring
3. **RDS Performance Insights**: Monitor database performance
4. **Cost Explorer**: Track AWS spending

## Troubleshooting

### Stack Creation Fails

```bash
# Check stack events
aws cloudformation describe-stack-events \
  --stack-name legacybridge-production \
  --region $AWS_REGION
```

### EKS Node Connection Issues

```bash
# Check node status
kubectl get nodes

# Describe problematic node
kubectl describe node <node-name>
```

### Database Connection Issues

```bash
# Test database connection
kubectl run -it --rm debug --image=postgres:15 --restart=Never -- \
  psql "postgresql://postgres:${DB_PASSWORD}@${DB_ENDPOINT}:5432/legacybridge"
```

## Cleanup

To delete all resources:

```bash
# Delete Kubernetes resources first
kubectl delete namespace legacybridge

# Delete CloudFormation stack
aws cloudformation delete-stack \
  --stack-name legacybridge-production \
  --region $AWS_REGION
```

**WARNING**: This will delete all data including databases and S3 buckets!