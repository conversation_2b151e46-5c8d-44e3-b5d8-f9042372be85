apiVersion: v1
kind: ConfigMap
metadata:
  name: kibana-config
  namespace: monitoring
data:
  kibana.yml: |
    server.name: kibana
    server.host: "0.0.0.0"
    server.basePath: "/kibana"
    server.rewriteBasePath: true
    
    elasticsearch.hosts: [ "http://elasticsearch:9200" ]
    monitoring.ui.container.elasticsearch.enabled: true
    
    # Security settings
    xpack.security.enabled: false
    xpack.encryptedSavedObjects.encryptionKey: "min-32-byte-long-strong-encryption-key"
    
    # Telemetry
    telemetry.enabled: false
    telemetry.optIn: false
    
    # Logging
    logging.dest: stdout
    logging.quiet: false
    logging.verbose: false
    
    # Features
    xpack.reporting.enabled: true
    xpack.reporting.encryptionKey: "min-32-byte-long-strong-encryption-key"
    xpack.reporting.kibanaServer.hostname: "0.0.0.0"
    
    # Index patterns
    kibana.index: ".kibana"
    kibana.defaultAppId: "discover"
    
    # Saved object migrations
    migrations.batchSize: 1000
    migrations.scrollDuration: "15m"
    
    # Performance
    elasticsearch.requestTimeout: 30000
    elasticsearch.shardTimeout: 30000
    elasticsearch.maxSockets: 1024
    elasticsearch.compression: true
    
  dashboard-config.json: |
    {
      "version": "8.11.0",
      "objects": [
        {
          "id": "legacybridge-logs-dashboard",
          "type": "dashboard",
          "attributes": {
            "title": "LegacyBridge Logs Dashboard",
            "hits": 0,
            "description": "Central logging dashboard for LegacyBridge application",
            "panelsJSON": "[{\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15},\"type\":\"search\",\"id\":\"legacybridge-error-logs\"},{\"gridData\":{\"x\":24,\"y\":0,\"w\":24,\"h\":15},\"type\":\"visualization\",\"id\":\"legacybridge-log-levels\"},{\"gridData\":{\"x\":0,\"y\":15,\"w\":48,\"h\":15},\"type\":\"visualization\",\"id\":\"legacybridge-response-times\"}]",
            "timeRestore": true,
            "timeTo": "now",
            "timeFrom": "now-1h",
            "refreshInterval": {
              "pause": false,
              "value": 10000
            }
          }
        },
        {
          "id": "legacybridge-error-logs",
          "type": "search",
          "attributes": {
            "title": "LegacyBridge Error Logs",
            "columns": ["@timestamp", "level", "message", "kubernetes.pod_name"],
            "sort": ["@timestamp", "desc"],
            "kibanaSavedObjectMeta": {
              "searchSourceJSON": "{\"index\":\"legacybridge-*\",\"query\":{\"match\":{\"level\":\"ERROR\"}}}"
            }
          }
        },
        {
          "id": "legacybridge-log-levels",
          "type": "visualization",
          "attributes": {
            "title": "Log Levels Distribution",
            "visState": "{\"type\":\"pie\",\"params\":{\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\"},\"aggs\":[{\"id\":\"1\",\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"type\":\"terms\",\"schema\":\"segment\",\"params\":{\"field\":\"level\",\"size\":5}}]}"
          }
        }
      ]
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kibana
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kibana
  template:
    metadata:
      labels:
        app: kibana
    spec:
      containers:
      - name: kibana
        image: docker.elastic.co/kibana/kibana:8.11.0
        env:
        - name: ELASTICSEARCH_HOSTS
          value: http://elasticsearch:9200
        - name: SERVER_BASEPATH
          value: /kibana
        - name: SERVER_REWRITEBASEPATH
          value: "true"
        ports:
        - containerPort: 5601
          name: http
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
        volumeMounts:
        - name: config
          mountPath: /usr/share/kibana/config/kibana.yml
          subPath: kibana.yml
        readinessProbe:
          httpGet:
            path: /kibana/api/status
            port: 5601
          initialDelaySeconds: 30
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /kibana/api/status
            port: 5601
          initialDelaySeconds: 90
          periodSeconds: 10
      volumes:
      - name: config
        configMap:
          name: kibana-config
---
apiVersion: v1
kind: Service
metadata:
  name: kibana
  namespace: monitoring
spec:
  ports:
  - port: 5601
    targetPort: 5601
  selector:
    app: kibana