// Security Audit Logging System for LegacyBridge
use std::sync::{Arc, RwLock};
use std::collections::VecDeque;
use std::path::PathBuf;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use tokio::fs::OpenOptions;
use tokio::io::AsyncWriteExt;

/// Security audit logger for tracking all security-related events
pub struct SecurityAuditLogger {
    config: AuditConfig,
    buffer: Arc<RwLock<VecDeque<AuditEvent>>>,
    file_writer: Option<Arc<tokio::sync::Mutex<tokio::fs::File>>>,
}

#[derive(Debug, Clone)]
pub struct AuditConfig {
    /// Enable audit logging
    pub enabled: bool,
    
    /// Maximum events to keep in memory
    pub buffer_size: usize,
    
    /// Log file path
    pub log_file_path: Option<PathBuf>,
    
    /// Log to stdout
    pub log_to_stdout: bool,
    
    /// Minimum event level to log
    pub min_level: AuditEventLevel,
    
    /// Include detailed request data
    pub include_request_data: bool,
    
    /// Rotate log file size (bytes)
    pub max_file_size: u64,
    
    /// Retention period (days)
    pub retention_days: u32,
}

impl Default for AuditConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            buffer_size: 10000,
            log_file_path: Some(PathBuf::from("./logs/security_audit.log")),
            log_to_stdout: false,
            min_level: AuditEventLevel::Info,
            include_request_data: true,
            max_file_size: 100 * 1024 * 1024, // 100MB
            retention_days: 90,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditEvent {
    /// Unique event ID
    pub id: String,
    
    /// Event timestamp
    pub timestamp: DateTime<Utc>,
    
    /// Event type
    pub event_type: AuditEventType,
    
    /// Event level
    pub level: AuditEventLevel,
    
    /// User/client identifier
    pub client_id: Option<String>,
    
    /// Source IP address
    pub source_ip: Option<String>,
    
    /// User agent
    pub user_agent: Option<String>,
    
    /// Request ID for correlation
    pub request_id: Option<String>,
    
    /// Event description
    pub description: String,
    
    /// Additional context data
    pub context: serde_json::Value,
    
    /// Outcome of the event
    pub outcome: AuditEventOutcome,
    
    /// Associated threat information
    pub threat_info: Option<ThreatInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AuditEventType {
    // Authentication events
    AuthenticationAttempt,
    AuthenticationSuccess,
    AuthenticationFailure,
    
    // Authorization events
    AuthorizationCheck,
    AuthorizationDenied,
    
    // Input validation events
    InputValidation,
    InputValidationFailure,
    ThreatDetected,
    ContentSanitized,
    
    // Rate limiting events
    RateLimitCheck,
    RateLimitExceeded,
    
    // Resource access events
    FileAccess,
    FileAccessDenied,
    ConversionAttempt,
    ConversionSuccess,
    ConversionFailure,
    
    // Security violations
    SecurityViolation,
    SuspiciousActivity,
    MaliciousContentBlocked,
    
    // Configuration changes
    ConfigurationChange,
    SecurityPolicyUpdate,
    
    // System events
    SystemStartup,
    SystemShutdown,
    SecurityScan,
    AuditLogRotation,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Ord, PartialOrd, Eq)]
pub enum AuditEventLevel {
    Debug,
    Info,
    Warning,
    Error,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AuditEventOutcome {
    Success,
    Failure,
    Blocked,
    Allowed,
    Unknown,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatInfo {
    pub threat_type: String,
    pub severity: String,
    pub confidence: f32,
    pub mitigation_applied: bool,
    pub details: serde_json::Value,
}

impl SecurityAuditLogger {
    pub async fn new(config: AuditConfig) -> Result<Self, AuditError> {
        let buffer = Arc::new(RwLock::new(VecDeque::with_capacity(config.buffer_size)));
        
        let file_writer = if let Some(ref path) = config.log_file_path {
            // Create log directory if it doesn't exist
            if let Some(parent) = path.parent() {
                tokio::fs::create_dir_all(parent).await
                    .map_err(|e| AuditError::FileError(format!("Failed to create log directory: {}", e)))?;
            }
            
            // Open log file in append mode
            let file = OpenOptions::new()
                .create(true)
                .append(true)
                .open(path)
                .await
                .map_err(|e| AuditError::FileError(format!("Failed to open log file: {}", e)))?;
            
            Some(Arc::new(tokio::sync::Mutex::new(file)))
        } else {
            None
        };
        
        Ok(Self {
            config,
            buffer,
            file_writer,
        })
    }
    
    /// Log an audit event
    pub async fn log_event(&self, mut event: AuditEvent) -> Result<(), AuditError> {
        if !self.config.enabled {
            return Ok(());
        }
        
        // Check event level
        if event.level < self.config.min_level {
            return Ok(());
        }
        
        // Ensure event has an ID
        if event.id.is_empty() {
            event.id = uuid::Uuid::new_v4().to_string();
        }
        
        // Add to buffer
        {
            let mut buffer = self.buffer.write().unwrap();
            buffer.push_back(event.clone());
            
            // Maintain buffer size
            while buffer.len() > self.config.buffer_size {
                buffer.pop_front();
            }
        }
        
        // Write to file if configured
        if let Some(ref writer) = self.file_writer {
            let mut file = writer.lock().await;
            let log_line = serde_json::to_string(&event)
                .map_err(|e| AuditError::SerializationError(e.to_string()))?;
            
            file.write_all(log_line.as_bytes()).await
                .map_err(|e| AuditError::FileError(format!("Failed to write to log file: {}", e)))?;
            file.write_all(b"\n").await
                .map_err(|e| AuditError::FileError(format!("Failed to write newline: {}", e)))?;
            file.flush().await
                .map_err(|e| AuditError::FileError(format!("Failed to flush log file: {}", e)))?;
        }
        
        // Log to stdout if configured
        if self.config.log_to_stdout {
            println!("[AUDIT] {}", serde_json::to_string(&event).unwrap_or_default());
        }
        
        Ok(())
    }
    
    /// Log an input validation event
    pub async fn log_validation(
        &self,
        client_id: Option<String>,
        request_id: String,
        format: &str,
        file_size: usize,
        validation_result: &crate::security::validator::ValidationResult,
    ) -> Result<(), AuditError> {
        let event_type = if validation_result.is_valid {
            AuditEventType::InputValidation
        } else {
            AuditEventType::InputValidationFailure
        };
        
        let level = if !validation_result.detected_threats.is_empty() {
            AuditEventLevel::Warning
        } else if !validation_result.is_valid {
            AuditEventLevel::Error
        } else {
            AuditEventLevel::Info
        };
        
        let threat_info = if !validation_result.detected_threats.is_empty() {
            let threat = &validation_result.detected_threats[0];
            Some(ThreatInfo {
                threat_type: format!("{:?}", threat.threat_type),
                severity: format!("{:?}", threat.severity),
                confidence: 1.0,
                mitigation_applied: validation_result.sanitized_content.is_some(),
                details: serde_json::json!({
                    "all_threats": validation_result.detected_threats.len(),
                    "description": threat.description,
                }),
            })
        } else {
            None
        };
        
        let event = AuditEvent {
            id: uuid::Uuid::new_v4().to_string(),
            timestamp: Utc::now(),
            event_type,
            level,
            client_id,
            source_ip: None,
            user_agent: None,
            request_id: Some(request_id),
            description: format!("Input validation for {} format, {} bytes", format, file_size),
            context: serde_json::json!({
                "format": format,
                "file_size": file_size,
                "errors": validation_result.errors.len(),
                "warnings": validation_result.warnings.len(),
                "threats": validation_result.detected_threats.len(),
            }),
            outcome: if validation_result.is_valid {
                AuditEventOutcome::Success
            } else {
                AuditEventOutcome::Failure
            },
            threat_info,
        };
        
        self.log_event(event).await
    }
    
    /// Log a rate limit event
    pub async fn log_rate_limit(
        &self,
        client_id: &str,
        exceeded: bool,
        current_rate: f64,
        limit: f64,
    ) -> Result<(), AuditError> {
        let event = AuditEvent {
            id: uuid::Uuid::new_v4().to_string(),
            timestamp: Utc::now(),
            event_type: if exceeded {
                AuditEventType::RateLimitExceeded
            } else {
                AuditEventType::RateLimitCheck
            },
            level: if exceeded {
                AuditEventLevel::Warning
            } else {
                AuditEventLevel::Debug
            },
            client_id: Some(client_id.to_string()),
            source_ip: None,
            user_agent: None,
            request_id: None,
            description: if exceeded {
                format!("Rate limit exceeded: {:.2} > {:.2}", current_rate, limit)
            } else {
                format!("Rate limit check: {:.2} / {:.2}", current_rate, limit)
            },
            context: serde_json::json!({
                "current_rate": current_rate,
                "limit": limit,
                "exceeded": exceeded,
            }),
            outcome: if exceeded {
                AuditEventOutcome::Blocked
            } else {
                AuditEventOutcome::Allowed
            },
            threat_info: None,
        };
        
        self.log_event(event).await
    }
    
    /// Log a conversion event
    pub async fn log_conversion(
        &self,
        client_id: Option<String>,
        request_id: String,
        input_format: &str,
        output_format: &str,
        file_size: usize,
        duration_ms: u64,
        success: bool,
        error: Option<String>,
    ) -> Result<(), AuditError> {
        let event = AuditEvent {
            id: uuid::Uuid::new_v4().to_string(),
            timestamp: Utc::now(),
            event_type: if success {
                AuditEventType::ConversionSuccess
            } else {
                AuditEventType::ConversionFailure
            },
            level: if success {
                AuditEventLevel::Info
            } else {
                AuditEventLevel::Error
            },
            client_id,
            source_ip: None,
            user_agent: None,
            request_id: Some(request_id),
            description: format!(
                "Conversion {} -> {}, {} bytes, {} ms",
                input_format, output_format, file_size, duration_ms
            ),
            context: serde_json::json!({
                "input_format": input_format,
                "output_format": output_format,
                "file_size": file_size,
                "duration_ms": duration_ms,
                "error": error,
            }),
            outcome: if success {
                AuditEventOutcome::Success
            } else {
                AuditEventOutcome::Failure
            },
            threat_info: None,
        };
        
        self.log_event(event).await
    }
    
    /// Log a security violation
    pub async fn log_security_violation(
        &self,
        client_id: Option<String>,
        violation_type: &str,
        description: &str,
        context: serde_json::Value,
    ) -> Result<(), AuditError> {
        let event = AuditEvent {
            id: uuid::Uuid::new_v4().to_string(),
            timestamp: Utc::now(),
            event_type: AuditEventType::SecurityViolation,
            level: AuditEventLevel::Critical,
            client_id,
            source_ip: None,
            user_agent: None,
            request_id: None,
            description: description.to_string(),
            context: serde_json::json!({
                "violation_type": violation_type,
                "details": context,
            }),
            outcome: AuditEventOutcome::Blocked,
            threat_info: Some(ThreatInfo {
                threat_type: violation_type.to_string(),
                severity: "Critical".to_string(),
                confidence: 1.0,
                mitigation_applied: true,
                details: context,
            }),
        };
        
        self.log_event(event).await
    }
    
    /// Get recent audit events
    pub fn get_recent_events(&self, limit: usize) -> Vec<AuditEvent> {
        let buffer = self.buffer.read().unwrap();
        buffer.iter()
            .rev()
            .take(limit)
            .cloned()
            .collect()
    }
    
    /// Search audit events
    pub fn search_events(
        &self,
        event_type: Option<AuditEventType>,
        level: Option<AuditEventLevel>,
        client_id: Option<&str>,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
    ) -> Vec<AuditEvent> {
        let buffer = self.buffer.read().unwrap();
        
        buffer.iter()
            .filter(|event| {
                if let Some(ref et) = event_type {
                    if event.event_type != *et {
                        return false;
                    }
                }
                
                if let Some(ref l) = level {
                    if event.level < *l {
                        return false;
                    }
                }
                
                if let Some(cid) = client_id {
                    if event.client_id.as_deref() != Some(cid) {
                        return false;
                    }
                }
                
                if let Some(st) = start_time {
                    if event.timestamp < st {
                        return false;
                    }
                }
                
                if let Some(et) = end_time {
                    if event.timestamp > et {
                        return false;
                    }
                }
                
                true
            })
            .cloned()
            .collect()
    }
    
    /// Generate audit report
    pub fn generate_report(&self, start_time: DateTime<Utc>, end_time: DateTime<Utc>) -> AuditReport {
        let events = self.search_events(None, None, None, Some(start_time), Some(end_time));
        
        let mut report = AuditReport {
            start_time,
            end_time,
            total_events: events.len(),
            events_by_type: std::collections::HashMap::new(),
            events_by_level: std::collections::HashMap::new(),
            threat_summary: ThreatSummary::default(),
            top_clients: Vec::new(),
            conversion_summary: ConversionSummary::default(),
        };
        
        // Analyze events
        let mut client_counts: std::collections::HashMap<String, usize> = std::collections::HashMap::new();
        
        for event in &events {
            // Count by type
            *report.events_by_type.entry(format!("{:?}", event.event_type)).or_insert(0) += 1;
            
            // Count by level
            *report.events_by_level.entry(format!("{:?}", event.level)).or_insert(0) += 1;
            
            // Count threats
            if event.threat_info.is_some() {
                report.threat_summary.total_threats += 1;
                if matches!(event.outcome, AuditEventOutcome::Blocked) {
                    report.threat_summary.blocked_threats += 1;
                }
            }
            
            // Count clients
            if let Some(ref client_id) = event.client_id {
                *client_counts.entry(client_id.clone()).or_insert(0) += 1;
            }
            
            // Count conversions
            match event.event_type {
                AuditEventType::ConversionSuccess => report.conversion_summary.successful_conversions += 1,
                AuditEventType::ConversionFailure => report.conversion_summary.failed_conversions += 1,
                _ => {}
            }
        }
        
        // Get top clients
        let mut client_vec: Vec<_> = client_counts.into_iter().collect();
        client_vec.sort_by(|a, b| b.1.cmp(&a.1));
        report.top_clients = client_vec.into_iter().take(10).collect();
        
        report
    }
    
    /// Rotate log file if needed
    pub async fn rotate_log_if_needed(&self) -> Result<(), AuditError> {
        if let Some(ref path) = self.config.log_file_path {
            let metadata = tokio::fs::metadata(path).await
                .map_err(|e| AuditError::FileError(format!("Failed to get file metadata: {}", e)))?;
            
            if metadata.len() > self.config.max_file_size {
                // Create backup filename with timestamp
                let backup_name = format!(
                    "{}.{}.backup",
                    path.display(),
                    Utc::now().format("%Y%m%d_%H%M%S")
                );
                
                // Rename current file
                tokio::fs::rename(path, &backup_name).await
                    .map_err(|e| AuditError::FileError(format!("Failed to rotate log file: {}", e)))?;
                
                // Log rotation event
                let event = AuditEvent {
                    id: uuid::Uuid::new_v4().to_string(),
                    timestamp: Utc::now(),
                    event_type: AuditEventType::AuditLogRotation,
                    level: AuditEventLevel::Info,
                    client_id: None,
                    source_ip: None,
                    user_agent: None,
                    request_id: None,
                    description: format!("Log file rotated: {} -> {}", path.display(), backup_name),
                    context: serde_json::json!({
                        "original_size": metadata.len(),
                        "backup_file": backup_name,
                    }),
                    outcome: AuditEventOutcome::Success,
                    threat_info: None,
                };
                
                self.log_event(event).await?;
            }
        }
        
        Ok(())
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditReport {
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub total_events: usize,
    pub events_by_type: std::collections::HashMap<String, usize>,
    pub events_by_level: std::collections::HashMap<String, usize>,
    pub threat_summary: ThreatSummary,
    pub top_clients: Vec<(String, usize)>,
    pub conversion_summary: ConversionSummary,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ThreatSummary {
    pub total_threats: usize,
    pub blocked_threats: usize,
    pub threat_types: std::collections::HashMap<String, usize>,
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ConversionSummary {
    pub successful_conversions: usize,
    pub failed_conversions: usize,
    pub conversion_formats: std::collections::HashMap<String, usize>,
}

#[derive(Debug)]
pub enum AuditError {
    FileError(String),
    SerializationError(String),
    ConfigError(String),
}

impl std::fmt::Display for AuditError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AuditError::FileError(msg) => write!(f, "Audit file error: {}", msg),
            AuditError::SerializationError(msg) => write!(f, "Audit serialization error: {}", msg),
            AuditError::ConfigError(msg) => write!(f, "Audit configuration error: {}", msg),
        }
    }
}

impl std::error::Error for AuditError {}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_audit_logging() {
        let config = AuditConfig {
            enabled: true,
            buffer_size: 100,
            log_file_path: None, // No file for testing
            log_to_stdout: false,
            min_level: AuditEventLevel::Info,
            include_request_data: true,
            max_file_size: 1024 * 1024,
            retention_days: 7,
        };
        
        let logger = SecurityAuditLogger::new(config).await.unwrap();
        
        // Log a test event
        let event = AuditEvent {
            id: "test-123".to_string(),
            timestamp: Utc::now(),
            event_type: AuditEventType::InputValidation,
            level: AuditEventLevel::Info,
            client_id: Some("client-001".to_string()),
            source_ip: Some("***********".to_string()),
            user_agent: None,
            request_id: Some("req-001".to_string()),
            description: "Test event".to_string(),
            context: serde_json::json!({"test": true}),
            outcome: AuditEventOutcome::Success,
            threat_info: None,
        };
        
        logger.log_event(event).await.unwrap();
        
        // Check recent events
        let recent = logger.get_recent_events(10);
        assert_eq!(recent.len(), 1);
        assert_eq!(recent[0].id, "test-123");
    }
    
    #[tokio::test]
    async fn test_event_search() {
        let config = AuditConfig::default();
        let logger = SecurityAuditLogger::new(config).await.unwrap();
        
        // Log multiple events
        for i in 0..5 {
            let event = AuditEvent {
                id: format!("test-{}", i),
                timestamp: Utc::now(),
                event_type: if i % 2 == 0 {
                    AuditEventType::ConversionSuccess
                } else {
                    AuditEventType::ConversionFailure
                },
                level: AuditEventLevel::Info,
                client_id: Some(format!("client-{}", i % 2)),
                source_ip: None,
                user_agent: None,
                request_id: None,
                description: format!("Test event {}", i),
                context: serde_json::json!({}),
                outcome: if i % 2 == 0 {
                    AuditEventOutcome::Success
                } else {
                    AuditEventOutcome::Failure
                },
                threat_info: None,
            };
            logger.log_event(event).await.unwrap();
        }
        
        // Search for specific event type
        let success_events = logger.search_events(
            Some(AuditEventType::ConversionSuccess),
            None,
            None,
            None,
            None,
        );
        assert_eq!(success_events.len(), 3);
        
        // Search by client ID
        let client_events = logger.search_events(
            None,
            None,
            Some("client-0"),
            None,
            None,
        );
        assert_eq!(client_events.len(), 3);
    }
}