// Memory Pool Management for LegacyBridge
use std::sync::{Arc, Mutex};
use std::collections::{HashMap, VecDeque};
use std::alloc::{alloc, dealloc, Layout};
use std::ptr::NonNull;
use std::time::{Duration, Instant};
use serde::{Serialize, Deserialize};

/// Advanced memory pool manager with multiple pool sizes
pub struct MemoryPoolManager {
    /// Small object pools (8 bytes to 256 bytes)
    small_pools: HashMap<usize, Arc<MemoryPool>>,
    
    /// Medium object pools (512 bytes to 8KB)
    medium_pools: HashMap<usize, Arc<MemoryPool>>,
    
    /// Large object pools (16KB to 1MB)
    large_pools: HashMap<usize, Arc<MemoryPool>>,
    
    /// Pool configuration
    config: PoolConfig,
    
    /// Memory usage statistics
    stats: Arc<Mutex<MemoryStats>>,
}

#[derive(Debug, Clone)]
pub struct PoolConfig {
    /// Enable memory pooling
    pub enable_pooling: bool,
    
    /// Maximum number of blocks per pool
    pub max_blocks_per_pool: usize,
    
    /// Maximum total memory usage (bytes)
    pub max_total_memory: usize,
    
    /// Pool cleanup interval
    pub cleanup_interval: Duration,
    
    /// Enable zero-initialization
    pub zero_on_alloc: bool,
    
    /// Enable memory usage tracking
    pub track_usage: bool,
}

impl Default for PoolConfig {
    fn default() -> Self {
        Self {
            enable_pooling: true,
            max_blocks_per_pool: 1000,
            max_total_memory: 500 * 1024 * 1024, // 500MB
            cleanup_interval: Duration::from_secs(60),
            zero_on_alloc: true,
            track_usage: true,
        }
    }
}

/// Individual memory pool for a specific size
struct MemoryPool {
    block_size: usize,
    free_blocks: Mutex<VecDeque<MemoryBlock>>,
    allocated_count: Mutex<usize>,
    config: PoolConfig,
}

struct MemoryBlock {
    ptr: NonNull<u8>,
    size: usize,
    last_used: Instant,
}

unsafe impl Send for MemoryBlock {}
unsafe impl Sync for MemoryBlock {}

impl MemoryPoolManager {
    pub fn new() -> Self {
        Self::with_config(PoolConfig::default())
    }
    
    pub fn with_config(config: PoolConfig) -> Self {
        let mut small_pools = HashMap::new();
        let mut medium_pools = HashMap::new();
        let mut large_pools = HashMap::new();
        
        // Initialize small pools (powers of 2: 8, 16, 32, 64, 128, 256)
        for i in 3..=8 {
            let size = 1 << i;
            small_pools.insert(size, Arc::new(MemoryPool::new(size, config.clone())));
        }
        
        // Initialize medium pools (512, 1K, 2K, 4K, 8K)
        for i in 9..=13 {
            let size = 1 << i;
            medium_pools.insert(size, Arc::new(MemoryPool::new(size, config.clone())));
        }
        
        // Initialize large pools (16K, 32K, 64K, 128K, 256K, 512K, 1M)
        for i in 14..=20 {
            let size = 1 << i;
            large_pools.insert(size, Arc::new(MemoryPool::new(size, config.clone())));
        }
        
        Self {
            small_pools,
            medium_pools,
            large_pools,
            config,
            stats: Arc::new(Mutex::new(MemoryStats::new())),
        }
    }
    
    /// Allocate memory from the appropriate pool
    pub fn allocate(&self, size: usize) -> Result<PooledMemory, MemoryError> {
        if !self.config.enable_pooling {
            return self.allocate_direct(size);
        }
        
        // Find the appropriate pool size (round up to power of 2)
        let pool_size = size.next_power_of_two();
        
        // Check memory limits
        {
            let stats = self.stats.lock().unwrap();
            if stats.total_allocated + pool_size > self.config.max_total_memory {
                return Err(MemoryError::MemoryLimitExceeded {
                    current: stats.total_allocated,
                    requested: pool_size,
                    limit: self.config.max_total_memory,
                });
            }
        }
        
        // Select the appropriate pool
        let pool = if pool_size <= 256 {
            self.small_pools.get(&pool_size)
        } else if pool_size <= 8192 {
            self.medium_pools.get(&pool_size)
        } else if pool_size <= 1048576 {
            self.large_pools.get(&pool_size)
        } else {
            None
        };
        
        if let Some(pool) = pool {
            match pool.allocate() {
                Ok(block) => {
                    self.update_stats(pool_size, true);
                    Ok(PooledMemory {
                        ptr: block.ptr,
                        size: block.size,
                        actual_size: size,
                        pool: Some(Arc::clone(pool)),
                        stats: Arc::clone(&self.stats),
                    })
                }
                Err(_) => self.allocate_direct(size),
            }
        } else {
            self.allocate_direct(size)
        }
    }
    
    /// Direct allocation (bypassing pools)
    fn allocate_direct(&self, size: usize) -> Result<PooledMemory, MemoryError> {
        if size == 0 {
            return Err(MemoryError::InvalidSize);
        }
        
        let layout = Layout::from_size_align(size, 8)
            .map_err(|_| MemoryError::InvalidAlignment)?;
        
        let ptr = unsafe { alloc(layout) };
        
        if ptr.is_null() {
            return Err(MemoryError::AllocationFailed);
        }
        
        let ptr = unsafe { NonNull::new_unchecked(ptr) };
        
        if self.config.zero_on_alloc {
            unsafe {
                std::ptr::write_bytes(ptr.as_ptr(), 0, size);
            }
        }
        
        self.update_stats(size, true);
        
        Ok(PooledMemory {
            ptr,
            size,
            actual_size: size,
            pool: None,
            stats: Arc::clone(&self.stats),
        })
    }
    
    /// Update memory statistics
    fn update_stats(&self, size: usize, allocated: bool) {
        if self.config.track_usage {
            let mut stats = self.stats.lock().unwrap();
            if allocated {
                stats.total_allocated += size;
                stats.allocation_count += 1;
                stats.current_usage += size;
                stats.peak_usage = stats.peak_usage.max(stats.current_usage);
            } else {
                stats.current_usage = stats.current_usage.saturating_sub(size);
                stats.deallocation_count += 1;
            }
        }
    }
    
    /// Get current memory statistics
    pub fn get_stats(&self) -> MemoryStats {
        self.stats.lock().unwrap().clone()
    }
    
    /// Cleanup unused memory blocks
    pub async fn cleanup(&self) {
        let cutoff_time = Instant::now() - self.config.cleanup_interval;
        
        // Cleanup all pools
        for pool in self.small_pools.values() {
            pool.cleanup(cutoff_time);
        }
        for pool in self.medium_pools.values() {
            pool.cleanup(cutoff_time);
        }
        for pool in self.large_pools.values() {
            pool.cleanup(cutoff_time);
        }
    }
    
    /// Start background cleanup task
    pub fn start_cleanup_task(&self) -> tokio::task::JoinHandle<()> {
        let cleanup_interval = self.config.cleanup_interval;
        let manager = Arc::new(self.clone());
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(cleanup_interval);
            loop {
                interval.tick().await;
                manager.cleanup().await;
            }
        })
    }
    
    /// Cleanup memory pools
    pub fn cleanup(&self) {
        // This is a synchronous version of the async cleanup
        let cutoff_time = Instant::now() - self.config.cleanup_interval;
        
        // Cleanup all pools
        for pool in self.small_pools.values() {
            pool.cleanup(cutoff_time);
        }
        for pool in self.medium_pools.values() {
            pool.cleanup(cutoff_time);
        }
        for pool in self.large_pools.values() {
            pool.cleanup(cutoff_time);
        }
    }
}

impl Clone for MemoryPoolManager {
    fn clone(&self) -> Self {
        Self {
            small_pools: self.small_pools.clone(),
            medium_pools: self.medium_pools.clone(),
            large_pools: self.large_pools.clone(),
            config: self.config.clone(),
            stats: Arc::clone(&self.stats),
        }
    }
}

impl MemoryPool {
    fn new(block_size: usize, config: PoolConfig) -> Self {
        Self {
            block_size,
            free_blocks: Mutex::new(VecDeque::with_capacity(config.max_blocks_per_pool)),
            allocated_count: Mutex::new(0),
            config,
        }
    }
    
    fn allocate(&self) -> Result<MemoryBlock, MemoryError> {
        // Try to get a block from the pool
        let mut free_blocks = self.free_blocks.lock().unwrap();
        
        if let Some(mut block) = free_blocks.pop_front() {
            block.last_used = Instant::now();
            
            if self.config.zero_on_alloc {
                unsafe {
                    std::ptr::write_bytes(block.ptr.as_ptr(), 0, block.size);
                }
            }
            
            *self.allocated_count.lock().unwrap() += 1;
            return Ok(block);
        }
        
        drop(free_blocks);
        
        // Allocate a new block
        self.allocate_new()
    }
    
    fn allocate_new(&self) -> Result<MemoryBlock, MemoryError> {
        let layout = Layout::from_size_align(self.block_size, 8)
            .map_err(|_| MemoryError::InvalidAlignment)?;
        
        let ptr = unsafe { alloc(layout) };
        
        if ptr.is_null() {
            return Err(MemoryError::AllocationFailed);
        }
        
        let ptr = unsafe { NonNull::new_unchecked(ptr) };
        
        if self.config.zero_on_alloc {
            unsafe {
                std::ptr::write_bytes(ptr.as_ptr(), 0, self.block_size);
            }
        }
        
        *self.allocated_count.lock().unwrap() += 1;
        
        Ok(MemoryBlock {
            ptr,
            size: self.block_size,
            last_used: Instant::now(),
        })
    }
    
    fn deallocate(&self, block: MemoryBlock) {
        let mut free_blocks = self.free_blocks.lock().unwrap();
        
        if free_blocks.len() < self.config.max_blocks_per_pool {
            free_blocks.push_back(block);
            *self.allocated_count.lock().unwrap() -= 1;
        } else {
            // Pool is full, deallocate the block
            drop(free_blocks);
            self.deallocate_block(block);
        }
    }
    
    fn deallocate_block(&self, block: MemoryBlock) {
        let layout = Layout::from_size_align(block.size, 8).unwrap();
        unsafe {
            dealloc(block.ptr.as_ptr(), layout);
        }
        *self.allocated_count.lock().unwrap() -= 1;
    }
    
    fn cleanup(&self, cutoff_time: Instant) {
        let mut free_blocks = self.free_blocks.lock().unwrap();
        let mut to_remove = Vec::new();
        
        // Find blocks that haven't been used recently
        for (i, block) in free_blocks.iter().enumerate() {
            if block.last_used < cutoff_time {
                to_remove.push(i);
            }
        }
        
        // Remove old blocks (in reverse order to maintain indices)
        for &i in to_remove.iter().rev() {
            if let Some(block) = free_blocks.remove(i) {
                let layout = Layout::from_size_align(block.size, 8).unwrap();
                unsafe {
                    dealloc(block.ptr.as_ptr(), layout);
                }
            }
        }
    }
}

/// RAII wrapper for pooled memory
pub struct PooledMemory {
    ptr: NonNull<u8>,
    size: usize,
    actual_size: usize,
    pool: Option<Arc<MemoryPool>>,
    stats: Arc<Mutex<MemoryStats>>,
}

impl PooledMemory {
    /// Get a pointer to the memory
    pub fn as_ptr(&self) -> *const u8 {
        self.ptr.as_ptr()
    }
    
    /// Get a mutable pointer to the memory
    pub fn as_mut_ptr(&mut self) -> *mut u8 {
        self.ptr.as_ptr()
    }
    
    /// Get the size of the allocated memory
    pub fn size(&self) -> usize {
        self.actual_size
    }
    
    /// Get a slice view of the memory
    pub unsafe fn as_slice(&self) -> &[u8] {
        std::slice::from_raw_parts(self.ptr.as_ptr(), self.actual_size)
    }
    
    /// Get a mutable slice view of the memory
    pub unsafe fn as_mut_slice(&mut self) -> &mut [u8] {
        std::slice::from_raw_parts_mut(self.ptr.as_ptr(), self.actual_size)
    }
}

impl Drop for PooledMemory {
    fn drop(&mut self) {
        if let Some(pool) = &self.pool {
            // Return to pool
            let block = MemoryBlock {
                ptr: self.ptr,
                size: self.size,
                last_used: Instant::now(),
            };
            pool.deallocate(block);
        } else {
            // Direct deallocation
            let layout = Layout::from_size_align(self.size, 8).unwrap();
            unsafe {
                dealloc(self.ptr.as_ptr(), layout);
            }
        }
        
        // Update statistics
        let mut stats = self.stats.lock().unwrap();
        stats.current_usage = stats.current_usage.saturating_sub(self.size);
        stats.deallocation_count += 1;
    }
}

unsafe impl Send for PooledMemory {}
unsafe impl Sync for PooledMemory {}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryStats {
    pub total_allocated: usize,
    pub current_usage: usize,
    pub peak_usage: usize,
    pub allocation_count: u64,
    pub deallocation_count: u64,
    pub pool_hit_rate: f64,
    pub total_allocations: u64,
    pub pool_hits: u64,
    pub active_objects: u64,
}

impl MemoryStats {
    fn new() -> Self {
        Self {
            total_allocated: 0,
            current_usage: 0,
            peak_usage: 0,
            allocation_count: 0,
            deallocation_count: 0,
            pool_hit_rate: 0.0,
            total_allocations: 0,
            pool_hits: 0,
            active_objects: 0,
        }
    }
}

#[derive(Debug, Clone)]
pub enum MemoryError {
    AllocationFailed,
    InvalidSize,
    InvalidAlignment,
    PoolExhausted,
    MemoryLimitExceeded {
        current: usize,
        requested: usize,
        limit: usize,
    },
}

impl std::fmt::Display for MemoryError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MemoryError::AllocationFailed => write!(f, "Memory allocation failed"),
            MemoryError::InvalidSize => write!(f, "Invalid allocation size"),
            MemoryError::InvalidAlignment => write!(f, "Invalid memory alignment"),
            MemoryError::PoolExhausted => write!(f, "Memory pool exhausted"),
            MemoryError::MemoryLimitExceeded { current, requested, limit } => {
                write!(f, "Memory limit exceeded: current {} + requested {} > limit {}", 
                    current, requested, limit)
            }
        }
    }
}

impl std::error::Error for MemoryError {}

/// Utility functions for memory management
pub mod utils {
    use super::*;
    
    /// Allocate aligned memory for SIMD operations
    pub fn allocate_aligned(size: usize, alignment: usize) -> Result<PooledMemory, MemoryError> {
        if !alignment.is_power_of_two() {
            return Err(MemoryError::InvalidAlignment);
        }
        
        let layout = Layout::from_size_align(size, alignment)
            .map_err(|_| MemoryError::InvalidAlignment)?;
        
        let ptr = unsafe { alloc(layout) };
        
        if ptr.is_null() {
            return Err(MemoryError::AllocationFailed);
        }
        
        let ptr = unsafe { NonNull::new_unchecked(ptr) };
        
        Ok(PooledMemory {
            ptr,
            size,
            actual_size: size,
            pool: None,
            stats: Arc::new(Mutex::new(MemoryStats::new())),
        })
    }
    
    /// Copy data with SIMD acceleration when possible
    pub unsafe fn fast_copy(src: *const u8, dst: *mut u8, len: usize) {
        #[cfg(any(target_arch = "x86", target_arch = "x86_64"))]
        {
            if is_x86_feature_detected!("avx2") {
                return fast_copy_avx2(src, dst, len);
            } else if is_x86_feature_detected!("sse2") {
                return fast_copy_sse2(src, dst, len);
            }
        }
        
        // Fallback to standard copy
        std::ptr::copy_nonoverlapping(src, dst, len);
    }
    
    #[cfg(any(target_arch = "x86", target_arch = "x86_64"))]
    unsafe fn fast_copy_avx2(src: *const u8, dst: *mut u8, len: usize) {
        use std::arch::x86_64::*;
        
        let mut offset = 0;
        let chunk_size = 32; // AVX2 operates on 256-bit (32-byte) chunks
        
        while offset + chunk_size <= len {
            let data = _mm256_loadu_si256(src.add(offset) as *const __m256i);
            _mm256_storeu_si256(dst.add(offset) as *mut __m256i, data);
            offset += chunk_size;
        }
        
        // Copy remaining bytes
        if offset < len {
            std::ptr::copy_nonoverlapping(src.add(offset), dst.add(offset), len - offset);
        }
    }
    
    #[cfg(any(target_arch = "x86", target_arch = "x86_64"))]
    unsafe fn fast_copy_sse2(src: *const u8, dst: *mut u8, len: usize) {
        use std::arch::x86_64::*;
        
        let mut offset = 0;
        let chunk_size = 16; // SSE2 operates on 128-bit (16-byte) chunks
        
        while offset + chunk_size <= len {
            let data = _mm_loadu_si128(src.add(offset) as *const __m128i);
            _mm_storeu_si128(dst.add(offset) as *mut __m128i, data);
            offset += chunk_size;
        }
        
        // Copy remaining bytes
        if offset < len {
            std::ptr::copy_nonoverlapping(src.add(offset), dst.add(offset), len - offset);
        }
    }
}