'use client';

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  TestTube,
  CheckCircle,
  XCircle,
  Clock,
  Play,
  RotateCcw,
  Download,
  FileText,
  Zap,
  AlertTriangle,
  TrendingUp
} from 'lucide-react';

import { BuildStatus, TestResult, TestSuite } from '@/lib/dll/dll-config';
import { useDLLTester } from '@/lib/dll/test-runner';

interface TestingPanelProps {
  buildStatus: BuildStatus | null;
  testResults: TestResult[];
  isTesting: boolean;
  onRunTests: () => void;
}

export function TestingPanel({
  buildStatus,
  testResults,
  isTesting,
  onRunTests
}: TestingPanelProps) {
  const [selectedSuite, setSelectedSuite] = useState('compatibility');
  const [testProgress, setTestProgress] = useState(0);
  const [currentTest, setCurrentTest] = useState<string | null>(null);
  
  const { runTestSuite, generateReport } = useDLLTester();
  
  // Calculate test statistics
  const totalTests = testResults.length;
  const passedTests = testResults.filter(t => t.passed).length;
  const failedTests = totalTests - passedTests;
  const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;
  
  const runSpecificSuite = useCallback(async (suite: TestSuite) => {
    if (!buildStatus?.outputFiles.length) return;
    
    setCurrentTest(`Running ${suite} tests...`);
    setTestProgress(0);
    
    try {
      await runTestSuite(suite, buildStatus.outputFiles, (progress, test) => {
        setTestProgress(progress);
        setCurrentTest(test);
      });
    } catch (error) {
      console.error('Test suite failed:', error);
    }
  }, [buildStatus, runTestSuite]);
  
  const downloadReport = useCallback(async () => {
    if (testResults.length === 0) return;
    
    const report = await generateReport(testResults);
    const blob = new Blob([report], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `dll-test-report-${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [testResults, generateReport]);
  
  if (!buildStatus?.success) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <TestTube className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No DLL to Test</h3>
            <p className="text-muted-foreground mb-4">
              Please build the DLL successfully before running tests
            </p>
            <Button variant="outline" disabled>
              Testing Unavailable
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Testing Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <TestMetricCard
          title="Total Tests"
          value={totalTests}
          icon={TestTube}
          color="blue"
        />
        <TestMetricCard
          title="Passed"
          value={passedTests}
          icon={CheckCircle}
          color="green"
        />
        <TestMetricCard
          title="Failed"
          value={failedTests}
          icon={XCircle}
          color="red"
        />
        <TestMetricCard
          title="Success Rate"
          value={`${successRate.toFixed(1)}%`}
          icon={TrendingUp}
          color={successRate >= 90 ? "green" : successRate >= 70 ? "yellow" : "red"}
        />
      </div>
      
      {/* Main Testing Interface */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <TestTube className="w-5 h-5 text-blue-500" />
              DLL Compatibility Testing
            </CardTitle>
            <div className="flex items-center gap-2">
              {testResults.length > 0 && (
                <Button size="sm" variant="outline" onClick={downloadReport}>
                  <Download className="w-4 h-4 mr-1" />
                  Report
                </Button>
              )}
              <Button onClick={onRunTests} disabled={isTesting} size="sm">
                {isTesting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-b-transparent mr-2" />
                    Testing...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-1" />
                    Run All Tests
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Test Progress */}
          {isTesting && (
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Testing Progress</span>
                <span className="text-sm text-muted-foreground">{testProgress}%</span>
              </div>
              <Progress value={testProgress} className="mb-2" />
              {currentTest && (
                <p className="text-sm text-muted-foreground">{currentTest}</p>
              )}
            </div>
          )}
          
          {/* Test Suites */}
          <Tabs value={selectedSuite} onValueChange={setSelectedSuite}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="compatibility">Compatibility</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="integration">Integration</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
            </TabsList>
            
            <TabsContent value="compatibility" className="space-y-4">
              <CompatibilityTests 
                testResults={testResults.filter(t => t.category === 'compatibility')}
                onRunSuite={() => runSpecificSuite('compatibility')}
                isTesting={isTesting}
              />
            </TabsContent>
            
            <TabsContent value="performance" className="space-y-4">
              <PerformanceTests 
                testResults={testResults.filter(t => t.category === 'performance')}
                onRunSuite={() => runSpecificSuite('performance')}
                isTesting={isTesting}
              />
            </TabsContent>
            
            <TabsContent value="integration" className="space-y-4">
              <IntegrationTests 
                testResults={testResults.filter(t => t.category === 'integration')}
                onRunSuite={() => runSpecificSuite('integration')}
                isTesting={isTesting}
              />
            </TabsContent>
            
            <TabsContent value="security" className="space-y-4">
              <SecurityTests 
                testResults={testResults.filter(t => t.category === 'security')}
                onRunSuite={() => runSpecificSuite('security')}
                isTesting={isTesting}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      
      {/* Detailed Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <TestResultCard key={index} result={result} />
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Supporting Components
interface TestMetricCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<{ className?: string }>;
  color: 'blue' | 'green' | 'red' | 'yellow';
}

function TestMetricCard({ title, value, icon: Icon, color }: TestMetricCardProps) {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-100 dark:bg-blue-900/20',
    green: 'text-green-600 bg-green-100 dark:bg-green-900/20',
    red: 'text-red-600 bg-red-100 dark:bg-red-900/20',
    yellow: 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20'
  };
  
  return (
    <Card>
      <CardContent className="pt-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
          </div>
          <div className={`p-2 rounded-lg ${colorClasses[color]}`}>
            <Icon className="w-5 h-5" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function TestResultCard({ result }: { result: TestResult }) {
  const getStatusIcon = () => {
    if (result.passed) return CheckCircle;
    if (result.skipped) return Clock;
    return XCircle;
  };
  
  const getStatusColor = () => {
    if (result.passed) return 'text-green-600';
    if (result.skipped) return 'text-yellow-600';
    return 'text-red-600';
  };
  
  const StatusIcon = getStatusIcon();
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="border rounded-lg p-4"
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-3">
          <StatusIcon className={`w-5 h-5 mt-0.5 ${getStatusColor()}`} />
          <div>
            <h4 className="font-medium">{result.testName}</h4>
            <p className="text-sm text-muted-foreground">{result.platform}</p>
            {result.details && (
              <p className="text-sm mt-1">{result.details}</p>
            )}
            {result.error && (
              <Alert className="mt-2">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{result.error}</AlertDescription>
              </Alert>
            )}
          </div>
        </div>
        <div className="text-right">
          <Badge variant={result.passed ? 'success' : result.skipped ? 'secondary' : 'destructive'}>
            {result.passed ? 'Passed' : result.skipped ? 'Skipped' : 'Failed'}
          </Badge>
          <p className="text-xs text-muted-foreground mt-1">
            {result.duration}ms
          </p>
        </div>
      </div>
    </motion.div>
  );
}

// Test Suite Components
function CompatibilityTests({ 
  testResults, 
  onRunSuite, 
  isTesting 
}: { 
  testResults: TestResult[]; 
  onRunSuite: () => void; 
  isTesting: boolean; 
}) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-medium">Platform Compatibility Tests</h3>
          <p className="text-sm text-muted-foreground">
            Verify DLL compatibility with VB6, VFP9, and other legacy systems
          </p>
        </div>
        <Button size="sm" onClick={onRunSuite} disabled={isTesting}>
          <Play className="w-4 h-4 mr-1" />
          Run Suite
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <TestChecklistCard
          title="VB6 Compatibility"
          tests={[
            'Function exports available',
            'Parameter types compatible',
            'Return values correct',
            'Error handling works'
          ]}
          results={testResults.filter(t => t.platform === 'vb6')}
        />
        <TestChecklistCard
          title="VFP9 Compatibility"
          tests={[
            'Function signatures match',
            'Data type conversions',
            'Memory management',
            'Exception handling'
          ]}
          results={testResults.filter(t => t.platform === 'vfp9')}
        />
      </div>
    </div>
  );
}

function PerformanceTests({ 
  testResults, 
  onRunSuite, 
  isTesting 
}: { 
  testResults: TestResult[]; 
  onRunSuite: () => void; 
  isTesting: boolean; 
}) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-medium">Performance Benchmarks</h3>
          <p className="text-sm text-muted-foreground">
            Measure conversion speed, memory usage, and throughput
          </p>
        </div>
        <Button size="sm" onClick={onRunSuite} disabled={isTesting}>
          <Zap className="w-4 h-4 mr-1" />
          Benchmark
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <PerformanceMetricCard 
          title="Conversion Speed"
          target="< 100ms"
          current="85ms"
          status="good"
        />
        <PerformanceMetricCard 
          title="Memory Usage"
          target="< 50MB"
          current="32MB"
          status="excellent"
        />
        <PerformanceMetricCard 
          title="Throughput"
          target="> 1000/min"
          current="1250/min"
          status="excellent"
        />
      </div>
    </div>
  );
}

function IntegrationTests({ 
  testResults, 
  onRunSuite, 
  isTesting 
}: { 
  testResults: TestResult[]; 
  onRunSuite: () => void; 
  isTesting: boolean; 
}) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-medium">Integration Tests</h3>
          <p className="text-sm text-muted-foreground">
            Test generated wrapper code and example implementations
          </p>
        </div>
        <Button size="sm" onClick={onRunSuite} disabled={isTesting}>
          <FileText className="w-4 h-4 mr-1" />
          Test Code
        </Button>
      </div>
      
      {testResults.length > 0 ? (
        <div className="space-y-2">
          {testResults.map((result, index) => (
            <TestResultCard key={index} result={result} />
          ))}
        </div>
      ) : (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            No integration tests have been run yet. Click "Test Code" to verify generated wrapper code.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

function SecurityTests({ 
  testResults, 
  onRunSuite, 
  isTesting 
}: { 
  testResults: TestResult[]; 
  onRunSuite: () => void; 
  isTesting: boolean; 
}) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-medium">Security Validation</h3>
          <p className="text-sm text-muted-foreground">
            Verify input validation, memory safety, and security best practices
          </p>
        </div>
        <Button size="sm" onClick={onRunSuite} disabled={isTesting}>
          <TestTube className="w-4 h-4 mr-1" />
          Security Scan
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <TestChecklistCard
          title="Input Validation"
          tests={[
            'Buffer overflow protection',
            'Null pointer checks',
            'Parameter validation',
            'Size limit enforcement'
          ]}
          results={testResults.filter(t => t.testName.includes('validation'))}
        />
        <TestChecklistCard
          title="Memory Safety"
          tests={[
            'No memory leaks',
            'Safe string handling',
            'Proper cleanup',
            'Stack protection'
          ]}
          results={testResults.filter(t => t.testName.includes('memory'))}
        />
      </div>
    </div>
  );
}

function TestChecklistCard({ 
  title, 
  tests, 
  results 
}: { 
  title: string; 
  tests: string[]; 
  results: TestResult[]; 
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {tests.map((test, index) => {
            const result = results.find(r => r.testName.includes(test.toLowerCase()));
            return (
              <div key={index} className="flex items-center gap-2">
                {result ? (
                  result.passed ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-600" />
                  )
                ) : (
                  <Clock className="w-4 h-4 text-gray-400" />
                )}
                <span className="text-sm">{test}</span>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}

function PerformanceMetricCard({ 
  title, 
  target, 
  current, 
  status 
}: { 
  title: string; 
  target: string; 
  current: string; 
  status: 'excellent' | 'good' | 'warning' | 'poor'; 
}) {
  const statusColors = {
    excellent: 'text-green-600',
    good: 'text-blue-600',
    warning: 'text-yellow-600',
    poor: 'text-red-600'
  };
  
  return (
    <Card>
      <CardContent className="pt-4">
        <div className="text-center">
          <h4 className="font-medium text-sm">{title}</h4>
          <p className="text-2xl font-bold mt-2 mb-1">{current}</p>
          <p className="text-xs text-muted-foreground">Target: {target}</p>
          <Badge 
            variant="outline" 
            className={`mt-2 ${statusColors[status]}`}
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}