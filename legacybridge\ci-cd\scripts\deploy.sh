#!/bin/bash
# Enterprise Deployment Script for LegacyBridge
# Handles deployment to staging and production environments with multiple strategies

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${ENVIRONMENT:-"staging"}
DEPLOYMENT_STRATEGY=${DEPLOYMENT_STRATEGY:-"rolling"}
VERSION=${VERSION:-"latest"}
NAMESPACE=${NAMESPACE:-"legacybridge"}
CANARY_PERCENTAGE=${CANARY_PERCENTAGE:-10}
ROLLBACK_ON_FAILURE=${ROLLBACK_ON_FAILURE:-"true"}
DRY_RUN=${DRY_RUN:-"false"}
HEALTH_CHECK_RETRIES=${HEALTH_CHECK_RETRIES:-30}
HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-10}

# Cloud provider
CLOUD_PROVIDER=${CLOUD_PROVIDER:-"aws"}

echo -e "${BLUE}======================================${NC}"
echo -e "${BLUE}LegacyBridge Deployment${NC}"
echo -e "${BLUE}======================================${NC}"
echo "Environment: $ENVIRONMENT"
echo "Strategy: $DEPLOYMENT_STRATEGY"
echo "Version: $VERSION"
echo "Namespace: $NAMESPACE"
echo "Cloud Provider: $CLOUD_PROVIDER"
echo "Dry Run: $DRY_RUN"
echo ""

# Function to check prerequisites
check_prerequisites() {
    echo -e "${YELLOW}Checking prerequisites...${NC}"
    
    # Check kubectl
    if ! command -v kubectl >/dev/null 2>&1; then
        echo -e "${RED}kubectl not found. Please install kubectl.${NC}"
        exit 1
    fi
    
    # Check cloud CLI tools
    case "$CLOUD_PROVIDER" in
        "aws")
            if ! command -v aws >/dev/null 2>&1; then
                echo -e "${RED}AWS CLI not found. Please install aws-cli.${NC}"
                exit 1
            fi
            ;;
        "azure")
            if ! command -v az >/dev/null 2>&1; then
                echo -e "${RED}Azure CLI not found. Please install az-cli.${NC}"
                exit 1
            fi
            ;;
        "gcp")
            if ! command -v gcloud >/dev/null 2>&1; then
                echo -e "${RED}Google Cloud SDK not found. Please install gcloud.${NC}"
                exit 1
            fi
            ;;
    esac
    
    # Check Helm if needed
    if [ "$DEPLOYMENT_STRATEGY" = "helm" ]; then
        if ! command -v helm >/dev/null 2>&1; then
            echo -e "${RED}Helm not found. Please install helm.${NC}"
            exit 1
        fi
    fi
    
    echo -e "${GREEN}Prerequisites check passed.${NC}"
}

# Function to configure kubectl
configure_kubectl() {
    echo -e "${YELLOW}Configuring kubectl...${NC}"
    
    case "$CLOUD_PROVIDER" in
        "aws")
            aws eks update-kubeconfig \
                --region ${AWS_REGION:-us-west-2} \
                --name legacybridge-${ENVIRONMENT}-cluster
            ;;
        "azure")
            az aks get-credentials \
                --resource-group legacybridge-${ENVIRONMENT} \
                --name legacybridge-${ENVIRONMENT}-cluster \
                --overwrite-existing
            ;;
        "gcp")
            gcloud container clusters get-credentials \
                legacybridge-${ENVIRONMENT}-cluster \
                --zone ${GCP_ZONE:-us-central1-a} \
                --project ${GCP_PROJECT}
            ;;
    esac
    
    # Verify connection
    kubectl cluster-info
    echo -e "${GREEN}kubectl configured successfully.${NC}"
}

# Function to create or update namespace
setup_namespace() {
    echo -e "${YELLOW}Setting up namespace...${NC}"
    
    if kubectl get namespace $NAMESPACE >/dev/null 2>&1; then
        echo "Namespace $NAMESPACE already exists."
    else
        echo "Creating namespace $NAMESPACE..."
        kubectl create namespace $NAMESPACE
    fi
    
    # Label namespace
    kubectl label namespace $NAMESPACE \
        environment=$ENVIRONMENT \
        managed-by=legacybridge \
        --overwrite
}

# Function to deploy secrets
deploy_secrets() {
    echo -e "${YELLOW}Deploying secrets...${NC}"
    
    # Check if secrets exist
    if kubectl get secret legacybridge-secrets -n $NAMESPACE >/dev/null 2>&1; then
        echo "Secrets already exist. Updating..."
    fi
    
    # Deploy secrets based on environment
    case "$ENVIRONMENT" in
        "staging")
            kubectl apply -f k8s/secrets-staging.yaml -n $NAMESPACE
            ;;
        "production")
            kubectl apply -f k8s/secrets-production.yaml -n $NAMESPACE
            ;;
    esac
}

# Function to deploy ConfigMaps
deploy_configmaps() {
    echo -e "${YELLOW}Deploying ConfigMaps...${NC}"
    
    # Update ConfigMap with environment-specific values
    sed "s/{{ENVIRONMENT}}/$ENVIRONMENT/g" k8s/configmap.yaml | \
    sed "s/{{VERSION}}/$VERSION/g" | \
    kubectl apply -n $NAMESPACE -f -
}

# Function to perform rolling deployment
deploy_rolling() {
    echo -e "${YELLOW}Performing rolling deployment...${NC}"
    
    # Update deployment images
    for component in frontend backend; do
        echo "Updating $component to version $VERSION..."
        
        kubectl set image deployment/legacybridge-$component \
            $component=${REGISTRY:-ghcr.io}/legacybridge/$component:$VERSION \
            -n $NAMESPACE \
            --record
        
        # Wait for rollout
        kubectl rollout status deployment/legacybridge-$component \
            -n $NAMESPACE \
            --timeout=600s
    done
}

# Function to perform blue-green deployment
deploy_blue_green() {
    echo -e "${YELLOW}Performing blue-green deployment...${NC}"
    
    # Create new "green" deployment
    for component in frontend backend; do
        echo "Creating green deployment for $component..."
        
        # Create green deployment
        sed "s/legacybridge-$component/legacybridge-$component-green/g" k8s/deployment.yaml | \
        sed "s|image: .*$component:.*|image: ${REGISTRY:-ghcr.io}/legacybridge/$component:$VERSION|g" | \
        kubectl apply -n $NAMESPACE -f -
        
        # Wait for green deployment
        kubectl rollout status deployment/legacybridge-$component-green \
            -n $NAMESPACE \
            --timeout=600s
    done
    
    # Run health checks on green deployment
    if health_check "green"; then
        echo -e "${GREEN}Health checks passed. Switching traffic to green...${NC}"
        
        # Switch services to green
        for component in frontend backend; do
            kubectl patch service legacybridge-$component-service \
                -n $NAMESPACE \
                -p '{"spec":{"selector":{"deployment":"green"}}}'
        done
        
        # Wait for traffic switch
        sleep 30
        
        # Delete old blue deployment
        for component in frontend backend; do
            kubectl delete deployment legacybridge-$component -n $NAMESPACE || true
            # Rename green to blue for next deployment
            kubectl patch deployment legacybridge-$component-green \
                -n $NAMESPACE \
                --type='json' \
                -p='[{"op": "replace", "path": "/metadata/name", "value": "legacybridge-'$component'"}]'
        done
    else
        echo -e "${RED}Health checks failed. Rolling back...${NC}"
        # Delete green deployment
        for component in frontend backend; do
            kubectl delete deployment legacybridge-$component-green -n $NAMESPACE
        done
        exit 1
    fi
}

# Function to perform canary deployment
deploy_canary() {
    echo -e "${YELLOW}Performing canary deployment (${CANARY_PERCENTAGE}% traffic)...${NC}"
    
    # Create canary deployment
    for component in frontend backend; do
        echo "Creating canary deployment for $component..."
        
        # Create canary deployment with single replica
        sed "s/legacybridge-$component/legacybridge-$component-canary/g" k8s/deployment.yaml | \
        sed "s|image: .*$component:.*|image: ${REGISTRY:-ghcr.io}/legacybridge/$component:$VERSION|g" | \
        sed "s/replicas: [0-9]*/replicas: 1/g" | \
        kubectl apply -n $NAMESPACE -f -
        
        # Wait for canary deployment
        kubectl rollout status deployment/legacybridge-$component-canary \
            -n $NAMESPACE \
            --timeout=300s
    done
    
    # Configure traffic split using service mesh or ingress
    if kubectl get virtualservice -n $NAMESPACE >/dev/null 2>&1; then
        # Istio service mesh
        echo "Configuring Istio traffic split..."
        kubectl apply -n $NAMESPACE -f - <<EOF
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: legacybridge-backend
spec:
  http:
  - match:
    - headers:
        canary:
          exact: "true"
    route:
    - destination:
        host: legacybridge-backend-canary
      weight: 100
  - route:
    - destination:
        host: legacybridge-backend
      weight: $((100 - CANARY_PERCENTAGE))
    - destination:
        host: legacybridge-backend-canary
      weight: $CANARY_PERCENTAGE
EOF
    else
        # Use ingress annotations
        kubectl annotate ingress legacybridge-ingress \
            -n $NAMESPACE \
            nginx.ingress.kubernetes.io/canary="true" \
            nginx.ingress.kubernetes.io/canary-weight="$CANARY_PERCENTAGE" \
            --overwrite
    fi
    
    # Monitor canary
    echo "Monitoring canary deployment for 10 minutes..."
    if monitor_canary; then
        echo -e "${GREEN}Canary metrics look good. Promoting to full deployment...${NC}"
        
        # Scale up canary and scale down stable
        for component in frontend backend; do
            kubectl scale deployment legacybridge-$component-canary \
                -n $NAMESPACE \
                --replicas=$(kubectl get deployment legacybridge-$component -n $NAMESPACE -o jsonpath='{.spec.replicas}')
            
            kubectl scale deployment legacybridge-$component \
                -n $NAMESPACE \
                --replicas=0
            
            # Rename canary to stable
            kubectl patch deployment legacybridge-$component-canary \
                -n $NAMESPACE \
                --type='json' \
                -p='[{"op": "replace", "path": "/metadata/name", "value": "legacybridge-'$component'"}]'
        done
    else
        echo -e "${RED}Canary metrics indicate issues. Rolling back...${NC}"
        # Delete canary
        for component in frontend backend; do
            kubectl delete deployment legacybridge-$component-canary -n $NAMESPACE
        done
        exit 1
    fi
}

# Function to perform health checks
health_check() {
    local deployment_suffix=${1:-""}
    local retries=$HEALTH_CHECK_RETRIES
    
    echo -e "${YELLOW}Running health checks...${NC}"
    
    while [ $retries -gt 0 ]; do
        local all_healthy=true
        
        for component in frontend backend; do
            local deployment_name="legacybridge-$component${deployment_suffix:+-$deployment_suffix}"
            
            # Check deployment ready replicas
            local ready_replicas=$(kubectl get deployment $deployment_name \
                -n $NAMESPACE \
                -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
            local desired_replicas=$(kubectl get deployment $deployment_name \
                -n $NAMESPACE \
                -o jsonpath='{.spec.replicas}' 2>/dev/null || echo "1")
            
            if [ "$ready_replicas" != "$desired_replicas" ]; then
                echo "$deployment_name: $ready_replicas/$desired_replicas replicas ready"
                all_healthy=false
            fi
            
            # Check endpoint health
            local service_name="legacybridge-$component-service"
            local endpoint=$(kubectl get endpoints $service_name \
                -n $NAMESPACE \
                -o jsonpath='{.subsets[0].addresses[0].ip}' 2>/dev/null || echo "")
            
            if [ -n "$endpoint" ]; then
                if ! kubectl exec -n $NAMESPACE deployment/$deployment_name -- \
                    curl -sf http://$endpoint:8080/health >/dev/null 2>&1; then
                    echo "$component health check failed"
                    all_healthy=false
                fi
            fi
        done
        
        if [ "$all_healthy" = "true" ]; then
            echo -e "${GREEN}All health checks passed!${NC}"
            return 0
        fi
        
        retries=$((retries - 1))
        echo "Health checks incomplete. Retrying in ${HEALTH_CHECK_INTERVAL}s... ($retries retries left)"
        sleep $HEALTH_CHECK_INTERVAL
    done
    
    echo -e "${RED}Health checks failed after $HEALTH_CHECK_RETRIES attempts.${NC}"
    return 1
}

# Function to monitor canary deployment
monitor_canary() {
    echo -e "${YELLOW}Monitoring canary metrics...${NC}"
    
    # Monitor for 10 minutes
    local monitor_duration=600
    local start_time=$(date +%s)
    local error_threshold=5
    
    while [ $(($(date +%s) - start_time)) -lt $monitor_duration ]; do
        # Check error rate
        local error_rate=$(kubectl exec -n $NAMESPACE \
            deployment/legacybridge-backend-canary -- \
            curl -s http://localhost:9090/metrics | \
            grep http_requests_total | \
            grep 'status="5' | \
            awk '{sum+=$2} END {print sum}' || echo "0")
        
        if [ "$error_rate" -gt "$error_threshold" ]; then
            echo -e "${RED}High error rate detected: $error_rate errors${NC}"
            return 1
        fi
        
        # Check response time
        local response_time=$(kubectl exec -n $NAMESPACE \
            deployment/legacybridge-backend-canary -- \
            curl -s http://localhost:9090/metrics | \
            grep http_request_duration_seconds | \
            grep 'quantile="0.95"' | \
            awk '{print $2}' || echo "0")
        
        echo "Canary metrics - Errors: $error_rate, P95 Response Time: ${response_time}s"
        sleep 30
    done
    
    echo -e "${GREEN}Canary monitoring completed successfully.${NC}"
    return 0
}

# Function to run post-deployment tests
run_post_deployment_tests() {
    echo -e "${YELLOW}Running post-deployment tests...${NC}"
    
    # Run smoke tests
    ./ci-cd/scripts/run-tests.sh smoke
    
    # Run basic integration tests
    if [ "$ENVIRONMENT" = "production" ]; then
        ./ci-cd/scripts/run-tests.sh integration
    fi
}

# Function to update monitoring
update_monitoring() {
    echo -e "${YELLOW}Updating monitoring configuration...${NC}"
    
    # Deploy Prometheus rules
    kubectl apply -f k8s/monitoring/prometheus-rules.yaml -n $NAMESPACE
    
    # Deploy Grafana dashboards
    kubectl apply -f k8s/monitoring/grafana-dashboards.yaml -n $NAMESPACE
    
    # Create deployment annotation in monitoring system
    if [ "$ENVIRONMENT" = "production" ]; then
        curl -X POST "${GRAFANA_URL}/api/annotations" \
            -H "Authorization: Bearer ${GRAFANA_API_KEY}" \
            -H "Content-Type: application/json" \
            -d "{
                \"dashboardId\": 1,
                \"panelId\": 1,
                \"tags\": [\"deployment\", \"$VERSION\"],
                \"text\": \"Deployed version $VERSION to $ENVIRONMENT\"
            }"
    fi
}

# Function to notify teams
notify_deployment() {
    local status=$1
    local message=$2
    
    echo -e "${YELLOW}Sending deployment notification...${NC}"
    
    # Slack notification
    if [ -n "${SLACK_WEBHOOK:-}" ]; then
        local color="good"
        local emoji="✅"
        if [ "$status" = "failure" ]; then
            color="danger"
            emoji="❌"
        fi
        
        curl -X POST "$SLACK_WEBHOOK" \
            -H "Content-Type: application/json" \
            -d "{
                \"text\": \"$emoji Deployment to $ENVIRONMENT\",
                \"attachments\": [{
                    \"color\": \"$color\",
                    \"fields\": [
                        {\"title\": \"Environment\", \"value\": \"$ENVIRONMENT\", \"short\": true},
                        {\"title\": \"Version\", \"value\": \"$VERSION\", \"short\": true},
                        {\"title\": \"Strategy\", \"value\": \"$DEPLOYMENT_STRATEGY\", \"short\": true},
                        {\"title\": \"Status\", \"value\": \"$status\", \"short\": true},
                        {\"title\": \"Message\", \"value\": \"$message\", \"short\": false}
                    ]
                }]
            }"
    fi
}

# Main deployment function
deploy() {
    echo -e "${BLUE}Starting deployment...${NC}"
    
    # Pre-deployment tasks
    setup_namespace
    deploy_secrets
    deploy_configmaps
    
    # Deploy based on strategy
    case "$DEPLOYMENT_STRATEGY" in
        "rolling")
            deploy_rolling
            ;;
        "blue-green")
            deploy_blue_green
            ;;
        "canary")
            deploy_canary
            ;;
        *)
            echo -e "${RED}Unknown deployment strategy: $DEPLOYMENT_STRATEGY${NC}"
            exit 1
            ;;
    esac
    
    # Post-deployment tasks
    if health_check; then
        run_post_deployment_tests
        update_monitoring
        notify_deployment "success" "Successfully deployed version $VERSION"
        echo -e "${GREEN}Deployment completed successfully!${NC}"
    else
        if [ "$ROLLBACK_ON_FAILURE" = "true" ]; then
            echo -e "${RED}Deployment failed. Initiating rollback...${NC}"
            ./ci-cd/scripts/rollback.sh
        fi
        notify_deployment "failure" "Deployment failed - health checks did not pass"
        exit 1
    fi
}

# Dry run function
dry_run() {
    echo -e "${YELLOW}Running in DRY RUN mode...${NC}"
    echo "Would perform the following actions:"
    echo "1. Configure kubectl for $CLOUD_PROVIDER in $ENVIRONMENT"
    echo "2. Create/update namespace: $NAMESPACE"
    echo "3. Deploy secrets and configmaps"
    echo "4. Deploy using $DEPLOYMENT_STRATEGY strategy"
    echo "5. Update images to version: $VERSION"
    echo "6. Run health checks and post-deployment tests"
    echo "7. Update monitoring and send notifications"
}

# Main execution
check_prerequisites

if [ "$DRY_RUN" = "true" ]; then
    dry_run
else
    configure_kubectl
    deploy
fi