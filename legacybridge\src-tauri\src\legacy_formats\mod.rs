// Legacy Formats Module
// Comprehensive support for all legacy formats with MCP integration

pub mod comprehensive_converter;

// Re-export main converter
pub use comprehensive_converter::{
    LegacyConverter, 
    ConversionOptions, 
    BuildResult, 
    ValidationResult
};

// Legacy format feature flags
pub mod features {
    use crate::config::Config;
    
    pub fn check_doc_enabled(config: &Config) -> bool {
        config.features.format_doc
    }
    
    pub fn check_wordperfect_enabled(config: &Config) -> bool {
        config.features.format_wordperfect
    }
    
    pub fn check_dbase_enabled(config: &Config) -> bool {
        config.features.format_dbase
    }
    
    pub fn check_lotus_enabled(config: &Config) -> bool {
        config.features.format_lotus
    }
    
    pub fn check_wordstar_enabled(config: &Config) -> bool {
        config.features.format_wordstar
    }
    
    pub fn get_enabled_legacy_formats(config: &Config) -> Vec<&'static str> {
        let mut formats = Vec::new();
        
        if check_doc_enabled(config) {
            formats.push("doc");
        }
        if check_wordperfect_enabled(config) {
            formats.push("wpd");
        }
        if check_dbase_enabled(config) {
            formats.push("dbf");
        }
        if check_lotus_enabled(config) {
            formats.push("wk1");
        }
        if check_wordstar_enabled(config) {
            formats.push("ws");
        }
        
        formats
    }
}

// Legacy format constants
pub mod constants {
    pub const SUPPORTED_LEGACY_FORMATS: &[&str] = &[
        "doc",      // Microsoft Word 97-2003
        "wpd",      // WordPerfect Document  
        "dbf",      // dBase Database
        "wk1",      // Lotus 1-2-3 Spreadsheet
        "wks",      // Lotus Works Spreadsheet
        "123",      // Lotus 1-2-3 Alternative
        "ws",       // WordStar Document
        "wsd",      // WordStar Document Alternative
    ];
    
    pub const LEGACY_TO_MODERN_MAPPING: &[(&str, &[&str])] = &[
        ("doc", &["rtf", "md", "html", "txt", "docx"]),
        ("wpd", &["rtf", "md", "html", "txt"]),
        ("dbf", &["csv", "json", "md", "html"]),
        ("wk1", &["csv", "json", "md", "html", "xlsx"]),
        ("wks", &["csv", "json", "md", "html", "xlsx"]),
        ("123", &["csv", "json", "md", "html", "xlsx"]),
        ("ws", &["txt", "md", "rtf", "html"]),
        ("wsd", &["txt", "md", "rtf", "html"]),
    ];
    
    pub const FEATURE_FLAGS: &[(&str, &str)] = &[
        ("doc", "format-doc"),
        ("wpd", "format-wordperfect"),  
        ("dbf", "format-dbase"),
        ("wk1", "format-lotus"),
        ("wks", "format-lotus"),
        ("123", "format-lotus"),
        ("ws", "format-wordstar"),
        ("wsd", "format-wordstar"),
    ];
}

// Utility functions
pub mod utils {
    use super::constants::*;
    
    pub fn is_legacy_format(format: &str) -> bool {
        SUPPORTED_LEGACY_FORMATS.contains(&format)
    }
    
    pub fn get_supported_outputs(input_format: &str) -> Option<&'static [&'static str]> {
        LEGACY_TO_MODERN_MAPPING.iter()
            .find(|(input, _)| *input == input_format)
            .map(|(_, outputs)| *outputs)
    }
    
    pub fn get_feature_flag(format: &str) -> Option<&'static str> {
        FEATURE_FLAGS.iter()
            .find(|(fmt, _)| *fmt == format)
            .map(|(_, flag)| *flag)
    }
    
    pub fn format_supports_conversion(input: &str, output: &str) -> bool {
        if let Some(supported_outputs) = get_supported_outputs(input) {
            supported_outputs.contains(&output)
        } else {
            false
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_legacy_format_detection() {
        assert!(utils::is_legacy_format("doc"));
        assert!(utils::is_legacy_format("wpd"));
        assert!(utils::is_legacy_format("dbf"));
        assert!(!utils::is_legacy_format("pdf"));
        assert!(!utils::is_legacy_format("docx"));
    }
    
    #[test]
    fn test_conversion_support() {
        assert!(utils::format_supports_conversion("doc", "rtf"));
        assert!(utils::format_supports_conversion("dbf", "csv"));
        assert!(!utils::format_supports_conversion("doc", "exe"));
    }
    
    #[test]
    fn test_feature_flags() {
        assert_eq!(utils::get_feature_flag("doc"), Some("format-doc"));
        assert_eq!(utils::get_feature_flag("wpd"), Some("format-wordperfect"));
        assert_eq!(utils::get_feature_flag("unknown"), None);
    }
}