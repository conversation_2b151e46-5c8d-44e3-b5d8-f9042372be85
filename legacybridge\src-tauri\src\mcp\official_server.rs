// Official rmcp SDK Integration
// Comprehensive MCP server implementation using rmcp v0.2.0 for LegacyBridge

#[cfg(feature = "mcp")]
use rmcp::{
    ServerHandler,
    model::{*, ErrorData as McpError},
    tool,
    tool_router,
    tool_handler,
    handler::server::router::tool::ToolRouter,
    ServiceExt,
    transport::stdio,
};

use crate::conversion::ConversionResult;
use crate::config::Config;
use crate::format_detection::FormatDetector;
use crate::legacy_formats::{LegacyConverter, ConversionOptions};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value as JsonValue};
use std::collections::HashMap;
use std::sync::Arc;
use std::future::Future;
use tokio::sync::RwLock;
use uuid::Uuid;
use tracing::{info, error, warn, debug};
use base64::{Engine as _, engine::general_purpose};

/// LegacyBridge MCP Server using official rmcp v0.2.0
pub struct LegacyBridgeMcpServerOfficial {
    config: Config,
    format_detector: FormatDetector,
    legacy_converter: LegacyConverter,
    active_jobs: Arc<RwLock<HashMap<String, ConversionJob>>>,
    stats: Arc<RwLock<ServerStats>>,
    tool_router: ToolRouter<Self>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversionJob {
    pub id: String,
    pub status: JobStatus,
    pub input_format: String,
    pub output_format: String,
    pub progress: f32,
    pub started_at: chrono::DateTime<chrono::Utc>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    pub error: Option<String>,
    pub result: Option<ConversionResult>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum JobStatus {
    Pending,
    Processing,
    Completed,
    Failed,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerStats {
    pub conversions_total: u64,
    pub conversions_successful: u64,
    pub conversions_failed: u64,
    pub uptime_seconds: u64,
    pub supported_formats: Vec<String>,
    pub legacy_formats_enabled: bool,
}

// Tool argument structures
#[derive(Debug, Deserialize)]
pub struct ConvertFileArgs {
    pub input_content: String,
    pub input_format: Option<String>,
    pub output_format: String,
    pub options: Option<JsonValue>,
}

#[derive(Debug, Deserialize)]
pub struct RtfToMarkdownArgs {
    pub rtf_content: String,
    pub preserve_formatting: Option<bool>,
    pub include_metadata: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct MarkdownToRtfArgs {
    pub markdown_content: String,
    pub template_style: Option<String>,
    pub font_settings: Option<JsonValue>,
}

#[derive(Debug, Deserialize)]
pub struct ConvertLegacyFormatArgs {
    pub input_content: String,
    pub format_type: String, // doc, wpd, dbf, wk1, ws
    pub output_format: String,
    pub options: Option<JsonValue>,
}

#[derive(Debug, Deserialize)]
pub struct DetectFormatArgs {
    pub file_content: String,
    pub filename: Option<String>,
    pub detailed_analysis: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct ValidateFileArgs {
    pub file_content: String,
    pub expected_format: Option<String>,
    pub strict_validation: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct BatchConvertArgs {
    pub files: Vec<BatchFileInput>,
    pub output_format: String,
    pub parallel: Option<bool>,
    pub options: Option<JsonValue>,
}

#[derive(Debug, Deserialize)]
pub struct BatchFileInput {
    pub content: String,
    pub filename: Option<String>,
    pub input_format: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct BuildDllArgs {
    pub target_language: String, // vb6, vfp9, etc.
    pub include_formats: Vec<String>,
    pub output_path: String,
    pub optimization_level: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct GetJobStatusArgs {
    pub job_id: String,
}

#[derive(Debug, Deserialize)]
pub struct GetFormatInfoArgs {
    pub format_id: String,
}

#[derive(Debug, Deserialize)]
pub struct ExtractTextArgs {
    pub file_content: String,
    pub format: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct GeneratePreviewArgs {
    pub file_content: String,
    pub format: Option<String>,
    pub max_pages: Option<usize>,
    pub include_styles: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct GetConversionOptionsArgs {
    pub input_format: String,
    pub output_format: String,
}

#[tool_router]
impl LegacyBridgeMcpServerOfficial {
    pub fn new(config: Config) -> Self {
        Self {
            format_detector: FormatDetector::new(),
            legacy_converter: LegacyConverter::new(&config),
            config,
            active_jobs: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(ServerStats {
                conversions_total: 0,
                conversions_successful: 0,
                conversions_failed: 0,
                uptime_seconds: 0,
                supported_formats: vec![
                    "rtf".to_string(), "md".to_string(), "txt".to_string(),
                    "html".to_string(), "xml".to_string(), "json".to_string(),
                    "doc".to_string(), "wpd".to_string(), "dbf".to_string(),
                    "wk1".to_string(), "ws".to_string()
                ],
                legacy_formats_enabled: true,
            })),
            tool_router: Self::tool_router(),
        }
    }
    
    /// Start the MCP server using stdio transport
    pub async fn run_stdio_server(self) -> Result<(), Box<dyn std::error::Error>> {
        info!("Starting LegacyBridge MCP Server with official rmcp SDK");
        
        let service = self.serve(stdio()).await?;
        service.waiting().await?;
        Ok(())
    }
    
    /// Create job for async operations
    async fn create_job(&self, input_format: String, output_format: String) -> String {
        let job_id = Uuid::new_v4().to_string();
        let job = ConversionJob {
            id: job_id.clone(),
            status: JobStatus::Pending,
            input_format,
            output_format,
            progress: 0.0,
            started_at: chrono::Utc::now(),
            completed_at: None,
            error: None,
            result: None,
        };
        
        self.active_jobs.write().await.insert(job_id.clone(), job);
        job_id
    }
    
    /// Update job status
    async fn update_job(&self, job_id: &str, status: JobStatus, progress: f32, result: Option<ConversionResult>, error: Option<String>) {
        if let Some(job) = self.active_jobs.write().await.get_mut(job_id) {
            job.status = status;
            job.progress = progress;
            if let Some(result) = result {
                job.result = Some(result);
            }
            if let Some(error) = error {
                job.error = Some(error);
            }
            if matches!(job.status, JobStatus::Completed | JobStatus::Failed) {
                job.completed_at = Some(chrono::Utc::now());
            }
        }
    }
    
    /// Get job status
    async fn get_job(&self, job_id: &str) -> Option<ConversionJob> {
        self.active_jobs.read().await.get(job_id).cloned()
    }
}

// Implement ServerHandler trait
#[cfg(feature = "mcp")]
#[tool_handler]
impl ServerHandler for LegacyBridgeMcpServerOfficial {
    fn get_info(&self) -> ServerInfo {
        ServerInfo {
            capabilities: ServerCapabilities::builder()
                .enable_tools()
                .enable_resources()
                .enable_prompts()
                .build(),
            instructions: Some("LegacyBridge converts legacy formats to modern ones".to_string()),
            ..Default::default()
        }
    }
    async fn list_resources(
        &self,
        _request: Option<PaginatedRequestParam>,
    ) -> Result<ListResourcesResult, McpError> {
        let resources = vec![
            Resource {
                uri: "formats://supported".to_string(),
                name: "Supported Formats".to_string(),
                description: Some("List all supported file formats with conversion capabilities".to_string()),
                mime_type: Some("application/json".to_string()),
            },
            Resource {
                uri: "formats://legacy".to_string(),
                name: "Legacy Formats".to_string(),
                description: Some("Detailed information about legacy format support (DOC, WordPerfect, dBase, etc.)".to_string()),
                mime_type: Some("application/json".to_string()),
            },
            Resource {
                uri: "stats://server".to_string(),
                name: "Server Statistics".to_string(),
                description: Some("LegacyBridge MCP server performance metrics and statistics".to_string()),
                mime_type: Some("application/json".to_string()),
            },
            Resource {
                uri: "jobs://active".to_string(),
                name: "Active Jobs".to_string(),
                description: Some("Currently active conversion jobs and their status".to_string()),
                mime_type: Some("application/json".to_string()),
            },
            Resource {
                uri: "config://server".to_string(),
                name: "Server Configuration".to_string(),
                description: Some("Current server configuration and feature flags".to_string()),
                mime_type: Some("application/json".to_string()),
            },
        ];
        Ok(ListResourcesResult::with_all_items(resources))
    }
    async fn read_resource(
        &self,
        request: ReadResourceRequestParam,
    ) -> Result<ReadResourceResult, McpError> {
        let uri = &request.uri;
        
        let content = match uri.as_str() {
            "formats://supported" => self.get_supported_formats().await,
            "formats://legacy" => self.get_legacy_formats_info().await,
            "stats://server" => self.get_server_stats().await,
            "jobs://active" => self.get_active_jobs().await,
            "config://server" => self.get_server_config().await,
            _ => return Err(McpError::invalid_params(format!("Unknown resource: {}", uri))),
        };
        
        Ok(ReadResourceResult {
            contents: vec![Content::text(content.to_string()).with_blob(
                Resource {
                    uri: uri.clone(),
                    mime_type: Some("application/json".to_string()),
                    data: Vec::new(),
                }
            )],
        })
    }
    async fn list_prompts(
        &self,
        _request: Option<PaginatedRequestParam>,
    ) -> Result<ListPromptsResult, McpError> {
        let prompts = vec![
            Prompt {
                name: "convert_legacy_document".to_string(),
                description: Some("Generate a conversion prompt for legacy documents".to_string()),
                arguments: Some(vec![
                    PromptArgument {
                        name: "format".to_string(),
                        description: Some("Legacy format to convert from".to_string()),
                        required: Some(true),
                    },
                    PromptArgument {
                        name: "target".to_string(),
                        description: Some("Target modern format".to_string()),
                        required: Some(true),
                    },
                ]),
            },
            Prompt {
                name: "batch_conversion_strategy".to_string(),
                description: Some("Generate strategy for batch converting legacy files".to_string()),
                arguments: Some(vec![
                    PromptArgument {
                        name: "file_count".to_string(),
                        description: Some("Number of files to convert".to_string()),
                        required: Some(true),
                    },
                    PromptArgument {
                        name: "formats".to_string(),
                        description: Some("Comma-separated list of formats".to_string()),
                        required: Some(true),
                    },
                ]),
            },
        ];
        Ok(ListPromptsResult::with_all_items(prompts))
    }
    
    async fn get_prompt(
        &self,
        request: GetPromptRequestParam,
    ) -> Result<GetPromptResult, McpError> {
        let prompt_name = &request.name;
        let arguments = request.arguments.as_ref().unwrap_or(&HashMap::new());
        
        let message = match prompt_name.as_str() {
            "convert_legacy_document" => {
                let format = arguments.get("format").unwrap_or(&JsonValue::String("unknown".to_string()));
                let target = arguments.get("target").unwrap_or(&JsonValue::String("markdown".to_string()));
                
                format!("You are converting a legacy {} document to {}. Here are the key considerations:\n\n1. Preserve document structure and formatting where possible\n2. Handle legacy-specific elements (embedded objects, macros, etc.)\n3. Convert character encodings appropriately\n4. Maintain data integrity throughout the process\n5. Generate metadata about the conversion process\n\nPlease ensure the output is clean, well-formatted, and maintains the original document's intent.", format, target)
            },
            "batch_conversion_strategy" => {
                let file_count = arguments.get("file_count").unwrap_or(&JsonValue::Number(serde_json::Number::from(1)));
                let formats = arguments.get("formats").unwrap_or(&JsonValue::String("mixed".to_string()));
                
                format!("You are planning a batch conversion of {} files with formats: {}. Consider these factors:\n\n1. Processing order (prioritize by complexity/size)\n2. Resource management (memory, CPU utilization)\n3. Error handling and recovery strategies\n4. Progress tracking and reporting\n5. Quality assurance checks\n6. Parallel processing opportunities\n\nProvide a comprehensive strategy that maximizes efficiency while ensuring quality.", file_count, formats)
            },
            _ => return Err(McpError::method_not_found(format!("Unknown prompt: {}", prompt_name))),
        };
        
        Ok(GetPromptResult {
            description: Some(format!("Generated prompt for {}", prompt_name)),
            messages: vec![PromptMessage {
                role: Role::User,
                content: PromptMessageContent::Text(TextContent {
                    text: message,
                }),
            }],
        })
    }
}

// Tool implementations
impl LegacyBridgeMcpServerOfficial {
    #[tool(description = "Convert a single file between formats. Supports all legacy formats including DOC, WordPerfect, dBase, Lotus 1-2-3, and WordStar.")]
    async fn convert_file(&self, args: ConvertFileArgs) -> Result<CallToolResult, McpError> {
        info!("Converting file: {} -> {}", args.input_format.as_deref().unwrap_or("auto"), args.output_format);
        
        // Decode base64 content
        let content = general_purpose::STANDARD.decode(&args.input_content)
            .map_err(|e| McpError::invalid_params(format!("Invalid base64 content: {}", e)))?;
        
        // Detect format if not provided
        let input_format = if let Some(format) = args.input_format {
            format
        } else {
            self.format_detector.detect_format(&content, None)
                .map_err(|e| McpError::internal_error(format!("Format detection failed: {}", e)))?
                .format_id
        };
        
        // Create conversion job
        let job_id = self.create_job(input_format.clone(), args.output_format.clone()).await;
        
        // Update stats
        {
            let mut stats = self.stats.write().await;
            stats.conversions_total += 1;
        }
        
        // Perform conversion
        match self.legacy_converter.convert(&content, &input_format, &args.output_format, args.options).await {
            Ok(result) => {
                self.update_job(&job_id, JobStatus::Completed, 100.0, Some(result.clone()), None).await;
                
                // Update success stats
                {
                    let mut stats = self.stats.write().await;
                    stats.conversions_successful += 1;
                }
                
                Ok(CallToolResult::success(vec![Content::text(
                    serde_json::to_string_pretty(&json!({
                        "success": true,
                        "job_id": job_id,
                        "input_format": input_format,
                        "output_format": args.output_format,
                        "content": result.content,
                        "metadata": result.metadata,
                        "conversion_time_ms": result.processing_time_ms
                    })).unwrap()
                )]))
            },
            Err(e) => {
                let error_msg = format!("Conversion failed: {}", e);
                self.update_job(&job_id, JobStatus::Failed, 0.0, None, Some(error_msg.clone())).await;
                
                // Update failure stats
                {
                    let mut stats = self.stats.write().await;
                    stats.conversions_failed += 1;
                }
                
                Err(McpError::internal_error(error_msg))
            }
        }
    }
    
    #[tool(description = "Convert RTF content directly to Markdown with advanced formatting preservation")]
    async fn rtf_to_markdown(&self, args: RtfToMarkdownArgs) -> Result<CallToolResult, McpError> {
        info!("Converting RTF to Markdown");
        
        let options = ConversionOptions {
            preserve_formatting: args.preserve_formatting.unwrap_or(true),
            include_metadata: args.include_metadata.unwrap_or(false),
            ..Default::default()
        };
        
        match self.legacy_converter.rtf_to_markdown(&args.rtf_content, &options).await {
            Ok(result) => {
                Ok(CallToolResult::success(vec![Content::text(
                    serde_json::to_string_pretty(&json!({
                        "success": true,
                        "markdown_content": result.content,
                        "metadata": result.metadata,
                        "conversion_time_ms": result.processing_time_ms
                    })).unwrap()
                )]))
            },
            Err(e) => Err(McpError::internal_error(format!("RTF to Markdown conversion failed: {}", e))),
        }
    }
    
    #[tool(description = "Convert Markdown content to RTF with customizable styling")]
    async fn markdown_to_rtf(&self, args: MarkdownToRtfArgs) -> Result<CallToolResult, McpError> {
        info!("Converting Markdown to RTF");
        
        let options = ConversionOptions {
            template_style: args.template_style.unwrap_or_else(|| "default".to_string()),
            font_settings: args.font_settings,
            ..Default::default()
        };
        
        match self.legacy_converter.markdown_to_rtf(&args.markdown_content, &options).await {
            Ok(result) => {
                Ok(CallToolResult::success(vec![Content::text(
                    serde_json::to_string_pretty(&json!({
                        "success": true,
                        "rtf_content": result.content,
                        "metadata": result.metadata,
                        "conversion_time_ms": result.processing_time_ms
                    })).unwrap()
                )]))
            },
            Err(e) => Err(McpError::internal_error(format!("Markdown to RTF conversion failed: {}", e))),
        }
    }
    
    #[tool(description = "Convert legacy formats (DOC, WordPerfect, dBase, Lotus 1-2-3, WordStar) to modern formats")]
    async fn convert_legacy_format(&self, args: ConvertLegacyFormatArgs) -> Result<CallToolResult, McpError> {
        info!("Converting legacy format: {} -> {}", args.format_type, args.output_format);
        
        // Decode base64 content
        let content = general_purpose::STANDARD.decode(&args.input_content)
            .map_err(|e| McpError::invalid_params(format!("Invalid base64 content: {}", e)))?;
        
        // Create job for tracking
        let job_id = self.create_job(args.format_type.clone(), args.output_format.clone()).await;
        
        // Perform legacy format conversion
        match self.legacy_converter.convert_legacy_format(&content, &args.format_type, &args.output_format, args.options).await {
            Ok(result) => {
                self.update_job(&job_id, JobStatus::Completed, 100.0, Some(result.clone()), None).await;
                
                Ok(CallToolResult::success(vec![Content::text(
                    serde_json::to_string_pretty(&json!({
                        "success": true,
                        "job_id": job_id,
                        "legacy_format": args.format_type,
                        "output_format": args.output_format,
                        "content": result.content,
                        "metadata": result.metadata,
                        "warnings": result.warnings,
                        "conversion_time_ms": result.processing_time_ms
                    })).unwrap()
                )]))
            },
            Err(e) => {
                let error_msg = format!("Legacy format conversion failed: {}", e);
                self.update_job(&job_id, JobStatus::Failed, 0.0, None, Some(error_msg.clone())).await;
                Err(McpError::internal_error(error_msg))
            }
        }
    }
    
    async fn handle_detect_format(&self, args: DetectFormatArgs) -> SdkResult<CallToolResult> {
        info!("Detecting file format");
        
        // Decode base64 content
        let content = general_purpose::STANDARD.decode(&args.file_content)
            .map_err(|e| McpError::invalid_params(format!("Invalid base64 content: {}", e)))?;
        
        match self.format_detector.detect_format(&content, args.filename.as_deref()) {
            Ok(detection_result) => {
                let detailed_info = if args.detailed_analysis.unwrap_or(false) {
                    self.format_detector.analyze_format_details(&content, &detection_result)
                } else {
                    None
                };
                
                Ok(CallToolResult::success(vec![Content::text(
                    serde_json::to_string_pretty(&json!({
                        "success": true,
                        "detected_format": detection_result.format_id,
                        "confidence": detection_result.confidence,
                        "file_size": content.len(),
                        "magic_bytes": detection_result.magic_bytes,
                        "detailed_analysis": detailed_info,
                        "supported_conversions": detection_result.supported_conversions
                    })).unwrap()
                )]))
            },
            Err(e) => Err(McpError::internal_error(format!("Format detection failed: {}", e))),
        }
    }
    
    #[tool(description = "Validate file integrity and format compliance")]
    async fn validate_file(&self, args: ValidateFileArgs) -> Result<CallToolResult, McpError> {
        info!("Validating file");
        
        // Decode base64 content
        let content = general_purpose::STANDARD.decode(&args.file_content)
            .map_err(|e| McpError::invalid_params(format!("Invalid base64 content: {}", e)))?;
        
        match self.format_detector.validate_file(&content, args.expected_format.as_deref(), args.strict_validation.unwrap_or(false)) {
            Ok(validation_result) => {
                Ok(CallToolResult::success(vec![Content::text(
                    serde_json::to_string_pretty(&json!({
                        "success": true,
                        "is_valid": validation_result.is_valid,
                        "detected_format": validation_result.detected_format,
                        "expected_format": args.expected_format,
                        "validation_errors": validation_result.errors,
                        "validation_warnings": validation_result.warnings,
                        "file_integrity": validation_result.integrity_check
                    })).unwrap()
                )]))
            },
            Err(e) => Err(McpError::internal_error(format!("File validation failed: {}", e))),
        }
    }
    
    #[tool(description = "Convert multiple files in batch with parallel processing")]
    async fn batch_convert(&self, args: BatchConvertArgs) -> Result<CallToolResult, McpError> {
        info!("Starting batch conversion of {} files", args.files.len());
        
        let job_id = self.create_job("batch".to_string(), args.output_format.clone()).await;
        let mut results = Vec::new();
        let mut successful = 0;
        let mut failed = 0;
        
        // Process files (parallel if requested)
        if args.parallel.unwrap_or(true) && args.files.len() > 1 {
            // Parallel processing using rayon
            use rayon::prelude::*;
            
            let conversion_results: Vec<_> = args.files.par_iter().enumerate().map(|(index, file)| {
                let content = match general_purpose::STANDARD.decode(&file.content) {
                    Ok(content) => content,
                    Err(e) => return (index, Err(format!("Invalid base64 content: {}", e))),
                };
                
                let input_format = if let Some(format) = &file.input_format {
                    format.clone()
                } else {
                    match self.format_detector.detect_format(&content, file.filename.as_deref()) {
                        Ok(detection) => detection.format_id,
                        Err(e) => return (index, Err(format!("Format detection failed: {}", e))),
                    }
                };
                
                // Note: This is a simplified version - in reality, you'd need to handle async properly in parallel context
                (index, Ok((input_format, content)))
            }).collect();
            
            for (index, result) in conversion_results {
                match result {
                    Ok((input_format, content)) => {
                        // Sequential conversion for now (could be optimized further)
                        match self.legacy_converter.convert(&content, &input_format, &args.output_format, args.options.clone()).await {
                            Ok(conversion_result) => {
                                results.push(json!({
                                    "index": index,
                                    "filename": args.files[index].filename,
                                    "success": true,
                                    "input_format": input_format,
                                    "content": conversion_result.content,
                                    "metadata": conversion_result.metadata
                                }));
                                successful += 1;
                            },
                            Err(e) => {
                                results.push(json!({
                                    "index": index,
                                    "filename": args.files[index].filename,
                                    "success": false,
                                    "error": format!("Conversion failed: {}", e)
                                }));
                                failed += 1;
                            }
                        }
                    },
                    Err(e) => {
                        results.push(json!({
                            "index": index,
                            "filename": args.files[index].filename,
                            "success": false,
                            "error": e
                        }));
                        failed += 1;
                    }
                }
            }
        } else {
            // Sequential processing
            for (index, file) in args.files.iter().enumerate() {
                let progress = (index as f32 / args.files.len() as f32) * 100.0;
                self.update_job(&job_id, JobStatus::Processing, progress, None, None).await;
                
                let content = match general_purpose::STANDARD.decode(&file.content) {
                    Ok(content) => content,
                    Err(e) => {
                        results.push(json!({
                            "index": index,
                            "filename": file.filename,
                            "success": false,
                            "error": format!("Invalid base64 content: {}", e)
                        }));
                        failed += 1;
                        continue;
                    }
                };
                
                let input_format = if let Some(format) = &file.input_format {
                    format.clone()
                } else {
                    match self.format_detector.detect_format(&content, file.filename.as_deref()) {
                        Ok(detection) => detection.format_id,
                        Err(e) => {
                            results.push(json!({
                                "index": index,
                                "filename": file.filename,
                                "success": false,
                                "error": format!("Format detection failed: {}", e)
                            }));
                            failed += 1;
                            continue;
                        }
                    }
                };
                
                match self.legacy_converter.convert(&content, &input_format, &args.output_format, args.options.clone()).await {
                    Ok(conversion_result) => {
                        results.push(json!({
                            "index": index,
                            "filename": file.filename,
                            "success": true,
                            "input_format": input_format,
                            "content": conversion_result.content,
                            "metadata": conversion_result.metadata
                        }));
                        successful += 1;
                    },
                    Err(e) => {
                        results.push(json!({
                            "index": index,
                            "filename": file.filename,
                            "success": false,
                            "error": format!("Conversion failed: {}", e)
                        }));
                        failed += 1;
                    }
                }
            }
        }
        
        self.update_job(&job_id, JobStatus::Completed, 100.0, None, None).await;
        
        Ok(CallToolResult::success(vec![Content::text(
            serde_json::to_string_pretty(&json!({
                "success": true,
                "job_id": job_id,
                "batch_summary": {
                    "total_files": args.files.len(),
                    "successful": successful,
                    "failed": failed,
                    "output_format": args.output_format
                },
                "results": results
            })).unwrap()
        )]))
    }
    
    #[tool(description = "Build legacy DLL for VB6/VFP9 integration")]
    async fn build_dll(&self, args: BuildDllArgs) -> Result<CallToolResult, McpError> {
        info!("Building DLL for {} with formats: {:?}", args.target_language, args.include_formats);
        
        let job_id = self.create_job("dll_build".to_string(), args.target_language.clone()).await;
        
        // This would typically involve calling the DLL builder
        match self.legacy_converter.build_dll(&args.target_language, &args.include_formats, &args.output_path, args.optimization_level.as_deref()).await {
            Ok(build_result) => {
                self.update_job(&job_id, JobStatus::Completed, 100.0, None, None).await;
                
                Ok(CallToolResult::success(vec![Content::text(
                    serde_json::to_string_pretty(&json!({
                        "success": true,
                        "job_id": job_id,
                        "target_language": args.target_language,
                        "dll_path": build_result.dll_path,
                        "included_formats": args.include_formats,
                        "build_time_ms": build_result.build_time_ms,
                        "dll_size_bytes": build_result.dll_size_bytes,
                        "exported_functions": build_result.exported_functions
                    })).unwrap()
                )]))
            },
            Err(e) => {
                let error_msg = format!("DLL build failed: {}", e);
                self.update_job(&job_id, JobStatus::Failed, 0.0, None, Some(error_msg.clone())).await;
                Err(McpError::internal_error(error_msg))
            }
        }
    }
    
    #[tool(description = "Get status of an async conversion job")]
    async fn get_job_status(&self, args: GetJobStatusArgs) -> Result<CallToolResult, McpError> {
        if let Some(job) = self.get_job(&args.job_id).await {
            Ok(CallToolResult::success(vec![Content::text(
                serde_json::to_string_pretty(&job).unwrap()
            )]))
        } else {
            Err(McpError::invalid_params(format!("Job not found: {}", args.job_id)))
        }
    }
    
    // Resource handler implementations
    async fn get_supported_formats(&self) -> JsonValue {
        json!({
            "formats": {
                "legacy": [
                    {
                        "id": "rtf",
                        "name": "Rich Text Format",
                        "extensions": [".rtf"],
                        "bidirectional": true,
                        "quality_rating": 5
                    },
                    {
                        "id": "doc",
                        "name": "Microsoft Word 97-2003",
                        "extensions": [".doc"],
                        "bidirectional": false,
                        "quality_rating": 4,
                        "feature_flag": "format-doc"
                    },
                    {
                        "id": "wpd",
                        "name": "WordPerfect Document",
                        "extensions": [".wpd", ".wp5", ".wp6"],
                        "bidirectional": false,
                        "quality_rating": 3,
                        "feature_flag": "format-wordperfect"
                    },
                    {
                        "id": "dbf",
                        "name": "dBase Database",
                        "extensions": [".dbf", ".db3", ".db4"],
                        "bidirectional": false,
                        "quality_rating": 4,
                        "feature_flag": "format-dbase"
                    },
                    {
                        "id": "wk1",
                        "name": "Lotus 1-2-3 Spreadsheet",
                        "extensions": [".wk1", ".wks", ".123"],
                        "bidirectional": false,
                        "quality_rating": 3,
                        "feature_flag": "format-lotus"
                    },
                    {
                        "id": "ws",
                        "name": "WordStar Document",
                        "extensions": [".ws", ".wsd"],
                        "bidirectional": false,
                        "quality_rating": 2,
                        "feature_flag": "format-wordstar"
                    }
                ],
                "modern": [
                    {
                        "id": "md",
                        "name": "Markdown",
                        "extensions": [".md", ".markdown"],
                        "bidirectional": true,
                        "quality_rating": 5
                    },
                    {
                        "id": "html",
                        "name": "HTML",
                        "extensions": [".html", ".htm"],
                        "bidirectional": true,
                        "quality_rating": 4
                    },
                    {
                        "id": "txt",
                        "name": "Plain Text",
                        "extensions": [".txt"],
                        "bidirectional": true,
                        "quality_rating": 5
                    },
                    {
                        "id": "json",
                        "name": "JSON",
                        "extensions": [".json"],
                        "bidirectional": true,
                        "quality_rating": 4
                    },
                    {
                        "id": "xml",
                        "name": "XML",
                        "extensions": [".xml"],
                        "bidirectional": true,
                        "quality_rating": 3
                    },
                    {
                        "id": "csv",
                        "name": "CSV",
                        "extensions": [".csv"],
                        "bidirectional": true,
                        "quality_rating": 5
                    }
                ]
            },
            "conversion_matrix": {
                "rtf": ["md", "html", "txt"],
                "doc": ["rtf", "md", "html", "txt"],
                "wpd": ["rtf", "md", "html", "txt"],
                "dbf": ["csv", "json", "md", "html"],
                "wk1": ["csv", "json", "md", "html"],
                "ws": ["txt", "md", "rtf"],
                "md": ["rtf", "html", "txt"],
                "html": ["md", "txt", "rtf"],
                "txt": ["md", "html", "rtf"],
                "json": ["xml", "csv", "md"],
                "xml": ["json", "md", "txt"],
                "csv": ["json", "md", "html"]
            }
        })
    }
    
    async fn get_legacy_formats_info(&self) -> JsonValue {
        json!({
            "legacy_formats": {
                "doc": {
                    "name": "Microsoft Word 97-2003",
                    "description": "Legacy binary Word format used in VB6/VFP9 applications",
                    "magic_bytes": ["D0CF11E0A1B11AE1"],
                    "conversion_capabilities": {
                        "preserves_formatting": true,
                        "supports_tables": true,
                        "supports_images": true,
                        "supports_metadata": true
                    },
                    "limitations": [
                        "Read-only conversion",
                        "Complex embedded objects may not convert perfectly",
                        "Macros are stripped during conversion"
                    ],
                    "recommended_outputs": ["rtf", "md", "html"]
                },
                "wpd": {
                    "name": "WordPerfect Document",
                    "description": "Corel WordPerfect documents - popular in legacy business environments",
                    "magic_bytes": ["FF575043", "FF574458"],
                    "conversion_capabilities": {
                        "preserves_formatting": true,
                        "supports_tables": true,
                        "supports_images": false,
                        "supports_metadata": true
                    },
                    "limitations": [
                        "Read-only conversion",
                        "Some WordPerfect-specific formatting may be lost",
                        "Image handling limited"
                    ],
                    "recommended_outputs": ["rtf", "md", "html", "txt"]
                },
                "dbf": {
                    "name": "dBase Database",
                    "description": "dBase database files - widely used in legacy data processing",
                    "magic_bytes": ["03", "04", "05"],
                    "conversion_capabilities": {
                        "preserves_formatting": false,
                        "supports_tables": true,
                        "supports_images": false,
                        "supports_metadata": true
                    },
                    "limitations": [
                        "Read-only conversion",
                        "Memo fields may require special handling",
                        "Index files (.ndx, .cdx) not processed"
                    ],
                    "recommended_outputs": ["csv", "json", "md"]
                },
                "wk1": {
                    "name": "Lotus 1-2-3 Spreadsheet",
                    "description": "Lotus 1-2-3 spreadsheet files - common in legacy financial systems",
                    "magic_bytes": ["0000020006040400", "00001A0000100400"],
                    "conversion_capabilities": {
                        "preserves_formatting": false,
                        "supports_tables": true,
                        "supports_images": false,
                        "supports_metadata": false
                    },
                    "limitations": [
                        "Read-only conversion",
                        "Formulas converted to values only",
                        "Macros are not supported",
                        "Complex formatting may be lost"
                    ],
                    "recommended_outputs": ["csv", "json", "md", "html"]
                },
                "ws": {
                    "name": "WordStar Document",
                    "description": "WordStar text processor files - early word processing standard",
                    "magic_bytes": ["1D7D"],
                    "conversion_capabilities": {
                        "preserves_formatting": false,
                        "supports_tables": false,
                        "supports_images": false,
                        "supports_metadata": false
                    },
                    "limitations": [
                        "Read-only conversion",
                        "Limited formatting preservation",
                        "Text-only output recommended"
                    ],
                    "recommended_outputs": ["txt", "md", "rtf"]
                }
            },
            "feature_flags": {
                "format-doc": "Enables Microsoft Word DOC format support",
                "format-wordperfect": "Enables WordPerfect format support",
                "format-dbase": "Enables dBase database format support",
                "format-lotus": "Enables Lotus 1-2-3 spreadsheet support",
                "format-wordstar": "Enables WordStar document support"
            }
        })
    }
    
    async fn get_server_stats(&self) -> JsonValue {
        let stats = self.stats.read().await;
        json!({
            "server_info": {
                "name": "LegacyBridge MCP Server",
                "version": "2.0.0",
                "protocol_version": "2024-11-05",
                "sdk": "rust-mcp-sdk v0.5.0"
            },
            "statistics": {
                "conversions_total": stats.conversions_total,
                "conversions_successful": stats.conversions_successful,
                "conversions_failed": stats.conversions_failed,
                "success_rate": if stats.conversions_total > 0 { 
                    (stats.conversions_successful as f64 / stats.conversions_total as f64) * 100.0 
                } else { 0.0 },
                "uptime_seconds": stats.uptime_seconds
            },
            "capabilities": {
                "supported_formats": stats.supported_formats,
                "legacy_formats_enabled": stats.legacy_formats_enabled,
                "parallel_processing": true,
                "batch_conversion": true,
                "dll_building": true,
                "format_detection": true,
                "file_validation": true
            }
        })
    }
    
    async fn get_active_jobs(&self) -> JsonValue {
        let jobs = self.active_jobs.read().await;
        json!({
            "active_jobs": jobs.values().collect::<Vec<_>>(),
            "total_active": jobs.len()
        })
    }
    
    async fn get_server_config(&self) -> JsonValue {
        json!({
            "config": {
                "legacy_formats": {
                    "doc_enabled": self.config.features.format_doc,
                    "wordperfect_enabled": self.config.features.format_wordperfect,
                    "dbase_enabled": self.config.features.format_dbase,
                    "lotus_enabled": self.config.features.format_lotus,
                    "wordstar_enabled": self.config.features.format_wordstar
                },
                "processing": {
                    "max_file_size_mb": self.config.processing.max_file_size_mb,
                    "parallel_jobs": self.config.processing.parallel_jobs,
                    "timeout_seconds": self.config.processing.timeout_seconds
                },
                "mcp_server": {
                    "transport": "stdio",
                    "protocol_version": "2024-11-05"
                }
            }
        })
    }
    
    #[tool(description = "Detect the format of a file from its content using AI-powered analysis")]
    async fn detect_format(&self, args: DetectFormatArgs) -> Result<CallToolResult, McpError> {
        info!("Detecting format for file");
        
        let content = general_purpose::STANDARD.decode(&args.file_content)
            .map_err(|e| McpError::invalid_params(format!("Invalid base64 content: {}", e)))?;
        
        match self.format_detector.detect_format(&content, args.filename.as_deref()) {
            Ok(detection_result) => {
                Ok(CallToolResult::success(vec![Content::text(
                    serde_json::to_string_pretty(&json!({
                        "success": true,
                        "format_id": detection_result.format_id,
                        "confidence": detection_result.confidence,
                        "detected_features": detection_result.detected_features,
                        "metadata": detection_result.metadata,
                        "detailed_analysis": args.detailed_analysis.unwrap_or(false)
                    })).unwrap()
                )]))
            },
            Err(e) => Err(McpError::internal_error(format!("Format detection failed: {}", e)))
        }
    }
    
    #[tool(description = "Get detailed information about a specific file format")]
    async fn get_format_info(&self, args: GetFormatInfoArgs) -> Result<CallToolResult, McpError> {
        info!("Getting format info for: {}", args.format_id);
        
        let format_info = match args.format_id.as_str() {
            "rtf" => json!({
                "id": "rtf",
                "name": "Rich Text Format",
                "extensions": [".rtf"],
                "mime_type": "application/rtf",
                "description": "Microsoft's proprietary document format",
                "features": ["formatting", "images", "tables", "fonts"],
                "conversion_quality": 5
            }),
            "doc" => json!({
                "id": "doc",
                "name": "Microsoft Word 97-2003",
                "extensions": [".doc"],
                "mime_type": "application/msword",
                "description": "Legacy Microsoft Word binary format",
                "features": ["formatting", "images", "tables", "macros", "ole"],
                "conversion_quality": 4
            }),
            "wpd" => json!({
                "id": "wpd",
                "name": "WordPerfect Document",
                "extensions": [".wpd", ".wp5", ".wp6"],
                "mime_type": "application/wordperfect",
                "description": "Corel WordPerfect document format",
                "features": ["formatting", "styles", "reveal_codes"],
                "conversion_quality": 3
            }),
            _ => return Err(McpError::invalid_params(format!("Unknown format: {}", args.format_id)))
        };
        
        Ok(CallToolResult::success(vec![Content::text(
            serde_json::to_string_pretty(&format_info).unwrap()
        )]))
    }
    
    #[tool(description = "Extract plain text content from any supported format")]
    async fn extract_text(&self, args: ExtractTextArgs) -> Result<CallToolResult, McpError> {
        info!("Extracting text from file");
        
        let content = general_purpose::STANDARD.decode(&args.file_content)
            .map_err(|e| McpError::invalid_params(format!("Invalid base64 content: {}", e)))?;
        
        let format = if let Some(format) = args.format {
            format
        } else {
            self.format_detector.detect_format(&content, None)
                .map_err(|e| McpError::internal_error(format!("Format detection failed: {}", e)))?
                .format_id
        };
        
        match self.legacy_converter.extract_text(&content, &format).await {
            Ok(text) => {
                Ok(CallToolResult::success(vec![Content::text(
                    serde_json::to_string_pretty(&json!({
                        "success": true,
                        "text": text,
                        "format": format,
                        "word_count": text.split_whitespace().count(),
                        "line_count": text.lines().count()
                    })).unwrap()
                )]))
            },
            Err(e) => Err(McpError::internal_error(format!("Text extraction failed: {}", e)))
        }
    }
    
    #[tool(description = "Generate a preview of the document in HTML format")]
    async fn generate_preview(&self, args: GeneratePreviewArgs) -> Result<CallToolResult, McpError> {
        info!("Generating preview");
        
        let content = general_purpose::STANDARD.decode(&args.file_content)
            .map_err(|e| McpError::invalid_params(format!("Invalid base64 content: {}", e)))?;
        
        let format = if let Some(format) = args.format {
            format
        } else {
            self.format_detector.detect_format(&content, None)
                .map_err(|e| McpError::internal_error(format!("Format detection failed: {}", e)))?
                .format_id
        };
        
        let options = ConversionOptions {
            max_pages: args.max_pages,
            include_styles: args.include_styles.unwrap_or(true),
            ..Default::default()
        };
        
        match self.legacy_converter.generate_preview(&content, &format, &options).await {
            Ok(preview_html) => {
                Ok(CallToolResult::success(vec![Content::text(
                    serde_json::to_string_pretty(&json!({
                        "success": true,
                        "preview_html": preview_html,
                        "format": format
                    })).unwrap()
                )]))
            },
            Err(e) => Err(McpError::internal_error(format!("Preview generation failed: {}", e)))
        }
    }
    
    #[tool(description = "Get conversion options and recommendations for a specific format pair")]
    async fn get_conversion_options(&self, args: GetConversionOptionsArgs) -> Result<CallToolResult, McpError> {
        info!("Getting conversion options for {} -> {}", args.input_format, args.output_format);
        
        let options = match (args.input_format.as_str(), args.output_format.as_str()) {
            ("rtf", "md") => json!({
                "recommended_options": {
                    "preserve_formatting": true,
                    "include_metadata": true,
                    "extract_images": true
                },
                "quality_score": 5,
                "limitations": [],
                "best_practices": ["Use preserve_formatting for complex documents"]
            }),
            ("doc", "md") => json!({
                "recommended_options": {
                    "preserve_formatting": true,
                    "handle_ole_objects": true,
                    "convert_macros": false
                },
                "quality_score": 4,
                "limitations": ["Macros will be removed", "Some formatting may be simplified"],
                "best_practices": ["Review converted tables", "Check embedded object handling"]
            }),
            _ => json!({
                "recommended_options": {},
                "quality_score": 3,
                "limitations": ["Conversion may have limitations"],
                "best_practices": ["Review output carefully"]
            })
        };
        
        Ok(CallToolResult::success(vec![Content::text(
            serde_json::to_string_pretty(&options).unwrap()
        )]))
    }
    
    #[tool(description = "List all supported file formats with conversion capabilities")]
    async fn list_supported_formats(&self) -> Result<CallToolResult, McpError> {
        info!("Listing supported formats");
        
        let formats = self.get_supported_formats().await;
        
        Ok(CallToolResult::success(vec![Content::text(
            formats.to_string()
        )]))
    }
    
    #[tool(description = "Get server information and capabilities")]
    async fn get_server_info(&self) -> Result<CallToolResult, McpError> {
        info!("Getting server info");
        
        let stats = self.stats.read().await;
        let info = json!({
            "server": {
                "name": "LegacyBridge MCP Server",
                "version": "2.0.0",
                "protocol_version": "2024-11-05",
                "sdk": "rust-mcp-sdk v0.5.0"
            },
            "capabilities": {
                "formats": stats.supported_formats.clone(),
                "legacy_support": stats.legacy_formats_enabled,
                "features": ["format_detection", "batch_conversion", "dll_building", "file_validation", "text_extraction", "preview_generation"]
            },
            "performance": {
                "conversions_total": stats.conversions_total,
                "success_rate": if stats.conversions_total > 0 { 
                    (stats.conversions_successful as f64 / stats.conversions_total as f64) * 100.0 
                } else { 0.0 }
            }
        });
        
        Ok(CallToolResult::success(vec![Content::text(
            serde_json::to_string_pretty(&info).unwrap()
        )]))
    }
}

// Re-export for easier imports
pub use LegacyBridgeMcpServerOfficial as OfficialMcpServer;