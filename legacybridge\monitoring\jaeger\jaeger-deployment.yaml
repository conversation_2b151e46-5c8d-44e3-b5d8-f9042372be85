apiVersion: apps/v1
kind: Deployment
metadata:
  name: jaeger
  namespace: monitoring
  labels:
    app: jaeger
    component: all-in-one
spec:
  replicas: 1
  selector:
    matchLabels:
      app: jaeger
      component: all-in-one
  template:
    metadata:
      labels:
        app: jaeger
        component: all-in-one
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "14269"
    spec:
      containers:
      - name: jaeger
        image: jaegertracing/all-in-one:1.50
        env:
        - name: COLLECTOR_OTLP_ENABLED
          value: "true"
        - name: SPAN_STORAGE_TYPE
          value: elasticsearch
        - name: ES_SERVER_URLS
          value: http://elasticsearch:9200
        - name: ES_INDEX_PREFIX
          value: jaeger
        - name: ES_TAGS_AS_FIELDS_ALL
          value: "true"
        - name: QUERY_BASE_PATH
          value: /jaeger
        ports:
        - containerPort: 5775
          protocol: UDP
          name: zk-compact
        - containerPort: 6831
          protocol: UDP
          name: jaeger-compact
        - containerPort: 6832
          protocol: UDP
          name: jaeger-binary
        - containerPort: 5778
          protocol: TCP
          name: serve-configs
        - containerPort: 16686
          protocol: TCP
          name: query
        - containerPort: 14250
          protocol: TCP
          name: grpc
        - containerPort: 14268
          protocol: TCP
          name: thrift
        - containerPort: 14269
          protocol: TCP
          name: metrics
        - containerPort: 4317
          protocol: TCP
          name: otlp-grpc
        - containerPort: 4318
          protocol: TCP
          name: otlp-http
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        readinessProbe:
          httpGet:
            path: /
            port: 14269
          initialDelaySeconds: 5
        livenessProbe:
          httpGet:
            path: /
            port: 14269
          initialDelaySeconds: 15
        volumeMounts:
        - name: config
          mountPath: /etc/jaeger
          readOnly: true
      volumes:
      - name: config
        configMap:
          name: jaeger-config
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-query
  namespace: monitoring
  labels:
    app: jaeger
    component: query
spec:
  ports:
  - name: http
    port: 16686
    targetPort: 16686
  - name: grpc
    port: 16685
    targetPort: 16685
  selector:
    app: jaeger
    component: all-in-one
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-collector
  namespace: monitoring
  labels:
    app: jaeger
    component: collector
spec:
  ports:
  - name: grpc
    port: 14250
    targetPort: 14250
  - name: thrift-http
    port: 14268
    targetPort: 14268
  - name: thrift-compact
    port: 6831
    targetPort: 6831
    protocol: UDP
  - name: thrift-binary
    port: 6832
    targetPort: 6832
    protocol: UDP
  - name: otlp-grpc
    port: 4317
    targetPort: 4317
  - name: otlp-http
    port: 4318
    targetPort: 4318
  selector:
    app: jaeger
    component: all-in-one
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-agent
  namespace: monitoring
  labels:
    app: jaeger
    component: agent
spec:
  ports:
  - name: config-rest
    port: 5778
    targetPort: 5778
  - name: compact
    port: 6831
    targetPort: 6831
    protocol: UDP
  - name: binary
    port: 6832
    targetPort: 6832
    protocol: UDP
  - name: sampling
    port: 5775
    targetPort: 5775
    protocol: UDP
  selector:
    app: jaeger
    component: all-in-one
  clusterIP: None