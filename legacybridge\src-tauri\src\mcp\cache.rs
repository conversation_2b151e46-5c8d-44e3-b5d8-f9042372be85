// Caching and Performance Optimization for LegacyBridge MCP Server
// Implements intelligent caching strategies and performance enhancements

use std::sync::Arc;
use tokio::sync::RwLock;
use std::collections::{HashMap, VecDeque};
use std::time::{Duration, Instant};
use serde::{Serialize, Deserialize};
use serde_json::{json, Value as JsonValue};
use sha2::{Sha256, Digest};
use lru::LruCache;
use std::num::NonZeroUsize;
use crate::mcp::types::IntegrationError;

/// Cache manager for conversion results
pub struct CacheManager {
    /// LRU cache for conversion results
    conversion_cache: Arc<RwLock<LruCache<String, CachedConversion>>>,
    
    /// Format detection cache
    format_cache: Arc<RwLock<LruCache<String, CachedFormat>>>,
    
    /// DLL build cache
    dll_cache: Arc<RwLock<HashMap<String, CachedDll>>>,
    
    /// Cache statistics
    stats: Arc<RwLock<CacheStats>>,
    
    /// Cache configuration
    config: CacheConfig,
}

#[derive(Clone)]
pub struct CacheConfig {
    /// Maximum number of cached conversions
    pub max_conversions: usize,
    
    /// Maximum number of cached format detections
    pub max_formats: usize,
    
    /// Cache TTL in seconds
    pub ttl_seconds: u64,
    
    /// Maximum size of cached content in bytes
    pub max_content_size: usize,
    
    /// Enable compression for cached content
    pub enable_compression: bool,
    
    /// Cache warming on startup
    pub warm_cache_on_startup: bool,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            max_conversions: 1000,
            max_formats: 500,
            ttl_seconds: 3600, // 1 hour
            max_content_size: 10 * 1024 * 1024, // 10MB
            enable_compression: true,
            warm_cache_on_startup: false,
        }
    }
}

#[derive(Clone, Serialize, Deserialize)]
struct CachedConversion {
    key: String,
    input_format: String,
    output_format: String,
    content: Vec<u8>,
    compressed: bool,
    created_at: Instant,
    access_count: u64,
    size: usize,
    quality_score: u8,
}

#[derive(Clone, Serialize, Deserialize)]
struct CachedFormat {
    key: String,
    format: String,
    confidence: f64,
    metadata: JsonValue,
    created_at: Instant,
}

#[derive(Clone)]
struct CachedDll {
    config_hash: String,
    dll_path: String,
    created_at: Instant,
    size: usize,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct CacheStats {
    pub total_hits: u64,
    pub total_misses: u64,
    pub conversion_hits: u64,
    pub conversion_misses: u64,
    pub format_hits: u64,
    pub format_misses: u64,
    pub dll_hits: u64,
    pub dll_misses: u64,
    pub total_saved_bytes: u64,
    pub total_saved_time_ms: u64,
    pub evictions: u64,
}

impl CacheManager {
    /// Create a new cache manager
    pub fn new(config: CacheConfig) -> Self {
        let conversion_cache = LruCache::new(
            NonZeroUsize::new(config.max_conversions).unwrap_or(NonZeroUsize::new(1000).unwrap())
        );
        
        let format_cache = LruCache::new(
            NonZeroUsize::new(config.max_formats).unwrap_or(NonZeroUsize::new(500).unwrap())
        );
        
        Self {
            conversion_cache: Arc::new(RwLock::new(conversion_cache)),
            format_cache: Arc::new(RwLock::new(format_cache)),
            dll_cache: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(CacheStats::default())),
            config,
        }
    }
    
    /// Generate cache key for conversion
    pub fn generate_conversion_key(
        content: &[u8],
        input_format: &str,
        output_format: &str,
        options: &JsonValue,
    ) -> String {
        let mut hasher = Sha256::new();
        hasher.update(content);
        hasher.update(input_format.as_bytes());
        hasher.update(output_format.as_bytes());
        hasher.update(serde_json::to_string(options).unwrap_or_default().as_bytes());
        
        format!("{:x}", hasher.finalize())
    }
    
    /// Get cached conversion result
    pub async fn get_conversion(
        &self,
        key: &str,
    ) -> Option<Vec<u8>> {
        let mut cache = self.conversion_cache.write().await;
        
        if let Some(cached) = cache.get_mut(key) {
            // Check TTL
            if cached.created_at.elapsed() > Duration::from_secs(self.config.ttl_seconds) {
                cache.pop(key);
                self.stats.write().await.evictions += 1;
                return None;
            }
            
            // Update stats
            cached.access_count += 1;
            let mut stats = self.stats.write().await;
            stats.total_hits += 1;
            stats.conversion_hits += 1;
            
            // Decompress if needed
            let content = if cached.compressed {
                Self::decompress(&cached.content).ok()?
            } else {
                cached.content.clone()
            };
            
            Some(content)
        } else {
            let mut stats = self.stats.write().await;
            stats.total_misses += 1;
            stats.conversion_misses += 1;
            None
        }
    }
    
    /// Store conversion result in cache
    pub async fn store_conversion(
        &self,
        key: String,
        input_format: String,
        output_format: String,
        content: Vec<u8>,
        quality_score: u8,
        processing_time_ms: u64,
    ) -> Result<(), IntegrationError> {
        // Check size limit
        if content.len() > self.config.max_content_size {
            return Ok(()); // Don't cache large files
        }
        
        // Compress if enabled and beneficial
        let (stored_content, compressed) = if self.config.enable_compression && content.len() > 1024 {
            match Self::compress(&content) {
                Ok(compressed) if compressed.len() < content.len() => (compressed, true),
                _ => (content.clone(), false),
            }
        } else {
            (content.clone(), false)
        };
        
        let cached = CachedConversion {
            key: key.clone(),
            input_format,
            output_format,
            content: stored_content,
            compressed,
            created_at: Instant::now(),
            access_count: 0,
            size: content.len(),
            quality_score,
        };
        
        // Update cache
        let mut cache = self.conversion_cache.write().await;
        cache.put(key, cached);
        
        // Update stats
        let mut stats = self.stats.write().await;
        stats.total_saved_bytes += content.len() as u64;
        stats.total_saved_time_ms += processing_time_ms;
        
        Ok(())
    }
    
    /// Get cached format detection
    pub async fn get_format(&self, key: &str) -> Option<(String, f64, JsonValue)> {
        let mut cache = self.format_cache.write().await;
        
        if let Some(cached) = cache.get_mut(key) {
            // Check TTL
            if cached.created_at.elapsed() > Duration::from_secs(self.config.ttl_seconds) {
                cache.pop(key);
                return None;
            }
            
            let mut stats = self.stats.write().await;
            stats.total_hits += 1;
            stats.format_hits += 1;
            
            Some((cached.format.clone(), cached.confidence, cached.metadata.clone()))
        } else {
            let mut stats = self.stats.write().await;
            stats.total_misses += 1;
            stats.format_misses += 1;
            None
        }
    }
    
    /// Store format detection result
    pub async fn store_format(
        &self,
        key: String,
        format: String,
        confidence: f64,
        metadata: JsonValue,
    ) {
        let cached = CachedFormat {
            key: key.clone(),
            format,
            confidence,
            metadata,
            created_at: Instant::now(),
        };
        
        let mut cache = self.format_cache.write().await;
        cache.put(key, cached);
    }
    
    /// Get cached DLL
    pub async fn get_dll(&self, config_hash: &str) -> Option<String> {
        let cache = self.dll_cache.read().await;
        
        if let Some(cached) = cache.get(config_hash) {
            // Check TTL (DLLs have longer TTL)
            if cached.created_at.elapsed() > Duration::from_secs(self.config.ttl_seconds * 24) {
                return None;
            }
            
            let mut stats = self.stats.write().await;
            stats.total_hits += 1;
            stats.dll_hits += 1;
            
            // Check if DLL still exists
            if std::path::Path::new(&cached.dll_path).exists() {
                Some(cached.dll_path.clone())
            } else {
                None
            }
        } else {
            let mut stats = self.stats.write().await;
            stats.total_misses += 1;
            stats.dll_misses += 1;
            None
        }
    }
    
    /// Store DLL in cache
    pub async fn store_dll(&self, config_hash: String, dll_path: String, size: usize) {
        let cached = CachedDll {
            config_hash: config_hash.clone(),
            dll_path,
            created_at: Instant::now(),
            size,
        };
        
        let mut cache = self.dll_cache.write().await;
        cache.insert(config_hash, cached);
    }
    
    /// Clear all caches
    pub async fn clear_all(&self) {
        self.conversion_cache.write().await.clear();
        self.format_cache.write().await.clear();
        self.dll_cache.write().await.clear();
        
        let mut stats = self.stats.write().await;
        *stats = CacheStats::default();
    }
    
    /// Get cache statistics
    pub async fn get_stats(&self) -> CacheStats {
        self.stats.read().await.clone()
    }
    
    /// Warm cache with common conversions
    pub async fn warm_cache(&self) -> Result<(), IntegrationError> {
        if !self.config.warm_cache_on_startup {
            return Ok(());
        }
        
        // Common RTF to Markdown conversions
        let common_formats = vec![
            ("rtf", "md"),
            ("doc", "md"),
            ("md", "rtf"),
            ("wordperfect", "md"),
        ];
        
        // Pre-cache format detection patterns
        for (format, _) in &common_formats {
            let key = format!("format_{}", format);
            self.store_format(
                key,
                format.to_string(),
                1.0,
                json!({
                    "warmed": true,
                    "common_format": true
                }),
            ).await;
        }
        
        Ok(())
    }
    
    /// Compress content using zstd
    fn compress(content: &[u8]) -> Result<Vec<u8>, IntegrationError> {
        // In a real implementation, use zstd or similar
        // For now, just return a copy
        Ok(content.to_vec())
    }
    
    /// Decompress content
    fn decompress(content: &[u8]) -> Result<Vec<u8>, IntegrationError> {
        // In a real implementation, use zstd or similar
        // For now, just return a copy
        Ok(content.to_vec())
    }
}

/// Performance optimizer for batch operations
pub struct PerformanceOptimizer {
    /// Optimal batch sizes for different file types
    batch_sizes: HashMap<String, usize>,
    
    /// Parallel job recommendations
    parallel_jobs: HashMap<String, usize>,
    
    /// Performance metrics
    metrics: Arc<RwLock<PerformanceMetrics>>,
}

#[derive(Debug, Default)]
struct PerformanceMetrics {
    avg_conversion_time_ms: HashMap<String, f64>,
    throughput_files_per_second: HashMap<String, f64>,
    memory_usage_mb: HashMap<String, f64>,
}

impl PerformanceOptimizer {
    pub fn new() -> Self {
        let mut batch_sizes = HashMap::new();
        batch_sizes.insert("small".to_string(), 50);  // < 1MB files
        batch_sizes.insert("medium".to_string(), 20); // 1-10MB files
        batch_sizes.insert("large".to_string(), 5);   // > 10MB files
        
        let mut parallel_jobs = HashMap::new();
        parallel_jobs.insert("small".to_string(), 8);
        parallel_jobs.insert("medium".to_string(), 4);
        parallel_jobs.insert("large".to_string(), 2);
        
        Self {
            batch_sizes,
            parallel_jobs,
            metrics: Arc::new(RwLock::new(PerformanceMetrics::default())),
        }
    }
    
    /// Get optimal batch size based on file characteristics
    pub fn get_optimal_batch_size(&self, avg_file_size: usize) -> usize {
        let category = if avg_file_size < 1_000_000 {
            "small"
        } else if avg_file_size < 10_000_000 {
            "medium"
        } else {
            "large"
        };
        
        self.batch_sizes.get(category).copied().unwrap_or(10)
    }
    
    /// Get optimal parallel jobs
    pub fn get_optimal_parallel_jobs(&self, avg_file_size: usize, cpu_cores: usize) -> usize {
        let category = if avg_file_size < 1_000_000 {
            "small"
        } else if avg_file_size < 10_000_000 {
            "medium"
        } else {
            "large"
        };
        
        let base_jobs = self.parallel_jobs.get(category).copied().unwrap_or(4);
        std::cmp::min(base_jobs, cpu_cores)
    }
    
    /// Update performance metrics
    pub async fn update_metrics(
        &self,
        format: String,
        conversion_time_ms: u64,
        file_size: usize,
    ) {
        let mut metrics = self.metrics.write().await;
        
        // Update average conversion time
        let avg_time = metrics.avg_conversion_time_ms.entry(format.clone()).or_insert(0.0);
        *avg_time = (*avg_time * 0.9) + (conversion_time_ms as f64 * 0.1); // Exponential moving average
        
        // Update throughput
        if conversion_time_ms > 0 {
            let throughput = 1000.0 / conversion_time_ms as f64;
            let avg_throughput = metrics.throughput_files_per_second.entry(format.clone()).or_insert(0.0);
            *avg_throughput = (*avg_throughput * 0.9) + (throughput * 0.1);
        }
        
        // Estimate memory usage
        let memory_mb = (file_size as f64 * 2.5) / (1024.0 * 1024.0); // Rough estimate
        let avg_memory = metrics.memory_usage_mb.entry(format).or_insert(0.0);
        *avg_memory = (*avg_memory * 0.9) + (memory_mb * 0.1);
    }
    
    /// Get performance recommendations
    pub async fn get_recommendations(&self) -> JsonValue {
        let metrics = self.metrics.read().await;
        
        let mut recommendations = Vec::new();
        
        // Check for slow formats
        for (format, avg_time) in &metrics.avg_conversion_time_ms {
            if *avg_time > 5000.0 {
                recommendations.push(json!({
                    "type": "performance",
                    "format": format,
                    "message": format!("{} conversions are slow (avg {}ms), consider using smaller batches", format, avg_time as u64),
                    "suggestion": "reduce_batch_size"
                }));
            }
        }
        
        // Check for memory usage
        for (format, avg_memory) in &metrics.memory_usage_mb {
            if *avg_memory > 100.0 {
                recommendations.push(json!({
                    "type": "memory",
                    "format": format,
                    "message": format!("{} conversions use high memory (avg {}MB)", format, avg_memory as u64),
                    "suggestion": "reduce_parallel_jobs"
                }));
            }
        }
        
        json!({
            "recommendations": recommendations,
            "metrics": {
                "avg_conversion_times": metrics.avg_conversion_time_ms.clone(),
                "throughput": metrics.throughput_files_per_second.clone(),
                "memory_usage": metrics.memory_usage_mb.clone(),
            }
        })
    }
}

// Extension for the MCP server
impl crate::mcp::server::LegacyBridgeMcpServer {
    /// Check cache before processing
    pub async fn check_cache(&self, key: &str) -> Option<Vec<u8>> {
        // Would need to add cache_manager field to server struct
        // self.cache_manager.get_conversion(key).await
        None
    }
    
    /// Store result in cache
    pub async fn store_in_cache(
        &self,
        key: String,
        input_format: String,
        output_format: String,
        content: Vec<u8>,
        quality_score: u8,
        processing_time_ms: u64,
    ) -> Result<(), IntegrationError> {
        // Would need to add cache_manager field to server struct
        // self.cache_manager.store_conversion(key, input_format, output_format, content, quality_score, processing_time_ms).await
        Ok(())
    }
}