// Job queue implementation using Redis
use legacybridge_shared::{
    types::{ConversionRequest, JobStatus},
    ServiceError, ServiceResult,
};
use redis::{AsyncCommands, RedisResult};
use serde::{Deserialize, Serialize};
use std::time::Duration;
use tracing::{info, warn, error};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct QueuedJob {
    pub job_id: String,
    pub request: ConversionRequest,
    pub priority: i32,
    pub queued_at: chrono::DateTime<chrono::Utc>,
    pub attempts: u32,
    pub max_attempts: u32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct JobResult {
    pub job_id: String,
    pub status: JobStatus,
    pub result: Option<String>, // Base64 encoded result
    pub error: Option<String>,
    pub processing_time_ms: Option<u64>,
    pub completed_at: chrono::DateTime<chrono::Utc>,
    pub metadata: Option<serde_json::Value>,
}

pub struct JobQueue {
    redis_client: redis::Client,
}

impl JobQueue {
    pub async fn new(redis_url: &str) -> ServiceResult<Self> {
        let redis_client = redis::Client::open(redis_url)?;
        
        // Test connection
        let mut conn = redis_client.get_async_connection().await?;
        let _: String = conn.ping().await?;
        
        Ok(Self { redis_client })
    }

    /// Add a job to the queue with priority
    pub async fn enqueue_job(&self, job: QueuedJob) -> ServiceResult<()> {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        // Serialize job
        let job_data = serde_json::to_string(&job)?;
        
        // Add to priority queue (using sorted set with priority as score)
        let queue_key = "conversion_queue";
        let _: () = conn.zadd(queue_key, job.job_id.clone(), -job.priority as f64).await?;
        
        // Store job data
        let job_key = format!("job:{}", job.job_id);
        let _: () = conn.setex(job_key, 3600 * 24, job_data).await?; // 24 hour TTL
        
        // Set job status
        let status_key = format!("job_status:{}", job.job_id);
        let _: () = conn.setex(status_key, 3600 * 24, "queued").await?;
        
        info!(job_id = %job.job_id, priority = job.priority, "Job enqueued");
        
        Ok(())
    }

    /// Get the next job from the queue
    pub async fn dequeue_job(&self) -> ServiceResult<Option<QueuedJob>> {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        // Get highest priority job (lowest score due to negative priority)
        let queue_key = "conversion_queue";
        let result: Vec<String> = conn.zpopmin(queue_key, 1).await?;
        
        if result.is_empty() {
            return Ok(None);
        }
        
        let job_id = &result[0];
        
        // Get job data
        let job_key = format!("job:{}", job_id);
        let job_data: Option<String> = conn.get(&job_key).await?;
        
        match job_data {
            Some(data) => {
                let job: QueuedJob = serde_json::from_str(&data)?;
                
                // Update status to processing
                let status_key = format!("job_status:{}", job_id);
                let _: () = conn.setex(status_key, 3600 * 24, "processing").await?;
                
                info!(job_id = %job_id, "Job dequeued for processing");
                Ok(Some(job))
            }
            None => {
                warn!(job_id = %job_id, "Job data not found for dequeued job");
                Ok(None)
            }
        }
    }

    /// Mark job as completed with result
    pub async fn complete_job(&self, job_result: JobResult) -> ServiceResult<()> {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        // Store result
        let result_key = format!("job_result:{}", job_result.job_id);
        let result_data = serde_json::to_string(&job_result)?;
        let _: () = conn.setex(result_key, 3600 * 24, result_data).await?;
        
        // Update status
        let status_key = format!("job_status:{}", job_result.job_id);
        let status_str = match job_result.status {
            JobStatus::Completed => "completed",
            JobStatus::Failed => "failed",
            JobStatus::Cancelled => "cancelled",
            _ => "unknown",
        };
        let _: () = conn.setex(status_key, 3600 * 24, status_str).await?;
        
        info!(
            job_id = %job_result.job_id,
            status = %status_str,
            "Job completed"
        );
        
        Ok(())
    }

    /// Get job status
    pub async fn get_job_status(&self, job_id: &str) -> ServiceResult<Option<String>> {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        let status_key = format!("job_status:{}", job_id);
        let status: Option<String> = conn.get(status_key).await?;
        
        Ok(status)
    }

    /// Get job result
    pub async fn get_job_result(&self, job_id: &str) -> ServiceResult<Option<JobResult>> {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        let result_key = format!("job_result:{}", job_id);
        let result_data: Option<String> = conn.get(result_key).await?;
        
        match result_data {
            Some(data) => {
                let result: JobResult = serde_json::from_str(&data)?;
                Ok(Some(result))
            }
            None => Ok(None),
        }
    }

    /// Cancel a job
    pub async fn cancel_job(&self, job_id: &str) -> ServiceResult<bool> {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        // Try to remove from queue first
        let queue_key = "conversion_queue";
        let removed: i32 = conn.zrem(queue_key, job_id).await?;
        
        if removed > 0 {
            // Job was in queue, mark as cancelled
            let status_key = format!("job_status:{}", job_id);
            let _: () = conn.setex(status_key, 3600 * 24, "cancelled").await?;
            
            info!(job_id = %job_id, "Job cancelled from queue");
            Ok(true)
        } else {
            // Check if job is currently processing
            let status = self.get_job_status(job_id).await?;
            match status.as_deref() {
                Some("processing") => {
                    // Job is being processed, can't cancel
                    warn!(job_id = %job_id, "Cannot cancel job that is currently processing");
                    Ok(false)
                }
                Some("completed") | Some("failed") | Some("cancelled") => {
                    // Job already finished
                    Ok(false)
                }
                _ => {
                    // Job not found
                    Ok(false)
                }
            }
        }
    }

    /// Get queue length
    pub async fn get_queue_length(&self) -> ServiceResult<i64> {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        let queue_key = "conversion_queue";
        let length: i64 = conn.zcard(queue_key).await?;
        
        Ok(length)
    }

    /// Requeue failed job with increased attempt count
    pub async fn requeue_job(&self, mut job: QueuedJob) -> ServiceResult<()> {
        if job.attempts >= job.max_attempts {
            return Err(ServiceError::BadRequest("Job has exceeded maximum retry attempts".to_string()));
        }
        
        job.attempts += 1;
        job.queued_at = chrono::Utc::now();
        
        self.enqueue_job(job).await?;
        
        Ok(())
    }

    /// Clean up expired jobs
    pub async fn cleanup_expired_jobs(&self) -> ServiceResult<u32> {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        // This would implement cleanup logic for old job data
        // For now, Redis TTL handles most cleanup automatically
        
        Ok(0)
    }

    /// Get job details
    pub async fn get_job(&self, job_id: &str) -> ServiceResult<Option<QueuedJob>> {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        let job_key = format!("job:{}", job_id);
        let job_data: Option<String> = conn.get(job_key).await?;
        
        match job_data {
            Some(data) => {
                let job: QueuedJob = serde_json::from_str(&data)?;
                Ok(Some(job))
            }
            None => Ok(None),
        }
    }
}
