groups:
  - name: legacybridge_slos
    interval: 30s
    rules:
      # API Availability SLO (99.9%)
      - record: slo:api_availability:rate5m
        expr: |
          sum(rate(http_requests_total{job="legacybridge-backend",status!~"5.."}[5m]))
          /
          sum(rate(http_requests_total{job="legacybridge-backend"}[5m]))

      - alert: SLO_APIAvailability
        expr: |
          (
            avg_over_time(slo:api_availability:rate5m[30m]) < 0.999
          )
        for: 5m
        labels:
          severity: warning
          team: backend
          slo: true
          service: legacybridge
        annotations:
          summary: "API Availability SLO breach risk"
          description: "API availability is {{ $value | humanizePercentage }} over the last 30 minutes (SLO: 99.9%)"
          runbook_url: "https://wiki.example.com/runbooks/slo/api-availability"
          dashboard_url: "https://grafana.example.com/d/slo-dashboard"

      # API Latency SLO (95th percentile < 500ms)
      - record: slo:api_latency_p95:histogram_quantile
        expr: |
          histogram_quantile(0.95,
            sum(rate(http_request_duration_seconds_bucket{job="legacybridge-backend"}[5m])) by (le)
          )

      - alert: SLO_APILatency
        expr: |
          slo:api_latency_p95:histogram_quantile > 0.5
        for: 10m
        labels:
          severity: warning
          team: backend
          slo: true
          service: legacybridge
        annotations:
          summary: "API Latency SLO breach"
          description: "95th percentile latency is {{ $value | humanizeDuration }} (SLO: < 500ms)"
          runbook_url: "https://wiki.example.com/runbooks/slo/api-latency"

      # Document Conversion Success Rate SLO (99%)
      - record: slo:conversion_success_rate:rate5m
        expr: |
          sum(rate(conversion_total{job="legacybridge-backend",status="success"}[5m]))
          /
          sum(rate(conversion_total{job="legacybridge-backend"}[5m]))

      - alert: SLO_ConversionSuccessRate
        expr: |
          (
            avg_over_time(slo:conversion_success_rate:rate5m[30m]) < 0.99
          )
        for: 10m
        labels:
          severity: warning
          team: backend
          slo: true
          service: legacybridge
        annotations:
          summary: "Document Conversion Success Rate SLO breach"
          description: "Conversion success rate is {{ $value | humanizePercentage }} over the last 30 minutes (SLO: 99%)"
          runbook_url: "https://wiki.example.com/runbooks/slo/conversion-success"

      # Error Budget Calculations
      - record: slo:error_budget:api_availability
        expr: |
          1 - (1 - 0.999) * (1 - avg_over_time(slo:api_availability:rate5m[30d]))

      - record: slo:error_budget:conversion_success
        expr: |
          1 - (1 - 0.99) * (1 - avg_over_time(slo:conversion_success_rate:rate5m[30d]))

      # Monthly SLO Reports
      - record: slo:monthly:api_availability
        expr: |
          avg_over_time(slo:api_availability:rate5m[30d])

      - record: slo:monthly:api_latency_p95
        expr: |
          quantile_over_time(0.95, slo:api_latency_p95:histogram_quantile[30d])

      - record: slo:monthly:conversion_success_rate
        expr: |
          avg_over_time(slo:conversion_success_rate:rate5m[30d])

  - name: legacybridge_slis
    interval: 30s
    rules:
      # Service Level Indicators (SLIs)
      - record: sli:request_rate
        expr: |
          sum(rate(http_requests_total{job="legacybridge-backend"}[5m])) by (method, status)

      - record: sli:error_rate
        expr: |
          sum(rate(http_requests_total{job="legacybridge-backend",status=~"5.."}[5m])) by (method)

      - record: sli:latency_p50
        expr: |
          histogram_quantile(0.50,
            sum(rate(http_request_duration_seconds_bucket{job="legacybridge-backend"}[5m])) by (le, method)
          )

      - record: sli:latency_p95
        expr: |
          histogram_quantile(0.95,
            sum(rate(http_request_duration_seconds_bucket{job="legacybridge-backend"}[5m])) by (le, method)
          )

      - record: sli:latency_p99
        expr: |
          histogram_quantile(0.99,
            sum(rate(http_request_duration_seconds_bucket{job="legacybridge-backend"}[5m])) by (le, method)
          )

      - record: sli:conversion_queue_time
        expr: |
          histogram_quantile(0.95,
            sum(rate(conversion_queue_time_seconds_bucket{job="legacybridge-backend"}[5m])) by (le)
          )

      - record: sli:conversion_processing_time
        expr: |
          histogram_quantile(0.95,
            sum(rate(conversion_processing_time_seconds_bucket{job="legacybridge-backend"}[5m])) by (le, format)
          )

      - record: sli:active_users
        expr: |
          sum(increase(user_activity_total{job="legacybridge-backend"}[5m])) by (activity_type)