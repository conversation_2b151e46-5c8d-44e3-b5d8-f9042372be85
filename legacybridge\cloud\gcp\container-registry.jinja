{#
Copyright 2024 LegacyBridge
Container Registry Template for Google Cloud Deployment Manager
#}

resources:
# Enable Container Registry API
- name: enable-container-registry-api
  type: servicemanagement.v1.serviceusage.service
  properties:
    serviceName: containerregistry.googleapis.com

# Container Registry Storage Bucket (GCR uses GCS)
- name: {{ env["name"] }}-gcr-bucket
  type: storage.v1.bucket
  properties:
    name: {{ properties["location"] }}.artifacts.{{ env["project"] }}.appspot.com
    location: {{ properties["location"] }}
    storageClass: STANDARD
    lifecycle:
      rule:
      - action:
          type: Delete
        condition:
          age: 90
          isLive: false
    versioning:
      enabled: true
    labels:
      environment: production
      application: legacybridge
  metadata:
    dependsOn:
    - enable-container-registry-api

outputs:
- name: registryUrl
  value: gcr.io/{{ env["project"] }}