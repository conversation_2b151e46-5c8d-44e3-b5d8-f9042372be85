# Auth Service Auto-Scaling Configuration
# Horizontal and Vertical Pod Autoscalers for the Authentication Service

apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: auth-service-hpa
  namespace: legacybridge
  labels:
    app: auth-service
    component: autoscaling
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: auth-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  # CPU-based scaling
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60
  # Memory-based scaling
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70
  # Custom metrics for auth service
  - type: Pods
    pods:
      metric:
        name: auth_requests_per_second
      target:
        type: AverageValue
        averageValue: "50"
  - type: Pods
    pods:
      metric:
        name: jwt_validation_queue_length
      target:
        type: AverageValue
        averageValue: "10"
  # Database connection pool utilization
  - type: Pods
    pods:
      metric:
        name: database_connection_pool_utilization
      target:
        type: AverageValue
        averageValue: "0.8"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      - type: Pods
        value: 1
        periodSeconds: 60
      selectPolicy: Min

---
# Vertical Pod Autoscaler for Auth Service
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: auth-service-vpa
  namespace: legacybridge
  labels:
    app: auth-service
    component: autoscaling
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: auth-service
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: auth-service
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 2000m
        memory: 4Gi
      controlledResources: ["cpu", "memory"]
      controlledValues: RequestsAndLimits

---
# Pod Disruption Budget for Auth Service
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: auth-service-pdb
  namespace: legacybridge
  labels:
    app: auth-service
    component: availability
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: auth-service

---
# ServiceMonitor for Auth Service Metrics
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: auth-service-metrics
  namespace: legacybridge
  labels:
    app: auth-service
    component: monitoring
spec:
  selector:
    matchLabels:
      app: auth-service
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    honorLabels: true
  - port: http
    interval: 30s
    path: /metrics
    honorLabels: true

---
# Custom Metrics Configuration for Auth Service
apiVersion: v1
kind: ConfigMap
metadata:
  name: auth-service-metrics-config
  namespace: legacybridge
  labels:
    app: auth-service
    component: metrics
data:
  metrics.yaml: |
    metrics:
      auth_requests_per_second:
        description: "Number of authentication requests per second"
        type: "gauge"
        labels: ["method", "status"]
      jwt_validation_queue_length:
        description: "Number of JWT tokens waiting for validation"
        type: "gauge"
      database_connection_pool_utilization:
        description: "Database connection pool utilization ratio"
        type: "gauge"
        labels: ["pool_name"]
      auth_success_rate:
        description: "Authentication success rate"
        type: "gauge"
        labels: ["method"]
      session_count:
        description: "Number of active sessions"
        type: "gauge"
      password_hash_duration:
        description: "Time taken to hash passwords"
        type: "histogram"
        buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0]

---
# Prometheus Rules for Auth Service
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: auth-service-alerts
  namespace: legacybridge
  labels:
    app: auth-service
    component: alerting
spec:
  groups:
  - name: auth-service.rules
    rules:
    # High request rate alert
    - alert: AuthServiceHighRequestRate
      expr: rate(auth_requests_per_second[5m]) > 100
      for: 2m
      labels:
        severity: warning
        service: auth-service
      annotations:
        summary: "Auth service experiencing high request rate"
        description: "Auth service has {{ $value }} requests per second, which is above the threshold"
    
    # High error rate alert
    - alert: AuthServiceHighErrorRate
      expr: rate(auth_requests_total{status=~"4..|5.."}[5m]) / rate(auth_requests_total[5m]) > 0.1
      for: 5m
      labels:
        severity: critical
        service: auth-service
      annotations:
        summary: "Auth service high error rate"
        description: "Auth service error rate is {{ $value | humanizePercentage }}"
    
    # Database connection pool exhaustion
    - alert: AuthServiceDatabasePoolExhaustion
      expr: database_connection_pool_utilization > 0.9
      for: 2m
      labels:
        severity: warning
        service: auth-service
      annotations:
        summary: "Auth service database pool near exhaustion"
        description: "Database connection pool utilization is {{ $value | humanizePercentage }}"
    
    # JWT validation queue backup
    - alert: AuthServiceJWTQueueBackup
      expr: jwt_validation_queue_length > 50
      for: 1m
      labels:
        severity: warning
        service: auth-service
      annotations:
        summary: "Auth service JWT validation queue backup"
        description: "JWT validation queue has {{ $value }} pending tokens"
    
    # Service down alert
    - alert: AuthServiceDown
      expr: up{job="auth-service"} == 0
      for: 1m
      labels:
        severity: critical
        service: auth-service
      annotations:
        summary: "Auth service is down"
        description: "Auth service has been down for more than 1 minute"

---
# Network Policy for Auth Service
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: auth-service-network-policy
  namespace: legacybridge
  labels:
    app: auth-service
    component: security
spec:
  podSelector:
    matchLabels:
      app: auth-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from Kong Gateway
  - from:
    - podSelector:
        matchLabels:
          app: kong-gateway
    ports:
    - protocol: TCP
      port: 3001
  # Allow traffic from other microservices
  - from:
    - podSelector:
        matchLabels:
          app: conversion-service
    - podSelector:
        matchLabels:
          app: file-service
    - podSelector:
        matchLabels:
          app: job-service
    ports:
    - protocol: TCP
      port: 3001
  # Allow monitoring traffic
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
  egress:
  # Allow traffic to PostgreSQL
  - to:
    - podSelector:
        matchLabels:
          app: postgresql
    ports:
    - protocol: TCP
      port: 5432
  # Allow traffic to Redis
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
