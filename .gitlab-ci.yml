variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  REGISTRY: $CI_REGISTRY
  IMAGE_NAME: $CI_PROJECT_PATH
  NODE_VERSION: "18"
  RUST_VERSION: "1.75"
  POSTGRES_DB: legacybridge_test
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  POSTGRES_HOST_AUTH_METHOD: trust

stages:
  - security
  - quality
  - test
  - build
  - deploy-staging
  - deploy-production
  - rollback

# Security Scanning
security:trivy:
  stage: security
  image: aquasec/trivy:latest
  script:
    - trivy fs --severity HIGH,CRITICAL --format json --output trivy-results.json .
    - trivy fs --severity HIGH,CRITICAL --format table .
  artifacts:
    reports:
      container_scanning: trivy-results.json
    expire_in: 1 week
  allow_failure: false

security:snyk:
  stage: security
  image: snyk/snyk:node
  before_script:
    - npm ci
  script:
    - snyk test --severity-threshold=high
    - snyk monitor
  only:
    - main
    - merge_requests

security:dependency-check:
  stage: security
  image: owasp/dependency-check:latest
  script:
    - /usr/share/dependency-check/bin/dependency-check.sh 
      --project "LegacyBridge" 
      --scan . 
      --format HTML 
      --out reports
      --enableRetired
      --enableExperimental
  artifacts:
    paths:
      - reports/
    expire_in: 1 week

# Code Quality
quality:frontend:
  stage: quality
  image: node:${NODE_VERSION}
  cache:
    paths:
      - node_modules/
  before_script:
    - npm ci
  script:
    - npm run lint
    - npm run typecheck
    - npm run format:check
  artifacts:
    reports:
      codequality: gl-code-quality-report.json

quality:backend:
  stage: quality
  image: rust:${RUST_VERSION}
  cache:
    paths:
      - target/
      - cargo/
  before_script:
    - rustup component add rustfmt clippy
  script:
    - cd src-tauri
    - cargo fmt -- --check
    - cargo clippy -- -D warnings
  artifacts:
    reports:
      codequality: gl-code-quality-report.json

# Testing
test:frontend-unit:
  stage: test
  image: node:${NODE_VERSION}
  cache:
    paths:
      - node_modules/
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  before_script:
    - npm ci
  script:
    - npm run test:ci
    - npm run test:coverage
  artifacts:
    paths:
      - coverage/
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
      junit: junit.xml
    expire_in: 1 week

test:backend-unit:
  stage: test
  image: rust:${RUST_VERSION}
  cache:
    paths:
      - target/
      - cargo/
  services:
    - postgres:15
    - redis:7-alpine
  before_script:
    - apt-get update && apt-get install -y pkg-config libssl-dev
    - rustup component add llvm-tools-preview
    - cargo install cargo-tarpaulin
  script:
    - cd src-tauri
    - cargo tarpaulin --out Xml --output-dir ../coverage
    - cargo test --all-features
  artifacts:
    paths:
      - coverage/
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura.xml
    expire_in: 1 week

test:integration:
  stage: test
  image: docker:24
  services:
    - docker:24-dind
  before_script:
    - apk add --no-cache docker-compose
  script:
    - docker-compose -f docker-compose.test.yml up --build --abort-on-container-exit
    - docker-compose -f docker-compose.test.yml down
  artifacts:
    paths:
      - test-results/
    expire_in: 1 week

test:e2e:
  stage: test
  image: mcr.microsoft.com/playwright:v1.40.0-focal
  cache:
    paths:
      - node_modules/
  before_script:
    - npm ci
    - npx playwright install
  script:
    - npm run test:e2e:ci
  artifacts:
    when: always
    paths:
      - playwright-report/
      - test-results/
    expire_in: 1 week

# Build Docker Images
.build:
  stage: build
  image: docker:24
  services:
    - docker:24-dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker buildx create --use
    - docker buildx inspect --bootstrap
  script:
    - |
      docker buildx build \
        --platform linux/amd64,linux/arm64 \
        --cache-from type=registry,ref=$CI_REGISTRY_IMAGE/${COMPONENT}:buildcache \
        --cache-to type=registry,ref=$CI_REGISTRY_IMAGE/${COMPONENT}:buildcache,mode=max \
        --tag $CI_REGISTRY_IMAGE/${COMPONENT}:$CI_COMMIT_SHA \
        --tag $CI_REGISTRY_IMAGE/${COMPONENT}:$CI_COMMIT_REF_SLUG \
        --tag $CI_REGISTRY_IMAGE/${COMPONENT}:latest \
        --build-arg VERSION=$CI_COMMIT_SHA \
        --push \
        -f Dockerfile.${COMPONENT} \
        .
  only:
    - main
    - tags

build:frontend:
  extends: .build
  variables:
    COMPONENT: frontend

build:backend:
  extends: .build
  variables:
    COMPONENT: backend

build:cli:
  extends: .build
  variables:
    COMPONENT: cli

# Deploy to Staging
deploy:staging:
  stage: deploy-staging
  image: bitnami/kubectl:latest
  environment:
    name: staging
    url: https://staging.legacybridge.com
  before_script:
    - echo "$KUBE_CONFIG_STAGING" | base64 -d > ~/.kube/config
  script:
    - |
      # Update deployments
      kubectl set image deployment/legacybridge-backend \
        backend=$CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA \
        -n legacybridge-staging
      
      kubectl set image deployment/legacybridge-frontend \
        frontend=$CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA \
        -n legacybridge-staging
      
      # Wait for rollout
      kubectl rollout status deployment/legacybridge-backend -n legacybridge-staging
      kubectl rollout status deployment/legacybridge-frontend -n legacybridge-staging
  only:
    - main

deploy:staging:smoke-test:
  stage: deploy-staging
  image: node:${NODE_VERSION}
  needs: ["deploy:staging"]
  cache:
    paths:
      - node_modules/
  before_script:
    - npm ci
  script:
    - npm run test:smoke -- --env staging
  only:
    - main

# Deploy to Production
deploy:production:
  stage: deploy-production
  image: bitnami/kubectl:latest
  environment:
    name: production
    url: https://app.legacybridge.com
  before_script:
    - echo "$KUBE_CONFIG_PRODUCTION" | base64 -d > ~/.kube/config
  script:
    - |
      VERSION=${CI_COMMIT_TAG#v}
      
      # Create new deployment
      kubectl apply -f - <<EOF
      apiVersion: apps/v1
      kind: Deployment
      metadata:
        name: legacybridge-backend-${VERSION}
        namespace: legacybridge
      spec:
        replicas: 3
        selector:
          matchLabels:
            app: legacybridge
            component: backend
            version: v${VERSION}
        template:
          metadata:
            labels:
              app: legacybridge
              component: backend
              version: v${VERSION}
          spec:
            containers:
            - name: backend
              image: $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA
              ports:
              - containerPort: 8080
      EOF
      
      # Wait for deployment
      kubectl rollout status deployment/legacybridge-backend-${VERSION} -n legacybridge
      
      # Canary deployment (10% traffic)
      kubectl patch service legacybridge-backend-service -n legacybridge \
        -p '{"spec":{"selector":{"version":"v'${VERSION}'"}},"metadata":{"annotations":{"nginx.ingress.kubernetes.io/canary":"true","nginx.ingress.kubernetes.io/canary-weight":"10"}}}'
      
      # Monitor for 10 minutes
      sleep 600
      
      # Full deployment
      kubectl patch service legacybridge-backend-service -n legacybridge \
        -p '{"spec":{"selector":{"version":"v'${VERSION}'"}},"metadata":{"annotations":{"nginx.ingress.kubernetes.io/canary":"false"}}}'
  only:
    - tags
  when: manual

deploy:production:health-check:
  stage: deploy-production
  image: node:${NODE_VERSION}
  needs: ["deploy:production"]
  cache:
    paths:
      - node_modules/
  before_script:
    - npm ci
  script:
    - npm run test:health-check -- --env production --version ${CI_COMMIT_TAG}
  only:
    - tags

# Rollback
rollback:production:
  stage: rollback
  image: bitnami/kubectl:latest
  environment:
    name: production
    url: https://app.legacybridge.com
  before_script:
    - echo "$KUBE_CONFIG_PRODUCTION" | base64 -d > ~/.kube/config
  script:
    - |
      # Get previous version
      PREVIOUS_VERSION=$(kubectl get deployment -n legacybridge \
        -l app=legacybridge,component=backend \
        -o jsonpath='{.items[1].metadata.labels.version}')
      
      # Rollback services
      kubectl patch service legacybridge-backend-service -n legacybridge \
        -p '{"spec":{"selector":{"version":"'${PREVIOUS_VERSION}'"}}}'
      
      kubectl patch service legacybridge-frontend-service -n legacybridge \
        -p '{"spec":{"selector":{"version":"'${PREVIOUS_VERSION}'"}}}'
      
      echo "Rolled back to version ${PREVIOUS_VERSION}"
  when: manual
  only:
    - tags

# Notifications
.notify:
  image: curlimages/curl:latest
  variables:
    GIT_STRATEGY: none

notify:slack:success:
  extends: .notify
  stage: .post
  script:
    - |
      curl -X POST $SLACK_WEBHOOK \
        -H 'Content-Type: application/json' \
        -d '{
          "text": "✅ Deployment successful!",
          "attachments": [{
            "color": "good",
            "fields": [
              {"title": "Project", "value": "'$CI_PROJECT_NAME'", "short": true},
              {"title": "Branch", "value": "'$CI_COMMIT_REF_NAME'", "short": true},
              {"title": "Commit", "value": "'$CI_COMMIT_SHORT_SHA'", "short": true},
              {"title": "Author", "value": "'$GITLAB_USER_NAME'", "short": true}
            ]
          }]
        }'
  only:
    - main
    - tags
  when: on_success

notify:slack:failure:
  extends: .notify
  stage: .post
  script:
    - |
      curl -X POST $SLACK_WEBHOOK \
        -H 'Content-Type: application/json' \
        -d '{
          "text": "❌ Deployment failed!",
          "attachments": [{
            "color": "danger",
            "fields": [
              {"title": "Project", "value": "'$CI_PROJECT_NAME'", "short": true},
              {"title": "Branch", "value": "'$CI_COMMIT_REF_NAME'", "short": true},
              {"title": "Pipeline", "value": "'$CI_PIPELINE_URL'", "short": false}
            ]
          }]
        }'
  only:
    - main
    - tags
  when: on_failure