# File Management Service Configuration

# Service Configuration
LEG<PERSON><PERSON><PERSON><PERSON><PERSON>_SERVICE_NAME=file-service
LEGACY<PERSON>IDGE_SERVICE_VERSION=0.1.0
LEGACYBRIDGE_SERVICE_PORT=3003
LEGACYBRIDGE_SERVICE_HOST=0.0.0.0
LEGACY<PERSON><PERSON><PERSON>_SERVICE_ENVIRONMENT=development

# Database Configuration
LEGACYBRIDGE_DATABASE_URL=postgresql://postgres:postgres@localhost:5432/legacybridge
LEGACYBRIDGE_DATABASE_MAX_CONNECTIONS=20
LEGACYBRIDGE_DATABASE_MIN_CONNECTIONS=5
LEGACYBRIDGE_DATABASE_ACQUIRE_TIMEOUT_SECONDS=10
LEGACYBRIDGE_DATABASE_IDLE_TIMEOUT_SECONDS=600
LEGACYBRIDGE_DATABASE_MAX_LIFETIME_SECONDS=1800

# Redis Configuration
LEGACYBR<PERSON>GE_REDIS_URL=redis://localhost:6379
LEGACY<PERSON>IDGE_REDIS_MAX_CONNECTIONS=10
LEGACYBR<PERSON>GE_REDIS_CONNECTION_TIMEOUT_SECONDS=5
LEGACYBRIDGE_REDIS_COMMAND_TIMEOUT_SECONDS=5

# AWS S3 Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin
AWS_ENDPOINT_URL=http://localhost:9000
S3_BUCKET_NAME=legacybridge-files

# File Storage Configuration
MAX_FILE_SIZE_MB=100
ALLOWED_FILE_TYPES=*
FILE_RETENTION_DAYS=365
ENABLE_FILE_VERSIONING=false
ENABLE_FILE_COMPRESSION=false

# Logging Configuration
LEGACYBRIDGE_LOGGING_LEVEL=info
LEGACYBRIDGE_LOGGING_FORMAT=json

# Metrics Configuration
LEGACYBRIDGE_METRICS_ENABLED=true
LEGACYBRIDGE_METRICS_PORT=9093
LEGACYBRIDGE_METRICS_PATH=/metrics

# CORS Configuration
LEGACYBRIDGE_CORS_ALLOWED_ORIGINS=*
LEGACYBRIDGE_CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
LEGACYBRIDGE_CORS_ALLOWED_HEADERS=Content-Type,Authorization
LEGACYBRIDGE_CORS_MAX_AGE_SECONDS=3600

# Rate Limiting Configuration
LEGACYBRIDGE_RATE_LIMITING_ENABLED=true
LEGACYBRIDGE_RATE_LIMITING_REQUESTS_PER_MINUTE=100
LEGACYBRIDGE_RATE_LIMITING_BURST_SIZE=10

# Tracing Configuration
JAEGER_AGENT_ENDPOINT=http://localhost:14268/api/traces
