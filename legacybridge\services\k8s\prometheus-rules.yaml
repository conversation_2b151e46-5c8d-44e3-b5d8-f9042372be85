# Prometheus Alerting Rules for LegacyBridge
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: monitoring
data:
  legacybridge.yml: |
    groups:
    - name: legacybridge.rules
      rules:
      # Service availability alerts
      - alert: ServiceDown
        expr: up{job=~"auth-service|conversion-service|file-service|job-service"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} has been down for more than 1 minute"

      - alert: HighErrorRate
        expr: |
          (
            sum(rate(http_requests_total{status=~"5.."}[5m])) by (service)
            /
            sum(rate(http_requests_total[5m])) by (service)
          ) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate for service {{ $labels.service }}"
          description: "Service {{ $labels.service }} has an error rate of {{ $value | humanizePercentage }}"

      - alert: CriticalErrorRate
        expr: |
          (
            sum(rate(http_requests_total{status=~"5.."}[5m])) by (service)
            /
            sum(rate(http_requests_total[5m])) by (service)
          ) > 0.10
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Critical error rate for service {{ $labels.service }}"
          description: "Service {{ $labels.service }} has a critical error rate of {{ $value | humanizePercentage }}"

      - alert: HighResponseTime
        expr: |
          histogram_quantile(0.95, 
            sum(rate(http_request_duration_seconds_bucket[5m])) by (le, service)
          ) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time for service {{ $labels.service }}"
          description: "95th percentile response time for {{ $labels.service }} is {{ $value }}s"

      # Circuit breaker alerts
      - alert: CircuitBreakerOpen
        expr: circuit_breaker_state == 2
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Circuit breaker open for service {{ $labels.service }}"
          description: "Circuit breaker for service {{ $labels.service }} has been open for more than 1 minute"

      - alert: CircuitBreakerHalfOpen
        expr: circuit_breaker_state == 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Circuit breaker half-open for service {{ $labels.service }}"
          description: "Circuit breaker for service {{ $labels.service }} has been half-open for more than 5 minutes"

      - alert: HighCircuitBreakerFailures
        expr: rate(circuit_breaker_failures_total[5m]) > 1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High circuit breaker failure rate for service {{ $labels.service }}"
          description: "Circuit breaker for service {{ $labels.service }} is failing at {{ $value }} failures per second"

      # Auto-scaling alerts
      - alert: HPAMaxReplicasReached
        expr: kube_hpa_status_current_replicas == kube_hpa_spec_max_replicas
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "HPA {{ $labels.hpa }} has reached maximum replicas"
          description: "HPA {{ $labels.hpa }} has been at maximum replicas ({{ $value }}) for more than 5 minutes"

      - alert: HPAScalingFrequent
        expr: |
          changes(kube_hpa_status_current_replicas[30m]) > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "HPA {{ $labels.hpa }} is scaling frequently"
          description: "HPA {{ $labels.hpa }} has scaled {{ $value }} times in the last 30 minutes"

      # Resource utilization alerts
      - alert: HighCPUUtilization
        expr: |
          (
            rate(container_cpu_usage_seconds_total{pod=~".*-service-.*"}[5m]) * 100
          ) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU utilization for pod {{ $labels.pod }}"
          description: "Pod {{ $labels.pod }} has CPU utilization of {{ $value }}%"

      - alert: HighMemoryUtilization
        expr: |
          (
            container_memory_usage_bytes{pod=~".*-service-.*"}
            /
            container_spec_memory_limit_bytes
          ) > 0.85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory utilization for pod {{ $labels.pod }}"
          description: "Pod {{ $labels.pod }} has memory utilization of {{ $value | humanizePercentage }}"

      # Service-specific alerts
      - alert: AuthServiceHighJWTQueue
        expr: jwt_validation_queue_length > 50
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Auth service JWT validation queue is backing up"
          description: "JWT validation queue has {{ $value }} pending tokens"

      - alert: ConversionServiceHighQueue
        expr: conversion_queue_length > 20
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Conversion service queue is backing up"
          description: "Conversion queue has {{ $value }} pending jobs"

      - alert: ConversionServiceSlowProcessing
        expr: |
          histogram_quantile(0.95, 
            rate(conversion_duration_seconds_bucket[5m])
          ) > 120
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Conversion service slow processing"
          description: "95th percentile conversion time is {{ $value }}s"

      - alert: FileServiceS3Failures
        expr: s3_operation_success_rate < 0.95
        for: 3m
        labels:
          severity: critical
        annotations:
          summary: "File service S3 operation failures"
          description: "S3 operation success rate is {{ $value | humanizePercentage }}"

      - alert: JobServiceHighQueue
        expr: job_queue_length > 50
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Job service queue is backing up"
          description: "Job queue has {{ $value }} pending jobs"

      - alert: JobServiceLowProcessingRate
        expr: rate(job_processing_rate[5m]) < 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Job service low processing rate"
          description: "Job processing rate is {{ $value }} jobs per second"

      # Database alerts
      - alert: DatabaseConnectionPoolExhaustion
        expr: database_connection_pool_utilization > 0.9
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Database connection pool near exhaustion"
          description: "Database connection pool utilization is {{ $value | humanizePercentage }}"

      - alert: DatabaseHighQueryTime
        expr: |
          histogram_quantile(0.95, 
            rate(database_query_duration_seconds_bucket[5m])
          ) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Database high query time"
          description: "95th percentile database query time is {{ $value }}s"

      # Redis alerts
      - alert: RedisConnectionFailures
        expr: rate(redis_connection_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Redis connection failures"
          description: "Redis is experiencing {{ $value }} connection failures per second"

      - alert: RedisHighMemoryUsage
        expr: redis_memory_usage_bytes / redis_memory_max_bytes > 0.85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is {{ $value | humanizePercentage }}"

      # Kong Gateway alerts
      - alert: KongGatewayHighLatency
        expr: |
          histogram_quantile(0.95, 
            rate(kong_latency_bucket[5m])
          ) > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Kong Gateway high latency"
          description: "95th percentile Kong latency is {{ $value }}ms"

      - alert: KongGatewayHighErrorRate
        expr: |
          (
            sum(rate(kong_http_status{code=~"5.."}[5m]))
            /
            sum(rate(kong_http_status[5m]))
          ) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Kong Gateway high error rate"
          description: "Kong Gateway error rate is {{ $value | humanizePercentage }}"

      # Kubernetes cluster alerts
      - alert: PodCrashLooping
        expr: |
          rate(kube_pod_container_status_restarts_total[15m]) * 60 * 15 > 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Pod {{ $labels.pod }} is crash looping"
          description: "Pod {{ $labels.pod }} has restarted {{ $value }} times in the last 15 minutes"

      - alert: PodNotReady
        expr: |
          kube_pod_status_ready{condition="false"} == 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Pod {{ $labels.pod }} is not ready"
          description: "Pod {{ $labels.pod }} has been not ready for more than 5 minutes"

      - alert: NodeNotReady
        expr: kube_node_status_condition{condition="Ready",status="true"} == 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Node {{ $labels.node }} is not ready"
          description: "Node {{ $labels.node }} has been not ready for more than 5 minutes"
