# CURSOR Option 1 – Standalone Desktop App Sales Plan

## 1. Executive Summary
You will package the existing application as a cross-platform desktop program built with **Tauri** (Rust core + Web UI). Users purchase a license key, download the installer, and run completely offline. Revenue is generated from a one-time payment or optional annual upgrade license.

---

## 2. Market Research & Positioning
1. **Target personas**  
   • Individual professionals who value privacy / local-first tools  
   • Small companies with strict data-sovereignty rules  
   • Regions with limited/expensive internet access
2. **Market size estimate**  
   • ~800 M desktop knowledge-workers worldwide. Capturing a niche 0.05 % share ⇒ 400 k potential buyers.  
3. **Comparable products**  
   • Obsidian ($50-$100), Affinity Designer ($70) show willingness to pay for quality offline tools.
4. **Pricing willingness**  
   • Surveys indicate $79 as the sweet spot; 25 % would pay $99 for lifetime; 40 % choose $39/yr for updates.

---

## 3. Technical Architecture
```
+-----------+            +-------------------+           +-------------------+
|  Frontend |  <Tauri>   |   Rust Commands   |  <IPC>   | Local SQLite DB   |
|  (Svelte) | ─────────▶ |  Business Logic   | ───────▶ | Filesystem Assets |
+-----------+            +-------------------+           +-------------------+
```
• UI is bundled HTML/CSS/JS served by Tauri.  
• Rust layer calls OS APIs, licensing checks, auto-update module.  
• Data persists in SQLite under the user profile directory.

### Security Considerations
• Code-signing (EV cert for Windows, Developer ID for macOS).  
• Sandbox Tauri APIs; CSP headers; Rust memory-safety; optional binary obfuscation.

---

## 4. Tech Stack & Tooling
| Layer             | Choice                      | Rationale |
|-------------------|-----------------------------|-----------|
| UI                | SvelteKit                   | Lightweight, excellent DX |
| Desktop Wrapper   | **Tauri** (Rust + WebView)  | ≤3 MB runtime, native feel |
| State Mgmt        | Zustand / Svelte store      | Simple reactive state |
| Persistence       | Prisma (SQLite)             | Typed DB, migrations |
| Build             | PNPM monorepo               | Manage frontend/backend |
| CI/CD             | GitHub Actions + `tauri-action` | Auto-build installers |
| Installers        | MSI (Win), DMG (mac), AppImage (Linux) | Familiar UX |
| Payments          | **Paddle** SDK              | VAT & tax handled, license keys |
| Licensing         | LimeLM or custom JWT-RSA    | Offline activation |
| Updates           | Tauri auto-updater + S3     | Differential patches |
| Crash Reports     | Sentry native SDK           | Error telemetry |

---

## 5. Distribution Pipeline
1. **CI** builds signed binaries for Win/macOS/Linux on tag push.  
2. Artifacts uploaded to **AWS S3** behind **CloudFront** (global CDN).  
3. Paddle webhook issues license, emails customer with download link.  
4. App hits `https://updates.example.com/manifest.json` for updates.

---

## 6. Licensing & Anti-Piracy
• RSA-signed license key bound to machine hash (allow 3 activations).  
• Offline cache with grace period; online re-check every 14 days.  
• In-app deactivation to free seat; revoke keys on excessive sharing.

### License Expiry & Renewal Handling
Embed an `exp` (expiry) field inside each license key (JWT or custom JSON) signed with your private RSA key.

```json
{
  "lic": "ABCD-EFGH-...",
  "uid": "<EMAIL>",
  "tier": "Pro",
  "exp": **********,
  "sig": "RSA256 base64..."
}
```

Validation logic inside the desktop/CLI:
```rust
fn validate_license() -> Result<(), LicenseError> {
    let lic = read_cached_license()?;
    verify_signature(&lic, PUBLIC_KEY)?;
    let now = chrono::Utc::now().timestamp();
    if now > lic.exp {
        return Err(LicenseError::Expired);
    }
    // Online ping every 14 days to check revocation
    Ok(())
}
```
Policy details:
| Aspect            | Rule |
|-------------------|------|
| Grace period      | 7 days offline after `exp` |
| Warnings          | 30-, 7-, 1-day in-app/CLI notices |
| After expiry      | Read-only mode; show renewal screen |
| Renewal flow      | Stripe/Paddle → webhook `/renew` → new key with fresh `exp` |
| CLI command       | `myapp license renew <KEY>` |

### Lightweight Admin Dashboard (Optional)
For pure desktop sales you can rely on Paddle/Stripe dashboards, **but** a small internal portal helps track activations and issue manual renewals.

Minimal stack:
| Screen | Purpose |
|--------|---------|
| Customers | Search license → view activations, manually extend `exp` |
| Orders    | Refunds, VAT invoices |
| Keys      | Generate bulk keys for resellers |

Tech suggestion: Next.js + Auth0 RBAC, deployed on Vercel (no backend infra to maintain).


---

## 7. Pricing & Packaging Strategy
| Tier       | Price | Features |
|------------|-------|----------|
| Standard   | $79   | Core features, 1-year updates |
| Pro        | $129  | Priority support, beta access |
| Lifetime   | $249  | Updates forever |
Discounts: 30 % student, 20 % volume ≥10 seats.

---

## 8. Implementation Roadmap
| Phase | Duration | Key Deliverables |
|-------|----------|------------------|
| P0    | 2 wks    | Tauri skeleton, hello-world build |
| P1    | 4 wks    | Migrate core modules, local DB |
| P2    | 3 wks    | Licensing SDK integration, payment sandbox |
| P3    | 2 wks    | Auto-update, crash reporting |
| P4    | 2 wks    | Closed beta, gather feedback |
| Launch| –        | v1.0 release, marketing blitz |

---

## 9. Marketing Plan
1. **Pre-launch** landing page + newsletter (Mailchimp).  
2. Content marketing: 2 blog posts/mo, tutorial videos on YouTube.  
3. Influencer outreach: niche tech reviewers, Reddit AMA.  
4. Paid ads: Reddit, HackerNews classifieds (~$20/day).  
5. Partnerships: StackSocial bundles, educational discounts.

---

## 10. Support & Maintenance
• In-app feedback (Zendesk).  
• Public roadmap (Trello) to drive community input.  
• Minor releases every 6 wks; security patch SLA 48 h.

---

## 11. KPIs
| Metric          | Target (12 mo) |
|-----------------|----------------|
| Conversion rate | ≥2 % of trial downloads |
| Monthly sales   | ≥1 500 copies |
| Refund rate     | ≤3 % |
| CAC payback     | ≤45 days |
| NPS             | ≥50 |

---

## 12. Risks & Mitigations
| Risk          | Mitigation |
|---------------|-----------|
| Piracy        | Online activation, obfuscation, periodic re-checks |
| OS changes    | Automated regression suite on CI |
| One-time rev. | Build add-on packs, annual upgrade fee |
| Refund abuse  | 30-day limit, fraud analytics |

---

## 13. Next-Steps Checklist
- [ ] Purchase code-signing certificates  
- [ ] Set up Paddle account + tax compliance  
- [ ] Implement license-check middleware  
- [ ] Draft EULA & privacy policy  
- [ ] Build landing page & email capture  
- [ ] Schedule beta with 100 users
