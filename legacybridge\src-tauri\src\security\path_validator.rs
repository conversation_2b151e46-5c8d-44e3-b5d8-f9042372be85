// CVE-LEVEL-004: Path Traversal Prevention
//
// This module implements comprehensive path validation to prevent directory traversal attacks
// and unauthorized file system access.

use std::path::{Path, PathBuf};
use std::fs;
use serde::{Deserialize, Serialize};

/// Security error types for path validation
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum SecurityError {
    InvalidPath {
        path: String,
        reason: String,
    },
    BlockedPath {
        path: String,
        blocked_pattern: String,
    },
    UnauthorizedPath {
        path: String,
        allowed_directories: Vec<PathBuf>,
    },
}

impl std::fmt::Display for SecurityError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SecurityError::InvalidPath { path, reason } => {
                write!(f, "Invalid path '{}': {}", path, reason)
            },
            SecurityError::BlockedPath { path, blocked_pattern } => {
                write!(f, "Blocked path '{}' contains pattern '{}'", path, blocked_pattern)
            },
            SecurityError::UnauthorizedPath { path, allowed_directories } => {
                write!(f, "Unauthorized path '{}', allowed directories: {:?}", path, allowed_directories)
            },
        }
    }
}

impl std::error::Error for SecurityError {}

/// Path validator for preventing directory traversal attacks
pub struct PathValidator {
    allowed_directories: Vec<PathBuf>,
    blocked_patterns: Vec<String>,
}

impl PathValidator {
    pub fn new() -> Self {
        Self {
            allowed_directories: vec![
                PathBuf::from("/tmp/legacybridge/uploads"),
                PathBuf::from("/tmp/legacybridge/output"),
                // Windows paths
                PathBuf::from("C:\\temp\\legacybridge\\uploads"),
                PathBuf::from("C:\\temp\\legacybridge\\output"),
                // Relative paths from current directory
                PathBuf::from("uploads"),
                PathBuf::from("output"),
                PathBuf::from("temp"),
            ],
            blocked_patterns: vec![
                "..".to_string(),
                "~".to_string(),
                "/etc/".to_string(),
                "/root/".to_string(),
                "/home/".to_string(),
                "C:\\Windows\\".to_string(),
                "C:\\Users\\<USER>\\Program Files\\".to_string(),
                "/usr/".to_string(),
                "/var/".to_string(),
                "/sys/".to_string(),
                "/proc/".to_string(),
                "/dev/".to_string(),
                "/boot/".to_string(),
            ],
        }
    }
    
    /// Add an allowed directory
    pub fn add_allowed_directory<P: AsRef<Path>>(&mut self, path: P) {
        self.allowed_directories.push(path.as_ref().to_path_buf());
    }
    
    /// Add a blocked pattern
    pub fn add_blocked_pattern(&mut self, pattern: String) {
        self.blocked_patterns.push(pattern);
    }
    
    /// Validate a path for security compliance
    pub fn validate_path(&self, path: &str) -> Result<PathBuf, SecurityError> {
        // Normalize the path
        let path_buf = PathBuf::from(path);
        
        // Try to canonicalize if the path exists, otherwise just normalize
        let normalized = if path_buf.exists() {
            path_buf.canonicalize()
                .map_err(|e| SecurityError::InvalidPath {
                    path: path.to_string(),
                    reason: format!("Cannot resolve path: {}", e),
                })?
        } else {
            // For non-existent paths, do basic normalization
            self.normalize_path(&path_buf)?
        };
        
        // Check for blocked patterns
        let path_str = normalized.to_string_lossy();
        for pattern in &self.blocked_patterns {
            if path_str.contains(pattern) {
                return Err(SecurityError::BlockedPath {
                    path: path.to_string(),
                    blocked_pattern: pattern.clone(),
                });
            }
        }
        
        // Check if path is within allowed directories
        for allowed_dir in &self.allowed_directories {
            if let Ok(canonical_allowed) = allowed_dir.canonicalize() {
                if normalized.starts_with(&canonical_allowed) {
                    return Ok(normalized);
                }
            } else {
                // For non-existent allowed directories, do prefix matching
                if normalized.starts_with(allowed_dir) {
                    return Ok(normalized);
                }
            }
        }
        
        Err(SecurityError::UnauthorizedPath {
            path: path.to_string(),
            allowed_directories: self.allowed_directories.clone(),
        })
    }
    
    /// Normalize a path without requiring it to exist
    fn normalize_path(&self, path: &Path) -> Result<PathBuf, SecurityError> {
        let mut normalized = PathBuf::new();
        
        for component in path.components() {
            match component {
                std::path::Component::Prefix(prefix) => {
                    normalized.push(prefix.as_os_str());
                },
                std::path::Component::RootDir => {
                    normalized.push("/");
                },
                std::path::Component::CurDir => {
                    // Skip current directory references
                },
                std::path::Component::ParentDir => {
                    // Check for directory traversal attempts
                    if !normalized.pop() {
                        return Err(SecurityError::InvalidPath {
                            path: path.to_string_lossy().to_string(),
                            reason: "Directory traversal attempt detected".to_string(),
                        });
                    }
                },
                std::path::Component::Normal(name) => {
                    normalized.push(name);
                },
            }
        }
        
        Ok(normalized)
    }
}

impl Default for PathValidator {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_path_traversal_prevention() {
        let validator = PathValidator::new();
        
        let attack_paths = vec![
            "../../../etc/passwd",
            "..\\..\\Windows\\System32\\config\\sam",
            "/etc/shadow",
            "C:\\Windows\\System32\\config\\sam",
            "uploads/../../../etc/passwd",
        ];
        
        for path in attack_paths {
            assert!(validator.validate_path(path).is_err(), "Path should be blocked: {}", path);
        }
    }
    
    #[test]
    fn test_allowed_paths() {
        let validator = PathValidator::new();
        
        let allowed_paths = vec![
            "uploads/test.rtf",
            "output/result.md",
            "temp/working.txt",
        ];
        
        for path in allowed_paths {
            // Note: These may fail if directories don't exist, but should not be blocked for security reasons
            let result = validator.validate_path(path);
            if result.is_err() {
                // Should be UnauthorizedPath, not BlockedPath or InvalidPath due to traversal
                match result.unwrap_err() {
                    SecurityError::UnauthorizedPath { .. } => {}, // Expected for non-existent dirs
                    other => panic!("Unexpected error for path {}: {:?}", path, other),
                }
            }
        }
    }
}
