// Enterprise-grade horizontal scaling tests
// Tests stateless design, session externalization, load balancing, and caching strategies

use legacybridge_shared::{
    cache::{CacheManager, SessionManager, RateLimiter},
    service_client::ServiceClient,
    types::User,
    ServiceResult,
};
use serde_json::json;
use std::time::Duration;
use uuid::Uuid;
use tokio::time::timeout;

// Test configuration for enterprise horizontal scaling
const REDIS_URL: &str = "redis://localhost:6379";
const SESSION_TTL: Duration = Duration::from_secs(3600);
const CACHE_TTL: Duration = Duration::from_secs(300);
const ENTERPRISE_TIMEOUT: Duration = Duration::from_secs(30);
const CONCURRENT_SESSIONS: usize = 100;
const LOAD_TEST_REQUESTS: usize = 1000;

#[tokio::test]
async fn test_stateless_session_management() {
    let session_manager = SessionManager::new(REDIS_URL, SESSION_TTL)
        .expect("Failed to create session manager");
    
    // Test session creation
    let user_session = json!({
        "user_id": "123e4567-e89b-12d3-a456-************",
        "username": "test_user",
        "roles": ["user", "admin"],
        "ip_address": "*************",
        "created_at": chrono::Utc::now().to_rfc3339(),
        "last_accessed": chrono::Utc::now().to_rfc3339()
    });
    
    let session_id = Uuid::new_v4().to_string();
    
    session_manager.create_session(&session_id, &user_session).await
        .expect("Session creation should succeed");
    
    // Test session retrieval
    let retrieved_session: Option<serde_json::Value> = session_manager.get_session(&session_id).await
        .expect("Session retrieval should succeed");
    
    assert!(retrieved_session.is_some(), "Session should exist");
    let session_data = retrieved_session.unwrap();
    assert_eq!(session_data["username"], "test_user");
    assert_eq!(session_data["roles"], json!(["user", "admin"]));
    
    // Test session update
    let updated_session = json!({
        "user_id": "123e4567-e89b-12d3-a456-************",
        "username": "test_user",
        "roles": ["user", "admin", "premium"],
        "ip_address": "*************",
        "created_at": session_data["created_at"],
        "last_accessed": chrono::Utc::now().to_rfc3339()
    });
    
    session_manager.update_session(&session_id, &updated_session).await
        .expect("Session update should succeed");
    
    let updated_retrieved: Option<serde_json::Value> = session_manager.get_session(&session_id).await
        .expect("Updated session retrieval should succeed");
    
    assert!(updated_retrieved.is_some());
    assert_eq!(updated_retrieved.unwrap()["roles"], json!(["user", "admin", "premium"]));
    
    // Test session deletion
    session_manager.delete_session(&session_id).await
        .expect("Session deletion should succeed");
    
    let deleted_session: Option<serde_json::Value> = session_manager.get_session(&session_id).await
        .expect("Deleted session check should succeed");
    
    assert!(deleted_session.is_none(), "Session should be deleted");
}

#[tokio::test]
async fn test_concurrent_session_operations() {
    let session_manager = SessionManager::new(REDIS_URL, SESSION_TTL)
        .expect("Failed to create session manager");
    
    // Create multiple concurrent sessions
    let mut handles = Vec::new();
    
    for i in 0..CONCURRENT_SESSIONS {
        let manager = session_manager.clone();
        let handle = tokio::spawn(async move {
            let session_id = format!("concurrent_session_{}", i);
            let user_session = json!({
                "user_id": Uuid::new_v4().to_string(),
                "username": format!("user_{}", i),
                "roles": ["user"],
                "ip_address": "*************",
                "created_at": chrono::Utc::now().to_rfc3339()
            });
            
            // Create session
            manager.create_session(&session_id, &user_session).await?;
            
            // Retrieve session
            let retrieved: Option<serde_json::Value> = manager.get_session(&session_id).await?;
            assert!(retrieved.is_some());
            
            // Update session
            let updated_session = json!({
                "user_id": user_session["user_id"],
                "username": user_session["username"],
                "roles": ["user", "verified"],
                "ip_address": "*************",
                "last_accessed": chrono::Utc::now().to_rfc3339()
            });
            
            manager.update_session(&session_id, &updated_session).await?;
            
            // Verify update
            let final_session: Option<serde_json::Value> = manager.get_session(&session_id).await?;
            assert!(final_session.is_some());
            assert_eq!(final_session.unwrap()["roles"], json!(["user", "verified"]));
            
            Ok::<(), Box<dyn std::error::Error + Send + Sync>>(())
        });
        handles.push(handle);
    }
    
    // Wait for all operations to complete
    let results = futures::future::join_all(handles).await;
    
    // Verify all operations succeeded
    let success_count = results.iter()
        .filter(|r| r.is_ok() && r.as_ref().unwrap().is_ok())
        .count();
    
    assert_eq!(success_count, CONCURRENT_SESSIONS, 
              "All concurrent session operations should succeed");
}

#[tokio::test]
async fn test_distributed_caching_strategy() {
    let cache_manager = CacheManager::new(REDIS_URL, CACHE_TTL)
        .expect("Failed to create cache manager");
    
    // Test basic cache operations
    let cache_key = "test_cache_key";
    let cache_value = json!({
        "data": "test_data",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "metadata": {
            "version": "1.0",
            "source": "test"
        }
    });
    
    // Set cache value
    cache_manager.set(cache_key, &cache_value).await
        .expect("Cache set should succeed");
    
    // Get cache value
    let retrieved_value: Option<serde_json::Value> = cache_manager.get(cache_key).await
        .expect("Cache get should succeed");
    
    assert!(retrieved_value.is_some());
    assert_eq!(retrieved_value.unwrap()["data"], "test_data");
    
    // Test cache expiration
    cache_manager.set_with_ttl(cache_key, &cache_value, Duration::from_secs(1)).await
        .expect("Cache set with TTL should succeed");
    
    // Wait for expiration
    tokio::time::sleep(Duration::from_secs(2)).await;
    
    let expired_value: Option<serde_json::Value> = cache_manager.get(cache_key).await
        .expect("Expired cache get should succeed");
    
    assert!(expired_value.is_none(), "Cache value should be expired");
    
    // Test cache invalidation
    cache_manager.set(cache_key, &cache_value).await
        .expect("Cache set should succeed");
    
    cache_manager.delete(cache_key).await
        .expect("Cache delete should succeed");
    
    let deleted_value: Option<serde_json::Value> = cache_manager.get(cache_key).await
        .expect("Deleted cache get should succeed");
    
    assert!(deleted_value.is_none(), "Cache value should be deleted");
}

#[tokio::test]
async fn test_load_balancing_session_affinity() {
    let session_manager = SessionManager::new(REDIS_URL, SESSION_TTL)
        .expect("Failed to create session manager");
    
    // Simulate multiple service instances accessing the same session
    let session_id = Uuid::new_v4().to_string();
    let user_session = json!({
        "user_id": Uuid::new_v4().to_string(),
        "username": "load_balance_user",
        "roles": ["user"],
        "ip_address": "*************",
        "instance_id": "instance_1"
    });
    
    // Instance 1 creates session
    session_manager.create_session(&session_id, &user_session).await
        .expect("Session creation should succeed");
    
    // Instance 2 accesses same session
    let retrieved_session: Option<serde_json::Value> = session_manager.get_session(&session_id).await
        .expect("Session retrieval should succeed");
    
    assert!(retrieved_session.is_some());
    assert_eq!(retrieved_session.unwrap()["username"], "load_balance_user");
    
    // Instance 3 updates session
    let updated_session = json!({
        "user_id": user_session["user_id"],
        "username": "load_balance_user",
        "roles": ["user", "verified"],
        "ip_address": "*************",
        "instance_id": "instance_3",
        "last_updated_by": "instance_3"
    });
    
    session_manager.update_session(&session_id, &updated_session).await
        .expect("Session update should succeed");
    
    // Instance 4 verifies update
    let final_session: Option<serde_json::Value> = session_manager.get_session(&session_id).await
        .expect("Final session retrieval should succeed");
    
    assert!(final_session.is_some());
    let session_data = final_session.unwrap();
    assert_eq!(session_data["roles"], json!(["user", "verified"]));
    assert_eq!(session_data["last_updated_by"], "instance_3");
}

#[tokio::test]
async fn test_rate_limiting_across_instances() {
    let rate_limiter = RateLimiter::new(REDIS_URL)
        .expect("Failed to create rate limiter");
    
    let user_id = "rate_limit_test_user";
    let limit = 5;
    let window = Duration::from_secs(60);
    
    // Test rate limiting across multiple "instances"
    let mut allowed_count = 0;
    let mut denied_count = 0;
    
    for i in 0..10 {
        let is_allowed = rate_limiter.check_rate_limit(user_id, limit, window).await
            .expect("Rate limit check should succeed");
        
        if is_allowed {
            allowed_count += 1;
        } else {
            denied_count += 1;
        }
        
        // Small delay between requests
        tokio::time::sleep(Duration::from_millis(10)).await;
    }
    
    assert_eq!(allowed_count, limit as usize, "Should allow exactly {} requests", limit);
    assert_eq!(denied_count, 5, "Should deny remaining requests");
    
    // Test rate limit reset
    let remaining = rate_limiter.get_remaining_requests(user_id, limit, window).await
        .expect("Get remaining requests should succeed");
    
    assert_eq!(remaining, 0, "Should have no remaining requests");
}

#[tokio::test]
async fn test_cache_performance_under_load() {
    let cache_manager = CacheManager::new(REDIS_URL, CACHE_TTL)
        .expect("Failed to create cache manager");
    
    let start_time = std::time::Instant::now();
    let mut handles = Vec::new();
    
    // Concurrent cache operations
    for i in 0..LOAD_TEST_REQUESTS {
        let manager = cache_manager.clone();
        let handle = tokio::spawn(async move {
            let key = format!("load_test_key_{}", i % 100); // 100 unique keys
            let value = json!({
                "request_id": i,
                "data": format!("test_data_{}", i),
                "timestamp": chrono::Utc::now().to_rfc3339()
            });
            
            // Set value
            manager.set(&key, &value).await?;
            
            // Get value
            let retrieved: Option<serde_json::Value> = manager.get(&key).await?;
            assert!(retrieved.is_some());
            
            Ok::<(), Box<dyn std::error::Error + Send + Sync>>(())
        });
        handles.push(handle);
    }
    
    // Wait for all operations
    let results = futures::future::join_all(handles).await;
    let duration = start_time.elapsed();
    
    // Count successful operations
    let success_count = results.iter()
        .filter(|r| r.is_ok() && r.as_ref().unwrap().is_ok())
        .count();
    
    // Enterprise performance requirements
    assert!(success_count >= LOAD_TEST_REQUESTS * 95 / 100, 
           "Should have at least 95% success rate under load");
    
    assert!(duration < Duration::from_secs(30), 
           "Load test should complete within 30 seconds");
    
    let ops_per_second = (success_count * 2) as f64 / duration.as_secs_f64(); // 2 ops per iteration
    println!("Cache performance: {:.2} operations/second", ops_per_second);
    
    // Should handle at least 100 ops/sec for enterprise requirements
    assert!(ops_per_second >= 100.0, 
           "Should handle at least 100 operations/second, got {:.2}", ops_per_second);
}

#[tokio::test]
async fn test_session_failover_and_recovery() {
    let session_manager = SessionManager::new(REDIS_URL, SESSION_TTL)
        .expect("Failed to create session manager");
    
    let session_id = Uuid::new_v4().to_string();
    let user_session = json!({
        "user_id": Uuid::new_v4().to_string(),
        "username": "failover_test_user",
        "roles": ["user"],
        "ip_address": "*************",
        "created_at": chrono::Utc::now().to_rfc3339()
    });
    
    // Create session
    session_manager.create_session(&session_id, &user_session).await
        .expect("Session creation should succeed");
    
    // Verify session exists
    assert!(session_manager.session_exists(&session_id).await
           .expect("Session existence check should succeed"));
    
    // Simulate service restart by creating new session manager
    let new_session_manager = SessionManager::new(REDIS_URL, SESSION_TTL)
        .expect("Failed to create new session manager");
    
    // Verify session persists across service restart
    let recovered_session: Option<serde_json::Value> = new_session_manager.get_session(&session_id).await
        .expect("Session recovery should succeed");
    
    assert!(recovered_session.is_some(), "Session should survive service restart");
    assert_eq!(recovered_session.unwrap()["username"], "failover_test_user");
}

#[tokio::test]
async fn test_stateless_service_design() {
    // Test that services can be stateless by storing all state externally
    let cache_manager = CacheManager::new(REDIS_URL, CACHE_TTL)
        .expect("Failed to create cache manager");
    
    let session_manager = SessionManager::new(REDIS_URL, SESSION_TTL)
        .expect("Failed to create session manager");
    
    // Simulate a stateless service handling a request
    let request_id = Uuid::new_v4().to_string();
    let user_id = Uuid::new_v4().to_string();
    
    // Store request state in cache
    let request_state = json!({
        "request_id": request_id,
        "user_id": user_id,
        "step": "authentication",
        "data": {
            "username": "stateless_user",
            "timestamp": chrono::Utc::now().to_rfc3339()
        }
    });
    
    let state_key = format!("request_state:{}", request_id);
    cache_manager.set(&state_key, &request_state).await
        .expect("Request state storage should succeed");
    
    // Store user session
    let user_session = json!({
        "user_id": user_id,
        "username": "stateless_user",
        "roles": ["user"],
        "authenticated": true
    });
    
    let session_id = format!("session:{}", user_id);
    session_manager.create_session(&session_id, &user_session).await
        .expect("Session creation should succeed");
    
    // Simulate service instance processing request
    let retrieved_state: Option<serde_json::Value> = cache_manager.get(&state_key).await
        .expect("State retrieval should succeed");
    
    assert!(retrieved_state.is_some());
    let state = retrieved_state.unwrap();
    assert_eq!(state["step"], "authentication");
    
    // Update request state
    let updated_state = json!({
        "request_id": request_id,
        "user_id": user_id,
        "step": "processing",
        "data": state["data"],
        "processed_by": "instance_2"
    });
    
    cache_manager.set(&state_key, &updated_state).await
        .expect("State update should succeed");
    
    // Simulate another service instance completing the request
    let final_state: Option<serde_json::Value> = cache_manager.get(&state_key).await
        .expect("Final state retrieval should succeed");
    
    assert!(final_state.is_some());
    assert_eq!(final_state.unwrap()["step"], "processing");
    
    // Cleanup
    cache_manager.delete(&state_key).await
        .expect("State cleanup should succeed");
    
    session_manager.delete_session(&session_id).await
        .expect("Session cleanup should succeed");
}
