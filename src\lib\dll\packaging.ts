import { useState, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { 
  BuildStatus, 
  TestResult, 
  DeploymentPackage, 
  PackageFormat,
  PackageConfiguration,
  PackageValidation
} from './dll-config';

export interface PackageConfig {
  format: PackageFormat;
  includeDocs: boolean;
  includeExamples: boolean;
  includeSource: boolean;
  createInstaller: boolean;
  packageName: string;
  version: string;
  description: string;
  author: string;
  license: string;
}

export function usePackaging() {
  const [isValidating, setIsValidating] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  
  const validatePackaging = useCallback(async (
    buildStatus: BuildStatus,
    config: PackageConfig
  ): Promise<PackageValidation> => {
    setIsValidating(true);
    try {
      const validation = await invoke<PackageValidation>('dll_validate_packaging', {
        buildStatus,
        config
      });
      return validation;
    } catch (error) {
      return {
        isValid: false,
        errors: [error instanceof Error ? error.message : 'Unknown validation error']
      };
    } finally {
      setIsValidating(false);
    }
  }, []);
  
  const createPackage = useCallback(async (
    buildStatus: BuildStatus,
    testResults: TestResult[],
    config: PackageConfig,
    onProgress?: (progress: number) => void
  ): Promise<DeploymentPackage> => {
    setIsCreating(true);
    
    try {
      // Set up progress listener
      const unlisten = await window.__TAURI__.event.listen<{ progress: number }>(
        'packaging-progress',
        (event) => {
          onProgress?.(event.payload.progress);
        }
      );
      
      try {
        const deploymentPackage = await invoke<DeploymentPackage>('dll_create_package', {
          buildStatus,
          testResults,
          config
        });
        
        return deploymentPackage;
      } finally {
        unlisten();
      }
    } finally {
      setIsCreating(false);
    }
  }, []);
  
  const downloadPackage = useCallback(async (
    packagePath: string,
    format: PackageFormat
  ): Promise<void> => {
    try {
      await invoke('dll_download_package', {
        packagePath,
        format
      });
    } catch (error) {
      throw new Error(`Failed to download package: ${error}`);
    }
  }, []);
  
  const getPackageContents = useCallback(async (
    packagePath: string
  ): Promise<string[]> => {
    try {
      const contents = await invoke<string[]>('dll_get_package_contents', {
        packagePath
      });
      return contents;
    } catch (error) {
      throw new Error(`Failed to get package contents: ${error}`);
    }
  }, []);
  
  const verifyPackage = useCallback(async (
    packagePath: string,
    expectedChecksum?: string
  ): Promise<{ isValid: boolean; actualChecksum: string }> => {
    try {
      const result = await invoke<{ isValid: boolean; actualChecksum: string }>('dll_verify_package', {
        packagePath,
        expectedChecksum
      });
      return result;
    } catch (error) {
      throw new Error(`Failed to verify package: ${error}`);
    }
  }, []);
  
  return {
    isValidating,
    isCreating,
    validatePackaging,
    createPackage,
    downloadPackage,
    getPackageContents,
    verifyPackage
  };
}

// Helper functions for package operations
export function getDefaultPackageConfig(): PackageConfig {
  return {
    format: 'zip',
    includeDocs: true,
    includeExamples: true,
    includeSource: false,
    createInstaller: false,
    packageName: 'LegacyBridge-DLL',
    version: '2.0.0',
    description: 'Document conversion library for legacy systems',
    author: 'LegacyBridge Team',
    license: 'MIT'
  };
}

export function getPackageFileExtension(format: PackageFormat): string {
  const extensions: Record<PackageFormat, string> = {
    zip: '.zip',
    tar: '.tar.gz',
    msi: '.msi',
    nsis: '.exe'
  };
  return extensions[format] || '.zip';
}

export function getPackageContentTypes(config: PackageConfig): string[] {
  const types: string[] = ['DLL Files', 'Configuration'];
  
  if (config.includeDocs) types.push('Documentation');
  if (config.includeExamples) types.push('Examples');
  if (config.includeSource) types.push('Source Code');
  if (config.createInstaller) types.push('Installer');
  
  return types;
}

export function estimatePackageSize(
  buildStatus: BuildStatus,
  config: PackageConfig
): number {
  let sizeInBytes = 0;
  
  // Base DLL size
  if (buildStatus.outputFiles) {
    buildStatus.outputFiles.forEach(file => {
      sizeInBytes += file.size || 0;
    });
  }
  
  // Estimate additional content sizes
  if (config.includeDocs) sizeInBytes += 2 * 1024 * 1024; // ~2MB for docs
  if (config.includeExamples) sizeInBytes += 1 * 1024 * 1024; // ~1MB for examples
  if (config.includeSource) sizeInBytes += 5 * 1024 * 1024; // ~5MB for source
  if (config.createInstaller) sizeInBytes += 3 * 1024 * 1024; // ~3MB for installer overhead
  
  return sizeInBytes;
}