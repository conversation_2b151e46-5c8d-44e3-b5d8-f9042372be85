#!/bin/bash
# Enterprise Security Scanning Script for LegacyBridge
# This script runs comprehensive security scans across the codebase

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
SEVERITY_THRESHOLD=${SEVERITY_THRESHOLD:-"HIGH,CRITICAL"}
SCAN_TYPE=${SCAN_TYPE:-"all"}
OUTPUT_FORMAT=${OUTPUT_FORMAT:-"json"}
REPORT_DIR="security-reports"
EXIT_ON_FAILURE=${EXIT_ON_FAILURE:-"true"}

# Create report directory
mkdir -p "$REPORT_DIR"

echo -e "${GREEN}Starting LegacyBridge Security Scan...${NC}"
echo "Severity Threshold: $SEVERITY_THRESHOLD"
echo "Scan Type: $SCAN_TYPE"
echo "Reports will be saved to: $REPORT_DIR"
echo ""

# Track overall scan status
SCAN_FAILED=0

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Trivy Security Scan
run_trivy_scan() {
    echo -e "${YELLOW}Running Trivy vulnerability scan...${NC}"
    
    if ! command_exists trivy; then
        echo -e "${RED}Trivy not found. Installing...${NC}"
        curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin
    fi
    
    # Filesystem scan
    trivy fs . \
        --severity "$SEVERITY_THRESHOLD" \
        --format "$OUTPUT_FORMAT" \
        --output "$REPORT_DIR/trivy-fs-report.$OUTPUT_FORMAT" \
        --ignore-unfixed \
        --security-checks vuln,config,secret \
        --skip-dirs node_modules,target,dist \
        || SCAN_FAILED=1
    
    # Config scan
    trivy config . \
        --severity "$SEVERITY_THRESHOLD" \
        --format "$OUTPUT_FORMAT" \
        --output "$REPORT_DIR/trivy-config-report.$OUTPUT_FORMAT" \
        || SCAN_FAILED=1
    
    # Docker image scans (if images exist)
    for component in frontend backend cli; do
        if docker images | grep -q "legacybridge/$component"; then
            echo "Scanning Docker image: legacybridge/$component"
            trivy image "legacybridge/$component:latest" \
                --severity "$SEVERITY_THRESHOLD" \
                --format "$OUTPUT_FORMAT" \
                --output "$REPORT_DIR/trivy-$component-image.$OUTPUT_FORMAT" \
                || SCAN_FAILED=1
        fi
    done
    
    echo -e "${GREEN}Trivy scan complete.${NC}"
}

# Snyk Security Scan
run_snyk_scan() {
    echo -e "${YELLOW}Running Snyk security scan...${NC}"
    
    if ! command_exists snyk; then
        echo -e "${RED}Snyk CLI not found. Please install: npm install -g snyk${NC}"
        return 1
    fi
    
    if [ -z "${SNYK_TOKEN:-}" ]; then
        echo -e "${RED}SNYK_TOKEN not set. Skipping Snyk scan.${NC}"
        return 0
    fi
    
    # Test for vulnerabilities
    snyk test \
        --severity-threshold="$SEVERITY_THRESHOLD" \
        --json-file-output="$REPORT_DIR/snyk-vulnerabilities.json" \
        || SCAN_FAILED=1
    
    # Test for code issues
    snyk code test \
        --severity-threshold="$SEVERITY_THRESHOLD" \
        --json-file-output="$REPORT_DIR/snyk-code.json" \
        || SCAN_FAILED=1
    
    echo -e "${GREEN}Snyk scan complete.${NC}"
}

# OWASP Dependency Check
run_dependency_check() {
    echo -e "${YELLOW}Running OWASP Dependency Check...${NC}"
    
    if ! command_exists dependency-check; then
        echo -e "${YELLOW}Downloading OWASP Dependency Check...${NC}"
        wget -O dependency-check.zip https://github.com/jeremylong/DependencyCheck/releases/download/v8.4.3/dependency-check-8.4.3-release.zip
        unzip -q dependency-check.zip
        chmod +x dependency-check/bin/dependency-check.sh
        DEPENDENCY_CHECK="./dependency-check/bin/dependency-check.sh"
    else
        DEPENDENCY_CHECK="dependency-check"
    fi
    
    $DEPENDENCY_CHECK \
        --project "LegacyBridge" \
        --scan . \
        --format JSON \
        --out "$REPORT_DIR/dependency-check-report.json" \
        --enableRetired \
        --enableExperimental \
        --exclude "**/node_modules/**" \
        --exclude "**/target/**" \
        --exclude "**/dist/**" \
        || SCAN_FAILED=1
    
    echo -e "${GREEN}Dependency Check complete.${NC}"
}

# GitLeaks Secret Scanning
run_gitleaks_scan() {
    echo -e "${YELLOW}Running GitLeaks secret scan...${NC}"
    
    if ! command_exists gitleaks; then
        echo -e "${YELLOW}Installing GitLeaks...${NC}"
        wget -O gitleaks.tar.gz https://github.com/gitleaks/gitleaks/releases/download/v8.18.0/gitleaks_8.18.0_linux_x64.tar.gz
        tar -xzf gitleaks.tar.gz
        chmod +x gitleaks
        GITLEAKS="./gitleaks"
    else
        GITLEAKS="gitleaks"
    fi
    
    $GITLEAKS detect \
        --source . \
        --report-format json \
        --report-path "$REPORT_DIR/gitleaks-report.json" \
        --verbose \
        || SCAN_FAILED=1
    
    echo -e "${GREEN}GitLeaks scan complete.${NC}"
}

# Semgrep SAST Scan
run_semgrep_scan() {
    echo -e "${YELLOW}Running Semgrep SAST scan...${NC}"
    
    if ! command_exists semgrep; then
        echo -e "${YELLOW}Installing Semgrep...${NC}"
        python3 -m pip install semgrep
    fi
    
    semgrep \
        --config=auto \
        --json \
        --output="$REPORT_DIR/semgrep-report.json" \
        --severity ERROR \
        --exclude node_modules \
        --exclude target \
        --exclude dist \
        . \
        || SCAN_FAILED=1
    
    echo -e "${GREEN}Semgrep scan complete.${NC}"
}

# License Compliance Check
run_license_check() {
    echo -e "${YELLOW}Running license compliance check...${NC}"
    
    # Node.js licenses
    if [ -f "package.json" ]; then
        npx license-checker \
            --json \
            --out "$REPORT_DIR/npm-licenses.json" \
            --excludePrivatePackages \
            || echo "npm license check failed"
    fi
    
    # Rust licenses
    if [ -f "src-tauri/Cargo.toml" ]; then
        cd src-tauri
        if command_exists cargo-license; then
            cargo-license --json > "../$REPORT_DIR/cargo-licenses.json" || echo "cargo license check failed"
        else
            echo "cargo-license not installed. Run: cargo install cargo-license"
        fi
        cd ..
    fi
    
    echo -e "${GREEN}License check complete.${NC}"
}

# Container Security Scan
run_container_scan() {
    echo -e "${YELLOW}Running container security scan...${NC}"
    
    # Scan Dockerfiles with Hadolint
    if command_exists hadolint; then
        find . -name "Dockerfile*" -exec hadolint {} \; > "$REPORT_DIR/hadolint-report.txt" 2>&1 || echo "Hadolint found issues"
    else
        echo "Hadolint not installed. Skipping Dockerfile linting."
    fi
    
    # Scan with Dockle
    if command_exists dockle; then
        for component in frontend backend cli; do
            if docker images | grep -q "legacybridge/$component"; then
                dockle \
                    --format json \
                    --output "$REPORT_DIR/dockle-$component.json" \
                    "legacybridge/$component:latest" \
                    || echo "Dockle scan failed for $component"
            fi
        done
    else
        echo "Dockle not installed. Skipping container scan."
    fi
    
    echo -e "${GREEN}Container scan complete.${NC}"
}

# Infrastructure as Code Security
run_iac_scan() {
    echo -e "${YELLOW}Running Infrastructure as Code security scan...${NC}"
    
    # Checkov for IaC scanning
    if command_exists checkov; then
        checkov \
            --directory . \
            --output json \
            --output-file-path "$REPORT_DIR" \
            --framework all \
            --skip-path node_modules,target,dist \
            || echo "Checkov found IaC issues"
    else
        echo "Checkov not installed. Run: pip install checkov"
    fi
    
    # Terrascan for Terraform
    if command_exists terrascan; then
        terrascan scan \
            --iac-type terraform \
            --iac-dir cloud/terraform \
            --output json \
            > "$REPORT_DIR/terrascan-report.json" \
            || echo "Terrascan found issues"
    fi
    
    echo -e "${GREEN}IaC scan complete.${NC}"
}

# Generate Summary Report
generate_summary() {
    echo -e "${YELLOW}Generating security scan summary...${NC}"
    
    cat > "$REPORT_DIR/summary.md" << EOF
# Security Scan Summary

**Date:** $(date)
**Severity Threshold:** $SEVERITY_THRESHOLD

## Scan Results

### Vulnerability Scans
- **Trivy:** $([ -f "$REPORT_DIR/trivy-fs-report.$OUTPUT_FORMAT" ] && echo "✓ Complete" || echo "✗ Failed/Skipped")
- **Snyk:** $([ -f "$REPORT_DIR/snyk-vulnerabilities.json" ] && echo "✓ Complete" || echo "✗ Failed/Skipped")
- **OWASP Dependency Check:** $([ -f "$REPORT_DIR/dependency-check-report.json" ] && echo "✓ Complete" || echo "✗ Failed/Skipped")

### Code Security
- **Semgrep SAST:** $([ -f "$REPORT_DIR/semgrep-report.json" ] && echo "✓ Complete" || echo "✗ Failed/Skipped")
- **GitLeaks:** $([ -f "$REPORT_DIR/gitleaks-report.json" ] && echo "✓ Complete" || echo "✗ Failed/Skipped")

### Container Security
- **Hadolint:** $([ -f "$REPORT_DIR/hadolint-report.txt" ] && echo "✓ Complete" || echo "✗ Failed/Skipped")
- **Dockle:** $(ls "$REPORT_DIR"/dockle-*.json 2>/dev/null | wc -l) images scanned

### Infrastructure Security
- **Checkov:** $([ -f "$REPORT_DIR/results_json.json" ] && echo "✓ Complete" || echo "✗ Failed/Skipped")
- **Terrascan:** $([ -f "$REPORT_DIR/terrascan-report.json" ] && echo "✓ Complete" || echo "✗ Failed/Skipped")

### License Compliance
- **NPM Licenses:** $([ -f "$REPORT_DIR/npm-licenses.json" ] && echo "✓ Complete" || echo "✗ Failed/Skipped")
- **Cargo Licenses:** $([ -f "$REPORT_DIR/cargo-licenses.json" ] && echo "✓ Complete" || echo "✗ Failed/Skipped")

## Critical Findings

EOF

    # Extract critical findings from reports
    if [ -f "$REPORT_DIR/trivy-fs-report.json" ]; then
        echo "### Trivy Critical Vulnerabilities" >> "$REPORT_DIR/summary.md"
        jq -r '.Results[]? | select(.Vulnerabilities != null) | .Vulnerabilities[] | select(.Severity == "CRITICAL") | "- \(.PkgName): \(.VulnerabilityID) - \(.Title)"' "$REPORT_DIR/trivy-fs-report.json" >> "$REPORT_DIR/summary.md" 2>/dev/null || echo "No critical vulnerabilities found" >> "$REPORT_DIR/summary.md"
    fi
    
    echo -e "${GREEN}Summary generated at: $REPORT_DIR/summary.md${NC}"
}

# Main execution
case "$SCAN_TYPE" in
    "all")
        run_trivy_scan
        run_snyk_scan
        run_dependency_check
        run_gitleaks_scan
        run_semgrep_scan
        run_license_check
        run_container_scan
        run_iac_scan
        ;;
    "vulnerabilities")
        run_trivy_scan
        run_snyk_scan
        run_dependency_check
        ;;
    "secrets")
        run_gitleaks_scan
        ;;
    "sast")
        run_semgrep_scan
        ;;
    "container")
        run_container_scan
        ;;
    "iac")
        run_iac_scan
        ;;
    "license")
        run_license_check
        ;;
    *)
        echo -e "${RED}Unknown scan type: $SCAN_TYPE${NC}"
        echo "Valid options: all, vulnerabilities, secrets, sast, container, iac, license"
        exit 1
        ;;
esac

# Generate summary
generate_summary

# Check if any scans failed
if [ "$SCAN_FAILED" -eq 1 ] && [ "$EXIT_ON_FAILURE" = "true" ]; then
    echo -e "${RED}Security scan failed! Check reports in $REPORT_DIR${NC}"
    exit 1
else
    echo -e "${GREEN}Security scan complete! Reports saved to $REPORT_DIR${NC}"
    exit 0
fi