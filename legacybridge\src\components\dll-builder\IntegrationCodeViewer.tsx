'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Code,
  Copy,
  Download,
  FileText,
  Info,
  CheckCircle,
  BookOpen,
  Terminal
} from 'lucide-react';

import { BuildStatus, DLLConfiguration, IntegrationCode } from '@/lib/dll/dll-config';
import { codeGenerator } from '@/lib/dll/code-generator';
import { useToast } from '@/hooks/use-toast';

interface IntegrationCodeViewerProps {
  buildStatus: BuildStatus | null;
  config: DLLConfiguration;
}

export function IntegrationCodeViewer({
  buildStatus,
  config
}: IntegrationCodeViewerProps) {
  const [selectedLanguage, setSelectedLanguage] = useState<string>('vb6');
  const [copiedSection, setCopiedSection] = useState<string | null>(null);
  const { toast } = useToast();

  // Generate integration code for all enabled languages
  const integrationCodes = useMemo(() => {
    if (!buildStatus?.success || !buildStatus.outputFiles.length) {
      return [];
    }

    const codes: IntegrationCode[] = [];
    const dllPath = buildStatus.outputFiles[0]; // Use first DLL for generation

    if (config.generateIntegrationCode.vb6) {
      codes.push(codeGenerator.generateVB6Code({
        dllPath,
        architecture: config.architectures[0],
        includeExamples: true,
        includeErrorHandling: true
      }));
    }

    if (config.generateIntegrationCode.vfp9) {
      codes.push(codeGenerator.generateVFP9Code({
        dllPath,
        architecture: config.architectures[0],
        includeExamples: true,
        includeErrorHandling: true
      }));
    }

    if (config.generateIntegrationCode.csharp) {
      codes.push(codeGenerator.generateCSharpCode({
        dllPath,
        architecture: config.architectures[0],
        includeExamples: true,
        includeErrorHandling: true,
        namespace: 'LegacyBridge.Interop'
      }));
    }

    if (config.generateIntegrationCode.python) {
      codes.push(codeGenerator.generatePythonCode({
        dllPath,
        architecture: config.architectures[0],
        includeExamples: true,
        includeErrorHandling: true
      }));
    }

    return codes;
  }, [buildStatus, config]);

  const copyToClipboard = async (text: string, section: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedSection(section);
      toast({
        title: "Copied to Clipboard",
        description: `${section} has been copied to your clipboard`,
        variant: "success"
      });
      setTimeout(() => setCopiedSection(null), 2000);
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy to clipboard",
        variant: "destructive"
      });
    }
  };

  const downloadCode = (code: IntegrationCode) => {
    const blob = new Blob([code.code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = code.fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Download Started",
      description: `${code.fileName} is downloading`,
      variant: "success"
    });
  };

  if (!buildStatus?.success) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Code className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Integration Code Available</h3>
            <p className="text-muted-foreground mb-4">
              Build your DLL successfully to generate integration code
            </p>
            <Button variant="outline" disabled>
              Waiting for Build
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (integrationCodes.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              No integration code languages selected. Enable languages in the configuration to generate code.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const selectedCode = integrationCodes.find(c => c.language === selectedLanguage);

  return (
    <div className="space-y-6">
      {/* Language Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="w-5 h-5 text-blue-500" />
            Integration Code
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedLanguage} onValueChange={setSelectedLanguage}>
            <TabsList className="grid grid-cols-4 w-full">
              {integrationCodes.map((code) => (
                <TabsTrigger
                  key={code.language}
                  value={code.language}
                  className="flex items-center gap-2"
                >
                  <LanguageIcon language={code.language} />
                  {getLanguageDisplay(code.language)}
                </TabsTrigger>
              ))}
            </TabsList>

            {selectedCode && (
              <TabsContent value={selectedLanguage} className="space-y-6 mt-6">
                {/* Code Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{selectedCode.fileName}</Badge>
                    <Badge variant="secondary">
                      {config.architectures.map(a => a === 'x86' ? '32-bit' : '64-bit').join(' & ')}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(selectedCode.code, 'Integration code')}
                    >
                      {copiedSection === 'Integration code' ? (
                        <CheckCircle className="w-4 h-4 mr-1" />
                      ) : (
                        <Copy className="w-4 h-4 mr-1" />
                      )}
                      Copy Code
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => downloadCode(selectedCode)}
                    >
                      <Download className="w-4 h-4 mr-1" />
                      Download
                    </Button>
                  </div>
                </div>

                {/* Documentation */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center gap-2">
                      <BookOpen className="w-4 h-4" />
                      Documentation
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <pre className="text-sm whitespace-pre-wrap text-muted-foreground">
                      {selectedCode.documentation}
                    </pre>
                  </CardContent>
                </Card>

                {/* Integration Code */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base flex items-center gap-2">
                        <FileText className="w-4 h-4" />
                        Integration Code
                      </CardTitle>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyToClipboard(selectedCode.code, 'Full code')}
                      >
                        {copiedSection === 'Full code' ? (
                          <CheckCircle className="w-3 h-3" />
                        ) : (
                          <Copy className="w-3 h-3" />
                        )}
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="relative">
                      <pre className="font-mono text-xs bg-muted p-4 rounded-lg overflow-x-auto max-h-96">
                        <code className={`language-${getHighlightLanguage(selectedCode.language)}`}>
                          {selectedCode.code}
                        </code>
                      </pre>
                    </div>
                  </CardContent>
                </Card>

                {/* Examples */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center gap-2">
                      <Terminal className="w-4 h-4" />
                      Usage Examples
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {selectedCode.examples.map((example, index) => (
                        <div key={index} className="relative">
                          <div className="flex items-center justify-between mb-2">
                            <Badge variant="outline">Example {index + 1}</Badge>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => copyToClipboard(example, `Example ${index + 1}`)}
                            >
                              {copiedSection === `Example ${index + 1}` ? (
                                <CheckCircle className="w-3 h-3" />
                              ) : (
                                <Copy className="w-3 h-3" />
                              )}
                            </Button>
                          </div>
                          <pre className="font-mono text-xs bg-muted p-3 rounded-lg overflow-x-auto">
                            <code className={`language-${getHighlightLanguage(selectedCode.language)}`}>
                              {example}
                            </code>
                          </pre>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Platform-Specific Notes */}
                <PlatformNotes language={selectedCode.language} architecture={config.architectures[0]} />
              </TabsContent>
            )}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

// Supporting Components
interface LanguageIconProps {
  language: string;
}

function LanguageIcon({ language }: LanguageIconProps) {
  const icons: Record<string, string> = {
    vb6: '🔷',
    vfp9: '🦊',
    csharp: '🟦',
    python: '🐍'
  };
  return <span className="text-sm">{icons[language] || '📄'}</span>;
}

function getLanguageDisplay(language: string): string {
  const displays: Record<string, string> = {
    vb6: 'VB6',
    vfp9: 'VFP9',
    csharp: 'C#',
    python: 'Python'
  };
  return displays[language] || language;
}

function getHighlightLanguage(language: string): string {
  const mappings: Record<string, string> = {
    vb6: 'vbnet',
    vfp9: 'foxpro',
    csharp: 'csharp',
    python: 'python'
  };
  return mappings[language] || 'plaintext';
}

interface PlatformNotesProps {
  language: string;
  architecture: 'x86' | 'x64';
}

function PlatformNotes({ language, architecture }: PlatformNotesProps) {
  const notes: Record<string, { title: string; items: string[] }> = {
    vb6: {
      title: 'VB6 Integration Notes',
      items: [
        `Ensure you're using the ${architecture} version of the DLL`,
        'Place the DLL in your application directory or system PATH',
        'Add the .bas module to your VB6 project',
        'VB6 only supports 32-bit (x86) DLLs',
        'Use error handling for production applications'
      ]
    },
    vfp9: {
      title: 'VFP9 Integration Notes',
      items: [
        `Compatible with ${architecture} architecture`,
        'Include the .prg file in your VFP9 project',
        'DLL must be in the application path or Windows system directory',
        'Use SET PROCEDURE TO legacybridge.prg ADDITIVE',
        'Instantiate with loConverter = CREATEOBJECT("LegacyBridge")'
      ]
    },
    csharp: {
      title: 'C# Integration Notes',
      items: [
        `Target platform must match DLL architecture (${architecture})`,
        'Add the wrapper class to your project',
        'Ensure proper disposal with using statement',
        'May require [STAThread] attribute for some operations',
        'Consider async wrappers for large batch operations'
      ]
    },
    python: {
      title: 'Python Integration Notes',
      items: [
        `Use ${architecture === 'x86' ? '32-bit' : '64-bit'} Python interpreter`,
        'Install with: pip install legacybridge (if published)',
        'Or copy legacybridge.py to your project',
        'DLL must be accessible via PATH or same directory',
        'Use context manager (with statement) for automatic cleanup'
      ]
    }
  };

  const platformNotes = notes[language];
  if (!platformNotes) return null;

  return (
    <Alert>
      <Info className="h-4 w-4" />
      <AlertDescription>
        <strong className="block mb-2">{platformNotes.title}</strong>
        <ul className="list-disc list-inside space-y-1">
          {platformNotes.items.map((item, index) => (
            <li key={index} className="text-sm">{item}</li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
}