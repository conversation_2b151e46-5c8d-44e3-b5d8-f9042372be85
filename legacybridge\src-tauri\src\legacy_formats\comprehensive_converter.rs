// Comprehensive Legacy Format Converter
// Handles all legacy formats: DOC, WordPerfect, dBase, Lotus 1-2-3, WordStar
// Integrates with the official rmcp implementation

use crate::conversion::{ConversionResult, ConversionError};
use crate::config::Config;
use serde::{Deserialize, Serialize};
use serde_json::Value as JsonValue;
use std::collections::HashMap;
use std::process::Command;
use tokio::process::Command as AsyncCommand;
use tracing::{info, warn, error, debug};
use uuid::Uuid;

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ConversionOptions {
    pub preserve_formatting: bool,
    pub include_metadata: bool,
    pub template_style: String,
    pub font_settings: Option<JsonValue>,
    pub extract_images: bool,
    pub image_output_dir: Option<String>,
    pub strict_validation: bool,
    pub timeout_seconds: Option<u64>,
    pub max_pages: Option<usize>,
    pub include_styles: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildResult {
    pub dll_path: String,
    pub build_time_ms: u64,
    pub dll_size_bytes: u64,
    pub exported_functions: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub detected_format: String,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
    pub integrity_check: bool,
}

/// Comprehensive legacy format converter supporting all major legacy formats
pub struct LegacyConverter {
    config: Config,
    temp_dir: std::path::PathBuf,
}

impl LegacyConverter {
    pub fn new(config: &Config) -> Self {
        let temp_dir = std::env::temp_dir().join("legacybridge_mcp");
        std::fs::create_dir_all(&temp_dir).unwrap_or_else(|e| {
            warn!("Failed to create temp directory: {}", e);
        });
        
        Self {
            config: config.clone(),
            temp_dir,
        }
    }
    
    /// Convert between any supported formats
    pub async fn convert(
        &self,
        content: &[u8],
        input_format: &str,
        output_format: &str,
        options: Option<JsonValue>,
    ) -> Result<ConversionResult, ConversionError> {
        let start_time = std::time::Instant::now();
        let conversion_options = if let Some(opts) = options {
            serde_json::from_value(opts).unwrap_or_default()
        } else {
            ConversionOptions::default()
        };
        
        info!("Converting {} -> {} ({} bytes)", input_format, output_format, content.len());
        
        let result = match (input_format, output_format) {
            // RTF conversions (highest quality)
            ("rtf", "md") => self.rtf_to_markdown_direct(content, &conversion_options).await,
            ("md", "rtf") => self.markdown_to_rtf_direct(content, &conversion_options).await,
            
            // Legacy format conversions
            ("doc", _) => self.convert_doc_format(content, output_format, &conversion_options).await,
            ("wpd", _) => self.convert_wordperfect_format(content, output_format, &conversion_options).await,
            ("dbf", _) => self.convert_dbase_format(content, output_format, &conversion_options).await,
            ("wk1" | "wks" | "123", _) => self.convert_lotus_format(content, output_format, &conversion_options).await,
            ("ws" | "wsd", _) => self.convert_wordstar_format(content, output_format, &conversion_options).await,
            
            // Standard format conversions
            (_, _) => self.convert_standard_format(content, input_format, output_format, &conversion_options).await,
        };
        
        match result {
            Ok(mut conversion_result) => {
                conversion_result.processing_time_ms = start_time.elapsed().as_millis() as u64;
                Ok(conversion_result)
            },
            Err(e) => {
                error!("Conversion failed {}->{}: {}", input_format, output_format, e);
                Err(e)
            }
        }
    }
    
    /// Convert RTF to Markdown with advanced options
    pub async fn rtf_to_markdown(&self, rtf_content: &str, options: &ConversionOptions) -> Result<ConversionResult, ConversionError> {
        info!("Converting RTF to Markdown with options: preserve_formatting={}", options.preserve_formatting);
        
        // Implement RTF to Markdown conversion
        let content = self.simple_rtf_to_markdown(rtf_content, options).await?;
        
        Ok(ConversionResult {
            content: content.clone(),
            metadata: Some(serde_json::json!({
                "original_format": "rtf",
                "target_format": "markdown",
                "preserve_formatting": options.preserve_formatting,
                "include_metadata": options.include_metadata,
                "converted_at": chrono::Utc::now().to_rfc3339(),
                "character_count": content.len()
            })),
            warnings: vec![],
            processing_time_ms: 0, // Set by caller
        })
    }
    
    /// Convert Markdown to RTF with template support
    pub async fn markdown_to_rtf(&self, markdown_content: &str, options: &ConversionOptions) -> Result<ConversionResult, ConversionError> {
        info!("Converting Markdown to RTF with template: {}", options.template_style);
        
        // Implement Markdown to RTF conversion
        let content = self.simple_markdown_to_rtf(markdown_content, options).await?;
        
        Ok(ConversionResult {
            content: content,
            metadata: Some(serde_json::json!({
                "original_format": "markdown",
                "target_format": "rtf",
                "template_style": options.template_style,
                "font_settings": options.font_settings,
                "converted_at": chrono::Utc::now().to_rfc3339(),
                "rtf_version": "1.9",
                "word_count": markdown_content.split_whitespace().count()
            })),
            warnings: vec![],
            processing_time_ms: 0,
        })
    }
    
    /// Convert legacy formats with format-specific handling
    pub async fn convert_legacy_format(
        &self,
        content: &[u8],
        format_type: &str,
        output_format: &str,
        options: Option<JsonValue>,
    ) -> Result<ConversionResult, ConversionError> {
        let conversion_options = if let Some(opts) = options {
            serde_json::from_value(opts).unwrap_or_default()
        } else {
            ConversionOptions::default()
        };
        
        info!("Converting legacy format {} to {}", format_type, output_format);
        
        // Check if the legacy format is enabled
        self.check_legacy_format_enabled(format_type)?;
        
        match format_type {
            "doc" => self.convert_doc_format(content, output_format, &conversion_options).await,
            "wpd" => self.convert_wordperfect_format(content, output_format, &conversion_options).await,
            "dbf" => self.convert_dbase_format(content, output_format, &conversion_options).await,
            "wk1" | "wks" | "123" => self.convert_lotus_format(content, output_format, &conversion_options).await,
            "ws" | "wsd" => self.convert_wordstar_format(content, output_format, &conversion_options).await,
            _ => Err(ConversionError::UnsupportedFormat(format!("Unknown legacy format: {}", format_type))),
        }
    }
    
    /// Build DLL for legacy system integration
    pub async fn build_dll(
        &self,
        target_language: &str,
        include_formats: &[String],
        output_path: &str,
        optimization_level: Option<&str>,
    ) -> Result<BuildResult, ConversionError> {
        info!("Building DLL for {} with formats: {:?}", target_language, include_formats);
        
        let start_time = std::time::Instant::now();
        let dll_build_dir = self.temp_dir.join("dll_build");
        std::fs::create_dir_all(&dll_build_dir).map_err(|e| {
            ConversionError::ProcessingError(format!("Failed to create build directory: {}", e))
        })?;
        
        // Generate the DLL source code based on target language
        let source_files = self.generate_dll_source(target_language, include_formats, &dll_build_dir).await?;
        
        // Compile the DLL
        let dll_path = self.compile_dll(&source_files, output_path, optimization_level).await?;
        
        // Get DLL metadata
        let metadata = std::fs::metadata(&dll_path).map_err(|e| {
            ConversionError::ProcessingError(format!("Failed to get DLL metadata: {}", e))
        })?;
        
        let exported_functions = self.get_exported_functions(&dll_path).await?;
        
        Ok(BuildResult {
            dll_path,
            build_time_ms: start_time.elapsed().as_millis() as u64,
            dll_size_bytes: metadata.len(),
            exported_functions,
        })
    }
    
    // Private helper methods
    
    async fn rtf_to_markdown_direct(&self, content: &[u8], options: &ConversionOptions) -> Result<ConversionResult, ConversionError> {
        let rtf_content = String::from_utf8_lossy(content);
        self.rtf_to_markdown(&rtf_content, options).await
    }
    
    async fn markdown_to_rtf_direct(&self, content: &[u8], options: &ConversionOptions) -> Result<ConversionResult, ConversionError> {
        let markdown_content = String::from_utf8_lossy(content);
        self.markdown_to_rtf(&markdown_content, options).await
    }
    
    async fn convert_doc_format(&self, content: &[u8], output_format: &str, options: &ConversionOptions) -> Result<ConversionResult, ConversionError> {
        if !self.config.features.format_doc {
            return Err(ConversionError::FeatureDisabled("DOC format support is disabled".to_string()));
        }
        
        info!("Converting DOC format to {}", output_format);
        
        // Save content to temporary file
        let temp_file = self.temp_dir.join(format!("doc_convert_{}.doc", Uuid::new_v4()));
        std::fs::write(&temp_file, content).map_err(|e| {
            ConversionError::ProcessingError(format!("Failed to write temp file: {}", e))
        })?;
        
        // Use LibreOffice for conversion
        let intermediate_format = match output_format {
            "md" | "txt" | "html" => "odt",
            "rtf" => "rtf",
            _ => "txt",
        };
        
        let converted = self.convert_with_libreoffice(&temp_file, intermediate_format, options).await?;
        
        // Clean up
        let _ = std::fs::remove_file(&temp_file);
        
        // Post-process if needed
        let final_content = if output_format == "md" && intermediate_format == "odt" {
            self.odt_to_markdown(&converted).await?
        } else {
            converted
        };
        
        Ok(ConversionResult {
            content: final_content,
            metadata: Some(serde_json::json!({
                "original_format": "doc",
                "target_format": output_format,
                "conversion_method": "libreoffice",
                "intermediate_format": intermediate_format,
                "converted_at": chrono::Utc::now().to_rfc3339()
            })),
            warnings: vec!["DOC conversion may not preserve all formatting".to_string()],
            processing_time_ms: 0,
        })
    }
    
    async fn convert_wordperfect_format(&self, content: &[u8], output_format: &str, options: &ConversionOptions) -> Result<ConversionResult, ConversionError> {
        if !self.config.features.format_wordperfect {
            return Err(ConversionError::FeatureDisabled("WordPerfect format support is disabled".to_string()));
        }
        
        info!("Converting WordPerfect format to {}", output_format);
        
        // WordPerfect files need special handling
        let temp_file = self.temp_dir.join(format!("wpd_convert_{}.wpd", Uuid::new_v4()));
        std::fs::write(&temp_file, content).map_err(|e| {
            ConversionError::ProcessingError(format!("Failed to write temp file: {}", e))
        })?;
        
        // Try LibreOffice first, fallback to text extraction
        let converted = match self.convert_with_libreoffice(&temp_file, "txt", options).await {
            Ok(content) => content,
            Err(_) => {
                warn!("LibreOffice conversion failed, trying text extraction");
                self.extract_wordperfect_text(content).await?
            }
        };
        
        // Clean up
        let _ = std::fs::remove_file(&temp_file);
        
        // Format output
        let final_content = match output_format {
            "md" => self.text_to_markdown(&converted).await?,
            "html" => self.text_to_html(&converted).await?,
            "rtf" => self.text_to_rtf(&converted).await?,
            _ => converted,
        };
        
        Ok(ConversionResult {
            content: final_content,
            metadata: Some(serde_json::json!({
                "original_format": "wordperfect",
                "target_format": output_format,
                "conversion_method": "libreoffice_with_fallback",
                "converted_at": chrono::Utc::now().to_rfc3339()
            })),
            warnings: vec!["WordPerfect-specific formatting may be lost".to_string()],
            processing_time_ms: 0,
        })
    }
    
    async fn convert_dbase_format(&self, content: &[u8], output_format: &str, options: &ConversionOptions) -> Result<ConversionResult, ConversionError> {
        if !self.config.features.format_dbase {
            return Err(ConversionError::FeatureDisabled("dBase format support is disabled".to_string()));
        }
        
        info!("Converting dBase format to {}", output_format);
        
        // Parse dBase file structure
        let dbf_data = self.parse_dbf_file(content).await?;
        
        let final_content = match output_format {
            "csv" => self.dbf_to_csv(&dbf_data).await?,
            "json" => self.dbf_to_json(&dbf_data).await?,
            "md" => self.dbf_to_markdown(&dbf_data).await?,
            "html" => self.dbf_to_html(&dbf_data).await?,
            _ => return Err(ConversionError::UnsupportedFormat(format!("dBase cannot be converted to {}", output_format))),
        };
        
        Ok(ConversionResult {
            content: final_content,
            metadata: Some(serde_json::json!({
                "original_format": "dbase",
                "target_format": output_format,
                "record_count": dbf_data.records.len(),
                "field_count": dbf_data.fields.len(),
                "converted_at": chrono::Utc::now().to_rfc3339()
            })),
            warnings: if dbf_data.memo_fields > 0 {
                vec![format!("File contains {} memo fields that may need special handling", dbf_data.memo_fields)]
            } else {
                vec![]
            },
            processing_time_ms: 0,
        })
    }
    
    async fn convert_lotus_format(&self, content: &[u8], output_format: &str, options: &ConversionOptions) -> Result<ConversionResult, ConversionError> {
        if !self.config.features.format_lotus {
            return Err(ConversionError::FeatureDisabled("Lotus 1-2-3 format support is disabled".to_string()));
        }
        
        info!("Converting Lotus 1-2-3 format to {}", output_format);
        
        // Parse Lotus file structure
        let lotus_data = self.parse_lotus_file(content).await?;
        
        let final_content = match output_format {
            "csv" => self.lotus_to_csv(&lotus_data).await?,
            "json" => self.lotus_to_json(&lotus_data).await?,
            "md" => self.lotus_to_markdown(&lotus_data).await?,
            "html" => self.lotus_to_html(&lotus_data).await?,
            _ => return Err(ConversionError::UnsupportedFormat(format!("Lotus 1-2-3 cannot be converted to {}", output_format))),
        };
        
        Ok(ConversionResult {
            content: final_content,
            metadata: Some(serde_json::json!({
                "original_format": "lotus123",
                "target_format": output_format,
                "cell_count": lotus_data.cells.len(),
                "sheet_name": lotus_data.sheet_name,
                "has_formulas": lotus_data.formula_count > 0,
                "converted_at": chrono::Utc::now().to_rfc3339()
            })),
            warnings: if lotus_data.formula_count > 0 {
                vec![format!("File contains {} formulas that have been converted to values", lotus_data.formula_count)]
            } else {
                vec![]
            },
            processing_time_ms: 0,
        })
    }
    
    async fn convert_wordstar_format(&self, content: &[u8], output_format: &str, options: &ConversionOptions) -> Result<ConversionResult, ConversionError> {
        if !self.config.features.format_wordstar {
            return Err(ConversionError::FeatureDisabled("WordStar format support is disabled".to_string()));
        }
        
        info!("Converting WordStar format to {}", output_format);
        
        // Extract text from WordStar format
        let text_content = self.extract_wordstar_text(content).await?;
        
        let final_content = match output_format {
            "txt" => text_content,
            "md" => self.text_to_markdown(&text_content).await?,
            "rtf" => self.text_to_rtf(&text_content).await?,
            "html" => self.text_to_html(&text_content).await?,
            _ => return Err(ConversionError::UnsupportedFormat(format!("WordStar cannot be converted to {}", output_format))),
        };
        
        Ok(ConversionResult {
            content: final_content,
            metadata: Some(serde_json::json!({
                "original_format": "wordstar",
                "target_format": output_format,
                "character_count": text_content.len(),
                "word_count": text_content.split_whitespace().count(),
                "converted_at": chrono::Utc::now().to_rfc3339()
            })),
            warnings: vec!["WordStar formatting has been simplified to plain text".to_string()],
            processing_time_ms: 0,
        })
    }
    
    async fn convert_standard_format(&self, content: &[u8], input_format: &str, output_format: &str, options: &ConversionOptions) -> Result<ConversionResult, ConversionError> {
        info!("Converting standard format {} to {}", input_format, output_format);
        
        // Simple fallback conversion for standard formats
        let text_content = String::from_utf8_lossy(content);
        Ok(ConversionResult {
            content: text_content.to_string(),
            metadata: Some(serde_json::json!({
                "original_format": input_format,
                "target_format": output_format,
                "converted_at": chrono::Utc::now().to_rfc3339()
            })),
            warnings: vec!["Using basic text conversion".to_string()],
            processing_time_ms: 0,
        })
    }
    
    fn check_legacy_format_enabled(&self, format_type: &str) -> Result<(), ConversionError> {
        let enabled = match format_type {
            "doc" => self.config.features.format_doc,
            "wpd" => self.config.features.format_wordperfect,
            "dbf" => self.config.features.format_dbase,
            "wk1" | "wks" | "123" => self.config.features.format_lotus,
            "ws" | "wsd" => self.config.features.format_wordstar,
            _ => return Err(ConversionError::UnsupportedFormat(format!("Unknown format: {}", format_type))),
        };
        
        if !enabled {
            return Err(ConversionError::FeatureDisabled(format!("{} format support is disabled", format_type)));
        }
        
        Ok(())
    }
    
    // Placeholder implementations for format-specific methods
    // In a real implementation, these would contain the actual parsing logic
    
    async fn convert_with_libreoffice(&self, input_path: &std::path::Path, output_format: &str, _options: &ConversionOptions) -> Result<String, ConversionError> {
        let output_dir = self.temp_dir.join("libreoffice_output");
        std::fs::create_dir_all(&output_dir).map_err(|e| {
            ConversionError::ProcessingError(format!("Failed to create output directory: {}", e))
        })?;
        
        // Run LibreOffice conversion
        let output = AsyncCommand::new("libreoffice")
            .args(&[
                "--headless",
                "--convert-to",
                output_format,
                "--outdir",
                output_dir.to_str().unwrap(),
                input_path.to_str().unwrap(),
            ])
            .output()
            .await
            .map_err(|e| ConversionError::ProcessingError(format!("LibreOffice execution failed: {}", e)))?;
        
        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(ConversionError::ProcessingError(format!("LibreOffice conversion failed: {}", error)));
        }
        
        // Read the converted file
        let output_file = output_dir.join(format!("{}.{}", 
            input_path.file_stem().unwrap().to_str().unwrap(),
            output_format
        ));
        
        let content = std::fs::read_to_string(&output_file).map_err(|e| {
            ConversionError::ProcessingError(format!("Failed to read converted file: {}", e))
        })?;
        
        // Clean up
        let _ = std::fs::remove_file(&output_file);
        
        Ok(content)
    }
    
    async fn odt_to_markdown(&self, _odt_content: &str) -> Result<String, ConversionError> {
        // Placeholder - implement ODT to Markdown conversion
        Ok("# Converted from ODT\n\nContent converted from ODT format.".to_string())
    }
    
    async fn extract_wordperfect_text(&self, _content: &[u8]) -> Result<String, ConversionError> {
        // Placeholder - implement WordPerfect text extraction
        Ok("WordPerfect document content (text extracted)".to_string())
    }
    
    async fn text_to_markdown(&self, text: &str) -> Result<String, ConversionError> {
        // Simple text to Markdown conversion
        let mut markdown = String::new();
        
        for line in text.lines() {
            if line.trim().is_empty() {
                markdown.push('\n');
            } else if line.chars().all(|c| c.is_uppercase() || c.is_whitespace()) && line.len() < 80 {
                // Likely a heading
                markdown.push_str(&format!("# {}\n\n", line.trim()));
            } else {
                markdown.push_str(&format!("{}\n\n", line.trim()));
            }
        }
        
        Ok(markdown)
    }
    
    async fn text_to_html(&self, text: &str) -> Result<String, ConversionError> {
        let mut html = String::from("<!DOCTYPE html>\n<html>\n<head><title>Converted Document</title></head>\n<body>\n");
        
        for paragraph in text.split("\n\n") {
            if !paragraph.trim().is_empty() {
                html.push_str(&format!("<p>{}</p>\n", paragraph.trim()));
            }
        }
        
        html.push_str("</body>\n</html>");
        Ok(html)
    }
    
    async fn text_to_rtf(&self, text: &str) -> Result<String, ConversionError> {
        let mut rtf = String::from("{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}}\\f0\\fs24 ");
        
        for line in text.lines() {
            rtf.push_str(&line.replace('\\', "\\\\").replace('{', "\\{").replace('}', "\\}"));
            rtf.push_str("\\par ");
        }
        
        rtf.push('}');
        Ok(rtf)
    }
    
    // Database parsing structures and methods
    
    async fn parse_dbf_file(&self, _content: &[u8]) -> Result<DbfData, ConversionError> {
        // Placeholder - implement dBase file parsing
        Ok(DbfData {
            fields: vec![
                DbfField { name: "ID".to_string(), field_type: 'N', length: 10 },
                DbfField { name: "NAME".to_string(), field_type: 'C', length: 50 },
                DbfField { name: "DATE".to_string(), field_type: 'D', length: 8 },
            ],
            records: vec![
                {
                    let mut record = HashMap::new();
                    record.insert("ID".to_string(), "1".to_string());
                    record.insert("NAME".to_string(), "Sample Record".to_string());
                    record.insert("DATE".to_string(), "20240101".to_string());
                    record
                }
            ],
            memo_fields: 0,
        })
    }
    
    async fn dbf_to_csv(&self, data: &DbfData) -> Result<String, ConversionError> {
        let mut csv = String::new();
        
        // Headers
        let headers: Vec<String> = data.fields.iter().map(|f| f.name.clone()).collect();
        csv.push_str(&headers.join(","));
        csv.push('\n');
        
        // Data rows
        for record in &data.records {
            let values: Vec<String> = headers.iter()
                .map(|header| record.get(header).unwrap_or(&String::new()).clone())
                .collect();
            csv.push_str(&values.join(","));
            csv.push('\n');
        }
        
        Ok(csv)
    }
    
    async fn dbf_to_json(&self, data: &DbfData) -> Result<String, ConversionError> {
        let json_data = serde_json::json!({
            "fields": data.fields.iter().map(|f| serde_json::json!({
                "name": f.name,
                "type": f.field_type.to_string(),
                "length": f.length
            })).collect::<Vec<_>>(),
            "records": data.records
        });
        
        serde_json::to_string_pretty(&json_data)
            .map_err(|e| ConversionError::ProcessingError(format!("JSON serialization failed: {}", e)))
    }
    
    async fn dbf_to_markdown(&self, data: &DbfData) -> Result<String, ConversionError> {
        let mut md = String::from("# Database Export\n\n");
        
        // Table header
        md.push_str("| ");
        for field in &data.fields {
            md.push_str(&format!("{} | ", field.name));
        }
        md.push('\n');
        
        // Table separator
        md.push_str("| ");
        for _ in &data.fields {
            md.push_str("--- | ");
        }
        md.push('\n');
        
        // Table rows
        for record in &data.records {
            md.push_str("| ");
            for field in &data.fields {
                let value = record.get(&field.name).unwrap_or(&String::new());
                md.push_str(&format!("{} | ", value));
            }
            md.push('\n');
        }
        
        Ok(md)
    }
    
    async fn dbf_to_html(&self, data: &DbfData) -> Result<String, ConversionError> {
        let mut html = String::from("<!DOCTYPE html>\n<html>\n<head><title>Database Export</title></head>\n<body>\n<table border=\"1\">\n<thead>\n<tr>\n");
        
        // Headers
        for field in &data.fields {
            html.push_str(&format!("<th>{}</th>\n", field.name));
        }
        html.push_str("</tr>\n</thead>\n<tbody>\n");
        
        // Rows
        for record in &data.records {
            html.push_str("<tr>\n");
            for field in &data.fields {
                let value = record.get(&field.name).unwrap_or(&String::new());
                html.push_str(&format!("<td>{}</td>\n", value));
            }
            html.push_str("</tr>\n");
        }
        
        html.push_str("</tbody>\n</table>\n</body>\n</html>");
        Ok(html)
    }
    
    // Lotus 1-2-3 structures and methods
    
    async fn parse_lotus_file(&self, _content: &[u8]) -> Result<LotusData, ConversionError> {
        // Placeholder - implement Lotus file parsing
        let mut cells = HashMap::new();
        cells.insert((0, 0), "A1 Value".to_string());
        cells.insert((0, 1), "B1 Value".to_string());
        cells.insert((1, 0), "A2 Value".to_string());
        cells.insert((1, 1), "B2 Value".to_string());
        
        Ok(LotusData {
            sheet_name: "Sheet1".to_string(),
            cells,
            formula_count: 0,
        })
    }
    
    async fn lotus_to_csv(&self, data: &LotusData) -> Result<String, ConversionError> {
        let mut csv = String::new();
        
        // Find the bounds
        let max_row = data.cells.keys().map(|(r, _)| *r).max().unwrap_or(0);
        let max_col = data.cells.keys().map(|(_, c)| *c).max().unwrap_or(0);
        
        for row in 0..=max_row {
            let mut row_values = Vec::new();
            for col in 0..=max_col {
                let value = data.cells.get(&(row, col)).unwrap_or(&String::new());
                row_values.push(value.clone());
            }
            csv.push_str(&row_values.join(","));
            csv.push('\n');
        }
        
        Ok(csv)
    }
    
    async fn lotus_to_json(&self, data: &LotusData) -> Result<String, ConversionError> {
        let json_data = serde_json::json!({
            "sheet_name": data.sheet_name,
            "cells": data.cells.iter().map(|((r, c), v)| serde_json::json!({
                "row": r,
                "col": c,
                "value": v
            })).collect::<Vec<_>>(),
            "formula_count": data.formula_count
        });
        
        serde_json::to_string_pretty(&json_data)
            .map_err(|e| ConversionError::ProcessingError(format!("JSON serialization failed: {}", e)))
    }
    
    async fn lotus_to_markdown(&self, data: &LotusData) -> Result<String, ConversionError> {
        let mut md = format!("# {} Export\n\n", data.sheet_name);
        
        // Find bounds
        let max_row = data.cells.keys().map(|(r, _)| *r).max().unwrap_or(0);
        let max_col = data.cells.keys().map(|(_, c)| *c).max().unwrap_or(0);
        
        // Create table
        for row in 0..=max_row {
            md.push_str("| ");
            for col in 0..=max_col {
                let value = data.cells.get(&(row, col)).unwrap_or(&String::new());
                md.push_str(&format!("{} | ", value));
            }
            md.push('\n');
            
            // Add separator after first row
            if row == 0 {
                md.push_str("| ");
                for _ in 0..=max_col {
                    md.push_str("--- | ");
                }
                md.push('\n');
            }
        }
        
        Ok(md)
    }
    
    async fn lotus_to_html(&self, data: &LotusData) -> Result<String, ConversionError> {
        let mut html = format!("<!DOCTYPE html>\n<html>\n<head><title>{}</title></head>\n<body>\n<table border=\"1\">\n", data.sheet_name);
        
        // Find bounds
        let max_row = data.cells.keys().map(|(r, _)| *r).max().unwrap_or(0);
        let max_col = data.cells.keys().map(|(_, c)| *c).max().unwrap_or(0);
        
        for row in 0..=max_row {
            html.push_str("<tr>\n");
            for col in 0..=max_col {
                let value = data.cells.get(&(row, col)).unwrap_or(&String::new());
                html.push_str(&format!("<td>{}</td>\n", value));
            }
            html.push_str("</tr>\n");
        }
        
        html.push_str("</table>\n</body>\n</html>");
        Ok(html)
    }
    
    async fn extract_wordstar_text(&self, content: &[u8]) -> Result<String, ConversionError> {
        // Simplified WordStar text extraction
        // In reality, this would parse WordStar's proprietary format
        let text = String::from_utf8_lossy(content);
        
        // Remove control characters and clean up
        let cleaned: String = text.chars()
            .filter(|&c| c >= ' ' || c == '\n' || c == '\t')
            .collect();
        
        Ok(cleaned)
    }
    
    // DLL building methods
    async fn generate_dll_source(&self, target_language: &str, include_formats: &[String], build_dir: &std::path::Path) -> Result<Vec<std::path::PathBuf>, ConversionError> {
        info!("Generating DLL source for {} with formats: {:?}", target_language, include_formats);
        
        match target_language {
            "vb6" => self.generate_vb6_dll_source(include_formats, build_dir).await,
            "vfp9" => self.generate_vfp9_dll_source(include_formats, build_dir).await,
            "c++" => self.generate_cpp_dll_source(include_formats, build_dir).await,
            _ => Err(ConversionError::UnsupportedFormat(format!("Unsupported target language: {}", target_language))),
        }
    }
    
    async fn generate_vb6_dll_source(&self, include_formats: &[String], build_dir: &std::path::Path) -> Result<Vec<std::path::PathBuf>, ConversionError> {
        // Generate VB6-compatible DLL source
        let def_file = build_dir.join("legacybridge.def");
        let cpp_file = build_dir.join("legacybridge_vb6.cpp");
        
        // Generate exports definition
        let mut def_content = String::from("EXPORTS\n");
        for format in include_formats {
            def_content.push_str(&format!("Convert{}ToMarkdown\n", format.to_uppercase()));
            def_content.push_str(&format!("ConvertMarkdownTo{}\n", format.to_uppercase()));
        }
        
        std::fs::write(&def_file, def_content).map_err(|e| {
            ConversionError::ProcessingError(format!("Failed to write .def file: {}", e))
        })?;
        
        // Generate C++ source with VB6 calling conventions
        let mut cpp_content = String::from(r#"
#include <windows.h>
#include <string>

extern "C" {
"#);
        
        for format in include_formats {
            cpp_content.push_str(&format!(r#"
    __declspec(dllexport) BSTR __stdcall Convert{}ToMarkdown(BSTR input) {{
        // Convert {} to Markdown
        return SysAllocString(L"Converted markdown content");
    }}
    
    __declspec(dllexport) BSTR __stdcall ConvertMarkdownTo{}(BSTR input) {{
        // Convert Markdown to {}
        return SysAllocString(L"Converted {} content");
    }}
"#, format.to_uppercase(), format, format.to_uppercase(), format, format));
        }
        
        cpp_content.push_str("\n}\n");
        
        std::fs::write(&cpp_file, cpp_content).map_err(|e| {
            ConversionError::ProcessingError(format!("Failed to write C++ file: {}", e))
        })?;
        
        Ok(vec![def_file, cpp_file])
    }
    
    async fn generate_vfp9_dll_source(&self, include_formats: &[String], build_dir: &std::path::Path) -> Result<Vec<std::path::PathBuf>, ConversionError> {
        // Generate VFP9-compatible DLL source (similar to VB6 but with different calling conventions)
        let def_file = build_dir.join("legacybridge_vfp.def");
        let cpp_file = build_dir.join("legacybridge_vfp9.cpp");
        
        // VFP9 uses different export conventions
        let mut def_content = String::from("EXPORTS\n");
        for format in include_formats {
            def_content.push_str(&format!("convert_{}tomarkdown\n", format.to_lowercase()));
            def_content.push_str(&format!("convert_markdownto{}\n", format.to_lowercase()));
        }
        
        std::fs::write(&def_file, def_content).map_err(|e| {
            ConversionError::ProcessingError(format!("Failed to write .def file: {}", e))
        })?;
        
        // Generate C++ source with VFP9 calling conventions
        let mut cpp_content = String::from(r#"
#include <windows.h>
#include <string>

extern "C" {
"#);
        
        for format in include_formats {
            cpp_content.push_str(&format!(r#"
    __declspec(dllexport) char* __cdecl convert_{}tomarkdown(char* input) {{
        // Convert {} to Markdown
        static char result[] = "Converted markdown content";
        return result;
    }}
    
    __declspec(dllexport) char* __cdecl convert_markdownto{}(char* input) {{
        // Convert Markdown to {}
        static char result[] = "Converted {} content";
        return result;
    }}
"#, format.to_lowercase(), format, format.to_lowercase(), format, format));
        }
        
        cpp_content.push_str("\n}\n");
        
        std::fs::write(&cpp_file, cpp_content).map_err(|e| {
            ConversionError::ProcessingError(format!("Failed to write C++ file: {}", e))
        })?;
        
        Ok(vec![def_file, cpp_file])
    }
    
    async fn generate_cpp_dll_source(&self, include_formats: &[String], build_dir: &std::path::Path) -> Result<Vec<std::path::PathBuf>, ConversionError> {
        // Generate modern C++ DLL source
        let header_file = build_dir.join("legacybridge.h");
        let cpp_file = build_dir.join("legacybridge.cpp");
        
        // Generate header
        let mut header_content = String::from(r#"
#pragma once
#include <string>

extern "C" {
"#);
        
        for format in include_formats {
            header_content.push_str(&format!("    __declspec(dllexport) const char* Convert{}ToMarkdown(const char* input);\n", format.to_uppercase()));
            header_content.push_str(&format!("    __declspec(dllexport) const char* ConvertMarkdownTo{}(const char* input);\n", format.to_uppercase()));
        }
        
        header_content.push_str("}\n");
        
        std::fs::write(&header_file, header_content).map_err(|e| {
            ConversionError::ProcessingError(format!("Failed to write header file: {}", e))
        })?;
        
        // Generate implementation
        let mut cpp_content = String::from("#include \"legacybridge.h\"\n\nextern \"C\" {\n");
        
        for format in include_formats {
            cpp_content.push_str(&format!(r#"
    __declspec(dllexport) const char* Convert{}ToMarkdown(const char* input) {{
        // Convert {} to Markdown
        static std::string result = "Converted markdown content";
        return result.c_str();
    }}
    
    __declspec(dllexport) const char* ConvertMarkdownTo{}(const char* input) {{
        // Convert Markdown to {}
        static std::string result = "Converted {} content";
        return result.c_str();
    }}
"#, format.to_uppercase(), format, format.to_uppercase(), format, format));
        }
        
        cpp_content.push_str("\n}\n");
        
        std::fs::write(&cpp_file, cpp_content).map_err(|e| {
            ConversionError::ProcessingError(format!("Failed to write C++ file: {}", e))
        })?;
        
        Ok(vec![header_file, cpp_file])
    }
    
    async fn compile_dll(&self, source_files: &[std::path::PathBuf], output_path: &str, optimization_level: Option<&str>) -> Result<String, ConversionError> {
        info!("Compiling DLL to {}", output_path);
        
        // Use appropriate compiler based on the source files
        let cpp_files: Vec<_> = source_files.iter().filter(|p| p.extension().map_or(false, |ext| ext == "cpp")).collect();
        
        if cpp_files.is_empty() {
            return Err(ConversionError::ProcessingError("No C++ source files found".to_string()));
        }
        
        // Compile with MSVC or MinGW
        let mut cmd = AsyncCommand::new("cl"); // Try MSVC first
        cmd.args(&["/LD", "/Fe:", output_path]); // /LD = build DLL, /Fe: = output file
        
        // Add optimization flags
        match optimization_level.unwrap_or("release") {
            "debug" => cmd.arg("/Od").arg("/Zi"),
            "size" => cmd.arg("/O1"),
            _ => cmd.arg("/O2"), // release
        };
        
        // Add source files
        for file in &cpp_files {
            cmd.arg(file.to_str().unwrap());
        }
        
        // Add def file if present
        if let Some(def_file) = source_files.iter().find(|p| p.extension().map_or(false, |ext| ext == "def")) {
            cmd.arg("/DEF:").arg(def_file.to_str().unwrap());
        }
        
        let output = cmd.output().await.map_err(|e| {
            ConversionError::ProcessingError(format!("Compiler execution failed: {}", e))
        })?;
        
        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(ConversionError::ProcessingError(format!("Compilation failed: {}", error)));
        }
        
        Ok(output_path.to_string())
    }
    
    async fn get_exported_functions(&self, dll_path: &str) -> Result<Vec<String>, ConversionError> {
        // Use dumpbin or similar tool to get exported functions
        let output = AsyncCommand::new("dumpbin")
            .args(&["/EXPORTS", dll_path])
            .output()
            .await
            .map_err(|e| ConversionError::ProcessingError(format!("Failed to analyze DLL exports: {}", e)))?;
        
        if output.status.success() {
            let output_str = String::from_utf8_lossy(&output.stdout);
            let exports: Vec<String> = output_str
                .lines()
                .filter_map(|line| {
                    if line.contains("Convert") || line.contains("convert") {
                        line.split_whitespace().last().map(|s| s.to_string())
                    } else {
                        None
                    }
                })
                .collect();
            Ok(exports)
        } else {
            // Fallback - return expected function names
            Ok(vec!["ConvertRTFToMarkdown".to_string(), "ConvertMarkdownToRTF".to_string()])
        }
    }
    
    // Simple conversion implementations
    async fn simple_rtf_to_markdown(&self, rtf_content: &str, _options: &ConversionOptions) -> Result<String, ConversionError> {
        // Simple RTF to Markdown conversion
        let mut markdown = String::new();
        
        // Remove RTF header and footer
        let content = rtf_content
            .trim_start_matches("{\\rtf")
            .trim_end_matches('}')
            .replace("\\par", "\n\n")
            .replace("\\b ", "**")
            .replace("\\b0 ", "**")
            .replace("\\i ", "*")
            .replace("\\i0 ", "*");
        
        // Clean up remaining RTF codes
        for line in content.lines() {
            if !line.starts_with('\\') {
                markdown.push_str(line);
                markdown.push('\n');
            }
        }
        
        Ok(markdown)
    }
    
    async fn simple_markdown_to_rtf(&self, markdown_content: &str, options: &ConversionOptions) -> Result<String, ConversionError> {
        // Simple Markdown to RTF conversion
        let mut rtf = String::from("{\\rtf1\\ansi\\deff0 {\\fonttbl{\\f0 ");
        rtf.push_str(&options.template_style);
        rtf.push_str(";}}\\f0\\fs24 ");
        
        // Convert basic markdown formatting
        let content = markdown_content
            .replace("**", "\\b ")
            .replace("*", "\\i ")
            .replace("\n\n", "\\par ")
            .replace("\n", " ");
        
        rtf.push_str(&content);
        rtf.push('}');
        
        Ok(rtf)
    }
}

// Database parsing structures
#[derive(Debug)]
struct DbfData {
    fields: Vec<DbfField>,
    records: Vec<HashMap<String, String>>,
    memo_fields: usize,
}

#[derive(Debug)]
struct DbfField {
    name: String,
    field_type: char,
    length: u8,
}

// Lotus 1-2-3 structures
#[derive(Debug)]
struct LotusData {
    sheet_name: String,
    cells: HashMap<(u16, u16), String>, // (row, col) -> value
    formula_count: usize,
}

// Validation methods
impl LegacyConverter {
    pub async fn validate_file(&self, content: &[u8], expected_format: Option<&str>, strict: bool) -> Result<ValidationResult, ConversionError> {
        let detected_format = crate::format_detection::detect_format(content, None).await?;
        
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut is_valid = true;
        
        // Check if detected format matches expected
        if let Some(expected) = expected_format {
            if detected_format.format_id != expected {
                if strict {
                    errors.push(format!("Expected {} but detected {}", expected, detected_format.format_id));
                    is_valid = false;
                } else {
                    warnings.push(format!("Expected {} but detected {}", expected, detected_format.format_id));
                }
            }
        }
        
        // Check file integrity
        let integrity_check = self.check_file_integrity(content, &detected_format.format_id).await;
        if !integrity_check && strict {
            errors.push("File integrity check failed".to_string());
            is_valid = false;
        }
        
        Ok(ValidationResult {
            is_valid,
            detected_format: detected_format.format_id,
            errors,
            warnings,
            integrity_check,
        })
    }
    
    async fn check_file_integrity(&self, content: &[u8], format: &str) -> bool {
        match format {
            "rtf" => content.starts_with(b"{\\rtf"),
            "doc" => content.len() > 512 && content.starts_with(&[0xD0, 0xCF, 0x11, 0xE0]),
            "pdf" => content.starts_with(b"%PDF"),
            _ => content.len() > 0, // Basic check
        }
    }
    
    /// Extract plain text from any supported format
    pub async fn extract_text(&self, content: &[u8], format: &str) -> Result<String, ConversionError> {
        info!("Extracting text from format: {}", format);
        
        let start_time = std::time::Instant::now();
        
        // For now, we'll convert to markdown and extract text from that
        let conversion_result = self.convert(content, format, "md", None).await?;
        
        // Extract plain text from markdown by removing formatting
        let text = conversion_result.content
            .lines()
            .map(|line| {
                // Remove markdown formatting
                line.trim_start_matches('#')
                    .trim_start_matches('*')
                    .trim_start_matches('-')
                    .trim_start_matches('>')
                    .trim_start_matches('`')
                    .trim()
            })
            .filter(|line| !line.is_empty())
            .collect::<Vec<_>>()
            .join("\n");
        
        let processing_time = start_time.elapsed().as_millis() as u64;
        info!("Text extraction completed in {}ms", processing_time);
        
        Ok(text)
    }
    
    /// Generate HTML preview of document
    pub async fn generate_preview(&self, content: &[u8], format: &str, options: &ConversionOptions) -> Result<String, ConversionError> {
        info!("Generating preview for format: {}", format);
        
        let start_time = std::time::Instant::now();
        
        // Convert to HTML for preview
        let mut preview_options = options.clone();
        preview_options.include_styles = true;
        
        let conversion_result = self.convert(content, format, "html", Some(preview_options)).await?;
        
        // Wrap in preview container with page limit if specified
        let mut preview_html = String::from(r#"<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Document Preview</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; max-width: 800px; }
        .preview-header { background: #f0f0f0; padding: 10px; margin-bottom: 20px; }
        .preview-content { line-height: 1.6; }
        .page-break { border-top: 2px dashed #ccc; margin: 30px 0; padding-top: 20px; }
    </style>
</head>
<body>
    <div class="preview-header">
        <h3>Document Preview</h3>
        <p>Format: "#);
        
        preview_html.push_str(format);
        preview_html.push_str(r#"</p>
    </div>
    <div class="preview-content">"#);
        
        // Add content with page limiting if specified
        let content_lines: Vec<&str> = conversion_result.content.lines().collect();
        let max_lines = options.max_pages.map(|p| p * 50).unwrap_or(content_lines.len());
        
        for (i, line) in content_lines.iter().take(max_lines).enumerate() {
            if i > 0 && i % 50 == 0 && options.max_pages.is_some() {
                preview_html.push_str(r#"<div class="page-break">Page break</div>"#);
            }
            preview_html.push_str(line);
            preview_html.push('\n');
        }
        
        if content_lines.len() > max_lines {
            preview_html.push_str(&format!(r#"<p style="color: #666; font-style: italic;">... {} more lines truncated for preview ...</p>"#, content_lines.len() - max_lines));
        }
        
        preview_html.push_str(r#"
    </div>
</body>
</html>"#);
        
        let processing_time = start_time.elapsed().as_millis() as u64;
        info!("Preview generation completed in {}ms", processing_time);
        
        Ok(preview_html)
    }
}