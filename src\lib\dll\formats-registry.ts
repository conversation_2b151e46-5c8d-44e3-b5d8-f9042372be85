export interface FormatDefinition {
  id: string;
  name: string;
  extensions: string[];
  language: string;
  icon?: string;
}

const formatDefinitions: Record<string, FormatDefinition> = {
  'vb6': {
    id: 'vb6',
    name: 'Visual Basic 6',
    extensions: ['.bas', '.cls', '.frm'],
    language: 'vb',
    icon: 'FileCode',
  },
  'vfp9': {
    id: 'vfp9',
    name: 'Visual FoxPro 9',
    extensions: ['.prg'],
    language: 'foxpro',
    icon: 'FileCode',
  },
  'csharp': {
    id: 'csharp',
    name: 'C#',
    extensions: ['.cs'],
    language: 'csharp',
    icon: 'FileCode',
  },
  'python': {
    id: 'python',
    name: 'Python',
    extensions: ['.py'],
    language: 'python',
    icon: 'FileCode',
  },
  'dll': {
    id: 'dll',
    name: 'Dynamic Link Library',
    extensions: ['.dll'],
    language: 'binary',
    icon: 'Package',
  },
};

export function getFormatDefinition(formatId: string): FormatDefinition | undefined {
  return formatDefinitions[formatId];
}

export function getFormatsByExtension(extension: string): FormatDefinition[] {
  return Object.values(formatDefinitions).filter(format =>
    format.extensions.includes(extension)
  );
}

export function getAllFormats(): FormatDefinition[] {
  return Object.values(formatDefinitions);
}