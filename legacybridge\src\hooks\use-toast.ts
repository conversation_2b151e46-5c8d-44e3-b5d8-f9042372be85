// Simple toast notification hook
// Provides a basic toast notification system for the DLL Builder

import { useState, useCallback } from 'react';

export interface Toast {
  id: string;
  title: string;
  description?: string;
  variant?: 'default' | 'success' | 'destructive' | 'warning';
  duration?: number;
}

interface ToastState {
  toasts: Toast[];
}

let toastIdCounter = 0;

export function useToast() {
  const [state, setState] = useState<ToastState>({ toasts: [] });

  const toast = useCallback((props: Omit<Toast, 'id'>) => {
    const id = `toast-${++toastIdCounter}`;
    const newToast: Toast = {
      id,
      variant: 'default',
      duration: 5000,
      ...props
    };

    setState((prev) => ({
      toasts: [...prev.toasts, newToast]
    }));

    // Auto-remove toast after duration
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        setState((prev) => ({
          toasts: prev.toasts.filter(t => t.id !== id)
        }));
      }, newToast.duration);
    }

    return id;
  }, []);

  const dismiss = useCallback((toastId: string) => {
    setState((prev) => ({
      toasts: prev.toasts.filter(t => t.id !== toastId)
    }));
  }, []);

  const dismissAll = useCallback(() => {
    setState({ toasts: [] });
  }, []);

  return {
    toast,
    dismiss,
    dismissAll,
    toasts: state.toasts
  };
}