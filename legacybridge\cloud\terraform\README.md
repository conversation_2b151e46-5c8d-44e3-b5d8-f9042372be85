# Multi-Cloud Terraform Infrastructure

## Overview

This directory contains Terraform modules for deploying LegacyBridge infrastructure across AWS, Azure, and Google Cloud Platform with a unified configuration approach.

## Features

- **Multi-cloud support**: Single configuration for AWS, Azure, or GCP
- **Modular design**: Reusable modules for each cloud provider
- **Best practices**: Security, high availability, and cost optimization
- **State management**: Remote state configuration for teams

## Prerequisites

1. Terraform >= 1.0
2. Cloud provider CLI tools:
   - AWS CLI (for AWS deployments)
   - Azure CLI (for Azure deployments)
   - gcloud SDK (for GCP deployments)
3. Appropriate cloud credentials configured

## Quick Start

### 1. Initialize Terraform

```bash
# Copy example variables
cp terraform.tfvars.example terraform.tfvars

# Edit terraform.tfvars with your configuration
vim terraform.tfvars

# Initialize Terraform
terraform init
```

### 2. Configure Backend (Optional but Recommended)

Create a `backend.tf` file for remote state:

#### AWS S3 Backend
```hcl
terraform {
  backend "s3" {
    bucket = "your-terraform-state-bucket"
    key    = "legacybridge/terraform.tfstate"
    region = "us-west-2"
    encrypt = true
    dynamodb_table = "terraform-state-lock"
  }
}
```

#### Azure Storage Backend
```hcl
terraform {
  backend "azurerm" {
    resource_group_name  = "terraform-state-rg"
    storage_account_name = "tfstateaccount"
    container_name      = "tfstate"
    key                 = "legacybridge.terraform.tfstate"
  }
}
```

#### GCS Backend
```hcl
terraform {
  backend "gcs" {
    bucket = "your-terraform-state-bucket"
    prefix = "legacybridge"
  }
}
```

### 3. Plan and Apply

```bash
# Review the plan
terraform plan

# Apply the infrastructure
terraform apply
```

## Configuration Options

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| cloud_provider | Target cloud platform | "aws", "azure", or "gcp" |
| region | Cloud region/location | "us-west-2" |
| gcp_project_id | GCP Project ID (GCP only) | "my-project-123" |

### Optional Variables

| Variable | Default | Description |
|----------|---------|-------------|
| environment | production | Environment name |
| kubernetes_version | 1.28 | Kubernetes version |
| node_count | 3 | Initial node count |
| min_node_count | 2 | Minimum nodes for auto-scaling |
| max_node_count | 20 | Maximum nodes for auto-scaling |
| db_storage_size | 100 | Database storage in GB |
| db_version | 15 | PostgreSQL version |

## Cloud-Specific Configurations

### AWS Deployment

```hcl
cloud_provider = "aws"
region = "us-west-2"
node_instance_type = "t3.large"
db_instance_type = "db.t3.medium"
redis_instance_type = "cache.t3.micro"
```

### Azure Deployment

```hcl
cloud_provider = "azure"
region = "eastus"
# Instance types are automatically mapped
```

### GCP Deployment

```hcl
cloud_provider = "gcp"
region = "us-central1"
gcp_project_id = "your-project-id"
# Instance types are automatically mapped
```

## Module Structure

```
terraform/
├── main.tf                 # Main configuration
├── terraform.tfvars.example # Example variables
├── modules/
│   ├── aws/               # AWS-specific resources
│   │   └── main.tf
│   ├── azure/             # Azure-specific resources
│   │   └── main.tf
│   └── gcp/               # GCP-specific resources
│       └── main.tf
```

## Outputs

After deployment, Terraform will output:

- `kubernetes_cluster_name`: Name of the Kubernetes cluster
- `kubernetes_cluster_endpoint`: Cluster API endpoint
- `database_endpoint`: Database connection endpoint
- `redis_endpoint`: Redis cache endpoint
- `storage_bucket`: Object storage bucket name
- `container_registry`: Container registry URL

## Post-Deployment Steps

### 1. Configure kubectl

#### AWS
```bash
aws eks update-kubeconfig --region $(terraform output -raw region) \
  --name $(terraform output -raw kubernetes_cluster_name)
```

#### Azure
```bash
az aks get-credentials --resource-group legacybridge-production \
  --name $(terraform output -raw kubernetes_cluster_name)
```

#### GCP
```bash
gcloud container clusters get-credentials \
  $(terraform output -raw kubernetes_cluster_name) \
  --zone $(terraform output -raw zone)
```

### 2. Deploy Kubernetes Resources

```bash
# Update image references in manifests
export REGISTRY=$(terraform output -raw container_registry)
sed -i "s|legacybridge/|${REGISTRY}/|g" ../../k8s/deployment.yaml

# Apply Kubernetes manifests
kubectl apply -f ../../k8s/
```

### 3. Create Application Secrets

```bash
# Get connection details
DB_ENDPOINT=$(terraform output -raw database_endpoint)
REDIS_ENDPOINT=$(terraform output -raw redis_endpoint)

# Create Kubernetes secrets
kubectl create secret generic legacybridge-secrets \
  --from-literal=database-url="postgresql://legacyadmin:PASSWORD@${DB_ENDPOINT}/legacybridge" \
  --from-literal=redis-url="redis://${REDIS_ENDPOINT}:6379" \
  --namespace legacybridge
```

## Cost Management

### Cost Estimation

```bash
# Install Infracost
curl -fsSL https://raw.githubusercontent.com/infracost/infracost/master/scripts/install.sh | sh

# Generate cost estimate
infracost breakdown --path . --show-skipped
```

### Cost Optimization Tips

1. **Use spot/preemptible instances** for non-critical workloads
2. **Enable auto-scaling** with appropriate limits
3. **Set up budget alerts** in your cloud provider
4. **Use reserved instances** for predictable workloads
5. **Implement lifecycle policies** for storage

## Security Considerations

1. **State File Security**:
   - Always use encrypted remote state
   - Restrict access to state storage
   - Never commit state files to version control

2. **Secrets Management**:
   - Use cloud-native secret managers
   - Rotate credentials regularly
   - Implement least privilege access

3. **Network Security**:
   - Private subnets for workloads
   - Security groups/firewall rules
   - Encryption in transit and at rest

## Troubleshooting

### Common Issues

1. **Authentication Errors**:
   ```bash
   # AWS
   aws sts get-caller-identity
   
   # Azure
   az account show
   
   # GCP
   gcloud auth application-default print-access-token
   ```

2. **Resource Limits**:
   - Check cloud provider quotas
   - Request limit increases if needed

3. **State Lock Issues**:
   ```bash
   # Force unlock (use with caution)
   terraform force-unlock <LOCK_ID>
   ```

## Destroy Infrastructure

To remove all resources:

```bash
# Remove Kubernetes resources first
kubectl delete namespace legacybridge

# Destroy Terraform resources
terraform destroy
```

**WARNING**: This will delete all resources including databases!

## Contributing

When adding new features:

1. Update the appropriate module in `modules/`
2. Ensure cross-cloud compatibility
3. Update documentation
4. Test on all supported clouds

## Support

For issues or questions:
1. Check cloud provider documentation
2. Review Terraform module documentation
3. Check GitHub issues