-- Enterprise-grade test suite for LegacyBridge transformer plugin
-- Tests request/response transformation for enterprise scenarios

local helpers = require "spec.helpers"
local cjson = require "cjson"

local PLUGIN_NAME = "legacybridge-transformer"

describe(PLUGIN_NAME .. ": (transformation)", function()
  local client
  local admin_client

  lazy_setup(function()
    local bp = helpers.get_db_utils(nil, nil, { PLUGIN_NAME })

    -- Create test service
    local service = bp.services:insert({
      protocol = "http",
      host = "httpbin.org",
      port = 80,
      path = "/anything",
      name = "transform-service"
    })

    -- Create test route
    local route = bp.routes:insert({
      hosts = { "transform.test.com" },
      service = service,
    })

    -- Add plugin to route with enterprise configuration
    bp.plugins:insert({
      name = PLUGIN_NAME,
      route = { id = route.id },
      config = {
        add_request_headers = {
          ["X-Enterprise-Version"] = "1.0.0",
          ["X-Service-Tier"] = "enterprise"
        },
        remove_request_headers = {
          "X-Kong-Request-ID",
          "X-Internal-Header"
        },
        add_response_headers = {
          ["X-API-Version"] = "v1",
          ["X-Response-Source"] = "legacybridge"
        },
        remove_response_headers = {
          "Server",
          "X-Powered-By"
        },
        transform_request_body = true,
        transform_response_body = true,
        add_request_metadata = true,
        add_response_metadata = true,
        request_transformations = {
          ["deprecated_field"] = {
            action = "remove"
          },
          ["old_name"] = {
            action = "rename",
            new_name = "new_name"
          }
        },
        response_transformations = {
          ["internal_data"] = {
            action = "remove"
          },
          ["api_version"] = {
            action = "add",
            value = "1.0"
          }
        },
        enable_cors = true,
        cors_origin = "https://enterprise.example.com",
        cors_methods = "GET,POST,PUT,DELETE,OPTIONS",
        cors_headers = "Content-Type,Authorization,X-API-Key",
        cors_max_age = 86400
      },
    })

    -- Start Kong
    assert(helpers.start_kong({
      database = "off",
      nginx_conf = "spec/fixtures/custom_nginx.template",
      plugins = "bundled," .. PLUGIN_NAME,
    }))

    client = helpers.proxy_client()
    admin_client = helpers.admin_client()
  end)

  lazy_teardown(function()
    if client then
      client:close()
    end
    if admin_client then
      admin_client:close()
    end
    helpers.stop_kong()
  end)

  describe("Enterprise Request Transformation", function()
    
    it("should add enterprise headers to requests", function()
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = {
          host = "transform.test.com"
        }
      }))
      
      assert.response(res).has.status(200)
      local body = assert.response(res).has.jsonbody()
      
      -- httpbin.org/anything echoes back the request
      assert.equal("1.0.0", body.headers["X-Enterprise-Version"])
      assert.equal("enterprise", body.headers["X-Service-Tier"])
      assert.equal("transform-service", body.headers["X-Service-Name"])
      assert.is_not_nil(body.headers["X-Request-Time"])
      assert.is_not_nil(body.headers["X-Request-ID"])
      assert.is_not_nil(body.headers["X-Correlation-ID"])
    end)

    it("should remove specified request headers", function()
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = {
          host = "transform.test.com",
          ["X-Internal-Header"] = "should-be-removed"
        }
      }))
      
      assert.response(res).has.status(200)
      local body = assert.response(res).has.jsonbody()
      
      -- Internal header should not reach backend
      assert.is_nil(body.headers["X-Internal-Header"])
    end)

    it("should transform request body with metadata", function()
      local request_data = {
        user = "test-user",
        deprecated_field = "should-be-removed",
        old_name = "should-be-renamed",
        data = "test-data"
      }
      
      local res = assert(client:send({
        method = "POST",
        path = "/test",
        headers = {
          host = "transform.test.com",
          ["Content-Type"] = "application/json",
          ["X-User-ID"] = "test-user-123"
        },
        body = cjson.encode(request_data)
      }))
      
      assert.response(res).has.status(200)
      local body = assert.response(res).has.jsonbody()
      local request_body = cjson.decode(body.data)
      
      -- Verify transformations
      assert.is_nil(request_body.deprecated_field)  -- Should be removed
      assert.is_nil(request_body.old_name)  -- Should be renamed
      assert.equal("should-be-renamed", request_body.new_name)  -- New name
      
      -- Verify metadata was added
      assert.is_not_nil(request_body._metadata)
      assert.equal("transform-service", request_body._metadata.service)
      assert.equal("test-user-123", request_body._metadata.user_id)
      assert.is_not_nil(request_body._metadata.timestamp)
      assert.is_not_nil(request_body._metadata.request_id)
    end)

    it("should handle malformed JSON gracefully", function()
      local res = assert(client:send({
        method = "POST",
        path = "/test",
        headers = {
          host = "transform.test.com",
          ["Content-Type"] = "application/json"
        },
        body = "{ invalid json }"
      }))
      
      -- Should not crash, pass through malformed JSON
      assert.response(res).has.status(200)
    end)

    it("should preserve non-JSON content types", function()
      local res = assert(client:send({
        method = "POST",
        path = "/test",
        headers = {
          host = "transform.test.com",
          ["Content-Type"] = "text/plain"
        },
        body = "plain text data"
      }))
      
      assert.response(res).has.status(200)
      local body = assert.response(res).has.jsonbody()
      assert.equal("plain text data", body.data)
    end)

  end)

  describe("Enterprise Response Transformation", function()
    
    it("should add enterprise response headers", function()
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = {
          host = "transform.test.com"
        }
      }))
      
      assert.response(res).has.status(200)
      
      -- Verify enterprise headers
      assert.response(res).has.header("X-API-Version")
      assert.response(res).has.header("X-Response-Source")
      assert.response(res).has.header("X-Service-Name")
      assert.response(res).has.header("X-Response-Time")
      assert.response(res).has.header("X-Request-ID")
      assert.response(res).has.header("X-Correlation-ID")
      
      assert.equal("v1", res.headers["X-API-Version"])
      assert.equal("legacybridge", res.headers["X-Response-Source"])
      assert.equal("transform-service", res.headers["X-Service-Name"])
    end)

    it("should add security headers", function()
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = {
          host = "transform.test.com"
        }
      }))
      
      assert.response(res).has.status(200)
      
      -- Verify security headers
      assert.response(res).has.header("X-Content-Type-Options")
      assert.response(res).has.header("X-Frame-Options")
      assert.response(res).has.header("X-XSS-Protection")
      assert.response(res).has.header("Referrer-Policy")
      
      assert.equal("nosniff", res.headers["X-Content-Type-Options"])
      assert.equal("DENY", res.headers["X-Frame-Options"])
      assert.equal("1; mode=block", res.headers["X-XSS-Protection"])
      assert.equal("strict-origin-when-cross-origin", res.headers["Referrer-Policy"])
    end)

    it("should add CORS headers", function()
      local res = assert(client:send({
        method = "OPTIONS",
        path = "/test",
        headers = {
          host = "transform.test.com",
          origin = "https://enterprise.example.com"
        }
      }))
      
      assert.response(res).has.status(200)
      
      -- Verify CORS headers
      assert.response(res).has.header("Access-Control-Allow-Origin")
      assert.response(res).has.header("Access-Control-Allow-Methods")
      assert.response(res).has.header("Access-Control-Allow-Headers")
      assert.response(res).has.header("Access-Control-Max-Age")
      
      assert.equal("https://enterprise.example.com", res.headers["Access-Control-Allow-Origin"])
      assert.equal("GET,POST,PUT,DELETE,OPTIONS", res.headers["Access-Control-Allow-Methods"])
      assert.equal("Content-Type,Authorization,X-API-Key", res.headers["Access-Control-Allow-Headers"])
      assert.equal("86400", res.headers["Access-Control-Max-Age"])
    end)

    it("should remove sensitive response headers", function()
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = {
          host = "transform.test.com"
        }
      }))
      
      assert.response(res).has.status(200)
      
      -- These headers should be removed for security
      assert.is_nil(res.headers["Server"])
      assert.is_nil(res.headers["X-Powered-By"])
    end)

    it("should add performance headers", function()
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = {
          host = "transform.test.com"
        }
      }))
      
      assert.response(res).has.status(200)
      
      -- Performance monitoring headers
      assert.response(res).has.header("X-Kong-Proxy-Latency")
      assert.response(res).has.header("X-Kong-Upstream-Latency")
    end)

  end)

  describe("Enterprise Security Tests", function()
    
    it("should prevent header injection in transformations", function()
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = {
          host = "transform.test.com",
          ["X-Malicious"] = "value\r\nX-Injected: evil"
        }
      }))
      
      assert.response(res).has.status(200)
      -- Should handle malicious headers gracefully
      assert.is_nil(res.headers["X-Injected"])
    end)

    it("should handle extremely large request bodies", function()
      local large_data = {
        data = string.rep("x", 10000),  -- 10KB of data
        array = {}
      }
      
      -- Create large array
      for i = 1, 1000 do
        table.insert(large_data.array, "item" .. i)
      end
      
      local res = assert(client:send({
        method = "POST",
        path = "/test",
        headers = {
          host = "transform.test.com",
          ["Content-Type"] = "application/json"
        },
        body = cjson.encode(large_data)
      }))
      
      -- Should handle large payloads without crashing
      assert.response(res).has.status(200)
    end)

    it("should validate against JSON injection attacks", function()
      local malicious_json = {
        ["__proto__"] = { "admin": true },
        ["constructor"] = { "prototype": { "isAdmin": true } },
        normal_field = "normal_value"
      }
      
      local res = assert(client:send({
        method = "POST",
        path = "/test",
        headers = {
          host = "transform.test.com",
          ["Content-Type"] = "application/json"
        },
        body = cjson.encode(malicious_json)
      }))
      
      assert.response(res).has.status(200)
      -- Should process safely without prototype pollution
    end)

  end)

  describe("Enterprise Performance Tests", function()
    
    it("should handle high-volume transformations efficiently", function()
      local start_time = ngx.now()
      local request_count = 100
      
      for i = 1, request_count do
        local test_data = {
          id = i,
          data = "test-data-" .. i,
          deprecated_field = "remove-me",
          old_name = "rename-me"
        }
        
        local res = assert(client:send({
          method = "POST",
          path = "/test",
          headers = {
            host = "transform.test.com",
            ["Content-Type"] = "application/json",
            ["X-Request-ID"] = "perf-test-" .. i
          },
          body = cjson.encode(test_data)
        }))
        
        assert.response(res).has.status(200)
      end
      
      local end_time = ngx.now()
      local duration = end_time - start_time
      
      -- Should complete within reasonable time for enterprise performance
      assert.is_true(duration < 15.0, "Performance test took too long: " .. duration .. "s")
    end)

    it("should maintain transformation accuracy under load", function()
      local concurrent_requests = 50
      local success_count = 0
      
      for i = 1, concurrent_requests do
        local test_data = {
          request_id = i,
          deprecated_field = "should-be-removed",
          old_name = "should-be-renamed"
        }
        
        local res = assert(client:send({
          method = "POST",
          path = "/test",
          headers = {
            host = "transform.test.com",
            ["Content-Type"] = "application/json",
            ["X-User-ID"] = "load-test-user-" .. i
          },
          body = cjson.encode(test_data)
        }))
        
        if res.status == 200 then
          local body = assert.response(res).has.jsonbody()
          local request_body = cjson.decode(body.data)
          
          -- Verify transformations were applied correctly
          if request_body._metadata and 
             request_body.new_name == "should-be-renamed" and
             request_body.deprecated_field == nil then
            success_count = success_count + 1
          end
        end
      end
      
      -- All transformations should be accurate
      assert.equal(concurrent_requests, success_count)
    end)

  end)

  describe("Enterprise Monitoring and Observability", function()
    
    it("should provide correlation IDs for request tracing", function()
      local correlation_id = "test-correlation-123"
      
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = {
          host = "transform.test.com",
          ["X-Correlation-ID"] = correlation_id
        }
      }))
      
      assert.response(res).has.status(200)
      assert.response(res).has.header("X-Correlation-ID")
      assert.equal(correlation_id, res.headers["X-Correlation-ID"])
    end)

    it("should generate correlation IDs when not provided", function()
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = {
          host = "transform.test.com"
        }
      }))
      
      assert.response(res).has.status(200)
      assert.response(res).has.header("X-Correlation-ID")
      assert.is_not_nil(res.headers["X-Correlation-ID"])
      assert.is_true(string.len(res.headers["X-Correlation-ID"]) > 0)
    end)

  end)

end)
