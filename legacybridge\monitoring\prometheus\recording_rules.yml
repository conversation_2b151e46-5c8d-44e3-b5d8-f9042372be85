groups:
  - name: legacybridge_recording_rules
    interval: 30s
    rules:
      # Request rate aggregations
      - record: instance:http_requests:rate5m
        expr: |
          sum(rate(http_requests_total{job="legacybridge-backend"}[5m])) by (instance)

      - record: job:http_requests:rate5m
        expr: |
          sum(rate(http_requests_total{job="legacybridge-backend"}[5m])) by (job)

      - record: method_status:http_requests:rate5m
        expr: |
          sum(rate(http_requests_total{job="legacybridge-backend"}[5m])) by (method, status)

      # Latency aggregations
      - record: instance:http_request_duration:p95_5m
        expr: |
          histogram_quantile(0.95,
            sum(rate(http_request_duration_seconds_bucket{job="legacybridge-backend"}[5m])) by (instance, le)
          )

      - record: instance:http_request_duration:p99_5m
        expr: |
          histogram_quantile(0.99,
            sum(rate(http_request_duration_seconds_bucket{job="legacybridge-backend"}[5m])) by (instance, le)
          )

      - record: method:http_request_duration:p95_5m
        expr: |
          histogram_quantile(0.95,
            sum(rate(http_request_duration_seconds_bucket{job="legacybridge-backend"}[5m])) by (method, le)
          )

      # Error rate aggregations
      - record: instance:http_errors:rate5m
        expr: |
          sum(rate(http_requests_total{job="legacybridge-backend",status=~"5.."}[5m])) by (instance)

      - record: method:http_errors:rate5m
        expr: |
          sum(rate(http_requests_total{job="legacybridge-backend",status=~"5.."}[5m])) by (method)

      # Conversion metrics aggregations
      - record: format:conversions:rate5m
        expr: |
          sum(rate(conversion_total{job="legacybridge-backend"}[5m])) by (format, status)

      - record: format:conversion_duration:p95_5m
        expr: |
          histogram_quantile(0.95,
            sum(rate(conversion_processing_time_seconds_bucket{job="legacybridge-backend"}[5m])) by (format, le)
          )

      # Resource utilization aggregations
      - record: namespace:container_memory_usage_bytes:sum
        expr: |
          sum(container_memory_working_set_bytes{namespace="legacybridge", container!=""}) by (namespace)

      - record: namespace:container_cpu_usage_seconds:sum_rate
        expr: |
          sum(rate(container_cpu_usage_seconds_total{namespace="legacybridge", container!=""}[5m])) by (namespace)

      - record: pod:container_memory_usage_bytes:sum
        expr: |
          sum(container_memory_working_set_bytes{namespace="legacybridge", container!=""}) by (pod, namespace)

      - record: pod:container_cpu_usage_seconds:sum_rate
        expr: |
          sum(rate(container_cpu_usage_seconds_total{namespace="legacybridge", container!=""}[5m])) by (pod, namespace)

      # Database connection pool metrics
      - record: job:pg_connections_active:ratio
        expr: |
          pg_stat_database_numbackends{datname="legacybridge"} 
          / 
          pg_settings_max_connections

      - record: job:pg_transactions:rate5m
        expr: |
          sum(rate(pg_stat_database_xact_commit{datname="legacybridge"}[5m]) + rate(pg_stat_database_xact_rollback{datname="legacybridge"}[5m]))

      # Redis metrics
      - record: job:redis_memory_usage:ratio
        expr: |
          redis_memory_used_bytes{job="redis"} 
          / 
          redis_memory_max_bytes{job="redis"}

      - record: job:redis_commands:rate5m
        expr: |
          sum(rate(redis_commands_total{job="redis"}[5m])) by (cmd)

      # Cluster-wide aggregations
      - record: cluster:http_requests:rate5m
        expr: |
          sum(rate(http_requests_total{job="legacybridge-backend"}[5m]))

      - record: cluster:http_errors:rate5m
        expr: |
          sum(rate(http_requests_total{job="legacybridge-backend",status=~"5.."}[5m]))

      - record: cluster:http_error_ratio:rate5m
        expr: |
          cluster:http_errors:rate5m / cluster:http_requests:rate5m

      - record: cluster:conversion_success_ratio:rate5m
        expr: |
          sum(rate(conversion_total{job="legacybridge-backend",status="success"}[5m]))
          /
          sum(rate(conversion_total{job="legacybridge-backend"}[5m]))

      # Business metrics
      - record: daily:conversions:total
        expr: |
          sum(increase(conversion_total{job="legacybridge-backend"}[1d])) by (format)

      - record: daily:unique_users:total
        expr: |
          count(count by (user_id) (increase(user_activity_total{job="legacybridge-backend"}[1d])))