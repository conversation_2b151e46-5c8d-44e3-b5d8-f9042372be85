#!/bin/bash
# Comprehensive Test Automation Script for LegacyBridge
# Runs all test suites with proper error handling and reporting

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TEST_TYPE=${TEST_TYPE:-"all"}
ENVIRONMENT=${ENVIRONMENT:-"local"}
COVERAGE_THRESHOLD=${COVERAGE_THRESHOLD:-80}
PARALLEL=${PARALLEL:-"true"}
REPORT_DIR="test-results"
COVERAGE_DIR="coverage"

# Create directories
mkdir -p "$REPORT_DIR" "$COVERAGE_DIR"

# Test status tracking
TESTS_FAILED=0
TOTAL_TESTS=0
PASSED_TESTS=0

echo -e "${BLUE}======================================${NC}"
echo -e "${BLUE}LegacyBridge Test Suite Runner${NC}"
echo -e "${BLUE}======================================${NC}"
echo "Test Type: $TEST_TYPE"
echo "Environment: $ENVIRONMENT"
echo "Coverage Threshold: $COVERAGE_THRESHOLD%"
echo "Parallel Execution: $PARALLEL"
echo ""

# Function to run a test suite
run_test_suite() {
    local suite_name=$1
    local command=$2
    
    echo -e "${YELLOW}Running $suite_name...${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if eval "$command"; then
        echo -e "${GREEN}✓ $suite_name passed${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ $suite_name failed${NC}"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    echo ""
}

# Frontend Unit Tests
run_frontend_unit_tests() {
    echo -e "${BLUE}Frontend Unit Tests${NC}"
    echo "===================="
    
    cd frontend || cd .
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo "Installing frontend dependencies..."
        npm ci
    fi
    
    # Run tests with coverage
    run_test_suite "Frontend Unit Tests" \
        "npm run test:ci -- --coverage --coverageDirectory=../$COVERAGE_DIR/frontend --json --outputFile=../$REPORT_DIR/frontend-unit.json"
    
    # Check coverage threshold
    if [ -f "../$COVERAGE_DIR/frontend/coverage-summary.json" ]; then
        coverage=$(jq -r '.total.lines.pct' "../$COVERAGE_DIR/frontend/coverage-summary.json")
        if (( $(echo "$coverage < $COVERAGE_THRESHOLD" | bc -l) )); then
            echo -e "${RED}Coverage ${coverage}% is below threshold ${COVERAGE_THRESHOLD}%${NC}"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        else
            echo -e "${GREEN}Coverage ${coverage}% meets threshold${NC}"
        fi
    fi
    
    cd - > /dev/null
}

# Backend Unit Tests
run_backend_unit_tests() {
    echo -e "${BLUE}Backend Unit Tests${NC}"
    echo "=================="
    
    cd src-tauri || cd .
    
    # Run Rust tests
    run_test_suite "Backend Unit Tests" \
        "cargo test --all-features -- --test-threads=4 --nocapture"
    
    # Run with coverage if tarpaulin is installed
    if command -v cargo-tarpaulin >/dev/null 2>&1; then
        echo "Generating backend coverage report..."
        cargo tarpaulin --out Xml --output-dir "../$COVERAGE_DIR/backend" || echo "Coverage generation failed"
    fi
    
    cd - > /dev/null
}

# Integration Tests
run_integration_tests() {
    echo -e "${BLUE}Integration Tests${NC}"
    echo "================="
    
    # Check if Docker is available
    if ! command -v docker >/dev/null 2>&1; then
        echo -e "${RED}Docker not found. Skipping integration tests.${NC}"
        return
    fi
    
    # Start test environment
    echo "Starting test environment..."
    docker-compose -f docker-compose.test.yml up -d
    
    # Wait for services to be ready
    echo "Waiting for services..."
    sleep 10
    
    # Run integration test suite
    run_test_suite "API Integration Tests" \
        "npm run test:integration -- --reporter json --reporter-options output=./$REPORT_DIR/integration.json"
    
    # Cleanup
    echo "Cleaning up test environment..."
    docker-compose -f docker-compose.test.yml down -v
}

# E2E Tests
run_e2e_tests() {
    echo -e "${BLUE}End-to-End Tests${NC}"
    echo "================"
    
    # Install Playwright if needed
    if [ ! -d "node_modules/@playwright" ]; then
        echo "Installing Playwright..."
        npx playwright install --with-deps
    fi
    
    # Start application if testing locally
    if [ "$ENVIRONMENT" = "local" ]; then
        echo "Starting application for E2E tests..."
        npm run dev &
        APP_PID=$!
        sleep 15
    fi
    
    # Run E2E tests
    run_test_suite "E2E Tests" \
        "npm run test:e2e -- --reporter=json --output=$REPORT_DIR/e2e-results.json"
    
    # Generate HTML report
    npx playwright show-report playwright-report || true
    
    # Stop application if started
    if [ "$ENVIRONMENT" = "local" ] && [ -n "${APP_PID:-}" ]; then
        kill $APP_PID || true
    fi
}

# Performance Tests
run_performance_tests() {
    echo -e "${BLUE}Performance Tests${NC}"
    echo "================="
    
    # Check if k6 is installed
    if ! command -v k6 >/dev/null 2>&1; then
        echo -e "${YELLOW}k6 not found. Installing...${NC}"
        sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
        echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
        sudo apt-get update
        sudo apt-get install k6
    fi
    
    # Run performance tests
    run_test_suite "Load Test" \
        "k6 run --out json=$REPORT_DIR/k6-results.json tests/performance/load-test.js"
    
    run_test_suite "Stress Test" \
        "k6 run --out json=$REPORT_DIR/k6-stress-results.json tests/performance/stress-test.js"
    
    # Generate performance report
    if [ -f "$REPORT_DIR/k6-results.json" ]; then
        echo "Generating performance report..."
        # Process k6 results and generate HTML report
        node scripts/generate-perf-report.js "$REPORT_DIR/k6-results.json" > "$REPORT_DIR/performance-report.html"
    fi
}

# Security Tests
run_security_tests() {
    echo -e "${BLUE}Security Tests${NC}"
    echo "=============="
    
    # Run security test suite
    run_test_suite "Security Tests" \
        "./ci-cd/scripts/security-scan.sh"
    
    # Run OWASP ZAP if available
    if command -v zap-cli >/dev/null 2>&1; then
        echo "Running OWASP ZAP scan..."
        zap-cli quick-scan --self-contained \
            --start-options '-config api.disablekey=true' \
            "http://localhost:3000" || echo "ZAP scan completed with findings"
    fi
}

# Smoke Tests
run_smoke_tests() {
    echo -e "${BLUE}Smoke Tests${NC}"
    echo "==========="
    
    local base_url=""
    case "$ENVIRONMENT" in
        "local")
            base_url="http://localhost:3000"
            ;;
        "staging")
            base_url="https://staging.legacybridge.com"
            ;;
        "production")
            base_url="https://app.legacybridge.com"
            ;;
    esac
    
    # Health check
    run_test_suite "Health Check" \
        "curl -f -s -o /dev/null -w '%{http_code}' $base_url/api/health | grep -q 200"
    
    # API availability
    run_test_suite "API Availability" \
        "curl -f -s -o /dev/null -w '%{http_code}' $base_url/api/v1/status | grep -q 200"
    
    # Frontend availability
    run_test_suite "Frontend Availability" \
        "curl -f -s -o /dev/null -w '%{http_code}' $base_url | grep -q 200"
}

# Accessibility Tests
run_accessibility_tests() {
    echo -e "${BLUE}Accessibility Tests${NC}"
    echo "=================="
    
    # Run axe-core tests
    if command -v axe >/dev/null 2>&1; then
        run_test_suite "Accessibility Tests" \
            "npm run test:a11y -- --reporter json > $REPORT_DIR/a11y-results.json"
    else
        echo "Installing axe-cli..."
        npm install -g @axe-core/cli
        run_test_suite "Accessibility Tests" \
            "axe http://localhost:3000 --save $REPORT_DIR/a11y-results.json"
    fi
}

# Visual Regression Tests
run_visual_tests() {
    echo -e "${BLUE}Visual Regression Tests${NC}"
    echo "====================="
    
    # Run Percy tests if configured
    if [ -n "${PERCY_TOKEN:-}" ]; then
        run_test_suite "Visual Regression Tests" \
            "npm run test:visual"
    else
        echo -e "${YELLOW}PERCY_TOKEN not set. Skipping visual regression tests.${NC}"
    fi
}

# Contract Tests
run_contract_tests() {
    echo -e "${BLUE}Contract Tests${NC}"
    echo "=============="
    
    # Run Pact tests
    run_test_suite "API Contract Tests" \
        "npm run test:contract -- --reporter json > $REPORT_DIR/contract-results.json"
}

# Generate Test Report
generate_test_report() {
    echo -e "${BLUE}Generating Test Report...${NC}"
    
    cat > "$REPORT_DIR/test-summary.md" << EOF
# Test Execution Summary

**Date:** $(date)
**Environment:** $ENVIRONMENT
**Test Type:** $TEST_TYPE

## Results Overview

- **Total Test Suites:** $TOTAL_TESTS
- **Passed:** $PASSED_TESTS
- **Failed:** $TESTS_FAILED
- **Success Rate:** $(awk "BEGIN {printf \"%.2f\", ($PASSED_TESTS/$TOTAL_TESTS)*100}")%

## Test Results

| Test Suite | Status | Duration | Coverage |
|------------|--------|----------|----------|
EOF

    # Add test results to report
    for result_file in "$REPORT_DIR"/*.json; do
        if [ -f "$result_file" ]; then
            suite_name=$(basename "$result_file" .json)
            # Parse JSON results and add to report
            echo "| $suite_name | ✓ | - | - |" >> "$REPORT_DIR/test-summary.md"
        fi
    done
    
    echo "" >> "$REPORT_DIR/test-summary.md"
    echo "## Coverage Summary" >> "$REPORT_DIR/test-summary.md"
    
    # Add coverage information
    if [ -f "$COVERAGE_DIR/frontend/coverage-summary.json" ]; then
        echo "### Frontend Coverage" >> "$REPORT_DIR/test-summary.md"
        jq -r '.total | "- Lines: \(.lines.pct)%\n- Statements: \(.statements.pct)%\n- Functions: \(.functions.pct)%\n- Branches: \(.branches.pct)%"' \
            "$COVERAGE_DIR/frontend/coverage-summary.json" >> "$REPORT_DIR/test-summary.md"
    fi
    
    # Generate HTML report
    if command -v allure >/dev/null 2>&1; then
        allure generate "$REPORT_DIR" -o "$REPORT_DIR/html-report" --clean
    fi
    
    echo -e "${GREEN}Test report generated at: $REPORT_DIR/test-summary.md${NC}"
}

# Main execution
case "$TEST_TYPE" in
    "all")
        if [ "$PARALLEL" = "true" ]; then
            # Run tests in parallel
            run_frontend_unit_tests &
            run_backend_unit_tests &
            wait
            run_integration_tests
            run_e2e_tests
            run_performance_tests
            run_security_tests
            run_accessibility_tests
            run_visual_tests
            run_contract_tests
        else
            # Run tests sequentially
            run_frontend_unit_tests
            run_backend_unit_tests
            run_integration_tests
            run_e2e_tests
            run_performance_tests
            run_security_tests
            run_accessibility_tests
            run_visual_tests
            run_contract_tests
        fi
        ;;
    "unit")
        run_frontend_unit_tests
        run_backend_unit_tests
        ;;
    "integration")
        run_integration_tests
        ;;
    "e2e")
        run_e2e_tests
        ;;
    "performance")
        run_performance_tests
        ;;
    "security")
        run_security_tests
        ;;
    "smoke")
        run_smoke_tests
        ;;
    "accessibility")
        run_accessibility_tests
        ;;
    "visual")
        run_visual_tests
        ;;
    "contract")
        run_contract_tests
        ;;
    *)
        echo -e "${RED}Unknown test type: $TEST_TYPE${NC}"
        echo "Valid options: all, unit, integration, e2e, performance, security, smoke, accessibility, visual, contract"
        exit 1
        ;;
esac

# Generate report
generate_test_report

# Exit with appropriate code
if [ "$TESTS_FAILED" -gt 0 ]; then
    echo -e "${RED}Test suite failed! $TESTS_FAILED test(s) failed.${NC}"
    exit 1
else
    echo -e "${GREEN}All tests passed! 🎉${NC}"
    exit 0
fi