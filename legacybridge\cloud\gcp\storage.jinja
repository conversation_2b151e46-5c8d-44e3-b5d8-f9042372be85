{#
Copyright 2024 LegacyBridge
Cloud Storage Template for Google Cloud Deployment Manager
#}

resources:
# Storage Bucket
- name: {{ env["name"] }}-bucket
  type: storage.v1.bucket
  properties:
    name: {{ env["name"] }}-{{ env["project"] }}-files
    location: {{ properties["location"] }}
    storageClass: {{ properties["storageClass"] }}
    
    # Versioning
    versioning:
      enabled: {{ properties["versioning"] }}
    
    # Lifecycle Rules
    lifecycle:
      rule: {{ properties["lifecycleRules"] }}
    
    # Encryption
    encryption:
      defaultKmsKeyName: projects/{{ env["project"] }}/locations/{{ properties["location"] | lower }}/keyRings/legacybridge/cryptoKeys/storage
    
    # Access Control
    iamConfiguration:
      uniformBucketLevelAccess:
        enabled: true
    
    # CORS Configuration
    cors:
    - origin: ["*"]
      method: ["GET", "HEAD", "PUT", "POST", "DELETE"]
      responseHeader: ["*"]
      maxAgeSeconds: 3600
    
    # Labels
    labels:
      environment: production
      application: legacybridge

# Bucket IAM Policy
- name: {{ env["name"] }}-bucket-iam
  type: storage.v1.bucket.iam
  properties:
    bucket: $(ref.{{ env["name"] }}-bucket.name)
    bindings:
    - role: roles/storage.objectViewer
      members:
      - allAuthenticatedUsers
    - role: roles/storage.objectAdmin
      members:
      - serviceAccount:{{ env["project"] }}@appspot.gserviceaccount.com

outputs:
- name: bucketName
  value: $(ref.{{ env["name"] }}-bucket.name)
- name: bucketUrl
  value: https://storage.googleapis.com/$(ref.{{ env["name"] }}-bucket.name)