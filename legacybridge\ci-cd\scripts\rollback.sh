#!/bin/bash
# Enterprise Rollback Script for LegacyBridge
# Handles quick rollback to previous version with minimal downtime

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${ENVIRONMENT:-"production"}
NAMESPACE=${NAMESPACE:-"legacybridge"}
ROLLBACK_STRATEGY=${ROLLBACK_STRATEGY:-"auto"}
TARGET_VERSION=${TARGET_VERSION:-""}
DRY_RUN=${DRY_RUN:-"false"}
BACKUP_BEFORE_ROLLBACK=${BACKUP_BEFORE_ROLLBACK:-"true"}
NOTIFY_TEAMS=${NOTIFY_TEAMS:-"true"}

# Cloud provider
CLOUD_PROVIDER=${CLOUD_PROVIDER:-"aws"}

echo -e "${RED}======================================${NC}"
echo -e "${RED}LegacyBridge Emergency Rollback${NC}"
echo -e "${RED}======================================${NC}"
echo "Environment: $ENVIRONMENT"
echo "Namespace: $NAMESPACE"
echo "Strategy: $ROLLBACK_STRATEGY"
echo "Target Version: ${TARGET_VERSION:-auto-detect}"
echo ""

# Function to get current version
get_current_version() {
    kubectl get deployment legacybridge-backend \
        -n $NAMESPACE \
        -o jsonpath='{.spec.template.spec.containers[0].image}' | \
        awk -F: '{print $2}'
}

# Function to get rollback history
get_rollback_history() {
    echo -e "${YELLOW}Deployment History:${NC}"
    kubectl rollout history deployment/legacybridge-backend -n $NAMESPACE
    kubectl rollout history deployment/legacybridge-frontend -n $NAMESPACE
}

# Function to find previous stable version
find_previous_version() {
    echo -e "${YELLOW}Finding previous stable version...${NC}"
    
    # Get deployment history
    local history=$(kubectl rollout history deployment/legacybridge-backend \
        -n $NAMESPACE \
        --output=json)
    
    # Extract previous revision
    local previous_revision=$(echo "$history" | \
        jq -r '.metadata.annotations."deployment.kubernetes.io/revision"' | \
        awk '{print $1-1}')
    
    # Get image from previous revision
    local previous_image=$(kubectl rollout history deployment/legacybridge-backend \
        -n $NAMESPACE \
        --revision=$previous_revision \
        -o jsonpath='{.spec.template.spec.containers[0].image}')
    
    echo "$previous_image" | awk -F: '{print $2}'
}

# Function to backup current state
backup_current_state() {
    if [ "$BACKUP_BEFORE_ROLLBACK" = "true" ]; then
        echo -e "${YELLOW}Backing up current state...${NC}"
        
        local backup_dir="/tmp/legacybridge-rollback-$(date +%Y%m%d-%H%M%S)"
        mkdir -p "$backup_dir"
        
        # Backup deployments
        kubectl get deployment -n $NAMESPACE -o yaml > "$backup_dir/deployments.yaml"
        
        # Backup services
        kubectl get service -n $NAMESPACE -o yaml > "$backup_dir/services.yaml"
        
        # Backup configmaps
        kubectl get configmap -n $NAMESPACE -o yaml > "$backup_dir/configmaps.yaml"
        
        # Backup current pod logs
        for pod in $(kubectl get pods -n $NAMESPACE -o jsonpath='{.items[*].metadata.name}'); do
            kubectl logs $pod -n $NAMESPACE --tail=1000 > "$backup_dir/logs-$pod.txt" || true
        done
        
        echo -e "${GREEN}Backup saved to: $backup_dir${NC}"
        
        # Upload backup to cloud storage
        case "$CLOUD_PROVIDER" in
            "aws")
                aws s3 cp "$backup_dir" "s3://legacybridge-backups/rollback/$(basename $backup_dir)" --recursive
                ;;
            "azure")
                az storage blob upload-batch \
                    --destination "rollback/$(basename $backup_dir)" \
                    --source "$backup_dir" \
                    --account-name legacybridgebackups
                ;;
            "gcp")
                gsutil -m cp -r "$backup_dir" "gs://legacybridge-backups/rollback/"
                ;;
        esac
    fi
}

# Function to perform kubectl rollback
rollback_kubectl() {
    echo -e "${YELLOW}Performing kubectl rollback...${NC}"
    
    for component in backend frontend; do
        echo "Rolling back legacybridge-$component..."
        
        if [ -n "$TARGET_VERSION" ]; then
            # Rollback to specific version
            kubectl set image deployment/legacybridge-$component \
                $component=${REGISTRY:-ghcr.io}/legacybridge/$component:$TARGET_VERSION \
                -n $NAMESPACE \
                --record
        else
            # Rollback to previous revision
            kubectl rollout undo deployment/legacybridge-$component \
                -n $NAMESPACE
        fi
        
        # Wait for rollback to complete
        kubectl rollout status deployment/legacybridge-$component \
            -n $NAMESPACE \
            --timeout=300s
    done
}

# Function to perform blue-green rollback
rollback_blue_green() {
    echo -e "${YELLOW}Performing blue-green rollback...${NC}"
    
    # Find previous deployment
    local previous_deployment=""
    for deployment in $(kubectl get deployments -n $NAMESPACE -o name | grep -E "legacybridge-(backend|frontend)-v"); do
        if kubectl get $deployment -n $NAMESPACE >/dev/null 2>&1; then
            previous_deployment=$deployment
            break
        fi
    done
    
    if [ -z "$previous_deployment" ]; then
        echo -e "${RED}No previous deployment found for blue-green rollback${NC}"
        exit 1
    fi
    
    echo "Switching traffic back to $previous_deployment..."
    
    # Switch services back
    for component in frontend backend; do
        local version=$(echo $previous_deployment | grep -oE "v[0-9]+\.[0-9]+\.[0-9]+")
        kubectl patch service legacybridge-$component-service \
            -n $NAMESPACE \
            -p '{"spec":{"selector":{"version":"'$version'"}}}'
    done
}

# Function to perform canary rollback
rollback_canary() {
    echo -e "${YELLOW}Performing canary rollback...${NC}"
    
    # Remove canary deployments
    for component in backend frontend; do
        kubectl delete deployment legacybridge-$component-canary \
            -n $NAMESPACE \
            --ignore-not-found=true
    done
    
    # Remove traffic splitting rules
    if kubectl get virtualservice -n $NAMESPACE >/dev/null 2>&1; then
        # Istio
        kubectl delete virtualservice legacybridge-backend -n $NAMESPACE --ignore-not-found=true
        kubectl delete virtualservice legacybridge-frontend -n $NAMESPACE --ignore-not-found=true
    else
        # Remove ingress annotations
        kubectl annotate ingress legacybridge-ingress \
            -n $NAMESPACE \
            nginx.ingress.kubernetes.io/canary- \
            nginx.ingress.kubernetes.io/canary-weight- \
            --overwrite
    fi
    
    # Scale up stable deployment if needed
    for component in backend frontend; do
        local current_replicas=$(kubectl get deployment legacybridge-$component \
            -n $NAMESPACE \
            -o jsonpath='{.status.replicas}')
        
        if [ "$current_replicas" -eq "0" ]; then
            kubectl scale deployment legacybridge-$component \
                -n $NAMESPACE \
                --replicas=3
        fi
    done
}

# Function to verify rollback
verify_rollback() {
    echo -e "${YELLOW}Verifying rollback...${NC}"
    
    local retries=30
    local all_healthy=false
    
    while [ $retries -gt 0 ] && [ "$all_healthy" = "false" ]; do
        all_healthy=true
        
        for component in backend frontend; do
            # Check deployment status
            local ready=$(kubectl get deployment legacybridge-$component \
                -n $NAMESPACE \
                -o jsonpath='{.status.conditions[?(@.type=="Available")].status}')
            
            if [ "$ready" != "True" ]; then
                echo "Deployment legacybridge-$component not ready yet..."
                all_healthy=false
            fi
            
            # Check pod status
            local pods_ready=$(kubectl get pods \
                -n $NAMESPACE \
                -l app=legacybridge,component=$component \
                -o jsonpath='{.items[*].status.conditions[?(@.type=="Ready")].status}' | \
                tr ' ' '\n' | grep -c "True" || echo "0")
            
            local expected_pods=$(kubectl get deployment legacybridge-$component \
                -n $NAMESPACE \
                -o jsonpath='{.spec.replicas}')
            
            if [ "$pods_ready" -lt "$expected_pods" ]; then
                echo "$component: $pods_ready/$expected_pods pods ready"
                all_healthy=false
            fi
        done
        
        if [ "$all_healthy" = "false" ]; then
            retries=$((retries - 1))
            echo "Waiting for rollback to stabilize... ($retries retries left)"
            sleep 10
        fi
    done
    
    if [ "$all_healthy" = "true" ]; then
        echo -e "${GREEN}Rollback completed successfully!${NC}"
        return 0
    else
        echo -e "${RED}Rollback verification failed!${NC}"
        return 1
    fi
}

# Function to run post-rollback tests
run_post_rollback_tests() {
    echo -e "${YELLOW}Running post-rollback tests...${NC}"
    
    # Basic health checks
    for component in backend frontend; do
        local service_ip=$(kubectl get service legacybridge-$component-service \
            -n $NAMESPACE \
            -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        
        if [ -n "$service_ip" ]; then
            if curl -sf "http://$service_ip/health" >/dev/null; then
                echo -e "${GREEN}$component health check passed${NC}"
            else
                echo -e "${RED}$component health check failed${NC}"
            fi
        fi
    done
    
    # Run smoke tests
    if [ -f "./ci-cd/scripts/run-tests.sh" ]; then
        TEST_TYPE=smoke ENVIRONMENT=$ENVIRONMENT ./ci-cd/scripts/run-tests.sh || true
    fi
}

# Function to update monitoring after rollback
update_monitoring_rollback() {
    echo -e "${YELLOW}Updating monitoring for rollback...${NC}"
    
    # Create rollback annotation
    if [ -n "${GRAFANA_URL:-}" ] && [ -n "${GRAFANA_API_KEY:-}" ]; then
        curl -X POST "${GRAFANA_URL}/api/annotations" \
            -H "Authorization: Bearer ${GRAFANA_API_KEY}" \
            -H "Content-Type: application/json" \
            -d "{
                \"dashboardId\": 1,
                \"tags\": [\"rollback\", \"emergency\"],
                \"text\": \"Emergency rollback from $(get_current_version) to ${TARGET_VERSION:-previous}\"
            }"
    fi
    
    # Alert monitoring team
    if [ -n "${PAGERDUTY_TOKEN:-}" ]; then
        curl -X POST "https://events.pagerduty.com/v2/enqueue" \
            -H "Content-Type: application/json" \
            -d "{
                \"routing_key\": \"${PAGERDUTY_TOKEN}\",
                \"event_action\": \"trigger\",
                \"payload\": {
                    \"summary\": \"Emergency rollback performed on $ENVIRONMENT\",
                    \"severity\": \"warning\",
                    \"source\": \"legacybridge-rollback\",
                    \"custom_details\": {
                        \"environment\": \"$ENVIRONMENT\",
                        \"from_version\": \"$(get_current_version)\",
                        \"to_version\": \"${TARGET_VERSION:-previous}\"
                    }
                }
            }"
    fi
}

# Function to notify teams about rollback
notify_rollback() {
    local status=$1
    
    if [ "$NOTIFY_TEAMS" = "true" ]; then
        echo -e "${YELLOW}Notifying teams about rollback...${NC}"
        
        local message="Emergency rollback on $ENVIRONMENT environment"
        local color="warning"
        local emoji="⚠️"
        
        if [ "$status" = "success" ]; then
            message="Rollback completed successfully"
            color="good"
            emoji="✅"
        elif [ "$status" = "failed" ]; then
            message="Rollback failed - manual intervention required!"
            color="danger"
            emoji="🚨"
        fi
        
        # Slack notification
        if [ -n "${SLACK_WEBHOOK:-}" ]; then
            curl -X POST "$SLACK_WEBHOOK" \
                -H "Content-Type: application/json" \
                -d "{
                    \"text\": \"$emoji $message\",
                    \"attachments\": [{
                        \"color\": \"$color\",
                        \"fields\": [
                            {\"title\": \"Environment\", \"value\": \"$ENVIRONMENT\", \"short\": true},
                            {\"title\": \"From Version\", \"value\": \"$(get_current_version)\", \"short\": true},
                            {\"title\": \"To Version\", \"value\": \"${TARGET_VERSION:-previous}\", \"short\": true},
                            {\"title\": \"Initiated By\", \"value\": \"${USER:-system}\", \"short\": true},
                            {\"title\": \"Timestamp\", \"value\": \"$(date)\", \"short\": false}
                        ]
                    }]
                }"
        fi
        
        # Email notification
        if [ -n "${EMAIL_ALERTS:-}" ]; then
            echo "$message - Check deployment logs for details" | \
                mail -s "[$ENVIRONMENT] LegacyBridge Rollback $status" "$EMAIL_ALERTS"
        fi
    fi
}

# Function to generate rollback report
generate_rollback_report() {
    echo -e "${YELLOW}Generating rollback report...${NC}"
    
    local report_file="/tmp/rollback-report-$(date +%Y%m%d-%H%M%S).md"
    
    cat > "$report_file" << EOF
# Rollback Report

**Date:** $(date)
**Environment:** $ENVIRONMENT
**Initiated By:** ${USER:-system}
**Reason:** ${ROLLBACK_REASON:-Emergency rollback}

## Version Information

- **From Version:** $(get_current_version)
- **To Version:** ${TARGET_VERSION:-previous}

## Rollback Timeline

$(kubectl get events -n $NAMESPACE --sort-by='.lastTimestamp' | tail -20)

## Current Deployment Status

### Backend
$(kubectl get deployment legacybridge-backend -n $NAMESPACE)

### Frontend
$(kubectl get deployment legacybridge-frontend -n $NAMESPACE)

## Pod Status

$(kubectl get pods -n $NAMESPACE -l app=legacybridge)

## Recommendations

1. Investigate the root cause of the deployment failure
2. Review deployment logs and metrics
3. Update runbooks based on lessons learned
4. Schedule post-mortem meeting

EOF

    echo -e "${GREEN}Rollback report saved to: $report_file${NC}"
    
    # Upload report
    case "$CLOUD_PROVIDER" in
        "aws")
            aws s3 cp "$report_file" "s3://legacybridge-reports/rollbacks/"
            ;;
        "azure")
            az storage blob upload \
                --file "$report_file" \
                --container-name rollback-reports \
                --name "$(basename $report_file)"
            ;;
        "gcp")
            gsutil cp "$report_file" "gs://legacybridge-reports/rollbacks/"
            ;;
    esac
}

# Dry run function
dry_run_rollback() {
    echo -e "${YELLOW}Running in DRY RUN mode...${NC}"
    echo "Would perform the following actions:"
    echo "1. Backup current deployment state"
    echo "2. Identify target version: ${TARGET_VERSION:-auto-detect previous}"
    echo "3. Rollback strategy: $ROLLBACK_STRATEGY"
    echo "4. Verify rollback health"
    echo "5. Run post-rollback tests"
    echo "6. Update monitoring and notify teams"
    
    get_rollback_history
}

# Main rollback function
perform_rollback() {
    echo -e "${RED}Starting emergency rollback...${NC}"
    
    # Get current version before rollback
    local current_version=$(get_current_version)
    echo "Current version: $current_version"
    
    # Determine target version
    if [ -z "$TARGET_VERSION" ]; then
        TARGET_VERSION=$(find_previous_version)
        echo "Auto-detected previous version: $TARGET_VERSION"
    fi
    
    # Backup current state
    backup_current_state
    
    # Perform rollback based on strategy
    case "$ROLLBACK_STRATEGY" in
        "auto"|"kubectl")
            rollback_kubectl
            ;;
        "blue-green")
            rollback_blue_green
            ;;
        "canary")
            rollback_canary
            ;;
        *)
            echo -e "${RED}Unknown rollback strategy: $ROLLBACK_STRATEGY${NC}"
            exit 1
            ;;
    esac
    
    # Verify rollback
    if verify_rollback; then
        run_post_rollback_tests
        update_monitoring_rollback
        generate_rollback_report
        notify_rollback "success"
        echo -e "${GREEN}Rollback completed successfully!${NC}"
        exit 0
    else
        notify_rollback "failed"
        echo -e "${RED}Rollback failed! Manual intervention required.${NC}"
        exit 1
    fi
}

# Main execution
if [ "$DRY_RUN" = "true" ]; then
    dry_run_rollback
else
    # Confirm rollback in production
    if [ "$ENVIRONMENT" = "production" ] && [ -t 0 ]; then
        echo -e "${RED}WARNING: You are about to rollback the PRODUCTION environment!${NC}"
        echo -n "Type 'ROLLBACK' to confirm: "
        read confirmation
        if [ "$confirmation" != "ROLLBACK" ]; then
            echo "Rollback cancelled."
            exit 0
        fi
    fi
    
    perform_rollback
fi