'use client';

import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Package, Settings, TestTube2, Rocket, Code, AlertCircle } from 'lucide-react';
import { ConfigurationPanel } from './ConfigurationPanel';
import { BuildProgress } from './BuildProgress';
import { TestingPanel } from './TestingPanel';
import { DeploymentPanel } from './DeploymentPanel';
import { IntegrationCodeViewer } from './IntegrationCodeViewer';
import { DLLConfiguration, BuildStatus, TestResult, DeploymentPackage } from '@/lib/dll/dll-config';
import { buildDLL } from '@/lib/dll/build-engine';
import { runTests } from '@/lib/dll/test-runner';
import { createDeploymentPackage } from '@/lib/dll/packaging';
import { generateIntegrationCode } from '@/lib/dll/code-generator';
import { useToast } from '@/lib/dll/use-toast';

export function DLLBuilderStudio() {
  const [configuration, setConfiguration] = useState<DLLConfiguration | null>(null);
  const [buildStatus, setBuildStatus] = useState<BuildStatus | null>(null);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [deploymentPackage, setDeploymentPackage] = useState<DeploymentPackage | null>(null);
  const [integrationCode, setIntegrationCode] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState('configure');
  const [isBuilding, setIsBuilding] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isPackaging, setIsPackaging] = useState(false);
  const { toast } = useToast();

  const handleConfigurationChange = useCallback((config: DLLConfiguration) => {
    setConfiguration(config);
    setBuildStatus(null);
    setTestResults([]);
    setDeploymentPackage(null);
    setIntegrationCode({});
  }, []);

  const handleBuild = useCallback(async () => {
    if (!configuration) {
      toast({
        title: 'Error',
        description: 'Please configure your DLL settings first',
        variant: 'destructive',
      });
      return;
    }

    setIsBuilding(true);
    try {
      const status = await buildDLL(configuration, (progress) => {
        setBuildStatus(progress);
      });
      
      setBuildStatus(status);
      
      if (status.success) {
        toast({
          title: 'Build Successful',
          description: 'Your DLL has been built successfully',
        });
        setActiveTab('test');
      } else {
        toast({
          title: 'Build Failed',
          description: status.errors?.[0] || 'An error occurred during build',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Build Error',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    } finally {
      setIsBuilding(false);
    }
  }, [configuration, toast]);

  const handleTest = useCallback(async () => {
    if (!configuration || !buildStatus?.outputPath) {
      toast({
        title: 'Error',
        description: 'Please build your DLL first',
        variant: 'destructive',
      });
      return;
    }

    setIsTesting(true);
    try {
      const results = await runTests(
        configuration,
        buildStatus.outputPath,
        (progress) => {
          // Update test progress if needed
        }
      );
      
      setTestResults(results);
      
      const failedTests = results.filter(r => !r.passed);
      if (failedTests.length === 0) {
        toast({
          title: 'All Tests Passed',
          description: `${results.length} tests completed successfully`,
        });
        setActiveTab('deploy');
      } else {
        toast({
          title: 'Some Tests Failed',
          description: `${failedTests.length} of ${results.length} tests failed`,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Test Error',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    } finally {
      setIsTesting(false);
    }
  }, [configuration, buildStatus, toast]);

  const handlePackage = useCallback(async () => {
    if (!configuration || !buildStatus?.outputPath) {
      toast({
        title: 'Error',
        description: 'Please build your DLL first',
        variant: 'destructive',
      });
      return;
    }

    setIsPackaging(true);
    try {
      const pkg = await createDeploymentPackage(configuration, buildStatus.outputPath);
      setDeploymentPackage(pkg);
      
      // Generate integration code for all platforms
      const code: Record<string, string> = {};
      for (const platform of ['vb6', 'vfp9', 'csharp', 'python']) {
        code[platform] = await generateIntegrationCode(configuration, platform as any);
      }
      setIntegrationCode(code);
      
      toast({
        title: 'Package Created',
        description: 'Deployment package created successfully',
      });
      setActiveTab('integrate');
    } catch (error) {
      toast({
        title: 'Packaging Error',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    } finally {
      setIsPackaging(false);
    }
  }, [configuration, buildStatus, toast]);

  const getStepStatus = (step: string) => {
    switch (step) {
      case 'configure':
        return configuration ? 'complete' : 'current';
      case 'build':
        if (!configuration) return 'pending';
        return buildStatus?.success ? 'complete' : configuration ? 'current' : 'pending';
      case 'test':
        if (!buildStatus?.success) return 'pending';
        return testResults.length > 0 ? 'complete' : 'current';
      case 'deploy':
        if (testResults.length === 0) return 'pending';
        return deploymentPackage ? 'complete' : 'current';
      case 'integrate':
        return deploymentPackage ? 'current' : 'pending';
      default:
        return 'pending';
    }
  };

  const getStepBadge = (status: string) => {
    switch (status) {
      case 'complete':
        return <Badge variant="default">Complete</Badge>;
      case 'current':
        return <Badge variant="secondary">Current</Badge>;
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  return (
    <div className="h-full flex flex-col">
      <Card className="mb-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-6 w-6" />
            DLL Builder Studio
          </CardTitle>
          <CardDescription>
            Build, test, and deploy DLLs for legacy system integration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex gap-4">
              <div className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                <span className="text-sm">Configure</span>
                {getStepBadge(getStepStatus('configure'))}
              </div>
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                <span className="text-sm">Build</span>
                {getStepBadge(getStepStatus('build'))}
              </div>
              <div className="flex items-center gap-2">
                <TestTube2 className="h-4 w-4" />
                <span className="text-sm">Test</span>
                {getStepBadge(getStepStatus('test'))}
              </div>
              <div className="flex items-center gap-2">
                <Rocket className="h-4 w-4" />
                <span className="text-sm">Deploy</span>
                {getStepBadge(getStepStatus('deploy'))}
              </div>
              <div className="flex items-center gap-2">
                <Code className="h-4 w-4" />
                <span className="text-sm">Integrate</span>
                {getStepBadge(getStepStatus('integrate'))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="configure">Configure</TabsTrigger>
            <TabsTrigger value="build" disabled={!configuration}>Build</TabsTrigger>
            <TabsTrigger value="test" disabled={!buildStatus?.success}>Test</TabsTrigger>
            <TabsTrigger value="deploy" disabled={testResults.length === 0}>Deploy</TabsTrigger>
            <TabsTrigger value="integrate" disabled={!deploymentPackage}>Integrate</TabsTrigger>
          </TabsList>

          <TabsContent value="configure" className="h-[calc(100%-40px)]">
            <ConfigurationPanel 
              configuration={configuration}
              onChange={handleConfigurationChange}
            />
          </TabsContent>

          <TabsContent value="build" className="h-[calc(100%-40px)]">
            {buildStatus ? (
              <BuildProgress
                status={buildStatus}
                configuration={configuration!}
                onRebuild={handleBuild}
              />
            ) : (
              <Card className="h-full flex items-center justify-center">
                <CardContent className="text-center">
                  <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">Ready to Build</h3>
                  <p className="text-muted-foreground mb-4">
                    Click the button below to start building your DLL
                  </p>
                  <Button onClick={handleBuild} disabled={isBuilding}>
                    {isBuilding ? 'Building...' : 'Start Build'}
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="test" className="h-[calc(100%-40px)]">
            <TestingPanel
              configuration={configuration!}
              testResults={testResults}
              onRunTests={handleTest}
              isRunning={isTesting}
            />
          </TabsContent>

          <TabsContent value="deploy" className="h-[calc(100%-40px)]">
            <DeploymentPanel
              configuration={configuration!}
              deploymentPackage={deploymentPackage}
              onCreatePackage={handlePackage}
              isCreating={isPackaging}
            />
          </TabsContent>

          <TabsContent value="integrate" className="h-[calc(100%-40px)]">
            {deploymentPackage && (
              <IntegrationCodeViewer
                configuration={configuration!}
                integrationCode={integrationCode}
              />
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}