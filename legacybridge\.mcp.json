{"name": "legacybridge-mcp", "version": "1.0.0", "description": "LegacyBridge MCP Server - Unified file format conversion for legacy applications", "server": {"command": "cargo", "args": ["run", "--bin", "legacybridge-mcp", "--", "server"], "env": {"RUST_LOG": "info"}}, "capabilities": {"resources": true, "tools": true, "prompts": true, "notifications": true}, "deployment": {"environments": {"development": {"server": {"protocol": "stdio", "host": "localhost", "port": 3000}, "security": {"enable_authentication": false, "enable_tls": false}, "logging": {"level": "debug", "format": "pretty"}}, "staging": {"server": {"protocol": "http", "host": "0.0.0.0", "port": 8080}, "security": {"enable_authentication": true, "enable_tls": true, "tls_cert": "/etc/legacybridge/cert.pem", "tls_key": "/etc/legacybridge/key.pem"}, "logging": {"level": "info", "format": "json"}}, "production": {"server": {"protocol": "websocket", "host": "0.0.0.0", "port": 443}, "security": {"enable_authentication": true, "enable_tls": true, "tls_cert": "/etc/legacybridge/cert.pem", "tls_key": "/etc/legacybridge/key.pem", "rate_limiting": {"enabled": true, "requests_per_minute": 100, "burst_size": 20}}, "performance": {"thread_pool_size": 16, "max_concurrent_requests": 200, "cache_size": 256}, "monitoring": {"enable_telemetry": true, "telemetry_endpoint": "https://telemetry.example.com", "enable_health_check": true, "health_check_endpoint": "/health"}, "logging": {"level": "warn", "format": "json", "output": {"console": false, "file": "/var/log/legacybridge/mcp.log", "syslog": true}}}}}, "resources": [{"name": "supported-formats", "description": "List of supported file formats for conversion", "uri": "legacy://formats", "mimeType": "application/json"}, {"name": "conversion-stats", "description": "Real-time conversion statistics and metrics", "uri": "legacy://stats", "mimeType": "application/json"}], "tools": [{"name": "convert", "description": "Convert files between legacy formats", "parameters": {"source_format": {"type": "string", "description": "Source file format (e.g., vfp, dbase, foxpro)"}, "target_format": {"type": "string", "description": "Target file format (e.g., json, csv, xml)"}, "file_path": {"type": "string", "description": "Path to the file to convert"}, "options": {"type": "object", "description": "Additional conversion options"}}}, {"name": "validate", "description": "Validate a legacy file format", "parameters": {"format": {"type": "string", "description": "File format to validate"}, "file_path": {"type": "string", "description": "Path to the file to validate"}}}, {"name": "batch_convert", "description": "Convert multiple files in batch", "parameters": {"source_format": {"type": "string", "description": "Source file format"}, "target_format": {"type": "string", "description": "Target file format"}, "input_directory": {"type": "string", "description": "Directory containing files to convert"}, "output_directory": {"type": "string", "description": "Directory for converted files"}, "recursive": {"type": "boolean", "description": "Process subdirectories recursively"}}}], "prompts": [{"name": "conversion-wizard", "description": "Interactive file conversion wizard", "parameters": {"mode": {"type": "string", "enum": ["guided", "expert"], "description": "Wizard mode"}}}], "notifications": [{"name": "conversion-progress", "description": "Real-time conversion progress updates"}, {"name": "conversion-complete", "description": "Notification when conversion is complete"}, {"name": "error", "description": "Error notifications during conversion"}], "configuration": {"max_file_size": "100MB", "supported_encodings": ["UTF-8", "ISO-8859-1", "Windows-1252"], "parallel_workers": 4, "timeout_seconds": 300, "cache_enabled": true, "cache_ttl_seconds": 3600}}