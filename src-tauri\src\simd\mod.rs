// SIMD Processing Module
// Phase 2.2.1: SIMD String Processing

pub mod string_processing;

pub use string_processing::{
    SimdStringProcessor,
    SimdPerformanceInfo,
    process_rtf_text_simd,
    find_rtf_control_positions,
    normalize_whitespace_simd,
    get_simd_info,
};

// Re-export existing SIMD modules if they exist
#[cfg(feature = "simd-conversion")]
pub use crate::conversion::simd_conversion::*;

#[cfg(feature = "simd-utils")]
pub use crate::conversion::markdown_simd_utils::*;
