// Test MCP Server - Minimal example to verify rmcp compilation
use rmcp::{
    ServerHand<PERSON>,
    model::{*, ErrorData as McpError},
    tool,
    tool_router,
    tool_handler,
    ServiceExt,
    transport::stdio,
};
use std::future::Future;

#[derive(Clone)]
pub struct TestMcpServer {
    counter: std::sync::Arc<tokio::sync::Mutex<i32>>,
    tool_router: rmcp::handler::server::router::tool::ToolRouter<Self>,
}

#[tool_router]
impl TestMcpServer {
    fn new() -> Self {
        Self {
            counter: std::sync::Arc::new(tokio::sync::Mutex::new(0)),
            tool_router: Self::tool_router(),
        }
    }

    #[tool(description = "Increment the counter by 1")]
    async fn increment(&self) -> Result<CallToolResult, McpError> {
        let mut counter = self.counter.lock().await;
        *counter += 1;
        Ok(CallToolResult::success(vec![Content::text(
            counter.to_string(),
        )]))
    }

    #[tool(description = "Get the current counter value")]
    async fn get(&self) -> Result<CallToolResult, McpError> {
        let counter = self.counter.lock().await;
        Ok(CallToolResult::success(vec![Content::text(
            counter.to_string(),
        )]))
    }
}

// Implement the server handler
#[tool_handler]
impl ServerHandler for TestMcpServer {
    fn get_info(&self) -> ServerInfo {
        ServerInfo {
            instructions: Some("A simple test server".into()),
            capabilities: ServerCapabilities::builder().enable_tools().build(),
            ..Default::default()
        }
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let service = TestMcpServer::new().serve(stdio()).await?;
    service.waiting().await?;
    Ok(())
}