apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: monitoring
data:
  alertmanager.yml: |
    global:
      resolve_timeout: 5m
      slack_api_url: 'YOUR_SLACK_WEBHOOK_URL'
      pagerduty_url: 'https://events.pagerduty.com/v2/enqueue'
      smtp_smarthost: 'smtp.example.com:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: 'YOUR_SMTP_PASSWORD'

    templates:
      - '/etc/alertmanager/templates/*.tmpl'

    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 12h
      receiver: 'default-receiver'
      routes:
        # Critical alerts go to PagerDuty
        - match:
            severity: critical
          receiver: pagerduty-critical
          continue: true
          
        # Backend team alerts
        - match:
            team: backend
          receiver: backend-team
          routes:
            - match:
                severity: critical
              receiver: backend-critical
              
        # Platform team alerts
        - match:
            team: platform
          receiver: platform-team
          
        # SLO violations
        - match:
            slo: "true"
          receiver: slo-violations
          group_interval: 5m
          repeat_interval: 30m

    receivers:
      - name: 'default-receiver'
        slack_configs:
          - channel: '#alerts'
            title: 'LegacyBridge Alert'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
            send_resolved: true

      - name: 'pagerduty-critical'
        pagerduty_configs:
          - service_key: 'YOUR_PAGERDUTY_SERVICE_KEY'
            description: '{{ .GroupLabels.alertname }}: {{ .CommonAnnotations.summary }}'
            details:
              firing: '{{ .Alerts.Firing | len }}'
              resolved: '{{ .Alerts.Resolved | len }}'
              alerts: '{{ range .Alerts }}{{ .Labels.alertname }}: {{ .Annotations.description }}{{ end }}'

      - name: 'backend-team'
        slack_configs:
          - channel: '#backend-alerts'
            title: 'Backend Alert: {{ .GroupLabels.alertname }}'
            text: '{{ .CommonAnnotations.description }}'
            actions:
              - type: button
                text: 'View Dashboard'
                url: '{{ .CommonAnnotations.dashboard_url }}'
              - type: button
                text: 'Runbook'
                url: '{{ .CommonAnnotations.runbook_url }}'

      - name: 'backend-critical'
        pagerduty_configs:
          - service_key: 'BACKEND_PAGERDUTY_KEY'
        slack_configs:
          - channel: '#backend-critical'
            title: '🚨 CRITICAL: {{ .GroupLabels.alertname }}'
            color: 'danger'

      - name: 'platform-team'
        slack_configs:
          - channel: '#platform-alerts'
            title: 'Platform Alert: {{ .GroupLabels.alertname }}'
        email_configs:
          - to: '<EMAIL>'
            headers:
              Subject: 'LegacyBridge Platform Alert: {{ .GroupLabels.alertname }}'

      - name: 'slo-violations'
        slack_configs:
          - channel: '#slo-alerts'
            title: 'SLO Violation: {{ .GroupLabels.alertname }}'
            text: |
              Service: {{ .CommonLabels.service }}
              SLO: {{ .CommonAnnotations.summary }}
              Current Value: {{ with index .Alerts 0 }}{{ .Annotations.description }}{{ end }}
        webhook_configs:
          - url: 'http://slo-reporter:8080/webhook'
            send_resolved: true

    inhibit_rules:
      - source_match:
          severity: 'critical'
        target_match:
          severity: 'warning'
        equal: ['alertname', 'cluster', 'service']
      - source_match:
          alertname: 'ServiceDown'
        target_match_re:
          alertname: '^(HighErrorRate|HighResponseTime)$'
        equal: ['service']