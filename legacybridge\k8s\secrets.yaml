apiVersion: v1
kind: Secret
metadata:
  name: legacybridge-secrets
  namespace: legacybridge
  labels:
    app: legacybridge
    component: secrets
type: Opaque
stringData:
  # Database credentials
  database-url: "postgresql://postgres:<EMAIL>:5432/legacybridge?sslmode=require"
  
  # Redis connection
  redis-url: "redis://:<EMAIL>:6379/0"
  
  # JWT secrets
  jwt-secret: "CHANGE_ME_TO_SECURE_RANDOM_STRING_MIN_32_CHARS"
  
  # API keys
  openai-api-key: "CHANGE_ME"
  anthropic-api-key: "CHANGE_ME"
  
  # OAuth credentials
  oauth-client-id: "CHAN<PERSON>_ME"
  oauth-client-secret: "CHANGE_ME"
  
  # S3/Object storage
  s3-access-key: "CHANGE_ME"
  s3-secret-key: "CHANGE_ME"
  s3-bucket: "legacybridge-files"
  s3-region: "us-west-2"
  
  # Encryption keys
  encryption-key: "<PERSON><PERSON><PERSON>_<PERSON>_TO_SECURE_RANDOM_STRING_32_CHARS"
  
  # SMTP configuration
  smtp-host: "smtp.example.com"
  smtp-port: "587"
  smtp-username: "CHANGE_ME"
  smtp-password: "CHANGE_ME"
  smtp-from: "<EMAIL>"