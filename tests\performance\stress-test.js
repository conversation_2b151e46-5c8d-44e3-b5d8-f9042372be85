import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const successRate = new Rate('successes');

// Stress test configuration - push system to its limits
export const options = {
  stages: [
    { duration: '2m', target: 200 },   // Ramp up to 200 users
    { duration: '5m', target: 200 },   // Stay at 200 users
    { duration: '2m', target: 500 },   // Ramp up to 500 users
    { duration: '5m', target: 500 },   // Stay at 500 users
    { duration: '2m', target: 1000 },  // Ramp up to 1000 users
    { duration: '5m', target: 1000 },  // Stay at 1000 users
    { duration: '10m', target: 0 },    // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests must complete below 2s
    http_req_failed: ['rate<0.5'],     // Error rate must be below 50%
    errors: ['rate<0.5'],              // Custom error rate below 50%
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';

// Heavier payloads for stress testing
const LARGE_DOCUMENT = 'A'.repeat(1024 * 100); // 100KB document

export default function () {
  const scenarios = [
    {
      name: 'Large File Conversion',
      url: '/api/v1/convert',
      method: 'POST',
      payload: {
        sourceFormat: 'docx',
        targetFormat: 'pdf',
        content: Buffer.from(LARGE_DOCUMENT).toString('base64'),
      },
      weight: 0.4,
    },
    {
      name: 'Concurrent API Calls',
      url: '/api/v1/batch-convert',
      method: 'POST',
      payload: {
        jobs: Array(10).fill({
          sourceFormat: 'rtf',
          targetFormat: 'markdown',
          content: 'SGVsbG8gV29ybGQh',
        }),
      },
      weight: 0.3,
    },
    {
      name: 'Complex Format Conversion',
      url: '/api/v1/convert',
      method: 'POST',
      payload: {
        sourceFormat: 'vsd',
        targetFormat: 'svg',
        content: 'SGVsbG8gV29ybGQh',
        options: {
          quality: 'high',
          preserveStyles: true,
          embedFonts: true,
        },
      },
      weight: 0.2,
    },
    {
      name: 'Status Polling',
      url: '/api/v1/status',
      method: 'GET',
      weight: 0.1,
    },
  ];

  // Select scenario
  const random = Math.random();
  let accumulator = 0;
  let selectedScenario;

  for (const scenario of scenarios) {
    accumulator += scenario.weight;
    if (random <= accumulator) {
      selectedScenario = scenario;
      break;
    }
  }

  // Execute scenario
  let response;
  const url = `${BASE_URL}${selectedScenario.url}`;
  const startTime = Date.now();

  if (selectedScenario.method === 'POST') {
    const params = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer stress-test-token',
      },
      timeout: '60s', // Longer timeout for stress test
    };

    response = http.post(url, JSON.stringify(selectedScenario.payload), params);
  } else {
    response = http.get(url, { timeout: '30s' });
  }

  const duration = Date.now() - startTime;

  // Check response
  const checks = {
    'status is 200': (r) => r.status === 200,
    'status is not 500': (r) => r.status < 500,
    'response time < 10s': (r) => duration < 10000,
    'has valid response': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body && (body.status || body.result || body.message);
      } catch {
        return false;
      }
    },
  };

  const checkResult = check(response, checks);
  errorRate.add(!checkResult);
  successRate.add(checkResult);

  // Log slow requests
  if (duration > 5000) {
    console.log(`Slow request: ${selectedScenario.name} took ${duration}ms`);
  }

  // Variable think time based on response
  if (response.status >= 500) {
    sleep(5); // Back off on server errors
  } else {
    sleep(Math.random() * 2); // 0-2 seconds
  }
}

export function handleSummary(data) {
  const report = generateStressTestReport(data);
  
  return {
    'stdout': report.text,
    'stress-test-report.json': JSON.stringify(data),
    'stress-test-report.html': report.html,
  };
}

function generateStressTestReport(data) {
  const totalRequests = data.metrics.http_reqs.values.count;
  const failedRequests = data.metrics.http_req_failed.values.passes;
  const avgResponseTime = data.metrics.http_req_duration.values.avg;
  const p95ResponseTime = data.metrics.http_req_duration.values['p(95)'];
  const p99ResponseTime = data.metrics.http_req_duration.values['p(99)'];
  const errorRate = data.metrics.errors.values.rate;
  const successRate = data.metrics.successes.values.rate;

  const text = `
Stress Test Results
===================
Peak Load: 1000 concurrent users
Total Requests: ${totalRequests}
Failed Requests: ${failedRequests}
Success Rate: ${(successRate * 100).toFixed(2)}%
Error Rate: ${(errorRate * 100).toFixed(2)}%

Response Times:
- Average: ${avgResponseTime.toFixed(2)}ms
- 95th Percentile: ${p95ResponseTime.toFixed(2)}ms
- 99th Percentile: ${p99ResponseTime.toFixed(2)}ms

System Behavior:
- ${errorRate < 0.1 ? '✅ System remained stable under load' : '⚠️ System showed signs of stress'}
- ${p95ResponseTime < 2000 ? '✅ Response times acceptable' : '❌ Response times degraded significantly'}
- ${successRate > 0.9 ? '✅ High success rate maintained' : '❌ Success rate dropped below acceptable levels'}

Recommendations:
${errorRate > 0.2 ? '- Consider implementing rate limiting\n' : ''}${p95ResponseTime > 2000 ? '- Optimize slow endpoints\n' : ''}${successRate < 0.9 ? '- Increase server capacity or implement auto-scaling\n' : ''}
`;

  const html = `
<!DOCTYPE html>
<html>
<head>
    <title>LegacyBridge Stress Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 10px; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric-card { background: #f9f9f9; padding: 20px; border-radius: 8px; border: 1px solid #e0e0e0; }
        .metric-value { font-size: 2em; font-weight: bold; color: #4CAF50; }
        .metric-label { color: #666; margin-top: 5px; }
        .warning { color: #ff9800; }
        .error { color: #f44336; }
        .success { color: #4CAF50; }
        .chart { margin: 20px 0; padding: 20px; background: #f9f9f9; border-radius: 8px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #4CAF50; color: white; }
        .recommendation { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 LegacyBridge Stress Test Report</h1>
        
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-value">${options.stages[4].target}</div>
                <div class="metric-label">Peak Concurrent Users</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${totalRequests.toLocaleString()}</div>
                <div class="metric-label">Total Requests</div>
            </div>
            <div class="metric-card">
                <div class="metric-value ${successRate > 0.9 ? 'success' : successRate > 0.7 ? 'warning' : 'error'}">
                    ${(successRate * 100).toFixed(1)}%
                </div>
                <div class="metric-label">Success Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value ${errorRate < 0.1 ? 'success' : errorRate < 0.3 ? 'warning' : 'error'}">
                    ${(errorRate * 100).toFixed(1)}%
                </div>
                <div class="metric-label">Error Rate</div>
            </div>
        </div>

        <div class="chart">
            <h2>Response Time Distribution</h2>
            <table>
                <tr>
                    <th>Percentile</th>
                    <th>Response Time</th>
                    <th>Status</th>
                </tr>
                <tr>
                    <td>Average</td>
                    <td>${avgResponseTime.toFixed(2)}ms</td>
                    <td class="${avgResponseTime < 1000 ? 'success' : avgResponseTime < 2000 ? 'warning' : 'error'}">
                        ${avgResponseTime < 1000 ? '✅ Excellent' : avgResponseTime < 2000 ? '⚠️ Acceptable' : '❌ Poor'}
                    </td>
                </tr>
                <tr>
                    <td>95th Percentile</td>
                    <td>${p95ResponseTime.toFixed(2)}ms</td>
                    <td class="${p95ResponseTime < 2000 ? 'success' : p95ResponseTime < 5000 ? 'warning' : 'error'}">
                        ${p95ResponseTime < 2000 ? '✅ Good' : p95ResponseTime < 5000 ? '⚠️ Needs Attention' : '❌ Critical'}
                    </td>
                </tr>
                <tr>
                    <td>99th Percentile</td>
                    <td>${p99ResponseTime.toFixed(2)}ms</td>
                    <td class="${p99ResponseTime < 5000 ? 'success' : p99ResponseTime < 10000 ? 'warning' : 'error'}">
                        ${p99ResponseTime < 5000 ? '✅ Acceptable' : p99ResponseTime < 10000 ? '⚠️ High' : '❌ Unacceptable'}
                    </td>
                </tr>
            </table>
        </div>

        <h2>System Behavior Analysis</h2>
        <ul>
            <li class="${errorRate < 0.1 ? 'success' : 'warning'}">
                ${errorRate < 0.1 ? '✅ System remained stable under extreme load' : '⚠️ System showed signs of stress under load'}
            </li>
            <li class="${p95ResponseTime < 2000 ? 'success' : 'error'}">
                ${p95ResponseTime < 2000 ? '✅ Response times remained acceptable' : '❌ Response times degraded significantly'}
            </li>
            <li class="${successRate > 0.9 ? 'success' : 'error'}">
                ${successRate > 0.9 ? '✅ High success rate maintained' : '❌ Success rate dropped below acceptable levels'}
            </li>
        </ul>

        ${errorRate > 0.2 || p95ResponseTime > 2000 || successRate < 0.9 ? `
        <h2>Recommendations</h2>
        <div class="recommendation">
            <ul>
                ${errorRate > 0.2 ? '<li>Implement rate limiting to prevent system overload</li>' : ''}
                ${p95ResponseTime > 2000 ? '<li>Optimize slow endpoints and database queries</li>' : ''}
                ${successRate < 0.9 ? '<li>Increase server capacity or implement auto-scaling</li>' : ''}
                ${errorRate > 0.3 ? '<li>Add circuit breakers to prevent cascading failures</li>' : ''}
                ${p99ResponseTime > 10000 ? '<li>Implement request timeouts and retry logic</li>' : ''}
            </ul>
        </div>
        ` : ''}

        <p style="text-align: center; color: #666; margin-top: 40px;">
            Generated on ${new Date().toLocaleString()}
        </p>
    </div>
</body>
</html>
`;

  return { text, html };
}