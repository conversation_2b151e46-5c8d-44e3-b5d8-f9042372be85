# 🏗️ PHASE 3: ARCHITECTURE IMPROVEMENTS (WEEKS 8-11)

**Priority:** P1 - Scalability Essential  
**Duration:** 4 Weeks  
**Team Size:** 8-10 Architecture Specialists  
**Dependencies:** Phase 1 Security & Phase 2 Performance Complete  

---

## 📊 PHASE OVERVIEW

This phase transforms LegacyBridge from a monolithic application into a scalable, enterprise-grade distributed system. The focus is on horizontal scaling, service decomposition, and implementing enterprise architecture patterns.

### 🎯 Phase Success Criteria
- **Horizontal scaling** capability up to 10 instances
- **Service decomposition** with independent deployments
- **Event-driven architecture** for async processing
- **Database layer** for persistence and audit trails

### 📋 Phase Deliverables
1. **Service Architecture** - Microservices decomposition
2. **API Gateway** - Centralized routing and security
3. **Database Layer** - PostgreSQL with audit capabilities
4. **Auto-Scaling** - Kubernetes HPA/VPA configuration
5. **Event System** - Asynchronous processing with Redis

### 🚨 Current Architecture Problems

**Monolithic Limitations:**
- Single point of failure
- Cannot scale individual components
- Stateful processing prevents load balancing
- No audit trail or conversion history
- Blocking I/O operations

---

## 🔧 PHASE 3.1: SERVICE ARCHITECTURE (WEEK 8)

**Agent Assignment:** Solutions Architect + Microservices Specialist  

### **Subtask 3.1.1: Service Decomposition**

#### **Target Service Architecture**

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Frontend]
        API_CLIENT[API Clients]
        MCP[MCP Clients]
    end
    
    subgraph "API Gateway Layer"
        GATEWAY[Kong API Gateway]
        LB[Load Balancer]
    end
    
    subgraph "Core Services"
        AUTH[Authentication Service]
        CONVERT[Conversion Service]
        FILE[File Management Service]
        JOB[Job Processing Service]
        NOTIFY[Notification Service]
    end
    
    subgraph "Data Layer"
        POSTGRES[(PostgreSQL)]
        REDIS[(Redis)]
        S3[(Object Storage)]
    end
    
    subgraph "Infrastructure"
        PROMETHEUS[Prometheus]
        JAEGER[Jaeger]
        ELK[ELK Stack]
    end
    
    WEB --> GATEWAY
    API_CLIENT --> GATEWAY
    MCP --> GATEWAY
    
    GATEWAY --> AUTH
    GATEWAY --> CONVERT
    GATEWAY --> FILE
    GATEWAY --> JOB
    
    CONVERT --> REDIS
    JOB --> REDIS
    AUTH --> POSTGRES
    FILE --> S3
    
    CONVERT --> PROMETHEUS
    JOB --> JAEGER
    FILE --> ELK
```

#### **Implementation Requirements:**

1. **Authentication Service**
   ```rust
   // services/auth-service/src/main.rs
   use axum::{
       extract::{State, Json},
       http::StatusCode,
       response::Json as ResponseJson,
       routing::{get, post},
       Router,
   };
   use serde::{Deserialize, Serialize};
   use sqlx::PgPool;
   use tower::ServiceBuilder;
   use tower_http::{cors::CorsLayer, trace::TraceLayer};
   
   #[derive(Clone)]
   pub struct AppState {
       db: PgPool,
       jwt_secret: String,
   }
   
   #[tokio::main]
   async fn main() -> Result<(), Box<dyn std::error::Error>> {
       let database_url = std::env::var("DATABASE_URL")?;
       let jwt_secret = std::env::var("JWT_SECRET")?;
       
       let pool = PgPool::connect(&database_url).await?;
       
       let state = AppState {
           db: pool,
           jwt_secret,
       };
       
       let app = Router::new()
           .route("/login", post(login))
           .route("/logout", post(logout))
           .route("/validate", post(validate_token))
           .route("/refresh", post(refresh_token))
           .route("/users", get(list_users).post(create_user))
           .route("/health", get(health_check))
           .layer(
               ServiceBuilder::new()
                   .layer(TraceLayer::new_for_http())
                   .layer(CorsLayer::permissive())
           )
           .with_state(state);
       
       let listener = tokio::net::TcpListener::bind("0.0.0.0:3001").await?;
       println!("Authentication service listening on port 3001");
       axum::serve(listener, app).await?;
       
       Ok(())
   }
   
   #[derive(Deserialize)]
   pub struct LoginRequest {
       username: String,
       password: String,
   }
   
   #[derive(Serialize)]
   pub struct LoginResponse {
       token: String,
       refresh_token: String,
       expires_in: u64,
       user: UserInfo,
   }
   
   async fn login(
       State(state): State<AppState>,
       Json(request): Json<LoginRequest>,
   ) -> Result<ResponseJson<LoginResponse>, StatusCode> {
       // Verify credentials against database
       let user = match verify_credentials(&state.db, &request.username, &request.password).await {
           Ok(user) => user,
           Err(_) => return Err(StatusCode::UNAUTHORIZED),
       };
       
       // Generate JWT tokens
       let token = generate_jwt_token(&state.jwt_secret, &user)?;
       let refresh_token = generate_refresh_token(&state.jwt_secret, &user)?;
       
       // Log authentication event
       log_auth_event(&state.db, &user.id, "login", true).await?;
       
       Ok(ResponseJson(LoginResponse {
           token,
           refresh_token,
           expires_in: 3600,
           user: UserInfo {
               id: user.id,
               username: user.username,
               roles: user.roles,
           },
       }))
   }
   
   async fn validate_token(
       State(state): State<AppState>,
       Json(token): Json<String>,
   ) -> Result<ResponseJson<TokenValidationResponse>, StatusCode> {
       match validate_jwt_token(&state.jwt_secret, &token) {
           Ok(claims) => Ok(ResponseJson(TokenValidationResponse {
               valid: true,
               user_id: claims.sub,
               roles: claims.roles,
               expires_at: claims.exp,
           })),
           Err(_) => Ok(ResponseJson(TokenValidationResponse {
               valid: false,
               user_id: None,
               roles: vec![],
               expires_at: 0,
           })),
       }
   }
   ```

2. **Conversion Service**
   ```rust
   // services/conversion-service/src/main.rs
   use axum::{
       extract::{State, Json, Multipart},
       http::StatusCode,
       response::Json as ResponseJson,
       routing::{post, get},
       Router,
   };
   use redis::AsyncCommands;
   use uuid::Uuid;
   
   #[derive(Clone)]
   pub struct AppState {
       redis: redis::Client,
       conversion_engine: Arc<ConversionEngine>,
   }
   
   #[tokio::main]
   async fn main() -> Result<(), Box<dyn std::error::Error>> {
       let redis_url = std::env::var("REDIS_URL").unwrap_or_else(|_| "redis://localhost".to_string());
       let redis_client = redis::Client::open(redis_url)?;
       
       let state = AppState {
           redis: redis_client,
           conversion_engine: Arc::new(ConversionEngine::new()),
       };
       
       let app = Router::new()
           .route("/convert", post(convert_file))
           .route("/convert/batch", post(convert_batch))
           .route("/convert/:job_id", get(get_conversion_status))
           .route("/convert/:job_id/result", get(get_conversion_result))
           .route("/formats", get(list_supported_formats))
           .route("/health", get(health_check))
           .with_state(state);
       
       let listener = tokio::net::TcpListener::bind("0.0.0.0:3002").await?;
       println!("Conversion service listening on port 3002");
       axum::serve(listener, app).await?;
       
       Ok(())
   }
   
   #[derive(Deserialize)]
   pub struct ConversionRequest {
       input_format: String,
       output_format: String,
       content: String, // Base64 encoded
       options: Option<serde_json::Value>,
   }
   
   #[derive(Serialize)]
   pub struct ConversionResponse {
       job_id: String,
       status: String,
       estimated_completion: Option<chrono::DateTime<chrono::Utc>>,
   }
   
   async fn convert_file(
       State(state): State<AppState>,
       Json(request): Json<ConversionRequest>,
   ) -> Result<ResponseJson<ConversionResponse>, StatusCode> {
       let job_id = Uuid::new_v4().to_string();
       
       // Decode base64 content
       let content = base64::decode(&request.content)
           .map_err(|_| StatusCode::BAD_REQUEST)?;
       
       // Create conversion job
       let job = ConversionJob {
           id: job_id.clone(),
           input_format: request.input_format,
           output_format: request.output_format,
           content,
           options: request.options.unwrap_or_default(),
           status: JobStatus::Queued,
           created_at: chrono::Utc::now(),
           started_at: None,
           completed_at: None,
           result: None,
           error: None,
       };
       
       // Store job in Redis
       let mut conn = state.redis.get_async_connection().await
           .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
       
       let job_data = serde_json::to_string(&job)
           .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
       
       conn.set(&format!("job:{}", job_id), &job_data).await
           .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
       
       // Queue job for processing
       conn.lpush("conversion_queue", &job_id).await
           .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
       
       // Estimate completion time based on queue length
       let queue_length: i64 = conn.llen("conversion_queue").await.unwrap_or(0);
       let estimated_completion = chrono::Utc::now() + 
           chrono::Duration::seconds(queue_length * 5); // 5 seconds per job estimate
       
       Ok(ResponseJson(ConversionResponse {
           job_id,
           status: "queued".to_string(),
           estimated_completion: Some(estimated_completion),
       }))
   }
   
   async fn get_conversion_status(
       State(state): State<AppState>,
       Path(job_id): Path<String>,
   ) -> Result<ResponseJson<JobStatusResponse>, StatusCode> {
       let mut conn = state.redis.get_async_connection().await
           .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
       
       let job_data: Option<String> = conn.get(&format!("job:{}", job_id)).await
           .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
       
       match job_data {
           Some(data) => {
               let job: ConversionJob = serde_json::from_str(&data)
                   .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
               
               Ok(ResponseJson(JobStatusResponse {
                   job_id: job.id,
                   status: job.status,
                   progress: calculate_progress(&job),
                   created_at: job.created_at,
                   completed_at: job.completed_at,
                   error: job.error,
               }))
           }
           None => Err(StatusCode::NOT_FOUND),
       }
   }
   ```

3. **File Management Service**
   ```rust
   // services/file-service/src/main.rs
   use axum::{
       extract::{State, Path, Multipart},
       http::StatusCode,
       response::Json as ResponseJson,
       routing::{post, get, delete},
       Router,
   };
   use aws_sdk_s3::{Client as S3Client, Config};
   use sqlx::PgPool;
   
   #[derive(Clone)]
   pub struct AppState {
       s3_client: S3Client,
       db: PgPool,
       bucket_name: String,
   }
   
   #[tokio::main]
   async fn main() -> Result<(), Box<dyn std::error::Error>> {
       let database_url = std::env::var("DATABASE_URL")?;
       let bucket_name = std::env::var("S3_BUCKET_NAME")?;
       
       let config = aws_config::load_from_env().await;
       let s3_client = S3Client::new(&config);
       let db = PgPool::connect(&database_url).await?;
       
       let state = AppState {
           s3_client,
           db,
           bucket_name,
       };
       
       let app = Router::new()
           .route("/files", post(upload_file))
           .route("/files/:file_id", get(download_file).delete(delete_file))
           .route("/files/:file_id/metadata", get(get_file_metadata))
           .route("/health", get(health_check))
           .with_state(state);
       
       let listener = tokio::net::TcpListener::bind("0.0.0.0:3003").await?;
       println!("File service listening on port 3003");
       axum::serve(listener, app).await?;
       
       Ok(())
   }
   
   async fn upload_file(
       State(state): State<AppState>,
       mut multipart: Multipart,
   ) -> Result<ResponseJson<FileUploadResponse>, StatusCode> {
       let mut file_name = None;
       let mut file_content = None;
       let mut content_type = None;
       
       while let Some(field) = multipart.next_field().await.unwrap() {
           let name = field.name().unwrap_or("").to_string();
           
           match name.as_str() {
               "file" => {
                   file_name = field.file_name().map(|s| s.to_string());
                   content_type = field.content_type().map(|s| s.to_string());
                   file_content = Some(field.bytes().await.map_err(|_| StatusCode::BAD_REQUEST)?);
               }
               _ => {}
           }
       }
       
       let file_content = file_content.ok_or(StatusCode::BAD_REQUEST)?;
       let file_name = file_name.unwrap_or_else(|| "unnamed".to_string());
       let content_type = content_type.unwrap_or_else(|| "application/octet-stream".to_string());
       
       // Generate unique file ID
       let file_id = Uuid::new_v4().to_string();
       let s3_key = format!("uploads/{}/{}", chrono::Utc::now().format("%Y/%m/%d"), file_id);
       
       // Upload to S3
       state.s3_client
           .put_object()
           .bucket(&state.bucket_name)
           .key(&s3_key)
           .content_type(&content_type)
           .body(file_content.into())
           .send()
           .await
           .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
       
       // Store metadata in database
       sqlx::query!(
           r#"
           INSERT INTO files (id, name, content_type, size, s3_key, uploaded_at)
           VALUES ($1, $2, $3, $4, $5, $6)
           "#,
           file_id,
           file_name,
           content_type,
           file_content.len() as i64,
           s3_key,
           chrono::Utc::now()
       )
       .execute(&state.db)
       .await
       .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
       
       Ok(ResponseJson(FileUploadResponse {
           file_id,
           name: file_name,
           size: file_content.len(),
           upload_url: format!("/files/{}", file_id),
       }))
   }
   ```

#### **Service Communication Patterns**

1. **Synchronous Communication (REST)**
   ```rust
   // shared/src/service_client.rs
   use reqwest::Client;
   use serde::{Deserialize, Serialize};
   
   pub struct ServiceClient {
       client: Client,
       base_url: String,
       auth_token: Option<String>,
   }
   
   impl ServiceClient {
       pub fn new(base_url: String) -> Self {
           Self {
               client: Client::new(),
               base_url,
               auth_token: None,
           }
       }
   
       pub fn with_auth_token(mut self, token: String) -> Self {
           self.auth_token = Some(token);
           self
       }
   
       pub async fn validate_user_token(&self, token: &str) -> Result<UserInfo, ServiceError> {
           let url = format!("{}/validate", self.base_url);
           
           let response = self.client
               .post(&url)
               .json(&token)
               .send()
               .await?;
   
           if response.status().is_success() {
               let validation: TokenValidationResponse = response.json().await?;
               if validation.valid {
                   Ok(UserInfo {
                       id: validation.user_id.unwrap(),
                       roles: validation.roles,
                   })
               } else {
                   Err(ServiceError::InvalidToken)
               }
           } else {
               Err(ServiceError::ServiceUnavailable)
           }
       }
   
       pub async fn upload_file(&self, content: Vec<u8>, filename: &str) -> Result<String, ServiceError> {
           let url = format!("{}/files", self.base_url);
           
           let form = reqwest::multipart::Form::new()
               .part("file", reqwest::multipart::Part::bytes(content)
                   .file_name(filename.to_string()));
   
           let mut request = self.client.post(&url).multipart(form);
           
           if let Some(token) = &self.auth_token {
               request = request.bearer_auth(token);
           }
   
           let response = request.send().await?;
           
           if response.status().is_success() {
               let upload_response: FileUploadResponse = response.json().await?;
               Ok(upload_response.file_id)
           } else {
               Err(ServiceError::UploadFailed)
           }
       }
   }
   ```

2. **Asynchronous Communication (Events)**
   ```rust
   // shared/src/events.rs
   use redis::AsyncCommands;
   use serde::{Deserialize, Serialize};
   
   #[derive(Serialize, Deserialize, Debug)]
   #[serde(tag = "type")]
   pub enum DomainEvent {
       UserAuthenticated {
           user_id: String,
           timestamp: chrono::DateTime<chrono::Utc>,
       },
       ConversionStarted {
           job_id: String,
           user_id: String,
           input_format: String,
           output_format: String,
           timestamp: chrono::DateTime<chrono::Utc>,
       },
       ConversionCompleted {
           job_id: String,
           user_id: String,
           success: bool,
           processing_time_ms: u64,
           timestamp: chrono::DateTime<chrono::Utc>,
       },
       FileUploaded {
           file_id: String,
           user_id: String,
           file_size: u64,
           timestamp: chrono::DateTime<chrono::Utc>,
       },
   }
   
   pub struct EventPublisher {
       redis_client: redis::Client,
   }
   
   impl EventPublisher {
       pub fn new(redis_url: &str) -> Result<Self, redis::RedisError> {
           let redis_client = redis::Client::open(redis_url)?;
           Ok(Self { redis_client })
       }
   
       pub async fn publish(&self, event: DomainEvent) -> Result<(), redis::RedisError> {
           let mut conn = self.redis_client.get_async_connection().await?;
           
           let event_data = serde_json::to_string(&event)
               .map_err(|e| redis::RedisError::from((redis::ErrorKind::InvalidClientConfig, "Serialization failed", e.to_string())))?;
           
           let channel = match &event {
               DomainEvent::UserAuthenticated { .. } => "auth_events",
               DomainEvent::ConversionStarted { .. } | DomainEvent::ConversionCompleted { .. } => "conversion_events",
               DomainEvent::FileUploaded { .. } => "file_events",
           };
           
           conn.publish(channel, event_data).await?;
           Ok(())
       }
   }
   
   pub struct EventSubscriber {
       redis_client: redis::Client,
   }
   
   impl EventSubscriber {
       pub fn new(redis_url: &str) -> Result<Self, redis::RedisError> {
           let redis_client = redis::Client::open(redis_url)?;
           Ok(Self { redis_client })
       }
   
       pub async fn subscribe<F>(&self, channels: &[&str], handler: F) -> Result<(), redis::RedisError> 
       where
           F: Fn(DomainEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> + Send + Sync + 'static,
       {
           let mut pubsub = self.redis_client.get_async_connection().await?.into_pubsub();
           
           for channel in channels {
               pubsub.subscribe(channel).await?;
           }
   
           let mut stream = pubsub.on_message();
           
           while let Some(msg) = stream.next().await {
               let payload: String = msg.get_payload()?;
               
               match serde_json::from_str::<DomainEvent>(&payload) {
                   Ok(event) => {
                       if let Err(e) = handler(event) {
                           eprintln!("Event handler error: {}", e);
                       }
                   }
                   Err(e) => {
                       eprintln!("Failed to deserialize event: {}", e);
                   }
               }
           }
           
           Ok(())
       }
   }
   ```

**Success Criteria:**
- ✅ Independent service deployment capability
- ✅ Service-to-service communication established
- ✅ Event-driven communication patterns implemented
- ✅ Clear service boundaries and responsibilities

---

### **Subtask 3.1.2: API Gateway Implementation**

#### **Implementation Requirements:**

1. **Kong API Gateway Configuration**
   ```yaml
   # kong-config.yaml
   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: kong-config
   data:
     kong.conf: |
       database = postgres
       pg_host = postgres-service
       pg_port = 5432
       pg_database = kong
       pg_user = kong
       pg_password = kong
       
       # Logging
       log_level = info
       access_log = /dev/stdout
       error_log = /dev/stderr
       
       # Performance
       nginx_worker_processes = auto
       nginx_worker_connections = 1024
       
       # Security
       trusted_ips = 0.0.0.0/0,::/0
       real_ip_header = X-Forwarded-For
       real_ip_recursive = on
   
   ---
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: kong-gateway
   spec:
     replicas: 2
     selector:
       matchLabels:
         app: kong-gateway
     template:
       metadata:
         labels:
           app: kong-gateway
       spec:
         containers:
         - name: kong
           image: kong:3.4-alpine
           env:
           - name: KONG_DATABASE
             value: "postgres"
           - name: KONG_PG_HOST
             value: "postgres-service"
           - name: KONG_PG_DATABASE
             value: "kong"
           - name: KONG_PG_USER
             value: "kong"
           - name: KONG_PG_PASSWORD
             valueFrom:
               secretKeyRef:
                 name: postgres-secret
                 key: password
           - name: KONG_PROXY_ACCESS_LOG
             value: "/dev/stdout"
           - name: KONG_ADMIN_ACCESS_LOG
             value: "/dev/stdout"
           - name: KONG_PROXY_ERROR_LOG
             value: "/dev/stderr"
           - name: KONG_ADMIN_ERROR_LOG
             value: "/dev/stderr"
           - name: KONG_ADMIN_LISTEN
             value: "0.0.0.0:8001"
           ports:
           - containerPort: 8000
             name: proxy
           - containerPort: 8001
             name: admin
           livenessProbe:
             httpGet:
               path: /status
               port: 8001
           readinessProbe:
             httpGet:
               path: /status
               port: 8001
   ```

2. **Kong Service Registration**
   ```bash
   #!/bin/bash
   # scripts/configure-kong.sh
   
   KONG_ADMIN_URL="http://localhost:8001"
   
   # Register Authentication Service
   curl -X POST $KONG_ADMIN_URL/services \
     --data "name=auth-service" \
     --data "protocol=http" \
     --data "host=auth-service" \
     --data "port=3001" \
     --data "path=/api/v1"
   
   curl -X POST $KONG_ADMIN_URL/services/auth-service/routes \
     --data "name=auth-route" \
     --data "paths[]=/auth" \
     --data "strip_path=true"
   
   # Register Conversion Service
   curl -X POST $KONG_ADMIN_URL/services \
     --data "name=conversion-service" \
     --data "protocol=http" \
     --data "host=conversion-service" \
     --data "port=3002" \
     --data "path=/api/v1"
   
   curl -X POST $KONG_ADMIN_URL/services/conversion-service/routes \
     --data "name=conversion-route" \
     --data "paths[]=/convert" \
     --data "strip_path=true"
   
   # Register File Service
   curl -X POST $KONG_ADMIN_URL/services \
     --data "name=file-service" \
     --data "protocol=http" \
     --data "host=file-service" \
     --data "port=3003" \
     --data "path=/api/v1"
   
   curl -X POST $KONG_ADMIN_URL/services/file-service/routes \
     --data "name=file-route" \
     --data "paths[]=/files" \
     --data "strip_path=true"
   
   # Configure Rate Limiting
   curl -X POST $KONG_ADMIN_URL/plugins \
     --data "name=rate-limiting" \
     --data "config.minute=100" \
     --data "config.policy=local"
   
   # Configure JWT Authentication
   curl -X POST $KONG_ADMIN_URL/plugins \
     --data "name=jwt" \
     --data "config.secret_is_base64=false"
   
   # Configure CORS
   curl -X POST $KONG_ADMIN_URL/plugins \
     --data "name=cors" \
     --data "config.origins=*" \
     --data "config.methods=GET,POST,PUT,DELETE,OPTIONS" \
     --data "config.headers=Content-Type,Authorization"
   
   # Configure Request/Response Logging
   curl -X POST $KONG_ADMIN_URL/plugins \
     --data "name=file-log" \
     --data "config.path=/var/log/kong/access.log"
   ```

3. **Custom Kong Plugins**
   ```lua
   -- kong/plugins/legacybridge-auth/handler.lua
   local jwt_decoder = require "resty.jwt"
   local http = require "resty.http"
   
   local LegacyBridgeAuthHandler = {}
   
   function LegacyBridgeAuthHandler:access(config)
       local token = kong.request.get_header("Authorization")
       
       if not token then
           return kong.response.exit(401, {message = "Missing authorization token"})
       end
       
       -- Extract Bearer token
       local bearer_token = string.match(token, "Bearer%s+(.+)")
       if not bearer_token then
           return kong.response.exit(401, {message = "Invalid authorization format"})
       end
       
       -- Validate token with auth service
       local httpc = http.new()
       local auth_service_url = config.auth_service_url or "http://auth-service:3001"
       
       local res, err = httpc:request_uri(auth_service_url .. "/validate", {
           method = "POST",
           body = '{"token":"' .. bearer_token .. '"}',
           headers = {
               ["Content-Type"] = "application/json",
           },
           timeout = 5000,
       })
       
       if not res or res.status ~= 200 then
           return kong.response.exit(401, {message = "Token validation failed"})
       end
       
       local cjson = require "cjson"
       local validation_result = cjson.decode(res.body)
       
       if not validation_result.valid then
           return kong.response.exit(401, {message = "Invalid token"})
       end
       
       -- Set user context for downstream services
       kong.service.request.set_header("X-User-ID", validation_result.user_id)
       kong.service.request.set_header("X-User-Roles", table.concat(validation_result.roles, ","))
   end
   
   return LegacyBridgeAuthHandler
   ```

**Success Criteria:**
- ✅ Centralized API management and security
- ✅ Request routing to appropriate services
- ✅ Rate limiting and CORS handling
- ✅ Authentication integration

---

### **Subtask 3.1.3: Database Layer Implementation**

#### **Implementation Requirements:**

1. **PostgreSQL Schema Design**
   ```sql
   -- migrations/001_initial_schema.sql
   
   -- Users and Authentication
   CREATE TABLE users (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       username VARCHAR(255) UNIQUE NOT NULL,
       email VARCHAR(255) UNIQUE NOT NULL,
       password_hash VARCHAR(255) NOT NULL,
       roles TEXT[] NOT NULL DEFAULT '{"user"}',
       created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
       updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
       last_login_at TIMESTAMPTZ,
       is_active BOOLEAN NOT NULL DEFAULT TRUE
   );
   
   CREATE INDEX idx_users_username ON users(username);
   CREATE INDEX idx_users_email ON users(email);
   CREATE INDEX idx_users_roles ON users USING GIN(roles);
   
   -- File Management
   CREATE TABLE files (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       user_id UUID NOT NULL REFERENCES users(id),
       name VARCHAR(255) NOT NULL,
       content_type VARCHAR(100) NOT NULL,
       size BIGINT NOT NULL,
       s3_key VARCHAR(500) NOT NULL,
       checksum VARCHAR(64),
       uploaded_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
       deleted_at TIMESTAMPTZ
   );
   
   CREATE INDEX idx_files_user_id ON files(user_id);
   CREATE INDEX idx_files_uploaded_at ON files(uploaded_at);
   CREATE INDEX idx_files_deleted_at ON files(deleted_at) WHERE deleted_at IS NOT NULL;
   
   -- Conversion Jobs
   CREATE TYPE job_status AS ENUM ('queued', 'processing', 'completed', 'failed', 'cancelled');
   
   CREATE TABLE conversion_jobs (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       user_id UUID NOT NULL REFERENCES users(id),
       input_file_id UUID REFERENCES files(id),
       output_file_id UUID REFERENCES files(id),
       input_format VARCHAR(50) NOT NULL,
       output_format VARCHAR(50) NOT NULL,
       status job_status NOT NULL DEFAULT 'queued',
       options JSONB,
       created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
       started_at TIMESTAMPTZ,
       completed_at TIMESTAMPTZ,
       processing_time_ms INTEGER,
       error_message TEXT,
       result_metadata JSONB
   );
   
   CREATE INDEX idx_conversion_jobs_user_id ON conversion_jobs(user_id);
   CREATE INDEX idx_conversion_jobs_status ON conversion_jobs(status);
   CREATE INDEX idx_conversion_jobs_created_at ON conversion_jobs(created_at);
   
   -- Audit Trail
   CREATE TABLE audit_events (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       user_id UUID REFERENCES users(id),
       event_type VARCHAR(100) NOT NULL,
       resource_type VARCHAR(100) NOT NULL,
       resource_id UUID,
       old_values JSONB,
       new_values JSONB,
       ip_address INET,
       user_agent TEXT,
       created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
   );
   
   CREATE INDEX idx_audit_events_user_id ON audit_events(user_id);
   CREATE INDEX idx_audit_events_event_type ON audit_events(event_type);
   CREATE INDEX idx_audit_events_created_at ON audit_events(created_at);
   CREATE INDEX idx_audit_events_resource ON audit_events(resource_type, resource_id);
   
   -- System Configuration
   CREATE TABLE system_config (
       key VARCHAR(255) PRIMARY KEY,
       value JSONB NOT NULL,
       description TEXT,
       updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
       updated_by UUID REFERENCES users(id)
   );
   
   -- Performance Metrics
   CREATE TABLE performance_metrics (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       metric_name VARCHAR(100) NOT NULL,
       metric_value DOUBLE PRECISION NOT NULL,
       labels JSONB,
       recorded_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
   );
   
   CREATE INDEX idx_performance_metrics_name_time ON performance_metrics(metric_name, recorded_at);
   
   -- Functions and Triggers
   CREATE OR REPLACE FUNCTION update_updated_at_column()
   RETURNS TRIGGER AS $$
   BEGIN
       NEW.updated_at = NOW();
       RETURN NEW;
   END;
   $$ language 'plpgsql';
   
   CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
       FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
   
   -- Audit trigger function
   CREATE OR REPLACE FUNCTION audit_trigger()
   RETURNS TRIGGER AS $$
   BEGIN
       IF TG_OP = 'INSERT' THEN
           INSERT INTO audit_events (event_type, resource_type, resource_id, new_values)
           VALUES (TG_OP, TG_TABLE_NAME, NEW.id, row_to_json(NEW));
           RETURN NEW;
       ELSIF TG_OP = 'UPDATE' THEN
           INSERT INTO audit_events (event_type, resource_type, resource_id, old_values, new_values)
           VALUES (TG_OP, TG_TABLE_NAME, NEW.id, row_to_json(OLD), row_to_json(NEW));
           RETURN NEW;
       ELSIF TG_OP = 'DELETE' THEN
           INSERT INTO audit_events (event_type, resource_type, resource_id, old_values)
           VALUES (TG_OP, TG_TABLE_NAME, OLD.id, row_to_json(OLD));
           RETURN OLD;
       END IF;
       RETURN NULL;
   END;
   $$ language 'plpgsql';
   
   -- Apply audit triggers
   CREATE TRIGGER audit_users AFTER INSERT OR UPDATE OR DELETE ON users
       FOR EACH ROW EXECUTE FUNCTION audit_trigger();
   
   CREATE TRIGGER audit_conversion_jobs AFTER INSERT OR UPDATE OR DELETE ON conversion_jobs
       FOR EACH ROW EXECUTE FUNCTION audit_trigger();
   ```

2. **Database Connection Management**
   ```rust
   // shared/src/database.rs
   use sqlx::{PgPool, Row};
   use std::time::Duration;
   
   pub struct DatabaseManager {
       pool: PgPool,
   }
   
   impl DatabaseManager {
       pub async fn new(database_url: &str) -> Result<Self, sqlx::Error> {
           let pool = sqlx::postgres::PgPoolOptions::new()
               .max_connections(20)
               .min_connections(5)
               .acquire_timeout(Duration::from_secs(10))
               .idle_timeout(Duration::from_secs(600))
               .max_lifetime(Duration::from_secs(1800))
               .connect(database_url)
               .await?;
   
           // Run migrations
           sqlx::migrate!("./migrations").run(&pool).await?;
   
           Ok(Self { pool })
       }
   
       pub fn pool(&self) -> &PgPool {
           &self.pool
       }
   
       pub async fn health_check(&self) -> Result<(), sqlx::Error> {
           sqlx::query("SELECT 1")
               .fetch_one(&self.pool)
               .await?;
           Ok(())
       }
   
       pub async fn get_system_config(&self, key: &str) -> Result<Option<serde_json::Value>, sqlx::Error> {
           let row = sqlx::query("SELECT value FROM system_config WHERE key = $1")
               .bind(key)
               .fetch_optional(&self.pool)
               .await?;
   
           Ok(row.map(|r| r.get(0)))
       }
   
       pub async fn set_system_config(
           &self,
           key: &str,
           value: &serde_json::Value,
           updated_by: uuid::Uuid,
       ) -> Result<(), sqlx::Error> {
           sqlx::query(
               "INSERT INTO system_config (key, value, updated_by) 
                VALUES ($1, $2, $3) 
                ON CONFLICT (key) DO UPDATE SET 
                value = EXCLUDED.value, 
                updated_by = EXCLUDED.updated_by, 
                updated_at = NOW()"
           )
           .bind(key)
           .bind(value)
           .bind(updated_by)
           .execute(&self.pool)
           .await?;
   
           Ok(())
       }
   
       pub async fn record_performance_metric(
           &self,
           metric_name: &str,
           metric_value: f64,
           labels: Option<&serde_json::Value>,
       ) -> Result<(), sqlx::Error> {
           sqlx::query(
               "INSERT INTO performance_metrics (metric_name, metric_value, labels) 
                VALUES ($1, $2, $3)"
           )
           .bind(metric_name)
           .bind(metric_value)
           .bind(labels)
           .execute(&self.pool)
           .await?;
   
           Ok(())
       }
   }
   ```

3. **Database Repositories**
   ```rust
   // shared/src/repositories/user_repository.rs
   use sqlx::PgPool;
   use uuid::Uuid;
   use chrono::{DateTime, Utc};
   
   pub struct UserRepository {
       pool: PgPool,
   }
   
   impl UserRepository {
       pub fn new(pool: PgPool) -> Self {
           Self { pool }
       }
   
       pub async fn create_user(&self, user: CreateUserRequest) -> Result<User, sqlx::Error> {
           let user_id = Uuid::new_v4();
           let password_hash = hash_password(&user.password)?;
   
           let row = sqlx::query_as!(
               User,
               r#"
               INSERT INTO users (id, username, email, password_hash, roles)
               VALUES ($1, $2, $3, $4, $5)
               RETURNING id, username, email, roles as "roles: Vec<String>", 
                        created_at, updated_at, last_login_at, is_active
               "#,
               user_id,
               user.username,
               user.email,
               password_hash,
               &user.roles
           )
           .fetch_one(&self.pool)
           .await?;
   
           Ok(row)
       }
   
       pub async fn find_by_username(&self, username: &str) -> Result<Option<User>, sqlx::Error> {
           let user = sqlx::query_as!(
               User,
               r#"
               SELECT id, username, email, roles as "roles: Vec<String>", 
                      created_at, updated_at, last_login_at, is_active
               FROM users 
               WHERE username = $1 AND is_active = true
               "#,
               username
           )
           .fetch_optional(&self.pool)
           .await?;
   
           Ok(user)
       }
   
       pub async fn update_last_login(&self, user_id: Uuid) -> Result<(), sqlx::Error> {
           sqlx::query!(
               "UPDATE users SET last_login_at = NOW() WHERE id = $1",
               user_id
           )
           .execute(&self.pool)
           .await?;
   
           Ok(())
       }
   
       pub async fn list_users(&self, limit: i64, offset: i64) -> Result<Vec<User>, sqlx::Error> {
           let users = sqlx::query_as!(
               User,
               r#"
               SELECT id, username, email, roles as "roles: Vec<String>", 
                      created_at, updated_at, last_login_at, is_active
               FROM users 
               WHERE is_active = true
               ORDER BY created_at DESC
               LIMIT $1 OFFSET $2
               "#,
               limit,
               offset
           )
           .fetch_all(&self.pool)
           .await?;
   
           Ok(users)
       }
   }
   ```

**Success Criteria:**
- ✅ ACID compliance with proper transactions
- ✅ Comprehensive audit trail implementation
- ✅ Performance metrics collection
- ✅ Database migrations and schema management

---

## 🔧 PHASE 3.2: SCALABILITY IMPLEMENTATION (WEEKS 9-10)

**Agent Assignment:** DevOps Engineer + Scalability Specialist  

### **Subtask 3.2.1: Horizontal Scaling**

#### **Implementation Requirements:**

1. **Stateless Service Design**
   ```rust
   // shared/src/session.rs
   use redis::AsyncCommands;
   use serde::{Deserialize, Serialize};
   use uuid::Uuid;
   
   #[derive(Serialize, Deserialize, Clone)]
   pub struct UserSession {
       pub user_id: Uuid,
       pub username: String,
       pub roles: Vec<String>,
       pub created_at: chrono::DateTime<chrono::Utc>,
       pub last_accessed: chrono::DateTime<chrono::Utc>,
       pub ip_address: String,
   }
   
   pub struct SessionManager {
       redis_client: redis::Client,
       session_ttl: usize, // seconds
   }
   
   impl SessionManager {
       pub fn new(redis_url: &str, session_ttl: usize) -> Result<Self, redis::RedisError> {
           let redis_client = redis::Client::open(redis_url)?;
           Ok(Self {
               redis_client,
               session_ttl,
           })
       }
   
       pub async fn create_session(
           &self,
           user_id: Uuid,
           username: String,
           roles: Vec<String>,
           ip_address: String,
       ) -> Result<String, redis::RedisError> {
           let session_id = Uuid::new_v4().to_string();
           let session = UserSession {
               user_id,
               username,
               roles,
               created_at: chrono::Utc::now(),
               last_accessed: chrono::Utc::now(),
               ip_address,
           };
   
           let mut conn = self.redis_client.get_async_connection().await?;
           let session_data = serde_json::to_string(&session).unwrap();
           
           conn.setex(&format!("session:{}", session_id), self.session_ttl, session_data).await?;
           
           Ok(session_id)
       }
   
       pub async fn get_session(&self, session_id: &str) -> Result<Option<UserSession>, redis::RedisError> {
           let mut conn = self.redis_client.get_async_connection().await?;
           
           let session_data: Option<String> = conn.get(&format!("session:{}", session_id)).await?;
           
           match session_data {
               Some(data) => {
                   let mut session: UserSession = serde_json::from_str(&data).unwrap();
                   
                   // Update last accessed time
                   session.last_accessed = chrono::Utc::now();
                   let updated_data = serde_json::to_string(&session).unwrap();
                   conn.setex(&format!("session:{}", session_id), self.session_ttl, updated_data).await?;
                   
                   Ok(Some(session))
               }
               None => Ok(None),
           }
       }
   
       pub async fn invalidate_session(&self, session_id: &str) -> Result<(), redis::RedisError> {
           let mut conn = self.redis_client.get_async_connection().await?;
           conn.del(&format!("session:{}", session_id)).await?;
           Ok(())
       }
   
       pub async fn cleanup_expired_sessions(&self) -> Result<(), redis::RedisError> {
           // Redis handles TTL automatically, but we can log cleanup activity
           Ok(())
       }
   }
   ```

2. **Load Balancer Configuration**
   ```yaml
   # kubernetes/load-balancer.yaml
   apiVersion: v1
   kind: Service
   metadata:
     name: legacybridge-lb
     annotations:
       service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
       service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
   spec:
     type: LoadBalancer
     selector:
       app: kong-gateway
     ports:
     - name: http
       port: 80
       targetPort: 8000
       protocol: TCP
     - name: https
       port: 443
       targetPort: 8443
       protocol: TCP
   
   ---
   apiVersion: networking.k8s.io/v1
   kind: Ingress
   metadata:
     name: legacybridge-ingress
     annotations:
       kubernetes.io/ingress.class: "nginx"
       nginx.ingress.kubernetes.io/rate-limit: "100"
       nginx.ingress.kubernetes.io/rate-limit-window: "1m"
       nginx.ingress.kubernetes.io/ssl-redirect: "true"
       nginx.ingress.kubernetes.io/cors-allow-origin: "*"
       nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
       nginx.ingress.kubernetes.io/cors-allow-headers: "Content-Type, Authorization"
   spec:
     tls:
     - hosts:
       - api.legacybridge.com
       secretName: legacybridge-tls
     rules:
     - host: api.legacybridge.com
       http:
         paths:
         - path: /
           pathType: Prefix
           backend:
             service:
               name: kong-gateway-service
               port:
                 number: 8000
   ```

3. **Service Deployment with Scaling**
   ```yaml
   # kubernetes/services/conversion-service.yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: conversion-service
     labels:
       app: conversion-service
   spec:
     replicas: 3
     strategy:
       type: RollingUpdate
       rollingUpdate:
         maxSurge: 1
         maxUnavailable: 0
     selector:
       matchLabels:
         app: conversion-service
     template:
       metadata:
         labels:
           app: conversion-service
       spec:
         containers:
         - name: conversion-service
           image: legacybridge/conversion-service:latest
           ports:
           - containerPort: 3002
           env:
           - name: REDIS_URL
             value: "redis://redis-service:6379"
           - name: DATABASE_URL
             valueFrom:
               secretKeyRef:
                 name: postgres-secret
                 key: database-url
           - name: RUST_LOG
             value: "info"
           resources:
             requests:
               memory: "256Mi"
               cpu: "200m"
             limits:
               memory: "512Mi"
               cpu: "500m"
           livenessProbe:
             httpGet:
               path: /health
               port: 3002
             initialDelaySeconds: 30
             periodSeconds: 10
           readinessProbe:
             httpGet:
               path: /health
               port: 3002
             initialDelaySeconds: 5
             periodSeconds: 5
         - name: redis-exporter
           image: oliver006/redis_exporter:latest
           ports:
           - containerPort: 9121
           env:
           - name: REDIS_ADDR
             value: "redis://redis-service:6379"
   
   ---
   apiVersion: v1
   kind: Service
   metadata:
     name: conversion-service
   spec:
     selector:
       app: conversion-service
     ports:
     - name: http
       port: 3002
       targetPort: 3002
     - name: metrics
       port: 9121
       targetPort: 9121
   ```

**Success Criteria:**
- ✅ Services are stateless and scalable
- ✅ Session state externalized to Redis
- ✅ Load balancing across multiple instances
- ✅ Rolling deployments without downtime

---

### **Subtask 3.2.2: Auto-Scaling Configuration**

#### **Implementation Requirements:**

1. **Horizontal Pod Autoscaler (HPA)**
   ```yaml
   # kubernetes/autoscaling/hpa.yaml
   apiVersion: autoscaling/v2
   kind: HorizontalPodAutoscaler
   metadata:
     name: conversion-service-hpa
   spec:
     scaleTargetRef:
       apiVersion: apps/v1
       kind: Deployment
       name: conversion-service
     minReplicas: 2
     maxReplicas: 20
     metrics:
     - type: Resource
       resource:
         name: cpu
         target:
           type: Utilization
           averageUtilization: 70
     - type: Resource
       resource:
         name: memory
         target:
           type: Utilization
           averageUtilization: 80
     - type: Pods
       pods:
         metric:
           name: conversion_queue_length
         target:
           type: AverageValue
           averageValue: "5"
     behavior:
       scaleUp:
         stabilizationWindowSeconds: 60
         policies:
         - type: Percent
           value: 100
           periodSeconds: 15
         - type: Pods
           value: 2
           periodSeconds: 60
       scaleDown:
         stabilizationWindowSeconds: 300
         policies:
         - type: Percent
           value: 10
           periodSeconds: 60
   
   ---
   apiVersion: autoscaling/v2
   kind: HorizontalPodAutoscaler
   metadata:
     name: auth-service-hpa
   spec:
     scaleTargetRef:
       apiVersion: apps/v1
       kind: Deployment
       name: auth-service
     minReplicas: 2
     maxReplicas: 10
     metrics:
     - type: Resource
       resource:
         name: cpu
         target:
           type: Utilization
           averageUtilization: 60
     - type: Resource
       resource:
         name: memory
         target:
           type: Utilization
           averageUtilization: 70
   
   ---
   apiVersion: autoscaling/v2
   kind: HorizontalPodAutoscaler
   metadata:
     name: file-service-hpa
   spec:
     scaleTargetRef:
       apiVersion: apps/v1
       kind: Deployment
       name: file-service
     minReplicas: 1
     maxReplicas: 8
     metrics:
     - type: Resource
       resource:
         name: cpu
         target:
           type: Utilization
           averageUtilization: 75
   ```

2. **Vertical Pod Autoscaler (VPA)**
   ```yaml
   # kubernetes/autoscaling/vpa.yaml
   apiVersion: autoscaling.k8s.io/v1
   kind: VerticalPodAutoscaler
   metadata:
     name: conversion-service-vpa
   spec:
     targetRef:
       apiVersion: apps/v1
       kind: Deployment
       name: conversion-service
     updatePolicy:
       updateMode: "Auto"
     resourcePolicy:
       containerPolicies:
       - containerName: conversion-service
         maxAllowed:
           cpu: "2"
           memory: "4Gi"
         minAllowed:
           cpu: "100m"
           memory: "128Mi"
         controlledResources: ["cpu", "memory"]
   
   ---
   apiVersion: autoscaling.k8s.io/v1
   kind: VerticalPodAutoscaler
   metadata:
     name: auth-service-vpa
   spec:
     targetRef:
       apiVersion: apps/v1
       kind: Deployment
       name: auth-service
     updatePolicy:
       updateMode: "Auto"
     resourcePolicy:
       containerPolicies:
       - containerName: auth-service
         maxAllowed:
           cpu: "1"
           memory: "2Gi"
         minAllowed:
           cpu: "50m"
           memory: "64Mi"
   ```

3. **Custom Metrics for Scaling**
   ```rust
   // shared/src/metrics/custom_metrics.rs
   use prometheus::{Gauge, Counter, Histogram, Registry, Opts};
   use std::sync::Arc;
   
   pub struct CustomMetrics {
       pub conversion_queue_length: Gauge,
       pub active_conversions: Gauge,
       pub conversion_duration: Histogram,
       pub conversion_errors: Counter,
       pub memory_usage: Gauge,
       pub db_connections: Gauge,
   }
   
   impl CustomMetrics {
       pub fn new(registry: &Registry) -> Result<Self, prometheus::Error> {
           let conversion_queue_length = Gauge::with_opts(
               Opts::new("conversion_queue_length", "Number of jobs in conversion queue")
           )?;
           registry.register(Box::new(conversion_queue_length.clone()))?;
   
           let active_conversions = Gauge::with_opts(
               Opts::new("active_conversions", "Number of currently processing conversions")
           )?;
           registry.register(Box::new(active_conversions.clone()))?;
   
           let conversion_duration = Histogram::with_opts(
               prometheus::HistogramOpts::new("conversion_duration_seconds", "Conversion processing time")
                   .buckets(vec![0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0])
           )?;
           registry.register(Box::new(conversion_duration.clone()))?;
   
           let conversion_errors = Counter::with_opts(
               Opts::new("conversion_errors_total", "Total number of conversion errors")
           )?;
           registry.register(Box::new(conversion_errors.clone()))?;
   
           let memory_usage = Gauge::with_opts(
               Opts::new("memory_usage_bytes", "Current memory usage in bytes")
           )?;
           registry.register(Box::new(memory_usage.clone()))?;
   
           let db_connections = Gauge::with_opts(
               Opts::new("database_connections_active", "Number of active database connections")
           )?;
           registry.register(Box::new(db_connections.clone()))?;
   
           Ok(Self {
               conversion_queue_length,
               active_conversions,
               conversion_duration,
               conversion_errors,
               memory_usage,
               db_connections,
           })
       }
   
       pub async fn update_queue_length(&self, redis_client: &redis::Client) -> Result<(), redis::RedisError> {
           let mut conn = redis_client.get_async_connection().await?;
           let queue_length: i64 = redis::cmd("LLEN")
               .arg("conversion_queue")
               .query_async(&mut conn)
               .await?;
           
           self.conversion_queue_length.set(queue_length as f64);
           Ok(())
       }
   
       pub fn record_conversion_start(&self) {
           self.active_conversions.inc();
       }
   
       pub fn record_conversion_complete(&self, duration: std::time::Duration, success: bool) {
           self.active_conversions.dec();
           self.conversion_duration.observe(duration.as_secs_f64());
           
           if !success {
               self.conversion_errors.inc();
           }
       }
   
       pub fn update_memory_usage(&self) {
           if let Ok(info) = sys_info::mem_info() {
               let used_memory = (info.total - info.free) * 1024; // Convert to bytes
               self.memory_usage.set(used_memory as f64);
           }
       }
   }
   
   // Metrics collection service
   pub struct MetricsCollector {
       metrics: Arc<CustomMetrics>,
       redis_client: redis::Client,
       db_pool: sqlx::PgPool,
   }
   
   impl MetricsCollector {
       pub fn new(
           metrics: Arc<CustomMetrics>,
           redis_client: redis::Client,
           db_pool: sqlx::PgPool,
       ) -> Self {
           Self {
               metrics,
               redis_client,
               db_pool,
           }
       }
   
       pub async fn start_collection_loop(&self) {
           let mut interval = tokio::time::interval(std::time::Duration::from_secs(30));
           
           loop {
               interval.tick().await;
               
               // Update queue length
               if let Err(e) = self.metrics.update_queue_length(&self.redis_client).await {
                   eprintln!("Failed to update queue length metric: {}", e);
               }
   
               // Update memory usage
               self.metrics.update_memory_usage();
   
               // Update database connection count
               let db_connections = self.db_pool.size() as f64;
               self.metrics.db_connections.set(db_connections);
           }
       }
   }
   ```

**Success Criteria:**
- ✅ Automatic scaling based on CPU, memory, and custom metrics
- ✅ Scaling policies prevent thrashing
- ✅ Custom metrics collection for domain-specific scaling
- ✅ Resource optimization through VPA

---

### **Subtask 3.2.3: Circuit Breaker Pattern**

#### **Implementation Requirements:**

1. **Circuit Breaker Implementation**
   ```rust
   // shared/src/circuit_breaker.rs
   use std::sync::{Arc, Mutex};
   use std::time::{Duration, Instant};
   use tokio::time::timeout;
   
   #[derive(Debug, Clone, Copy, PartialEq)]
   pub enum CircuitState {
       Closed,   // Normal operation
       Open,     // Failing fast
       HalfOpen, // Testing if service recovered
   }
   
   pub struct CircuitBreaker {
       state: Arc<Mutex<CircuitBreakerState>>,
       config: CircuitBreakerConfig,
   }
   
   struct CircuitBreakerState {
       state: CircuitState,
       failure_count: u32,
       last_failure_time: Option<Instant>,
       last_success_time: Option<Instant>,
       next_attempt: Option<Instant>,
   }
   
   pub struct CircuitBreakerConfig {
       pub failure_threshold: u32,
       pub recovery_timeout: Duration,
       pub request_timeout: Duration,
       pub half_open_max_calls: u32,
   }
   
   impl Default for CircuitBreakerConfig {
       fn default() -> Self {
           Self {
               failure_threshold: 5,
               recovery_timeout: Duration::from_secs(60),
               request_timeout: Duration::from_secs(10),
               half_open_max_calls: 3,
           }
       }
   }
   
   #[derive(Debug)]
   pub enum CircuitBreakerError {
       CircuitOpen,
       Timeout,
       ServiceError(Box<dyn std::error::Error + Send + Sync>),
   }
   
   impl CircuitBreaker {
       pub fn new(config: CircuitBreakerConfig) -> Self {
           Self {
               state: Arc::new(Mutex::new(CircuitBreakerState {
                   state: CircuitState::Closed,
                   failure_count: 0,
                   last_failure_time: None,
                   last_success_time: None,
                   next_attempt: None,
               })),
               config,
           }
       }
   
       pub async fn call<F, Fut, T, E>(&self, operation: F) -> Result<T, CircuitBreakerError>
       where
           F: FnOnce() -> Fut,
           Fut: std::future::Future<Output = Result<T, E>>,
           E: Into<Box<dyn std::error::Error + Send + Sync>>,
       {
           // Check if we should attempt the call
           if !self.should_attempt_call() {
               return Err(CircuitBreakerError::CircuitOpen);
           }
   
           // Execute the operation with timeout
           let result = timeout(self.config.request_timeout, operation()).await;
   
           match result {
               Ok(Ok(value)) => {
                   self.on_success();
                   Ok(value)
               }
               Ok(Err(e)) => {
                   self.on_failure();
                   Err(CircuitBreakerError::ServiceError(e.into()))
               }
               Err(_) => {
                   self.on_failure();
                   Err(CircuitBreakerError::Timeout)
               }
           }
       }
   
       fn should_attempt_call(&self) -> bool {
           let mut state = self.state.lock().unwrap();
           let now = Instant::now();
   
           match state.state {
               CircuitState::Closed => true,
               CircuitState::Open => {
                   if let Some(next_attempt) = state.next_attempt {
                       if now >= next_attempt {
                           state.state = CircuitState::HalfOpen;
                           state.failure_count = 0;
                           true
                       } else {
                           false
                       }
                   } else {
                       false
                   }
               }
               CircuitState::HalfOpen => {
                   state.failure_count < self.config.half_open_max_calls
               }
           }
       }
   
       fn on_success(&self) {
           let mut state = self.state.lock().unwrap();
           state.failure_count = 0;
           state.last_success_time = Some(Instant::now());
           state.state = CircuitState::Closed;
           state.next_attempt = None;
       }
   
       fn on_failure(&self) {
           let mut state = self.state.lock().unwrap();
           state.failure_count += 1;
           state.last_failure_time = Some(Instant::now());
   
           if state.failure_count >= self.config.failure_threshold {
               state.state = CircuitState::Open;
               state.next_attempt = Some(Instant::now() + self.config.recovery_timeout);
           }
       }
   
       pub fn get_state(&self) -> CircuitState {
           self.state.lock().unwrap().state
       }
   
       pub fn get_stats(&self) -> CircuitBreakerStats {
           let state = self.state.lock().unwrap();
           CircuitBreakerStats {
               state: state.state,
               failure_count: state.failure_count,
               last_failure_time: state.last_failure_time,
               last_success_time: state.last_success_time,
           }
       }
   }
   
   #[derive(Debug)]
   pub struct CircuitBreakerStats {
       pub state: CircuitState,
       pub failure_count: u32,
       pub last_failure_time: Option<Instant>,
       pub last_success_time: Option<Instant>,
   }
   ```

2. **Service Client with Circuit Breaker**
   ```rust
   // shared/src/resilient_client.rs
   use crate::circuit_breaker::{CircuitBreaker, CircuitBreakerConfig, CircuitBreakerError};
   use reqwest::Client;
   use std::sync::Arc;
   
   pub struct ResilientServiceClient {
       client: Client,
       base_url: String,
       circuit_breaker: Arc<CircuitBreaker>,
   }
   
   impl ResilientServiceClient {
       pub fn new(base_url: String, circuit_config: Option<CircuitBreakerConfig>) -> Self {
           let config = circuit_config.unwrap_or_default();
           
           Self {
               client: Client::new(),
               base_url,
               circuit_breaker: Arc::new(CircuitBreaker::new(config)),
           }
       }
   
       pub async fn post_with_fallback<T, R, F>(
           &self,
           path: &str,
           body: &T,
           fallback: F,
       ) -> Result<R, Box<dyn std::error::Error + Send + Sync>>
       where
           T: serde::Serialize,
           R: serde::de::DeserializeOwned,
           F: FnOnce() -> Result<R, Box<dyn std::error::Error + Send + Sync>>,
       {
           let url = format!("{}{}", self.base_url, path);
           
           let result = self.circuit_breaker.call(|| async {
               self.client
                   .post(&url)
                   .json(body)
                   .send()
                   .await?
                   .json::<R>()
                   .await
                   .map_err(|e| e.into())
           }).await;
   
           match result {
               Ok(response) => Ok(response),
               Err(CircuitBreakerError::CircuitOpen) => {
                   // Circuit is open, use fallback
                   fallback()
               }
               Err(CircuitBreakerError::Timeout) => {
                   // Timeout, use fallback
                   fallback()
               }
               Err(CircuitBreakerError::ServiceError(e)) => {
                   // Service error, try fallback
                   fallback().or_else(|_| Err(e))
               }
           }
       }
   
       pub async fn get_with_cache<R>(
           &self,
           path: &str,
           cache_key: &str,
           cache: &Arc<dyn Cache<String, R>>,
       ) -> Result<R, Box<dyn std::error::Error + Send + Sync>>
       where
           R: serde::de::DeserializeOwned + Clone + Send + Sync + 'static,
       {
           // Try cache first
           if let Some(cached_value) = cache.get(cache_key) {
               return Ok(cached_value);
           }
   
           let url = format!("{}{}", self.base_url, path);
           
           let result = self.circuit_breaker.call(|| async {
               self.client
                   .get(&url)
                   .send()
                   .await?
                   .json::<R>()
                   .await
                   .map_err(|e| e.into())
           }).await;
   
           match result {
               Ok(response) => {
                   // Cache successful response
                   cache.set(cache_key.to_string(), response.clone());
                   Ok(response)
               }
               Err(_) => {
                   // If circuit is open or service fails, return cached value if available
                   cache.get(cache_key)
                       .ok_or_else(|| "No cached value available".into())
               }
           }
       }
   }
   ```

3. **Fallback Strategies**
   ```rust
   // services/conversion-service/src/fallback.rs
   use serde::{Deserialize, Serialize};
   
   pub struct ConversionFallbackHandler {
       cached_results: Arc<dyn Cache<String, ConversionResult>>,
       simple_converter: SimpleConverter,
   }
   
   impl ConversionFallbackHandler {
       pub fn new(cache: Arc<dyn Cache<String, ConversionResult>>) -> Self {
           Self {
               cached_results: cache,
               simple_converter: SimpleConverter::new(),
           }
       }
   
       pub fn handle_conversion_failure(
           &self,
           request: &ConversionRequest,
           error: &CircuitBreakerError,
       ) -> Result<ConversionResult, ConversionError> {
           // Try cached result first
           let cache_key = self.generate_cache_key(request);
           if let Some(cached_result) = self.cached_results.get(&cache_key) {
               return Ok(cached_result);
           }
   
           // Fallback to simple conversion for basic formats
           match (&request.input_format.as_str(), &request.output_format.as_str()) {
               ("rtf", "txt") => {
                   // Simple text extraction
                   let text = self.simple_converter.extract_text_from_rtf(&request.content)?;
                   Ok(ConversionResult {
                       content: text,
                       metadata: HashMap::new(),
                       warnings: vec!["Used fallback conversion - formatting may be lost".to_string()],
                       processing_time_ms: 0,
                   })
               }
               ("md", "txt") => {
                   // Simple markdown to text
                   let text = self.simple_converter.markdown_to_text(&request.content)?;
                   Ok(ConversionResult {
                       content: text,
                       metadata: HashMap::new(),
                       warnings: vec!["Used fallback conversion".to_string()],
                       processing_time_ms: 0,
                   })
               }
               _ => {
                   // No fallback available
                   Err(ConversionError::ServiceUnavailable {
                       message: format!("Conversion service unavailable and no fallback for {} to {}", 
                                      request.input_format, request.output_format),
                       retry_after: Some(Duration::from_secs(60)),
                   })
               }
           }
       }
   
       fn generate_cache_key(&self, request: &ConversionRequest) -> String {
           use std::collections::hash_map::DefaultHasher;
           use std::hash::{Hash, Hasher};
           
           let mut hasher = DefaultHasher::new();
           request.input_format.hash(&mut hasher);
           request.output_format.hash(&mut hasher);
           request.content.hash(&mut hasher);
           
           format!("conversion:{:x}", hasher.finish())
       }
   }
   ```

**Success Criteria:**
- ✅ Circuit breakers prevent cascade failures
- ✅ Fallback strategies provide degraded service
- ✅ Automatic recovery when services restore
- ✅ Monitoring and alerting for circuit breaker states

---

## 📊 PHASE 3 SUCCESS CRITERIA

### **Service Architecture Achievement**
- ✅ **Independent Services**: Each service can be deployed independently
- ✅ **Horizontal Scaling**: Linear scalability up to 10 instances per service
- ✅ **API Gateway**: Centralized routing, authentication, and rate limiting
- ✅ **Database Layer**: ACID compliance with comprehensive audit trail

### **Scalability Implementation**
- ✅ **Auto-Scaling**: HPA and VPA configured with custom metrics
- ✅ **Stateless Design**: All services externalize state to Redis/PostgreSQL
- ✅ **Load Balancing**: Traffic distributed evenly across instances
- ✅ **Circuit Breakers**: Fault tolerance and graceful degradation

### **Enterprise Architecture Patterns**
- ✅ **Event-Driven Communication**: Asynchronous processing with Redis
- ✅ **Service Mesh Ready**: Prepared for Istio/Linkerd integration
- ✅ **Observability**: Comprehensive monitoring and tracing
- ✅ **Security**: Service-to-service authentication and authorization

**Next Phase Dependency:** Phase 4 (Comprehensive Testing) requires stable, scalable architecture from Phase 3.