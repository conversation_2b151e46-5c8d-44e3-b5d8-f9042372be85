// User management handlers
use axum::{
    extract::{Extension, Path, Query},
    http::StatusCode,
    response::<PERSON><PERSON> as Response<PERSON><PERSON>,
    <PERSON><PERSON>,
};
use legacybridge_shared::{
    auth::{UserInfo, password},
    database::{UserRepository, CreateUserRequest, UpdateUserRequest, Repository},
    types::{ApiResponse, PaginationParams, PaginatedResponse, User},
    ServiceError, ServiceResult,
};
use serde::{Deserialize, Serialize};
use tracing::{info, warn, error};
use uuid::Uuid;
use validator::Validate;

use crate::AppState;

#[derive(Debug, Deserialize, Validate)]
pub struct CreateUserRequestBody {
    #[validate(length(min = 3, max = 50))]
    pub username: String,
    #[validate(email)]
    pub email: String,
    #[validate(length(min = 8))]
    pub password: String,
    pub roles: Option<Vec<String>>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateUserRequestBody {
    #[validate(length(min = 3, max = 50))]
    pub username: Option<String>,
    #[validate(email)]
    pub email: Option<String>,
    pub roles: Option<Vec<String>>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct ChangePasswordRequest {
    #[validate(length(min = 1))]
    pub current_password: String,
    #[validate(length(min = 8))]
    pub new_password: String,
}

/// List users with pagination
pub async fn list_users(
    Extension(state): Extension<AppState>,
    Query(params): Query<PaginationParams>,
) -> ServiceResult<ResponseJson<ApiResponse<PaginatedResponse<User>>>> {
    info!("Listing users");

    let user_repo = UserRepository::new(state.db.pool().clone());
    
    let page = params.page.unwrap_or(1);
    let limit = params.limit.unwrap_or(20).min(100); // Max 100 items per page
    let offset = (page - 1) * limit;

    let users = user_repo.list(limit as i64, offset as i64).await?;
    let total = user_repo.count().await? as u64;
    let total_pages = (total + limit as u64 - 1) / limit as u64;

    let response = PaginatedResponse {
        items: users,
        total,
        page,
        limit,
        total_pages: total_pages as u32,
    };

    Ok(ResponseJson(ApiResponse::success(response)))
}

/// Create a new user
pub async fn create_user(
    Extension(state): Extension<AppState>,
    Json(request): Json<CreateUserRequestBody>,
) -> ServiceResult<ResponseJson<ApiResponse<User>>> {
    info!(username = %request.username, email = %request.email, "Creating new user");

    // Validate request
    request.validate().map_err(ServiceError::from)?;

    let user_repo = UserRepository::new(state.db.pool().clone());

    // Check if username already exists
    if user_repo.find_by_username(&request.username).await?.is_some() {
        return Err(ServiceError::Conflict("Username already exists".to_string()));
    }

    // Check if email already exists
    if user_repo.find_by_email(&request.email).await?.is_some() {
        return Err(ServiceError::Conflict("Email already exists".to_string()));
    }

    // Create user
    let create_request = CreateUserRequest {
        username: request.username,
        email: request.email,
        password: request.password,
        roles: request.roles.unwrap_or_else(|| vec!["user".to_string()]),
    };

    let user = user_repo.create(create_request).await?;

    info!(user_id = %user.id, username = %user.username, "User created successfully");

    Ok(ResponseJson(ApiResponse::success(user)))
}

/// Get user by ID
pub async fn get_user(
    Extension(state): Extension<AppState>,
    Path(user_id): Path<Uuid>,
) -> ServiceResult<ResponseJson<ApiResponse<User>>> {
    info!(user_id = %user_id, "Getting user");

    let user_repo = UserRepository::new(state.db.pool().clone());
    
    let user = user_repo
        .find_by_id(user_id)
        .await?
        .ok_or_else(|| ServiceError::NotFound("User not found".to_string()))?;

    Ok(ResponseJson(ApiResponse::success(user)))
}

/// Update user
pub async fn update_user(
    Extension(state): Extension<AppState>,
    Path(user_id): Path<Uuid>,
    Json(request): Json<UpdateUserRequestBody>,
) -> ServiceResult<ResponseJson<ApiResponse<User>>> {
    info!(user_id = %user_id, "Updating user");

    // Validate request
    request.validate().map_err(ServiceError::from)?;

    let user_repo = UserRepository::new(state.db.pool().clone());

    // Check if user exists
    if user_repo.find_by_id(user_id).await?.is_none() {
        return Err(ServiceError::NotFound("User not found".to_string()));
    }

    // Check for username conflicts
    if let Some(ref username) = request.username {
        if let Some(existing_user) = user_repo.find_by_username(username).await? {
            if existing_user.id != user_id {
                return Err(ServiceError::Conflict("Username already exists".to_string()));
            }
        }
    }

    // Check for email conflicts
    if let Some(ref email) = request.email {
        if let Some(existing_user) = user_repo.find_by_email(email).await? {
            if existing_user.id != user_id {
                return Err(ServiceError::Conflict("Email already exists".to_string()));
            }
        }
    }

    let update_request = UpdateUserRequest {
        username: request.username,
        email: request.email,
        roles: request.roles,
        is_active: request.is_active,
    };

    let user = user_repo.update(user_id, update_request).await?;

    // Invalidate user session if user was deactivated
    if let Some(false) = request.is_active {
        let session_key = format!("session:{}", user_id);
        if let Err(e) = state.cache.delete(&session_key).await {
            warn!(error = %e, "Failed to invalidate user session");
        }
    }

    info!(user_id = %user_id, "User updated successfully");

    Ok(ResponseJson(ApiResponse::success(user)))
}

/// Delete user (soft delete)
pub async fn delete_user(
    Extension(state): Extension<AppState>,
    Path(user_id): Path<Uuid>,
) -> ServiceResult<StatusCode> {
    info!(user_id = %user_id, "Deleting user");

    let user_repo = UserRepository::new(state.db.pool().clone());

    // Check if user exists
    if user_repo.find_by_id(user_id).await?.is_none() {
        return Err(ServiceError::NotFound("User not found".to_string()));
    }

    // Soft delete user
    user_repo.delete(user_id).await?;

    // Invalidate user session
    let session_key = format!("session:{}", user_id);
    if let Err(e) = state.cache.delete(&session_key).await {
        warn!(error = %e, "Failed to invalidate user session");
    }

    info!(user_id = %user_id, "User deleted successfully");

    Ok(StatusCode::NO_CONTENT)
}

/// Get current user profile (requires authentication)
pub async fn get_profile(
    Extension(state): Extension<AppState>,
    // TODO: Add authentication middleware to extract user from JWT
) -> ServiceResult<ResponseJson<ApiResponse<User>>> {
    // This would be implemented with authentication middleware
    // For now, return an error
    Err(ServiceError::Internal("Authentication middleware not implemented".to_string()))
}

/// Update current user profile
pub async fn update_profile(
    Extension(state): Extension<AppState>,
    Json(request): Json<UpdateUserRequestBody>,
) -> ServiceResult<ResponseJson<ApiResponse<User>>> {
    // This would be implemented with authentication middleware
    // For now, return an error
    Err(ServiceError::Internal("Authentication middleware not implemented".to_string()))
}

/// Change password for current user
pub async fn change_password(
    Extension(state): Extension<AppState>,
    Json(request): Json<ChangePasswordRequest>,
) -> ServiceResult<ResponseJson<ApiResponse<String>>> {
    // This would be implemented with authentication middleware
    // For now, return an error
    Err(ServiceError::Internal("Authentication middleware not implemented".to_string()))
}
