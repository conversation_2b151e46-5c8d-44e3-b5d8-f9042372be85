// Simple LegacyBridge MCP Server Binary
// Minimal standalone MCP server for testing

#[cfg(feature = "mcp")]
use legacybridge::mcp::official_server::LegacyBridgeMcpServerOfficial;
use legacybridge::config::Config;
use tracing::{info, error};
use tracing_subscriber;

#[cfg(feature = "mcp")]
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing
    tracing_subscriber::fmt::init();
    
    info!("Starting LegacyBridge MCP Server (Simple)");
    
    // Load configuration
    let config = Config::default();
    
    // Create and run the MCP server
    let server = LegacyBridgeMcpServerOfficial::new(config);
    
    match server.run_stdio_server().await {
        Ok(_) => {
            info!("MCP Server stopped gracefully");
            Ok(())
        }
        Err(e) => {
            error!("MCP Server error: {}", e);
            Err(e)
        }
    }
}

#[cfg(not(feature = "mcp"))]
fn main() {
    eprintln!("MCP feature is not enabled. Please compile with --features mcp");
    std::process::exit(1);
}