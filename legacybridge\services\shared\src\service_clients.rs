// Service-specific resilient clients with fallback strategies
use crate::resilient_client::{ResilientServiceClient, FallbackStrategy, CacheFallbackStrategy, StaticResponseFallbackStrategy, DegradedServiceFallbackStrategy};
use crate::circuit_breaker::CircuitBreakerRegistry;
use crate::cache::CacheManager;
use crate::service_discovery::ServiceRegistry;
use crate::error::{ServiceError, ServiceResult};
use crate::types::*;
use crate::auth::{UserInfo, TokenValidationResponse};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::Duration;
use uuid::Uuid;

/// Resilient Auth Service Client with fallback strategies
pub struct ResilientAuthServiceClient {
    client: ResilientServiceClient,
}

impl ResilientAuthServiceClient {
    pub fn new(
        circuit_breaker_registry: Arc<CircuitBreakerRegistry>,
        cache: Arc<CacheManager>,
        service_registry: Arc<ServiceRegistry>,
    ) -> Self {
        let mut client = ResilientServiceClient::new(
            circuit_breaker_registry,
            cache,
            service_registry,
        );

        // Register fallback strategies for auth service
        client.register_fallback(
            "auth-service".to_string(),
            AuthServiceFallbackStrategy::new(),
        );

        Self { client }
    }

    pub fn with_auth_token(mut self, token: String) -> Self {
        self.client = self.client.with_auth_token(token);
        self
    }

    pub async fn validate_token(&self, token: &str) -> ServiceResult<TokenValidationResponse> {
        #[derive(Serialize)]
        struct ValidateRequest {
            token: String,
        }

        let request = ValidateRequest {
            token: token.to_string(),
        };

        let cache_key = format!("auth:token_validation:{}", token);
        
        self.client
            .request_with_fallback(
                "auth-service",
                "POST",
                "/validate",
                Some(&request),
                Some(&cache_key),
                Some(Duration::from_secs(300)), // Cache for 5 minutes
            )
            .await
    }

    pub async fn get_user_info(&self, user_id: Uuid) -> ServiceResult<UserInfo> {
        let cache_key = format!("auth:user_info:{}", user_id);
        
        self.client
            .request_with_fallback::<(), UserInfo>(
                "auth-service",
                "GET",
                &format!("/users/{}", user_id),
                None,
                Some(&cache_key),
                Some(Duration::from_secs(600)), // Cache for 10 minutes
            )
            .await
    }
}

/// Fallback strategy for Auth Service
pub struct AuthServiceFallbackStrategy;

impl AuthServiceFallbackStrategy {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl FallbackStrategy for AuthServiceFallbackStrategy {
    async fn execute<T, R>(
        &self,
        method: &str,
        path: &str,
        _body: Option<&T>,
        cache: &CacheManager,
    ) -> ServiceResult<R>
    where
        T: serde::Serialize + Send + Sync,
        R: for<'de> serde::Deserialize<'de> + Clone + Send + Sync + 'static,
    {
        // For token validation, return a degraded response
        if path == "/validate" && method == "POST" {
            let degraded_response = serde_json::json!({
                "valid": false,
                "user_id": null,
                "error": "Authentication service unavailable - access denied for safety"
            });
            
            let response = serde_json::from_value(degraded_response)?;
            return Ok(response);
        }

        // For user info, try cache first
        if path.starts_with("/users/") && method == "GET" {
            let cache_key = format!("auth:user_info:{}", path.trim_start_matches("/users/"));
            
            if let Ok(Some(cached_user)) = cache.get::<R>(&cache_key).await {
                return Ok(cached_user);
            }
        }

        Err(ServiceError::ServiceUnavailable(
            "Auth service unavailable and no fallback data".to_string(),
        ))
    }
}

/// Resilient Conversion Service Client with fallback strategies
pub struct ResilientConversionServiceClient {
    client: ResilientServiceClient,
}

impl ResilientConversionServiceClient {
    pub fn new(
        circuit_breaker_registry: Arc<CircuitBreakerRegistry>,
        cache: Arc<CacheManager>,
        service_registry: Arc<ServiceRegistry>,
    ) -> Self {
        let mut client = ResilientServiceClient::new(
            circuit_breaker_registry,
            cache,
            service_registry,
        );

        // Register fallback strategies for conversion service
        client.register_fallback(
            "conversion-service".to_string(),
            ConversionServiceFallbackStrategy::new(),
        );

        Self { client }
    }

    pub fn with_auth_token(mut self, token: String) -> Self {
        self.client = self.client.with_auth_token(token);
        self
    }

    pub async fn convert_document(&self, request: &ConversionRequest) -> ServiceResult<ConversionResponse> {
        // No caching for conversion requests as they're unique
        self.client
            .request_with_fallback(
                "conversion-service",
                "POST",
                "/convert",
                Some(request),
                None,
                None,
            )
            .await
    }

    pub async fn get_conversion_status(&self, job_id: &str) -> ServiceResult<JobStatusResponse> {
        let cache_key = format!("conversion:status:{}", job_id);
        
        self.client
            .request_with_fallback::<(), JobStatusResponse>(
                "conversion-service",
                "GET",
                &format!("/convert/{}", job_id),
                None,
                Some(&cache_key),
                Some(Duration::from_secs(30)), // Short cache for status
            )
            .await
    }

    pub async fn get_conversion_result(&self, job_id: &str) -> ServiceResult<ConversionResult> {
        let cache_key = format!("conversion:result:{}", job_id);
        
        self.client
            .request_with_fallback::<(), ConversionResult>(
                "conversion-service",
                "GET",
                &format!("/convert/{}/result", job_id),
                None,
                Some(&cache_key),
                Some(Duration::from_secs(3600)), // Cache results for 1 hour
            )
            .await
    }
}

/// Fallback strategy for Conversion Service
pub struct ConversionServiceFallbackStrategy;

impl ConversionServiceFallbackStrategy {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl FallbackStrategy for ConversionServiceFallbackStrategy {
    async fn execute<T, R>(
        &self,
        method: &str,
        path: &str,
        _body: Option<&T>,
        cache: &CacheManager,
    ) -> ServiceResult<R>
    where
        T: serde::Serialize + Send + Sync,
        R: for<'de> serde::Deserialize<'de> + Clone + Send + Sync + 'static,
    {
        // For new conversion requests, return error
        if path == "/convert" && method == "POST" {
            return Err(ServiceError::ServiceUnavailable(
                "Conversion service is temporarily unavailable. Please try again later.".to_string(),
            ));
        }

        // For status checks, try cache first
        if path.contains("/convert/") && method == "GET" {
            let job_id = path.split('/').nth(2).unwrap_or("");
            
            if path.ends_with("/result") {
                let cache_key = format!("conversion:result:{}", job_id);
                if let Ok(Some(cached_result)) = cache.get::<R>(&cache_key).await {
                    return Ok(cached_result);
                }
            } else {
                let cache_key = format!("conversion:status:{}", job_id);
                if let Ok(Some(cached_status)) = cache.get::<R>(&cache_key).await {
                    return Ok(cached_status);
                }
                
                // Return degraded status response
                let degraded_response = serde_json::json!({
                    "job_id": job_id,
                    "status": "unknown",
                    "message": "Conversion service unavailable - status unknown"
                });
                
                let response = serde_json::from_value(degraded_response)?;
                return Ok(response);
            }
        }

        Err(ServiceError::ServiceUnavailable(
            "Conversion service unavailable and no fallback data".to_string(),
        ))
    }
}

/// Resilient File Service Client with fallback strategies
pub struct ResilientFileServiceClient {
    client: ResilientServiceClient,
}

impl ResilientFileServiceClient {
    pub fn new(
        circuit_breaker_registry: Arc<CircuitBreakerRegistry>,
        cache: Arc<CacheManager>,
        service_registry: Arc<ServiceRegistry>,
    ) -> Self {
        let mut client = ResilientServiceClient::new(
            circuit_breaker_registry,
            cache,
            service_registry,
        );

        // Register fallback strategies for file service
        client.register_fallback(
            "file-service".to_string(),
            FileServiceFallbackStrategy::new(),
        );

        Self { client }
    }

    pub fn with_auth_token(mut self, token: String) -> Self {
        self.client = self.client.with_auth_token(token);
        self
    }

    pub async fn get_file_metadata(&self, file_id: Uuid) -> ServiceResult<FileMetadata> {
        let cache_key = format!("file:metadata:{}", file_id);
        
        self.client
            .request_with_fallback::<(), FileMetadata>(
                "file-service",
                "GET",
                &format!("/files/{}/metadata", file_id),
                None,
                Some(&cache_key),
                Some(Duration::from_secs(1800)), // Cache for 30 minutes
            )
            .await
    }

    pub async fn delete_file(&self, file_id: Uuid) -> ServiceResult<()> {
        self.client
            .request_with_fallback::<(), ()>(
                "file-service",
                "DELETE",
                &format!("/files/{}", file_id),
                None,
                None,
                None,
            )
            .await
    }
}

/// Fallback strategy for File Service
pub struct FileServiceFallbackStrategy;

impl FileServiceFallbackStrategy {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl FallbackStrategy for FileServiceFallbackStrategy {
    async fn execute<T, R>(
        &self,
        method: &str,
        path: &str,
        _body: Option<&T>,
        cache: &CacheManager,
    ) -> ServiceResult<R>
    where
        T: serde::Serialize + Send + Sync,
        R: for<'de> serde::Deserialize<'de> + Clone + Send + Sync + 'static,
    {
        // For file metadata, try cache first
        if path.contains("/files/") && path.contains("/metadata") && method == "GET" {
            let file_id = path.split('/').nth(2).unwrap_or("");
            let cache_key = format!("file:metadata:{}", file_id);
            
            if let Ok(Some(cached_metadata)) = cache.get::<R>(&cache_key).await {
                return Ok(cached_metadata);
            }
        }

        // For file operations, return appropriate error
        if method == "DELETE" {
            return Err(ServiceError::ServiceUnavailable(
                "File service unavailable - cannot delete file at this time".to_string(),
            ));
        }

        Err(ServiceError::ServiceUnavailable(
            "File service unavailable and no fallback data".to_string(),
        ))
    }
}
