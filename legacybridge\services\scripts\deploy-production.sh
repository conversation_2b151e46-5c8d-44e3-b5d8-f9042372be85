#!/bin/bash

# Production Deployment Script for LegacyBridge
# Deploys the complete microservices architecture to Kubernetes

set -e

# Configuration
NAMESPACE="legacybridge"
KUBECTL_TIMEOUT="600s"
DEPLOYMENT_VERSION="${DEPLOYMENT_VERSION:-v1.0.0}"
ENVIRONMENT="${ENVIRONMENT:-production}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo "🚀 LegacyBridge Production Deployment"
echo "====================================="
echo "Version: $DEPLOYMENT_VERSION"
echo "Environment: $ENVIRONMENT"
echo "Namespace: $NAMESPACE"
echo "Timestamp: $(date)"
echo ""

# Function to check prerequisites
check_prerequisites() {
    echo -e "${BLUE}🔍 Checking prerequisites...${NC}"
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        echo -e "${RED}❌ kubectl is not installed${NC}"
        exit 1
    fi
    
    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        echo -e "${RED}❌ Cannot connect to Kubernetes cluster${NC}"
        exit 1
    fi
    
    # Check if we have the right context
    local current_context=$(kubectl config current-context)
    echo -e "${GREEN}✅ Connected to cluster: $current_context${NC}"
    
    # Check if namespace exists
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        echo -e "${GREEN}✅ Namespace $NAMESPACE exists${NC}"
    else
        echo -e "${YELLOW}⚠️  Creating namespace $NAMESPACE${NC}"
        kubectl create namespace "$NAMESPACE"
        kubectl label namespace "$NAMESPACE" name="$NAMESPACE"
    fi
    
    # Check for required storage classes
    if kubectl get storageclass fast-ssd &> /dev/null; then
        echo -e "${GREEN}✅ Storage class 'fast-ssd' available${NC}"
    else
        echo -e "${YELLOW}⚠️  Storage class 'fast-ssd' not found, using default${NC}"
        # Update manifests to use default storage class
        find ../k8s/production -name "*.yaml" -exec sed -i 's/storageClassName: "fast-ssd"/storageClassName: "default"/g' {} \;
    fi
}

# Function to apply manifests with retry
apply_manifest() {
    local manifest_file=$1
    local description=$2
    local max_retries=${3:-3}
    local retry_count=0
    
    echo -e "${BLUE}📄 Applying $description...${NC}"
    
    while [ $retry_count -lt $max_retries ]; do
        if kubectl apply -f "$manifest_file" --timeout="$KUBECTL_TIMEOUT"; then
            echo -e "${GREEN}✅ Successfully applied $description${NC}"
            return 0
        else
            retry_count=$((retry_count + 1))
            if [ $retry_count -lt $max_retries ]; then
                echo -e "${YELLOW}⚠️  Retry $retry_count/$max_retries for $description${NC}"
                sleep 10
            else
                echo -e "${RED}❌ Failed to apply $description after $max_retries attempts${NC}"
                return 1
            fi
        fi
    done
}

# Function to wait for deployment
wait_for_deployment() {
    local deployment_name=$1
    local timeout=${2:-600}
    
    echo -e "${BLUE}⏳ Waiting for deployment $deployment_name...${NC}"
    
    if kubectl wait --for=condition=available deployment/"$deployment_name" \
        --namespace="$NAMESPACE" --timeout="${timeout}s"; then
        echo -e "${GREEN}✅ Deployment $deployment_name is ready${NC}"
        return 0
    else
        echo -e "${RED}❌ Deployment $deployment_name failed to become ready${NC}"
        
        # Show pod status for debugging
        echo "Pod status:"
        kubectl get pods -n "$NAMESPACE" -l app="$deployment_name" -o wide
        
        # Show recent events
        echo "Recent events:"
        kubectl get events -n "$NAMESPACE" --sort-by='.lastTimestamp' | tail -10
        
        return 1
    fi
}

# Function to wait for statefulset
wait_for_statefulset() {
    local statefulset_name=$1
    local timeout=${2:-600}
    
    echo -e "${BLUE}⏳ Waiting for statefulset $statefulset_name...${NC}"
    
    if kubectl wait --for=condition=ready pod -l app="$statefulset_name" \
        --namespace="$NAMESPACE" --timeout="${timeout}s"; then
        echo -e "${GREEN}✅ StatefulSet $statefulset_name is ready${NC}"
        return 0
    else
        echo -e "${RED}❌ StatefulSet $statefulset_name failed to become ready${NC}"
        return 1
    fi
}

# Function to verify service health
verify_service_health() {
    local service_name=$1
    local port=$2
    local health_path=${3:-"/health"}
    
    echo -e "${BLUE}🔍 Verifying $service_name health...${NC}"
    
    # Port forward to test service
    kubectl port-forward -n "$NAMESPACE" "svc/$service_name" "$port:$port" &
    local port_forward_pid=$!
    
    # Wait for port forward to establish
    sleep 5
    
    # Test health endpoint
    local health_check_attempts=0
    local max_health_attempts=10
    
    while [ $health_check_attempts -lt $max_health_attempts ]; do
        if curl -f -s "http://localhost:$port$health_path" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name is healthy${NC}"
            kill $port_forward_pid 2>/dev/null || true
            return 0
        fi
        
        health_check_attempts=$((health_check_attempts + 1))
        sleep 3
    done
    
    echo -e "${YELLOW}⚠️  $service_name health check inconclusive${NC}"
    kill $port_forward_pid 2>/dev/null || true
    return 1
}

# Function to deploy infrastructure
deploy_infrastructure() {
    echo -e "${PURPLE}🏗️  Deploying infrastructure services...${NC}"
    
    # Deploy infrastructure (PostgreSQL, Redis, Kong)
    apply_manifest "../k8s/production/infrastructure.yaml" "Infrastructure services"
    
    # Wait for PostgreSQL
    wait_for_statefulset "postgresql" 600
    
    # Wait for Redis
    wait_for_deployment "redis" 300
    
    # Wait for Kong Gateway
    wait_for_deployment "kong-gateway" 600
    
    echo -e "${GREEN}✅ Infrastructure deployment complete${NC}"
}

# Function to deploy microservices
deploy_microservices() {
    echo -e "${PURPLE}🔧 Deploying microservices...${NC}"
    
    # Deploy services in dependency order
    local services=("auth-service" "conversion-service" "file-service" "job-service")
    
    for service in "${services[@]}"; do
        echo -e "${BLUE}📦 Deploying $service...${NC}"
        
        if [ -f "../k8s/production/${service}-deployment.yaml" ]; then
            apply_manifest "../k8s/production/${service}-deployment.yaml" "$service"
            wait_for_deployment "$service" 600
        else
            echo -e "${YELLOW}⚠️  Deployment file for $service not found${NC}"
        fi
    done
    
    echo -e "${GREEN}✅ Microservices deployment complete${NC}"
}

# Function to deploy monitoring and auto-scaling
deploy_monitoring_and_scaling() {
    echo -e "${PURPLE}📊 Deploying monitoring and auto-scaling...${NC}"
    
    # Deploy auto-scaling configurations
    if [ -f "../k8s/auth-service-autoscaling.yaml" ]; then
        apply_manifest "../k8s/auth-service-autoscaling.yaml" "Auth service auto-scaling"
    fi
    
    if [ -f "../k8s/conversion-service-autoscaling.yaml" ]; then
        apply_manifest "../k8s/conversion-service-autoscaling.yaml" "Conversion service auto-scaling"
    fi
    
    if [ -f "../k8s/file-service-autoscaling.yaml" ]; then
        apply_manifest "../k8s/file-service-autoscaling.yaml" "File service auto-scaling"
    fi
    
    if [ -f "../k8s/job-service-autoscaling.yaml" ]; then
        apply_manifest "../k8s/job-service-autoscaling.yaml" "Job service auto-scaling"
    fi
    
    # Deploy monitoring stack
    if [ -f "../k8s/monitoring-stack.yaml" ]; then
        apply_manifest "../k8s/monitoring-stack.yaml" "Monitoring stack"
        
        # Wait for monitoring components
        wait_for_deployment "prometheus" 300
        wait_for_deployment "grafana" 300
        wait_for_deployment "alertmanager" 300
    fi
    
    echo -e "${GREEN}✅ Monitoring and auto-scaling deployment complete${NC}"
}

# Function to configure Kong routes
configure_kong_routes() {
    echo -e "${PURPLE}🌐 Configuring Kong API Gateway routes...${NC}"
    
    # Wait for Kong to be fully ready
    sleep 30
    
    # Run Kong configuration script
    if [ -f "./setup-kong-advanced-routing.sh" ]; then
        echo -e "${BLUE}🔧 Setting up Kong routes...${NC}"
        
        # Port forward to Kong admin API
        kubectl port-forward -n "$NAMESPACE" svc/kong-gateway 8001:8001 &
        local kong_pid=$!
        
        sleep 10
        
        # Run Kong setup
        if ./setup-kong-advanced-routing.sh; then
            echo -e "${GREEN}✅ Kong routes configured successfully${NC}"
        else
            echo -e "${YELLOW}⚠️  Kong route configuration may have issues${NC}"
        fi
        
        kill $kong_pid 2>/dev/null || true
    else
        echo -e "${YELLOW}⚠️  Kong setup script not found${NC}"
    fi
}

# Function to run post-deployment verification
verify_deployment() {
    echo -e "${PURPLE}🔍 Running post-deployment verification...${NC}"
    
    # Check all deployments
    echo -e "${BLUE}📊 Deployment status:${NC}"
    kubectl get deployments -n "$NAMESPACE" -o wide
    
    echo ""
    echo -e "${BLUE}📊 Pod status:${NC}"
    kubectl get pods -n "$NAMESPACE" -o wide
    
    echo ""
    echo -e "${BLUE}📊 Service status:${NC}"
    kubectl get services -n "$NAMESPACE" -o wide
    
    echo ""
    echo -e "${BLUE}📊 HPA status:${NC}"
    kubectl get hpa -n "$NAMESPACE" -o wide 2>/dev/null || echo "No HPAs found"
    
    # Verify service health
    local services=("auth-service:3001" "conversion-service:3002" "file-service:3003" "job-service:3004")
    
    for service_info in "${services[@]}"; do
        local service_name=$(echo $service_info | cut -d: -f1)
        local port=$(echo $service_info | cut -d: -f2)
        
        verify_service_health "$service_name" "$port"
    done
    
    # Check Kong Gateway
    verify_service_health "kong-gateway" "8000" "/status"
}

# Function to show access information
show_access_info() {
    echo ""
    echo -e "${BLUE}🌐 Access Information${NC}"
    echo "===================="
    
    # Get Kong Gateway external IP
    local kong_external_ip=$(kubectl get svc kong-gateway -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "pending")
    
    if [ "$kong_external_ip" != "pending" ] && [ -n "$kong_external_ip" ]; then
        echo -e "${GREEN}🌍 Kong Gateway External IP: $kong_external_ip${NC}"
        echo "  API Endpoints:"
        echo "    Auth Service: http://$kong_external_ip/auth"
        echo "    Conversion Service: http://$kong_external_ip/convert"
        echo "    File Service: http://$kong_external_ip/files"
        echo "    Job Service: http://$kong_external_ip/jobs"
        echo ""
        echo "  Admin Interface: http://$kong_external_ip:8001"
        echo "  Manager Interface: http://$kong_external_ip:8002"
    else
        echo -e "${YELLOW}⚠️  Kong Gateway external IP is pending...${NC}"
        echo "  Use port-forward to access services:"
        echo "    kubectl port-forward -n $NAMESPACE svc/kong-gateway 8000:80"
    fi
    
    echo ""
    echo -e "${BLUE}📊 Monitoring Access:${NC}"
    echo "  Prometheus: kubectl port-forward -n monitoring svc/prometheus 9090:9090"
    echo "  Grafana: kubectl port-forward -n monitoring svc/grafana 3000:3000"
    echo "  AlertManager: kubectl port-forward -n monitoring svc/alertmanager 9093:9093"
    
    echo ""
    echo -e "${BLUE}🔧 Useful Commands:${NC}"
    echo "  View logs: kubectl logs -n $NAMESPACE -l app=<service-name> -f"
    echo "  Scale service: kubectl scale deployment <service-name> --replicas=<count> -n $NAMESPACE"
    echo "  Check HPA: kubectl get hpa -n $NAMESPACE -w"
    echo "  View events: kubectl get events -n $NAMESPACE --sort-by='.lastTimestamp'"
}

# Main deployment function
main() {
    local deployment_start=$(date +%s)
    
    check_prerequisites
    
    echo ""
    echo -e "${PURPLE}🚀 Starting production deployment...${NC}"
    
    # Deploy in phases
    deploy_infrastructure
    sleep 30  # Allow infrastructure to stabilize
    
    deploy_microservices
    sleep 15  # Allow services to start
    
    deploy_monitoring_and_scaling
    sleep 15  # Allow monitoring to start
    
    configure_kong_routes
    sleep 10  # Allow routes to propagate
    
    verify_deployment
    
    local deployment_end=$(date +%s)
    local deployment_duration=$((deployment_end - deployment_start))
    
    echo ""
    echo -e "${GREEN}🎉 Production deployment completed successfully!${NC}"
    echo -e "${BLUE}⏱️  Total deployment time: ${deployment_duration}s${NC}"
    
    show_access_info
    
    echo ""
    echo -e "${BLUE}📋 Next Steps:${NC}"
    echo "1. Verify all services are healthy and responding"
    echo "2. Run the comprehensive test suite"
    echo "3. Configure monitoring alerts and dashboards"
    echo "4. Set up backup and disaster recovery procedures"
    echo "5. Configure CI/CD pipelines for future deployments"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "verify")
        verify_deployment
        ;;
    "info")
        show_access_info
        ;;
    "help")
        echo "Usage: $0 [deploy|verify|info|help]"
        echo "  deploy: Full production deployment (default)"
        echo "  verify: Run post-deployment verification"
        echo "  info:   Show access information"
        echo "  help:   Show this help message"
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
