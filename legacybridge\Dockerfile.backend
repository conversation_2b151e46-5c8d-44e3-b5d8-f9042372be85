# Multi-stage build for LegacyBridge API Server
FROM rust:1.75-slim AS builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    build-essential \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy the entire legacybridge directory
COPY . .

# Move to the src-tauri directory where Cargo.toml is located
WORKDIR /app/src-tauri

# Build the API server binary
RUN cargo build --release --bin api_server

# Runtime image
FROM debian:bookworm-slim AS runtime

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -r -s /bin/false legacybridge

# Create directories
RUN mkdir -p /app/data /app/logs /app/cache \
    && chown legacybridge:legacybridge /app/data /app/logs /app/cache

# Copy binary
COPY --from=builder /app/src-tauri/target/release/api_server /app/legacybridge-server
RUN chown legacybridge:legacybridge /app/legacybridge-server

# Copy configuration (will be created later)
# COPY docker/config/production.toml /app/config.toml
# RUN chown legacybridge:legacybridge /app/config.toml

USER legacybridge
WORKDIR /app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

EXPOSE 8080

# Environment variables
ENV RUST_LOG=info
ENV LEGACY_BRIDGE_CONFIG=/app/config.toml
ENV LEGACY_BRIDGE_DATA_DIR=/app/data
ENV LEGACY_BRIDGE_CACHE_DIR=/app/cache

CMD ["./legacybridge-server"]