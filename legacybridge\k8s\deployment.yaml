apiVersion: apps/v1
kind: Deployment
metadata:
  name: legacybridge-backend
  namespace: legacybridge
  labels:
    app: legacybridge
    component: backend
    version: v2.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app: legacybridge
      component: backend
  template:
    metadata:
      labels:
        app: legacybridge
        component: backend
        version: v2.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: legacybridge-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: backend
        image: legacybridge/backend:v2.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        - containerPort: 8765
          name: mcp
          protocol: TCP
        - containerPort: 9090
          name: metrics
          protocol: TCP
        env:
        - name: RUST_LOG
          valueFrom:
            configMapKeyRef:
              name: legacybridge-config
              key: rust-log-level
        - name: LEGACY_BRIDGE_ENV
          valueFrom:
            configMapKeyRef:
              name: legacybridge-config
              key: environment
        - name: LEGACY_BRIDGE_DB_URL
          valueFrom:
            secretKeyRef:
              name: legacybridge-secrets
              key: database-url
        - name: LEGACY_BRIDGE_REDIS_URL
          valueFrom:
            secretKeyRef:
              name: legacybridge-secrets
              key: redis-url
        - name: LEGACY_BRIDGE_JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: legacybridge-secrets
              key: jwt-secret
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: legacybridge-secrets
              key: openai-api-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: legacybridge-secrets
              key: anthropic-api-key
        - name: S3_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: legacybridge-secrets
              key: s3-access-key
        - name: S3_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: legacybridge-secrets
              key: s3-secret-key
        - name: METRICS_ENABLED
          valueFrom:
            configMapKeyRef:
              name: legacybridge-config
              key: metrics-enabled
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: data
          mountPath: /app/data
        - name: cache
          mountPath: /app/cache
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: config
        configMap:
          name: legacybridge-config
      - name: data
        persistentVolumeClaim:
          claimName: legacybridge-data
      - name: cache
        persistentVolumeClaim:
          claimName: legacybridge-cache
      - name: logs
        persistentVolumeClaim:
          claimName: legacybridge-logs

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: legacybridge-frontend
  namespace: legacybridge
  labels:
    app: legacybridge
    component: frontend
    version: v2.0.0
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: legacybridge
      component: frontend
  template:
    metadata:
      labels:
        app: legacybridge
        component: frontend
        version: v2.0.0
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: frontend
        image: legacybridge/frontend:v2.0.0
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: NEXT_PUBLIC_API_URL
          valueFrom:
            configMapKeyRef:
              name: legacybridge-config
              key: api-url
        - name: NEXT_PUBLIC_WS_URL
          valueFrom:
            configMapKeyRef:
              name: legacybridge-config
              key: ws-url
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /api/health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5