# LegacyBridge CI/CD Pipeline

This directory contains the complete CI/CD pipeline configuration for LegacyBridge, supporting multiple CI/CD platforms and deployment strategies.

## Overview

The LegacyBridge CI/CD pipeline provides:

- **Multi-Platform Support**: GitHub Actions, GitLab CI, Jenkins, Azure DevOps
- **Comprehensive Security Scanning**: Trivy, Snyk, OWASP, GitLeaks, Semgrep
- **Automated Testing**: Unit, Integration, E2E, Performance, Security, Accessibility
- **Multiple Deployment Strategies**: Rolling, Blue-Green, Canary
- **Multi-Cloud Support**: AWS, Azure, Google Cloud
- **Automated Rollback**: Emergency rollback with health checks
- **Enterprise Features**: Audit logging, notifications, monitoring integration

## Directory Structure

```
ci-cd/
├── README.md                    # This file
├── scripts/
│   ├── deploy.sh               # Main deployment script
│   ├── rollback.sh             # Emergency rollback script
│   ├── run-tests.sh            # Test automation script
│   └── security-scan.sh        # Security scanning script
└── configs/
    └── (platform-specific configs)
```

## Quick Start

### Prerequisites

1. **Container Registry Access**
   - GitHub Container Registry (ghcr.io)
   - Or your preferred registry (ACR, ECR, GCR)

2. **Kubernetes Access**
   - kubectl configured with cluster access
   - Appropriate RBAC permissions

3. **Cloud CLI Tools** (based on your provider)
   - AWS CLI for EKS
   - Azure CLI for AKS
   - gcloud for GKE

4. **Security Tools** (optional but recommended)
   - Trivy for vulnerability scanning
   - Snyk CLI with API token
   - GitLeaks for secret scanning

### Basic Deployment

```bash
# Deploy to staging with rolling update
ENVIRONMENT=staging ./ci-cd/scripts/deploy.sh

# Deploy to production with blue-green strategy
ENVIRONMENT=production DEPLOYMENT_STRATEGY=blue-green ./ci-cd/scripts/deploy.sh

# Deploy specific version with canary
ENVIRONMENT=production DEPLOYMENT_STRATEGY=canary VERSION=v2.1.0 ./ci-cd/scripts/deploy.sh
```

### Running Tests

```bash
# Run all tests
./ci-cd/scripts/run-tests.sh

# Run specific test suite
TEST_TYPE=unit ./ci-cd/scripts/run-tests.sh

# Run tests for specific environment
ENVIRONMENT=staging TEST_TYPE=smoke ./ci-cd/scripts/run-tests.sh
```

### Security Scanning

```bash
# Run all security scans
./ci-cd/scripts/security-scan.sh

# Run specific scan type
SCAN_TYPE=vulnerabilities ./ci-cd/scripts/security-scan.sh

# Run with custom severity threshold
SEVERITY_THRESHOLD=MEDIUM,HIGH,CRITICAL ./ci-cd/scripts/security-scan.sh
```

### Emergency Rollback

```bash
# Automatic rollback to previous version
ENVIRONMENT=production ./ci-cd/scripts/rollback.sh

# Rollback to specific version
ENVIRONMENT=production TARGET_VERSION=v2.0.5 ./ci-cd/scripts/rollback.sh

# Dry run to see what would happen
DRY_RUN=true ./ci-cd/scripts/rollback.sh
```

## CI/CD Platforms

### GitHub Actions

Located in `.github/workflows/production-deploy.yml`

**Features:**
- Automatic triggers on push to main and tags
- Matrix builds for multiple components
- Integrated security scanning
- Deployment environments with approvals
- Automatic rollback on failure

**Setup:**
1. Add secrets in GitHub repository settings:
   - `SNYK_TOKEN`
   - `AWS_ACCESS_KEY_ID` / `AWS_SECRET_ACCESS_KEY`
   - `SLACK_WEBHOOK`

2. Configure environments:
   - staging (auto-deploy)
   - production (manual approval required)

### GitLab CI

Located in `.gitlab-ci.yml`

**Features:**
- Pipeline stages for security, quality, test, build, deploy
- Parallel job execution
- Container scanning
- Review apps for merge requests
- Rollback stage

**Setup:**
1. Add CI/CD variables:
   - `KUBE_CONFIG_STAGING` (base64 encoded)
   - `KUBE_CONFIG_PRODUCTION` (base64 encoded)
   - `SNYK_TOKEN`
   - `SLACK_WEBHOOK`

2. Configure protected branches and tags

### Jenkins

Located in `Jenkinsfile`

**Features:**
- Kubernetes pod agents
- Blue Ocean compatible
- Parallel test execution
- Input steps for production deployment
- Post-build notifications

**Setup:**
1. Install required plugins:
   - Kubernetes Plugin
   - Docker Pipeline
   - Slack Notification
   - HTML Publisher

2. Configure credentials:
   - `kubeconfig-staging`
   - `kubeconfig-production`
   - `github-registry`

### Azure DevOps

Located in `azure-pipelines.yml`

**Features:**
- Multi-stage pipeline
- Service connections for AKS
- Approval gates
- Azure-specific integrations
- Deployment strategies

**Setup:**
1. Create service connections:
   - Azure subscription
   - AKS clusters
   - Container registry

2. Add variable groups:
   - legacybridge-secrets

## Deployment Strategies

### Rolling Deployment (Default)

- Gradually replaces old pods with new ones
- Zero downtime
- Automatic rollback on failure
- Best for: Regular updates

```bash
DEPLOYMENT_STRATEGY=rolling ./ci-cd/scripts/deploy.sh
```

### Blue-Green Deployment

- Creates complete new environment (green)
- Switches traffic after health checks
- Instant rollback capability
- Best for: Major updates, database migrations

```bash
DEPLOYMENT_STRATEGY=blue-green ./ci-cd/scripts/deploy.sh
```

### Canary Deployment

- Deploys to subset of users (default 10%)
- Monitors metrics before full rollout
- Gradual rollout with monitoring
- Best for: High-risk changes, A/B testing

```bash
DEPLOYMENT_STRATEGY=canary CANARY_PERCENTAGE=20 ./ci-cd/scripts/deploy.sh
```

## Testing Strategy

### Test Types

1. **Unit Tests**
   - Frontend: Jest + React Testing Library
   - Backend: Rust built-in test framework
   - Coverage threshold: 80%

2. **Integration Tests**
   - API endpoint testing
   - Database integration
   - External service mocking

3. **E2E Tests**
   - Playwright for browser automation
   - Critical user journeys
   - Cross-browser testing

4. **Performance Tests**
   - k6 for load testing
   - Response time monitoring
   - Throughput testing

5. **Security Tests**
   - OWASP ZAP scanning
   - Dependency vulnerability checks
   - Secret detection

6. **Accessibility Tests**
   - axe-core integration
   - WCAG 2.1 compliance
   - Automated a11y checks

### Running Tests in CI

All CI platforms run tests automatically. To run locally:

```bash
# Full test suite
npm run test:all

# With coverage
npm run test:coverage

# E2E tests
npm run test:e2e
```

## Security Scanning

### Scan Types

1. **Vulnerability Scanning**
   - Trivy for containers and dependencies
   - Snyk for known vulnerabilities
   - OWASP Dependency Check

2. **Secret Detection**
   - GitLeaks for committed secrets
   - Pre-commit hooks
   - Regular repository scanning

3. **SAST (Static Analysis)**
   - Semgrep for code patterns
   - SonarQube integration
   - Custom security rules

4. **Container Security**
   - Hadolint for Dockerfile linting
   - Dockle for image security
   - Registry scanning

5. **Infrastructure Security**
   - Checkov for IaC scanning
   - Terrascan for Terraform
   - Policy as Code

### Security Reports

Reports are saved to `security-reports/` directory:
- `trivy-fs-report.json` - Filesystem vulnerabilities
- `snyk-vulnerabilities.json` - Dependency vulnerabilities
- `gitleaks-report.json` - Secret detection
- `semgrep-report.json` - Code security issues
- `summary.md` - Executive summary

## Monitoring Integration

### Metrics Collection

- Prometheus metrics exported from applications
- Custom metrics for business KPIs
- Infrastructure metrics from cloud providers

### Dashboards

- Grafana dashboards for visualization
- Deployment annotations
- SLO/SLA tracking

### Alerting

- PagerDuty integration for critical alerts
- Slack notifications for deployments
- Email alerts for failures

## Troubleshooting

### Common Issues

1. **Deployment Fails Health Checks**
   ```bash
   # Check pod logs
   kubectl logs -n legacybridge -l app=legacybridge --tail=100
   
   # Check events
   kubectl get events -n legacybridge --sort-by='.lastTimestamp'
   ```

2. **Image Pull Errors**
   ```bash
   # Verify registry credentials
   kubectl get secret regcred -n legacybridge -o yaml
   
   # Test registry access
   docker pull ghcr.io/legacybridge/backend:latest
   ```

3. **Rollback Needed**
   ```bash
   # Quick rollback
   ./ci-cd/scripts/rollback.sh
   
   # Check rollback history
   kubectl rollout history deployment/legacybridge-backend -n legacybridge
   ```

### Debug Mode

Enable debug output:
```bash
# For deployment
DEBUG=true ./ci-cd/scripts/deploy.sh

# For tests
DEBUG=true ./ci-cd/scripts/run-tests.sh
```

## Best Practices

1. **Always Test in Staging First**
   - No direct production deployments
   - Staging should mirror production

2. **Use Semantic Versioning**
   - Tag releases properly (v1.2.3)
   - Document breaking changes

3. **Monitor After Deployment**
   - Watch metrics for 30 minutes
   - Check error rates and latency
   - Verify business metrics

4. **Document Changes**
   - Update CHANGELOG.md
   - Include migration guides
   - Notify stakeholders

5. **Security First**
   - Run security scans on every build
   - Fix critical vulnerabilities immediately
   - Regular dependency updates

## Emergency Procedures

### Production Down

1. **Immediate Rollback**
   ```bash
   ENVIRONMENT=production ./ci-cd/scripts/rollback.sh
   ```

2. **Check Status**
   ```bash
   kubectl get all -n legacybridge
   kubectl top nodes
   kubectl top pods -n legacybridge
   ```

3. **Scale if Needed**
   ```bash
   kubectl scale deployment legacybridge-backend --replicas=5 -n legacybridge
   ```

### Data Recovery

1. **Database Rollback**
   - Use point-in-time recovery
   - Restore from automated backups

2. **File Recovery**
   - S3/Blob storage versioning
   - Backup restoration procedures

## Contributing

1. **Adding New CI Platform**
   - Follow existing patterns
   - Include all stages (security, test, build, deploy)
   - Document setup process

2. **Updating Scripts**
   - Test locally first
   - Update documentation
   - Consider backward compatibility

3. **Security Updates**
   - Priority fixes for vulnerabilities
   - Update all CI platforms
   - Notify team of changes

## Support

For issues or questions:
1. Check logs in `/var/log/legacybridge/`
2. Review deployment events in Kubernetes
3. Contact DevOps team in #devops Slack channel
4. Create issue in GitHub repository

---

Remember: **With great deployment power comes great responsibility!** 🚀