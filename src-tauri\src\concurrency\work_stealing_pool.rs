// Work-Stealing Thread Pool Implementation
// Based on CURSOR-08-PERFORMANCE-OPTIMIZATION.MD specifications

use std::sync::{Arc, Mutex};
use std::sync::mpsc::{channel, Sender, Receiver};
use std::thread;
use std::collections::VecDeque;
use std::time::Duration;

pub struct WorkStealingThreadPool {
    workers: Vec<Worker>,
    sender: Sender<Job>,
    job_queues: Vec<Arc<Mutex<VecDeque<Job>>>>,
}

type Job = Box<dyn FnOnce() + Send + 'static>;

struct Worker {
    id: usize,
    thread: Option<thread::Join<PERSON>andle<()>>,
}

impl WorkStealingThreadPool {
    pub fn new(size: usize) -> Self {
        assert!(size > 0);

        let (sender, receiver) = channel();
        let receiver = Arc::new(Mutex::new(receiver));
        
        let mut workers = Vec::with_capacity(size);
        let mut job_queues = Vec::with_capacity(size);

        // Create job queues for each worker
        for _ in 0..size {
            job_queues.push(Arc::new(Mutex::new(VecDeque::new())));
        }

        // Create workers
        for id in 0..size {
            let receiver = Arc::clone(&receiver);
            let queues = job_queues.clone();
            let local_queue = Arc::clone(&job_queues[id]);

            let thread = thread::spawn(move || {
                Worker::run(id, receiver, local_queue, queues);
            });

            workers.push(Worker {
                id,
                thread: Some(thread),
            });
        }

        Self {
            workers,
            sender,
            job_queues,
        }
    }

    pub fn execute<F>(&self, f: F) 
    where
        F: FnOnce() + Send + 'static,
    {
        let job = Box::new(f);
        self.sender.send(job).unwrap();
    }

    pub fn execute_batch<F, I>(&self, tasks: I)
    where
        F: FnOnce() + Send + 'static,
        I: IntoIterator<Item = F>,
    {
        // Distribute tasks across worker queues
        for (i, task) in tasks.into_iter().enumerate() {
            let queue_idx = i % self.job_queues.len();
            let queue = &self.job_queues[queue_idx];
            
            if let Ok(mut queue) = queue.try_lock() {
                queue.push_back(Box::new(task));
            } else {
                // Fallback to global queue if local queue is busy
                self.execute(task);
            }
        }
    }

    pub fn get_stats(&self) -> PoolStats {
        let mut total_queued = 0;
        let mut queue_sizes = Vec::new();

        for queue in &self.job_queues {
            if let Ok(queue) = queue.try_lock() {
                let size = queue.len();
                total_queued += size;
                queue_sizes.push(size);
            } else {
                queue_sizes.push(0); // Queue is busy
            }
        }

        PoolStats {
            worker_count: self.workers.len(),
            total_queued_jobs: total_queued,
            queue_sizes,
        }
    }
}

impl Worker {
    fn run(
        id: usize,
        receiver: Arc<Mutex<Receiver<Job>>>,
        local_queue: Arc<Mutex<VecDeque<Job>>>,
        all_queues: Vec<Arc<Mutex<VecDeque<Job>>>>,
    ) {
        loop {
            // 1. Check local queue first
            if let Some(job) = Self::get_local_job(&local_queue) {
                job();
                continue;
            }

            // 2. Try to steal from other workers
            if let Some(job) = Self::steal_job(id, &all_queues) {
                job();
                continue;
            }

            // 3. Check global queue
            if let Some(job) = Self::get_global_job(&receiver) {
                job();
                continue;
            }

            // 4. Brief sleep before retrying
            thread::sleep(Duration::from_millis(1));
        }
    }

    fn get_local_job(queue: &Arc<Mutex<VecDeque<Job>>>) -> Option<Job> {
        queue.lock().ok()?.pop_front()
    }

    fn steal_job(worker_id: usize, all_queues: &[Arc<Mutex<VecDeque<Job>>>]) -> Option<Job> {
        // Try to steal from other workers' queues
        for (i, queue) in all_queues.iter().enumerate() {
            if i != worker_id {
                if let Ok(mut queue) = queue.try_lock() {
                    if let Some(job) = queue.pop_back() {
                        return Some(job);
                    }
                }
            }
        }
        None
    }

    fn get_global_job(receiver: &Arc<Mutex<Receiver<Job>>>) -> Option<Job> {
        receiver.lock().ok()?.try_recv().ok()
    }
}

impl Drop for WorkStealingThreadPool {
    fn drop(&mut self) {
        // Send shutdown signal by dropping sender
        drop(&self.sender);

        // Wait for all workers to finish
        for worker in &mut self.workers {
            if let Some(thread) = worker.thread.take() {
                thread.join().unwrap();
            }
        }
    }
}

#[derive(Debug)]
pub struct PoolStats {
    pub worker_count: usize,
    pub total_queued_jobs: usize,
    pub queue_sizes: Vec<usize>,
}

// Batch conversion processor using work-stealing pool
use crate::conversion::{ConversionError, ConversionResult};

pub struct BatchConversionProcessor {
    thread_pool: WorkStealingThreadPool,
    max_concurrent_jobs: usize,
}

#[derive(Clone)]
pub struct ConversionRequest {
    pub content: String,
    pub input_format: String,
    pub output_format: String,
    pub file_id: String,
}

impl BatchConversionProcessor {
    pub fn new(thread_count: usize) -> Self {
        Self {
            thread_pool: WorkStealingThreadPool::new(thread_count),
            max_concurrent_jobs: thread_count * 2,
        }
    }

    pub async fn process_batch(
        &self,
        files: Vec<ConversionRequest>
    ) -> Vec<ConversionResult<String>> {
        let total_files = files.len();
        let results = Arc::new(Mutex::new(vec![None; total_files]));
        let (sender, mut receiver) = tokio::sync::mpsc::channel(self.max_concurrent_jobs);

        // Process files in parallel with work stealing
        let chunk_size = (total_files / num_cpus::get()).max(1);
        
        for (chunk_idx, chunk) in files.chunks(chunk_size).enumerate() {
            let results = Arc::clone(&results);
            let sender = sender.clone();
            let chunk = chunk.to_vec();
            
            self.thread_pool.execute(move || {
                for (file_idx, request) in chunk.into_iter().enumerate() {
                    let global_idx = chunk_idx * chunk_size + file_idx;
                    
                    // Process individual file
                    let result = match request.input_format.as_str() {
                        "rtf" => crate::conversion::rtf_to_markdown(&request.content),
                        "md" | "markdown" => crate::conversion::markdown_to_rtf(&request.content),
                        _ => Err(ConversionError::ParseError(format!("Unsupported format: {}", request.input_format))),
                    };

                    // Store result
                    if let Ok(mut results) = results.lock() {
                        results[global_idx] = Some(result);
                    }

                    // Notify completion
                    let _ = sender.try_send(global_idx);
                }
            });
        }

        // Wait for all tasks to complete
        drop(sender); // Close sender to signal completion
        
        let mut completed = 0;
        while completed < total_files {
            if let Some(_) = receiver.recv().await {
                completed += 1;
            }
        }

        // Extract results
        let results = results.lock().unwrap();
        results.iter()
            .map(|opt| opt.as_ref().unwrap().clone())
            .collect()
    }

    pub fn get_pool_stats(&self) -> PoolStats {
        self.thread_pool.get_stats()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::atomic::{AtomicUsize, Ordering};
    use std::time::Instant;

    #[test]
    fn test_work_stealing_efficiency() {
        let pool = WorkStealingThreadPool::new(4);
        let counter = Arc::new(AtomicUsize::new(0));
        
        // Submit uneven workload
        let tasks: Vec<_> = (0..100).map(|i| {
            let counter = Arc::clone(&counter);
            move || {
                // Simulate variable work
                thread::sleep(Duration::from_millis(if i % 10 == 0 { 10 } else { 1 }));
                counter.fetch_add(1, Ordering::SeqCst);
            }
        }).collect();
        
        let start = Instant::now();
        pool.execute_batch(tasks);
        
        // Wait for completion
        while counter.load(Ordering::SeqCst) < 100 {
            thread::sleep(Duration::from_millis(1));
        }
        
        let duration = start.elapsed();
        // Work stealing should help with load balancing
        assert!(duration < Duration::from_secs(2));
    }

    #[tokio::test]
    async fn test_batch_conversion() {
        let processor = BatchConversionProcessor::new(num_cpus::get());
        
        // Create test requests
        let requests: Vec<ConversionRequest> = (0..10)
            .map(|i| ConversionRequest {
                content: format!("# Test document {}", i),
                input_format: "md".to_string(),
                output_format: "rtf".to_string(),
                file_id: format!("file_{}", i),
            })
            .collect();
        
        let start = Instant::now();
        let results = processor.process_batch(requests).await;
        let duration = start.elapsed();
        
        assert_eq!(results.len(), 10);
        assert!(duration < Duration::from_secs(5)); // Should complete quickly
        
        // Verify all conversions succeeded
        for result in results {
            assert!(result.is_ok());
        }
    }
}
