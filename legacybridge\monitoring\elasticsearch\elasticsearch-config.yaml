apiVersion: v1
kind: ConfigMap
metadata:
  name: elasticsearch-config
  namespace: monitoring
data:
  elasticsearch.yml: |
    cluster.name: legacybridge-logs
    node.name: ${HOSTNAME}
    network.host: 0.0.0.0
    discovery.seed_hosts:
      - elasticsearch-0.elasticsearch-headless
      - elasticsearch-1.elasticsearch-headless
      - elasticsearch-2.elasticsearch-headless
    cluster.initial_master_nodes:
      - elasticsearch-0
      - elasticsearch-1
      - elasticsearch-2
    
    # Security settings
    xpack.security.enabled: true
    xpack.security.transport.ssl.enabled: true
    xpack.security.transport.ssl.verification_mode: certificate
    xpack.security.transport.ssl.keystore.path: /usr/share/elasticsearch/config/certs/elastic-certificates.p12
    xpack.security.transport.ssl.truststore.path: /usr/share/elasticsearch/config/certs/elastic-certificates.p12
    
    # Monitoring
    xpack.monitoring.collection.enabled: true
    xpack.monitoring.elasticsearch.collection.enabled: true
    
    # Index lifecycle management
    xpack.ilm.enabled: true
    
    # Machine learning
    xpack.ml.enabled: false
    
    # Performance settings
    indices.memory.index_buffer_size: 30%
    indices.queries.cache.size: 20%
    indices.fielddata.cache.size: 20%
    
  log4j2.properties: |
    status = error
    appender.console.type = Console
    appender.console.name = console
    appender.console.layout.type = PatternLayout
    appender.console.layout.pattern = [%d{ISO8601}][%-5p][%-25c{1.}] [%node_name]%marker %m%n
    
    rootLogger.level = info
    rootLogger.appenderRef.console.ref = console
    
    logger.deprecation.name = org.elasticsearch.deprecation
    logger.deprecation.level = warn
    
  jvm.options: |
    -Xms2g
    -Xmx2g
    -XX:+UseG1GC
    -XX:MaxGCPauseMillis=50
    -XX:InitiatingHeapOccupancyPercent=30
    -XX:+HeapDumpOnOutOfMemoryError
    -XX:HeapDumpPath=/usr/share/elasticsearch/data
    -XX:ErrorFile=/usr/share/elasticsearch/logs/hs_err_pid%p.log
    -Xlog:gc*,gc+age=trace,safepoint:file=/usr/share/elasticsearch/logs/gc.log:utctime,pid,tags:filecount=32,filesize=64m