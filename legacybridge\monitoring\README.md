# LegacyBridge Monitoring Stack

## Overview

This directory contains the complete monitoring and observability infrastructure for LegacyBridge, providing:

- **Metrics Collection**: Prometheus for time-series metrics
- **Visualization**: Grafana dashboards for real-time insights
- **Distributed Tracing**: <PERSON>aeger for request flow analysis
- **Log Aggregation**: ELK stack for centralized logging
- **Alerting**: AlertManager for intelligent alert routing
- **SLO/SLA Monitoring**: Automated tracking of service level objectives

## Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   LegacyBridge  │────▶│   Prometheus    │────▶│     Grafana     │
│   Application   │     │   (Metrics)     │     │  (Dashboards)   │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                       │                         ▲
         │                       ▼                         │
         │              ┌─────────────────┐               │
         │              │  AlertManager   │───────────────┘
         │              │   (Alerting)    │
         │              └─────────────────┘
         │
         ├──────────────▶┌─────────────────┐
         │               │     Jaeger      │
         │               │   (Tracing)     │
         │               └─────────────────┘
         │
         └──────────────▶┌─────────────────┐     ┌─────────────────┐
                         │    Logstash     │────▶│  Elasticsearch  │
                         │ (Log Pipeline)  │     │  (Log Storage)  │
                         └─────────────────┘     └─────────────────┘
                                                          │
                                                          ▼
                                                 ┌─────────────────┐
                                                 │     Kibana      │
                                                 │ (Log Analysis)  │
                                                 └─────────────────┘
```

## Components

### 1. Prometheus (Metrics)
- **Purpose**: Collects and stores time-series metrics
- **Configuration**: `prometheus/prometheus.yml`
- **Alert Rules**: `prometheus/alert_rules.yml`
- **SLO Rules**: `prometheus/slo_rules.yml`
- **Recording Rules**: `prometheus/recording_rules.yml`

### 2. Grafana (Visualization)
- **Purpose**: Visualizes metrics and logs
- **Dashboards**:
  - `legacybridge-overview.json`: Main operational dashboard
  - `legacybridge-slo.json`: SLO/SLA tracking dashboard
- **Data Sources**: Prometheus, Jaeger, Elasticsearch, AlertManager

### 3. Jaeger (Tracing)
- **Purpose**: Distributed request tracing
- **Configuration**: `jaeger/jaeger-config.yaml`
- **Integration**: OpenTelemetry protocol support

### 4. ELK Stack (Logging)
- **Elasticsearch**: Log storage and indexing
- **Logstash**: Log processing and enrichment
- **Kibana**: Log search and analysis
- **Configuration**: `elasticsearch/` directory

### 5. AlertManager (Alerting)
- **Purpose**: Alert routing and notification
- **Configuration**: `alerting/alertmanager-config.yaml`
- **Integrations**: Slack, PagerDuty, Email

## Deployment

### Prerequisites
- Kubernetes cluster (1.28+)
- kubectl configured
- 100GB+ storage available
- DNS configured for monitoring.legacybridge.com

### Quick Start

```bash
# Deploy the entire monitoring stack
./scripts/deploy-monitoring.sh

# Check health status
./scripts/health-check.sh

# View Grafana admin password
kubectl get secret grafana-credentials -n monitoring -o jsonpath='{.data.admin-password}' | base64 -d
```

### Manual Deployment

```bash
# Create namespace
kubectl create namespace monitoring

# Deploy Prometheus
kubectl apply -f prometheus/

# Deploy Grafana
kubectl apply -f grafana/

# Deploy Jaeger
kubectl apply -f jaeger/

# Deploy ELK Stack
kubectl apply -f elasticsearch/

# Deploy AlertManager
kubectl apply -f alerting/
```

## Accessing Services

After deployment, services are available at:

- **Prometheus**: http://monitoring.legacybridge.com/prometheus
- **Grafana**: http://monitoring.legacybridge.com/grafana
- **Kibana**: http://monitoring.legacybridge.com/kibana
- **Jaeger**: http://monitoring.legacybridge.com/jaeger
- **AlertManager**: http://monitoring.legacybridge.com/alertmanager

For local development, use port-forwarding:

```bash
# Grafana
kubectl port-forward -n monitoring svc/grafana 3000:3000

# Prometheus
kubectl port-forward -n monitoring svc/prometheus 9090:9090

# Kibana
kubectl port-forward -n monitoring svc/kibana 5601:5601
```

## Key Metrics

### Application Metrics
- `http_requests_total`: Total HTTP requests
- `http_request_duration_seconds`: Request latency histogram
- `conversion_total`: Document conversions by status
- `conversion_processing_time_seconds`: Conversion duration
- `conversion_queue_size`: Pending conversions

### SLO Metrics
- `slo:api_availability:rate5m`: API availability rate
- `slo:api_latency_p95:histogram_quantile`: 95th percentile latency
- `slo:conversion_success_rate:rate5m`: Conversion success rate
- `slo:error_budget:*`: Error budget calculations

## Alert Rules

### Critical Alerts
- **ServiceDown**: Service unavailable for >1 minute
- **HighErrorRate**: Error rate >5% for 5 minutes
- **DatabaseConnectionPoolExhausted**: >90% connections used
- **DiskSpaceLow**: <10% disk space remaining

### Warning Alerts
- **HighResponseTime**: P95 latency >1s for 10 minutes
- **ConversionQueueBacklog**: >100 items queued for 10 minutes
- **HighMemoryUsage**: >90% memory usage
- **CertificateExpiringSoon**: SSL cert expires in <30 days

### SLO Alerts
- **SLO_APIAvailability**: <99.9% availability
- **SLO_APILatency**: P95 >500ms
- **SLO_ConversionSuccessRate**: <99% success rate

## Dashboards

### LegacyBridge Overview
Main operational dashboard showing:
- Request rate and error rate
- Response time percentiles
- Active conversions and queue size
- Resource utilization
- SLO compliance status

### SLO Dashboard
Service level objective tracking:
- Monthly SLO performance
- Error budget consumption
- SLI trends over time
- Latency distribution heatmap

## Log Analysis

### Log Format
Application logs follow structured JSON format:
```json
{
  "@timestamp": "2024-01-15T10:30:45.123Z",
  "level": "INFO",
  "service": "legacybridge",
  "trace_id": "abc123",
  "message": "Conversion completed",
  "user_id": "user456",
  "duration_ms": 1234
}
```

### Kibana Searches
Common queries:
- Errors: `level:ERROR`
- Slow requests: `response_time:>1000`
- User activity: `user_id:"specific-user"`
- Trace correlation: `trace_id:"specific-trace"`

## Troubleshooting

### Prometheus Not Scraping
1. Check service discovery: `kubectl logs -n monitoring prometheus-0`
2. Verify endpoints: `kubectl get endpoints -n legacybridge`
3. Check annotations: `kubectl describe pod -n legacybridge`

### Missing Logs
1. Check Logstash pipeline: `kubectl logs -n monitoring logstash-0`
2. Verify Elasticsearch indices: `curl elasticsearch:9200/_cat/indices`
3. Check Filebeat/Fluentd agents on nodes

### No Traces
1. Verify OTLP endpoint: `kubectl logs -n monitoring jaeger-0`
2. Check application instrumentation
3. Verify service name in Jaeger UI

### Alert Not Firing
1. Check alert rules: http://prometheus:9090/rules
2. Verify AlertManager config: http://alertmanager:9093/#/status
3. Check notification integrations

## Best Practices

### Metric Naming
- Use standard Prometheus naming conventions
- Include unit suffixes (_seconds, _bytes, _total)
- Use consistent label names

### Dashboard Design
- Keep dashboards focused on specific use cases
- Use appropriate visualization types
- Include documentation panels

### Alert Design
- Alert on symptoms, not causes
- Include runbook links in annotations
- Use appropriate severity levels

### Log Management
- Use structured logging (JSON)
- Include correlation IDs
- Implement log sampling for high-volume services

## Maintenance

### Regular Tasks
- Review and tune alert thresholds monthly
- Clean up old indices/metrics (automated via ILM)
- Update dashboards based on operational needs
- Review SLO targets quarterly

### Backup
- Prometheus data: Included in StatefulSet volumes
- Grafana dashboards: Stored in ConfigMaps
- Elasticsearch indices: Snapshot to S3 daily

### Scaling
- Prometheus: Use federation for multi-region
- Elasticsearch: Add nodes to cluster
- Jaeger: Use Elasticsearch backend for scale

## Security

### Access Control
- Grafana: LDAP/OAuth integration
- Kibana: Role-based access control
- Prometheus: Read-only by default

### Data Retention
- Metrics: 30 days (configurable)
- Logs: 7 days hot, 30 days warm, 90 days cold
- Traces: 7 days

### Secrets Management
- Stored in Kubernetes secrets
- Rotated quarterly
- Encrypted at rest

## Contributing

When adding new monitoring:
1. Define metrics in application code
2. Add Prometheus scraping configuration
3. Create Grafana dashboard
4. Define appropriate alerts
5. Document in this README

## Support

For issues or questions:
- Check logs: `kubectl logs -n monitoring <pod-name>`
- Review documentation above
- Contact: <EMAIL>