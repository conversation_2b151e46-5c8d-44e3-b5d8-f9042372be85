use std::fs::{self, File};
use std::io::{self, Write};
use std::path::{Path, PathBuf};
use serde::{Serialize, Deserialize};
use sha2::{Sha256, Digest};
use tauri::Manager;
use zip::write::FileOptions;
use flate2::write::GzEncoder;
use flate2::Compression;
use tar::Builder as TarBuilder;
use chrono::Utc;

use super::{DllError, DllResult};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageConfig {
    pub format: PackageFormat,
    pub include_docs: bool,
    pub include_examples: bool,
    pub include_source: bool,
    pub create_installer: bool,
    pub package_name: String,
    pub version: String,
    pub description: String,
    pub author: String,
    pub license: String,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum PackageFormat {
    <PERSON><PERSON>,
    Tar,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DeploymentPackage {
    pub file_name: String,
    pub file_path: String,
    pub file_size: u64,
    pub format: PackageFormat,
    pub included_files: Vec<String>,
    pub download_url: String,
    pub checksum: String,
    pub created_at: String,
    pub metadata: PackageMetadata,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageMetadata {
    pub version: String,
    pub architecture: Vec<String>,
    pub included_formats: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PackageValidation {
    pub is_valid: bool,
    pub errors: Vec<String>,
}

pub struct PackageBuilder {
    config: PackageConfig,
    build_path: PathBuf,
    output_path: PathBuf,
}

impl PackageBuilder {
    pub fn new(config: PackageConfig, build_path: PathBuf, output_path: PathBuf) -> Self {
        Self {
            config,
            build_path,
            output_path,
        }
    }

    pub async fn validate<T: Manager<R>, R: tauri::Runtime>(
        &self,
        app: &T,
    ) -> DllResult<PackageValidation> {
        let mut errors = Vec::new();

        // Check if build path exists
        if !self.build_path.exists() {
            errors.push("Build directory does not exist".to_string());
        }

        // Check if DLL files exist
        let dll_files = self.find_dll_files()?;
        if dll_files.is_empty() {
            errors.push("No DLL files found to package".to_string());
        }

        // Validate package name
        if self.config.package_name.is_empty() {
            errors.push("Package name cannot be empty".to_string());
        }

        // Validate version format
        if !self.is_valid_version(&self.config.version) {
            errors.push("Invalid version format (use semantic versioning)".to_string());
        }

        // Platform-specific validation
        match self.config.format {
            PackageFormat::Msi | PackageFormat::Nsis => {
                if cfg!(not(target_os = "windows")) {
                    errors.push("MSI/NSIS packages can only be created on Windows".to_string());
                }
            }
            _ => {}
        }

        Ok(PackageValidation {
            is_valid: errors.is_empty(),
            errors,
        })
    }

    pub async fn create_package<T: Manager<R>, R: tauri::Runtime>(
        &self,
        app: &T,
    ) -> DllResult<DeploymentPackage> {
        // Create output directory if it doesn't exist
        fs::create_dir_all(&self.output_path)?;

        // Collect files to package
        let mut included_files = Vec::new();
        
        // Add DLL files
        let dll_files = self.find_dll_files()?;
        for dll in &dll_files {
            included_files.push(dll.file_name().unwrap().to_string_lossy().to_string());
        }

        // Add documentation if requested
        if self.config.include_docs {
            let docs = self.collect_documentation()?;
            included_files.extend(docs);
        }

        // Add examples if requested
        if self.config.include_examples {
            let examples = self.collect_examples()?;
            included_files.extend(examples);
        }

        // Add source code if requested
        if self.config.include_source {
            let sources = self.collect_source_code()?;
            included_files.extend(sources);
        }

        // Create package based on format
        let package_file = match self.config.format {
            PackageFormat::Zip => self.create_zip_package(&dll_files, app).await?,
            PackageFormat::Tar => self.create_tar_package(&dll_files, app).await?,
            PackageFormat::Msi => self.create_msi_package(&dll_files, app).await?,
            PackageFormat::Nsis => self.create_nsis_package(&dll_files, app).await?,
        };

        // Calculate checksum
        let checksum = self.calculate_checksum(&package_file)?;

        // Get file size
        let metadata = fs::metadata(&package_file)?;
        let file_size = metadata.len();

        // Create deployment package info
        let deployment_package = DeploymentPackage {
            file_name: package_file.file_name().unwrap().to_string_lossy().to_string(),
            file_path: package_file.to_string_lossy().to_string(),
            file_size,
            format: self.config.format,
            included_files,
            download_url: format!("file://{}", package_file.display()),
            checksum,
            created_at: Utc::now().to_rfc3339(),
            metadata: PackageMetadata {
                version: self.config.version.clone(),
                architecture: vec!["x86".to_string(), "x64".to_string()],
                included_formats: vec![
                    "rtf".to_string(),
                    "doc".to_string(),
                    "wordperfect".to_string(),
                    "lotus123".to_string(),
                    "dbase".to_string(),
                ],
            },
        };

        Ok(deployment_package)
    }

    async fn create_zip_package<T: Manager<R>, R: tauri::Runtime>(
        &self,
        dll_files: &[PathBuf],
        app: &T,
    ) -> DllResult<PathBuf> {
        let package_name = format!(
            "{}-{}.zip",
            self.config.package_name,
            self.config.version
        );
        let package_path = self.output_path.join(&package_name);

        let file = File::create(&package_path)?;
        let mut zip = zip::ZipWriter::new(file);

        let options = FileOptions::default()
            .compression_method(zip::CompressionMethod::Deflated)
            .unix_permissions(0o755);

        // Add DLL files
        for (index, dll_path) in dll_files.iter().enumerate() {
            let progress = ((index + 1) as f32 / dll_files.len() as f32 * 50.0) as u32;
            app.emit_all("packaging-progress", PackageProgress { progress })?;

            let file_name = dll_path.file_name().unwrap().to_string_lossy();
            zip.start_file(format!("dll/{}", file_name), options)?;
            
            let contents = fs::read(dll_path)?;
            zip.write_all(&contents)?;
        }

        // Add README
        zip.start_file("README.txt", options)?;
        zip.write_all(self.generate_readme().as_bytes())?;

        // Add integration examples
        if self.config.include_examples {
            self.add_examples_to_zip(&mut zip, &options)?;
        }

        // Add documentation
        if self.config.include_docs {
            self.add_docs_to_zip(&mut zip, &options)?;
        }

        zip.finish()?;

        app.emit_all("packaging-progress", PackageProgress { progress: 100 })?;

        Ok(package_path)
    }

    async fn create_tar_package<T: Manager<R>, R: tauri::Runtime>(
        &self,
        dll_files: &[PathBuf],
        app: &T,
    ) -> DllResult<PathBuf> {
        let package_name = format!(
            "{}-{}.tar.gz",
            self.config.package_name,
            self.config.version
        );
        let package_path = self.output_path.join(&package_name);

        let tar_gz = File::create(&package_path)?;
        let enc = GzEncoder::new(tar_gz, Compression::default());
        let mut tar = TarBuilder::new(enc);

        // Add DLL files
        for (index, dll_path) in dll_files.iter().enumerate() {
            let progress = ((index + 1) as f32 / dll_files.len() as f32 * 50.0) as u32;
            app.emit_all("packaging-progress", PackageProgress { progress })?;

            let file_name = dll_path.file_name().unwrap().to_string_lossy();
            tar.append_path_with_name(dll_path, format!("dll/{}", file_name))?;
        }

        // Add README
        let readme = self.generate_readme();
        let mut header = tar::Header::new_gnu();
        header.set_path("README.txt")?;
        header.set_size(readme.len() as u64);
        header.set_mode(0o644);
        header.set_cksum();
        tar.append(&header, readme.as_bytes())?;

        tar.finish()?;

        app.emit_all("packaging-progress", PackageProgress { progress: 100 })?;

        Ok(package_path)
    }

    async fn create_msi_package<T: Manager<R>, R: tauri::Runtime>(
        &self,
        dll_files: &[PathBuf],
        app: &T,
    ) -> DllResult<PathBuf> {
        // This is a placeholder - actual MSI creation would use WiX or similar
        Err(DllError::PlatformNotSupported(
            "MSI package creation not yet implemented".to_string()
        ))
    }

    async fn create_nsis_package<T: Manager<R>, R: tauri::Runtime>(
        &self,
        dll_files: &[PathBuf],
        app: &T,
    ) -> DllResult<PathBuf> {
        // This is a placeholder - actual NSIS creation would use NSIS compiler
        Err(DllError::PlatformNotSupported(
            "NSIS package creation not yet implemented".to_string()
        ))
    }

    fn find_dll_files(&self) -> DllResult<Vec<PathBuf>> {
        let mut dll_files = Vec::new();

        for entry in fs::read_dir(&self.build_path)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.extension().and_then(|s| s.to_str()) == Some("dll") {
                dll_files.push(path);
            }
        }

        Ok(dll_files)
    }

    fn collect_documentation(&self) -> DllResult<Vec<String>> {
        // Placeholder for documentation collection
        Ok(vec![
            "docs/API.md".to_string(),
            "docs/Integration.md".to_string(),
            "docs/FAQ.md".to_string(),
        ])
    }

    fn collect_examples(&self) -> DllResult<Vec<String>> {
        // Placeholder for examples collection
        Ok(vec![
            "examples/vb6_example.bas".to_string(),
            "examples/vfp9_example.prg".to_string(),
            "examples/csharp_example.cs".to_string(),
        ])
    }

    fn collect_source_code(&self) -> DllResult<Vec<String>> {
        // Placeholder for source code collection
        Ok(vec![
            "src/lib.rs".to_string(),
            "src/conversion/mod.rs".to_string(),
            "src/ffi.rs".to_string(),
        ])
    }

    fn calculate_checksum(&self, file_path: &Path) -> DllResult<String> {
        let mut file = File::open(file_path)?;
        let mut hasher = Sha256::new();
        io::copy(&mut file, &mut hasher)?;
        let result = hasher.finalize();
        Ok(format!("{:x}", result))
    }

    fn is_valid_version(&self, version: &str) -> bool {
        // Simple semantic versioning check
        let parts: Vec<&str> = version.split('.').collect();
        if parts.len() != 3 {
            return false;
        }
        
        parts.iter().all(|part| part.parse::<u32>().is_ok())
    }

    fn generate_readme(&self) -> String {
        format!(
            r#"# {} v{}

{}

Author: {}
License: {}

## Installation

1. Extract all files to your desired location
2. Register DLL files as needed for your platform
3. Import integration code into your project
4. See documentation folder for detailed instructions

## Contents

- dll/         - Compiled DLL files for x86 and x64
- docs/        - API documentation and guides
- examples/    - Integration examples for various platforms
- integration/ - Helper code for VB6, VFP9, and other platforms

## Support

For support, please visit: https://legacybridge.com/support

Copyright © {} {}
"#,
            self.config.package_name,
            self.config.version,
            self.config.description,
            self.config.author,
            self.config.license,
            Utc::now().year(),
            self.config.author
        )
    }

    fn add_examples_to_zip(
        &self,
        zip: &mut zip::ZipWriter<File>,
        options: &FileOptions,
    ) -> DllResult<()> {
        // VB6 Example
        zip.start_file("examples/vb6_integration.bas", *options)?;
        zip.write_all(VB6_EXAMPLE.as_bytes())?;

        // VFP9 Example
        zip.start_file("examples/vfp9_integration.prg", *options)?;
        zip.write_all(VFP9_EXAMPLE.as_bytes())?;

        Ok(())
    }

    fn add_docs_to_zip(
        &self,
        zip: &mut zip::ZipWriter<File>,
        options: &FileOptions,
    ) -> DllResult<()> {
        // API Documentation
        zip.start_file("docs/API_Reference.md", *options)?;
        zip.write_all(API_DOCS.as_bytes())?;

        // Integration Guide
        zip.start_file("docs/Integration_Guide.md", *options)?;
        zip.write_all(INTEGRATION_GUIDE.as_bytes())?;

        Ok(())
    }
}

#[derive(Clone, Serialize)]
struct PackageProgress {
    progress: u32,
}

// Example content constants
const VB6_EXAMPLE: &str = r#"' VB6 Integration Example for LegacyBridge DLL

Option Explicit

Private Declare Function ConvertDocument Lib "legacybridge.dll" _
    (ByVal inputPath As String, ByVal outputPath As String, _
     ByVal fromFormat As String, ByVal toFormat As String) As Long

Private Declare Function GetLastError Lib "legacybridge.dll" () As Long

Private Declare Sub GetErrorMessage Lib "legacybridge.dll" _
    (ByVal errorCode As Long, ByVal buffer As String, ByVal bufferSize As Long)

Public Function ConvertFile(inputFile As String, outputFile As String) As Boolean
    Dim result As Long
    Dim errorCode As Long
    Dim errorMsg As String * 256
    
    ' Call the conversion function
    result = ConvertDocument(inputFile, outputFile, "auto", "markdown")
    
    If result = 0 Then
        ConvertFile = True
    Else
        ' Get error details
        errorCode = GetLastError()
        GetErrorMessage errorCode, errorMsg, 256
        
        MsgBox "Conversion failed: " & Trim(errorMsg), vbCritical
        ConvertFile = False
    End If
End Function
"#;

const VFP9_EXAMPLE: &str = r#"* VFP9 Integration Example for LegacyBridge DLL

DECLARE INTEGER ConvertDocument IN legacybridge.dll ;
    STRING inputPath, STRING outputPath, ;
    STRING fromFormat, STRING toFormat

DECLARE INTEGER GetLastError IN legacybridge.dll

DECLARE GetErrorMessage IN legacybridge.dll ;
    INTEGER errorCode, STRING @buffer, INTEGER bufferSize

FUNCTION ConvertFile(lcInputFile, lcOutputFile)
    LOCAL lnResult, lnErrorCode, lcErrorMsg
    
    lcErrorMsg = SPACE(256)
    
    * Call the conversion function
    lnResult = ConvertDocument(lcInputFile, lcOutputFile, "auto", "markdown")
    
    IF lnResult = 0
        RETURN .T.
    ELSE
        * Get error details
        lnErrorCode = GetLastError()
        GetErrorMessage(lnErrorCode, @lcErrorMsg, 256)
        
        MESSAGEBOX("Conversion failed: " + ALLTRIM(lcErrorMsg), 16)
        RETURN .F.
    ENDIF
ENDFUNC
"#;

const API_DOCS: &str = r#"# LegacyBridge DLL API Reference

## Functions

### ConvertDocument
Converts a document from one format to another.

```c
int ConvertDocument(
    const char* inputPath,
    const char* outputPath,
    const char* fromFormat,
    const char* toFormat
);
```

**Parameters:**
- `inputPath`: Path to the input file
- `outputPath`: Path where the output file will be saved
- `fromFormat`: Source format ("rtf", "doc", "wordperfect", "lotus123", "dbase", or "auto" for detection)
- `toFormat`: Target format ("markdown", "rtf", or "plain")

**Returns:**
- 0 on success
- Error code on failure

### GetLastError
Gets the last error code from the DLL.

```c
int GetLastError();
```

### GetErrorMessage
Gets a human-readable error message for an error code.

```c
void GetErrorMessage(int errorCode, char* buffer, int bufferSize);
```
"#;

const INTEGRATION_GUIDE: &str = r#"# Integration Guide

## Platform Support

LegacyBridge DLL supports the following platforms:
- Visual Basic 6
- Visual FoxPro 9
- C#/.NET
- Python (via ctypes)
- Any language that supports C-style DLL imports

## Architecture Support

Both x86 (32-bit) and x64 (64-bit) versions are included. Make sure to use the correct version for your application.

## Basic Integration Steps

1. Copy the appropriate DLL to your application directory
2. Import the DLL functions in your programming language
3. Call ConvertDocument with appropriate parameters
4. Handle errors using GetLastError and GetErrorMessage

## Error Handling

Always check the return value of ConvertDocument. Non-zero values indicate an error.
"#;