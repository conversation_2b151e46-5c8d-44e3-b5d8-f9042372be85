groups:
  - name: legacybridge_application
    interval: 30s
    rules:
      # High Error Rate
      - alert: HighErrorRate
        expr: |
          (
            sum(rate(http_requests_total{job="legacybridge-backend",status=~"5.."}[5m]))
            /
            sum(rate(http_requests_total{job="legacybridge-backend"}[5m]))
          ) > 0.05
        for: 5m
        labels:
          severity: critical
          team: backend
          service: legacybridge
        annotations:
          summary: "High error rate detected for LegacyBridge"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
          runbook_url: "https://wiki.example.com/runbooks/legacybridge/high-error-rate"
          dashboard_url: "https://grafana.example.com/d/legacybridge-overview"

      # High Response Time
      - alert: HighResponseTime
        expr: |
          histogram_quantile(0.95, 
            sum(rate(http_request_duration_seconds_bucket{job="legacybridge-backend"}[5m])) by (le)
          ) > 1.0
        for: 10m
        labels:
          severity: warning
          team: backend
          service: legacybridge
        annotations:
          summary: "High response time for LegacyBridge API"
          description: "95th percentile response time is {{ $value | humanizeDuration }} for the last 10 minutes"
          runbook_url: "https://wiki.example.com/runbooks/legacybridge/high-response-time"

      # Service Down
      - alert: ServiceDown
        expr: up{job=~"legacybridge-.*"} == 0
        for: 1m
        labels:
          severity: critical
          team: platform
          service: legacybridge
        annotations:
          summary: "LegacyBridge service is down"
          description: "{{ $labels.job }} on {{ $labels.instance }} has been down for more than 1 minute"
          runbook_url: "https://wiki.example.com/runbooks/legacybridge/service-down"

      # Pod Restart Loop
      - alert: PodRestartLoop
        expr: |
          rate(kube_pod_container_status_restarts_total{namespace="legacybridge"}[15m]) * 60 * 15 > 3
        for: 5m
        labels:
          severity: critical
          team: platform
          service: legacybridge
        annotations:
          summary: "Pod is in restart loop"
          description: "Pod {{ $labels.pod }} in namespace {{ $labels.namespace }} has restarted {{ $value }} times in the last 15 minutes"
          runbook_url: "https://wiki.example.com/runbooks/kubernetes/pod-restart-loop"

      # High Memory Usage
      - alert: HighMemoryUsage
        expr: |
          (
            container_memory_working_set_bytes{namespace="legacybridge", container!=""}
            /
            container_spec_memory_limit_bytes{namespace="legacybridge", container!=""}
          ) > 0.9
        for: 5m
        labels:
          severity: warning
          team: platform
          service: legacybridge
        annotations:
          summary: "High memory usage detected"
          description: "Container {{ $labels.container }} in pod {{ $labels.pod }} is using {{ $value | humanizePercentage }} of its memory limit"
          runbook_url: "https://wiki.example.com/runbooks/kubernetes/high-memory-usage"

      # Conversion Queue Backlog
      - alert: ConversionQueueBacklog
        expr: conversion_queue_size{job="legacybridge-backend"} > 100
        for: 10m
        labels:
          severity: warning
          team: backend
          service: legacybridge
        annotations:
          summary: "Document conversion queue is backing up"
          description: "Conversion queue has {{ $value }} items pending for more than 10 minutes"
          runbook_url: "https://wiki.example.com/runbooks/legacybridge/conversion-queue-backlog"

      # Low Conversion Success Rate
      - alert: LowConversionSuccessRate
        expr: |
          (
            sum(rate(conversion_total{job="legacybridge-backend",status="success"}[5m]))
            /
            sum(rate(conversion_total{job="legacybridge-backend"}[5m]))
          ) < 0.9
        for: 15m
        labels:
          severity: warning
          team: backend
          service: legacybridge
        annotations:
          summary: "Document conversion success rate is low"
          description: "Conversion success rate is {{ $value | humanizePercentage }} for the last 15 minutes"
          runbook_url: "https://wiki.example.com/runbooks/legacybridge/low-conversion-rate"

  - name: infrastructure
    interval: 30s
    rules:
      # Node CPU Pressure
      - alert: NodeCPUPressure
        expr: |
          (
            1 - (
              avg by (instance) (
                irate(node_cpu_seconds_total{mode="idle"}[5m])
              )
            )
          ) > 0.9
        for: 10m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High CPU usage on node"
          description: "Node {{ $labels.instance }} has CPU usage of {{ $value | humanizePercentage }}"
          runbook_url: "https://wiki.example.com/runbooks/infrastructure/high-cpu"

      # Node Memory Pressure
      - alert: NodeMemoryPressure
        expr: |
          (
            1 - (
              node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes
            )
          ) > 0.9
        for: 10m
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "High memory usage on node"
          description: "Node {{ $labels.instance }} has memory usage of {{ $value | humanizePercentage }}"
          runbook_url: "https://wiki.example.com/runbooks/infrastructure/high-memory"

      # Disk Space Low
      - alert: DiskSpaceLow
        expr: |
          (
            node_filesystem_avail_bytes{fstype!~"tmpfs|fuse.lxcfs|squashfs|vfat"}
            /
            node_filesystem_size_bytes{fstype!~"tmpfs|fuse.lxcfs|squashfs|vfat"}
          ) < 0.1
        for: 5m
        labels:
          severity: critical
          team: platform
        annotations:
          summary: "Low disk space on node"
          description: "Node {{ $labels.instance }} has only {{ $value | humanizePercentage }} disk space remaining on {{ $labels.mountpoint }}"
          runbook_url: "https://wiki.example.com/runbooks/infrastructure/low-disk-space"

      # Certificate Expiry
      - alert: CertificateExpiringSoon
        expr: |
          probe_ssl_earliest_cert_expiry{job="blackbox-http"} - time() < 86400 * 30
        for: 1h
        labels:
          severity: warning
          team: platform
        annotations:
          summary: "SSL certificate expiring soon"
          description: "Certificate for {{ $labels.instance }} expires in {{ $value | humanizeDuration }}"
          runbook_url: "https://wiki.example.com/runbooks/infrastructure/certificate-expiry"

  - name: database
    interval: 30s
    rules:
      # Database Connection Pool Exhausted
      - alert: DatabaseConnectionPoolExhausted
        expr: |
          pg_stat_database_numbackends{datname="legacybridge"} 
          / 
          pg_settings_max_connections > 0.9
        for: 5m
        labels:
          severity: critical
          team: backend
          service: postgres
        annotations:
          summary: "PostgreSQL connection pool nearly exhausted"
          description: "Database is using {{ $value | humanizePercentage }} of available connections"
          runbook_url: "https://wiki.example.com/runbooks/database/connection-pool-exhausted"

      # Database Replication Lag
      - alert: DatabaseReplicationLag
        expr: |
          pg_replication_lag{job="postgresql"} > 10
        for: 5m
        labels:
          severity: warning
          team: backend
          service: postgres
        annotations:
          summary: "PostgreSQL replication lag is high"
          description: "Replication lag is {{ $value }} seconds"
          runbook_url: "https://wiki.example.com/runbooks/database/replication-lag"

      # Redis Memory Usage High
      - alert: RedisMemoryHigh
        expr: |
          redis_memory_used_bytes{job="redis"} 
          / 
          redis_memory_max_bytes{job="redis"} > 0.9
        for: 10m
        labels:
          severity: warning
          team: backend
          service: redis
        annotations:
          summary: "Redis memory usage is high"
          description: "Redis is using {{ $value | humanizePercentage }} of available memory"
          runbook_url: "https://wiki.example.com/runbooks/redis/high-memory"