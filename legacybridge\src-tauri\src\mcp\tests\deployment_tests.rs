#[cfg(test)]
mod deployment_tests {
    use crate::mcp::{
        deployment::{DeploymentConfig, EnvironmentConfig, Environment},
        launcher::Mc<PERSON><PERSON>auncher,
        security::{SecurityConfig, AuthMethod},
        performance::PerformanceConfig,
        monitoring::MonitoringConfig,
    };
    use std::env;
    use std::path::PathBuf;
    use tempfile::TempDir;
    use tokio::time::{sleep, Duration};

    #[test]
    fn test_deployment_config_loading() {
        let config = DeploymentConfig::default();
        assert_eq!(config.environment, Environment::Development);
        assert_eq!(config.server.protocol, "stdio");
        assert!(!config.security.authentication.enabled);
    }

    #[test]
    fn test_environment_config_selection() {
        let mut config = DeploymentConfig::default();
        
        // Test development environment
        config.environment = Environment::Development;
        let env_config = config.get_environment_config();
        assert_eq!(env_config.server.protocol, "stdio");
        assert!(!env_config.security.authentication.enabled);
        
        // Test staging environment
        config.environment = Environment::Staging;
        let env_config = config.get_environment_config();
        assert_eq!(env_config.server.protocol, "http");
        assert!(env_config.security.authentication.enabled);
        
        // Test production environment
        config.environment = Environment::Production;
        let env_config = config.get_environment_config();
        assert_eq!(env_config.server.protocol, "websocket");
        assert!(env_config.security.authentication.enabled);
        assert!(env_config.security.tls.enabled);
    }

    #[test]
    fn test_config_from_environment_variables() {
        env::set_var("MCP_ENVIRONMENT", "production");
        env::set_var("MCP_SERVER_HOST", "0.0.0.0");
        env::set_var("MCP_SERVER_PORT", "8443");
        env::set_var("MCP_SECURITY_AUTH_ENABLED", "true");
        env::set_var("MCP_SECURITY_TLS_ENABLED", "true");
        
        let config = DeploymentConfig::from_env().unwrap();
        assert_eq!(config.environment, Environment::Production);
        assert_eq!(config.server.host, "0.0.0.0");
        assert_eq!(config.server.port, 8443);
        assert!(config.security.authentication.enabled);
        assert!(config.security.tls.enabled);
        
        // Clean up
        env::remove_var("MCP_ENVIRONMENT");
        env::remove_var("MCP_SERVER_HOST");
        env::remove_var("MCP_SERVER_PORT");
        env::remove_var("MCP_SECURITY_AUTH_ENABLED");
        env::remove_var("MCP_SECURITY_TLS_ENABLED");
    }

    #[test]
    fn test_config_from_file() {
        let temp_dir = TempDir::new().unwrap();
        let config_path = temp_dir.path().join("config.toml");
        
        let config_content = r#"
environment = "staging"

[server]
protocol = "http"
host = "127.0.0.1"
port = 8080

[security.authentication]
enabled = true
method = "apikey"
api_keys = [
    { id = "test-key", key = "secret-123", name = "Test Key", permissions = ["legacy:read"] }
]

[security.tls]
enabled = true
cert_path = "/etc/certs/cert.pem"
key_path = "/etc/certs/key.pem"

[performance]
thread_pool_size = 8
max_concurrent_requests = 150
cache_size = 128
"#;
        
        std::fs::write(&config_path, config_content).unwrap();
        
        let config = DeploymentConfig::from_file(&config_path).unwrap();
        assert_eq!(config.environment, Environment::Staging);
        assert_eq!(config.server.protocol, "http");
        assert_eq!(config.server.host, "127.0.0.1");
        assert_eq!(config.server.port, 8080);
        assert!(config.security.authentication.enabled);
        assert_eq!(config.security.authentication.method, AuthMethod::ApiKey);
        assert_eq!(config.security.authentication.api_keys.len(), 1);
        assert!(config.security.tls.enabled);
        assert_eq!(config.performance.thread_pool_size, 8);
        assert_eq!(config.performance.max_concurrent_requests, 150);
    }

    #[test]
    fn test_config_validation() {
        let mut config = DeploymentConfig::default();
        
        // Valid config should pass
        assert!(config.validate().is_ok());
        
        // Invalid port
        config.server.port = 0;
        assert!(config.validate().is_err());
        config.server.port = 70000;
        assert!(config.validate().is_err());
        config.server.port = 8080;
        
        // Invalid protocol
        config.server.protocol = "invalid".to_string();
        assert!(config.validate().is_err());
        config.server.protocol = "http".to_string();
        
        // TLS enabled but no cert/key
        config.security.tls.enabled = true;
        config.security.tls.cert_path = None;
        config.security.tls.key_path = None;
        assert!(config.validate().is_err());
        
        // Fix TLS config
        config.security.tls.cert_path = Some(PathBuf::from("/etc/cert.pem"));
        config.security.tls.key_path = Some(PathBuf::from("/etc/key.pem"));
        assert!(config.validate().is_ok());
    }

    #[tokio::test]
    async fn test_launcher_initialization() {
        let launcher = McpLauncher::new();
        assert!(launcher.is_ok());
        
        let launcher = launcher.unwrap();
        assert!(launcher.server.is_none());
        assert!(launcher.shutdown_signal.is_none());
    }

    #[tokio::test]
    async fn test_security_manager_integration() {
        let mut security_config = SecurityConfig::default();
        security_config.authentication.enabled = true;
        security_config.authentication.method = AuthMethod::ApiKey;
        security_config.authentication.api_keys.push(crate::mcp::security::ApiKey {
            id: "test-id".to_string(),
            key: "test-key-123".to_string(),
            name: "Test API Key".to_string(),
            permissions: vec!["legacy:read".to_string(), "legacy:convert".to_string()],
            rate_limit: Some(100),
        });
        
        let manager = crate::mcp::security::SecurityManager::new(security_config).unwrap();
        
        // Test API key authentication
        let result = manager.authenticate_api_key("test-key-123").unwrap();
        assert!(result.is_some());
        let api_key = result.unwrap();
        assert_eq!(api_key.id, "test-id");
        assert_eq!(api_key.permissions.len(), 2);
        
        // Test invalid key
        let result = manager.authenticate_api_key("invalid-key").unwrap();
        assert!(result.is_none());
    }

    #[tokio::test]
    async fn test_performance_manager_integration() {
        let perf_config = PerformanceConfig {
            max_concurrent_requests: 5,
            ..Default::default()
        };
        
        let manager = crate::mcp::performance::PerformanceManager::new(perf_config).unwrap();
        
        // Test semaphore limiting
        let mut permits = vec![];
        for _ in 0..5 {
            permits.push(manager.acquire_permit().await.unwrap());
        }
        
        // 6th permit should timeout
        let result = tokio::time::timeout(
            Duration::from_millis(100),
            manager.acquire_permit()
        ).await;
        assert!(result.is_err());
        
        // Release one permit
        drop(permits.pop());
        
        // Now we should be able to acquire
        let result = tokio::time::timeout(
            Duration::from_millis(100),
            manager.acquire_permit()
        ).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_monitoring_manager_integration() {
        let monitoring_config = MonitoringConfig::default();
        let manager = crate::mcp::monitoring::MonitoringManager::new(monitoring_config).unwrap();
        
        // Record some metrics
        manager.record_counter("test_requests", 10);
        manager.set_gauge("test_memory", 45.5);
        manager.record_histogram("test_latency", 100.0);
        manager.record_histogram("test_latency", 150.0);
        manager.record_histogram("test_latency", 200.0);
        
        // Start and end a trace
        let trace = manager.start_trace("test_operation");
        sleep(Duration::from_millis(10)).await;
        trace.end("success");
        
        // Check health
        let health = manager.check_health().await;
        assert!(matches!(health.status, crate::mcp::monitoring::ServiceStatus::Healthy));
        assert!(health.checks.contains_key("memory"));
        assert!(health.checks.contains_key("cpu"));
        
        // Export metrics
        let metrics = manager.export_metrics().await.unwrap();
        assert!(metrics.get("counters").is_some());
        assert!(metrics.get("gauges").is_some());
        assert!(metrics.get("histograms").is_some());
    }

    #[test]
    fn test_environment_specific_configs() {
        // Development config
        let dev_config = DeploymentConfig {
            environment: Environment::Development,
            ..Default::default()
        };
        let env_config = dev_config.get_environment_config();
        assert_eq!(env_config.server.protocol, "stdio");
        assert!(!env_config.security.authentication.enabled);
        assert!(!env_config.security.tls.enabled);
        assert_eq!(env_config.monitoring.log_level, crate::mcp::monitoring::LogLevel::Debug);
        
        // Staging config
        let staging_config = DeploymentConfig {
            environment: Environment::Staging,
            ..Default::default()
        };
        let env_config = staging_config.get_environment_config();
        assert_eq!(env_config.server.protocol, "http");
        assert!(env_config.security.authentication.enabled);
        assert!(env_config.security.tls.enabled);
        assert_eq!(env_config.monitoring.log_level, crate::mcp::monitoring::LogLevel::Info);
        
        // Production config
        let prod_config = DeploymentConfig {
            environment: Environment::Production,
            ..Default::default()
        };
        let env_config = prod_config.get_environment_config();
        assert_eq!(env_config.server.protocol, "websocket");
        assert!(env_config.security.authentication.enabled);
        assert!(env_config.security.tls.enabled);
        assert!(env_config.security.rate_limiting.enabled);
        assert!(env_config.monitoring.telemetry.enabled);
        assert_eq!(env_config.monitoring.log_level, crate::mcp::monitoring::LogLevel::Warn);
    }

    #[test]
    fn test_config_merging() {
        let mut base_config = DeploymentConfig::default();
        base_config.server.host = "localhost".to_string();
        base_config.server.port = 3000;
        
        // Simulate loading partial config from file
        let mut file_config = DeploymentConfig::default();
        file_config.server.port = 8080;
        file_config.security.authentication.enabled = true;
        
        // Merge configs (file config overrides base)
        base_config.server.port = file_config.server.port;
        base_config.security = file_config.security;
        
        assert_eq!(base_config.server.host, "localhost");
        assert_eq!(base_config.server.port, 8080);
        assert!(base_config.security.authentication.enabled);
    }
}