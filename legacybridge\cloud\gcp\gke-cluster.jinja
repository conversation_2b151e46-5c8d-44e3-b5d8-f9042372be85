{#
Copyright 2024 LegacyBridge
GKE Cluster Template for Google Cloud Deployment Manager
#}

resources:
# GKE Cluster
- name: {{ env["name"] }}-cluster
  type: container.v1.cluster
  properties:
    zone: {{ properties["zone"] }}
    cluster:
      name: {{ env["name"] }}
      initialClusterVersion: {{ properties["clusterVersion"] }}
      network: {{ properties["network"] }}
      subnetwork: {{ properties["subnetwork"] }}
      
      # IP Allocation
      ipAllocationPolicy:
        useIpAliases: true
        clusterSecondaryRangeName: pods
        servicesSecondaryRangeName: services
      
      # Master configuration
      masterAuth:
        clientCertificateConfig:
          issueClientCertificate: false
      
      # Security
      binaryAuthorization:
        enabled: false
      shieldedNodes:
        enabled: true
      workloadIdentityConfig:
        workloadPool: {{ env["project"] }}.svc.id.goog
      
      # Networking
      networkPolicy:
        enabled: true
        provider: CALICO
      privateClusterConfig:
        enablePrivateNodes: true
        enablePrivateEndpoint: false
        masterIpv4CidrBlock: **********/28
      
      # Addons
      addonsConfig: {{ properties["addonsConfig"] }}
      
      # Logging and Monitoring
      loggingService: logging.googleapis.com/kubernetes
      monitoringService: monitoring.googleapis.com/kubernetes
      
      # Node Pools
      nodePools:
      - name: default-pool
        initialNodeCount: {{ properties["initialNodeCount"] }}
        config:
          machineType: {{ properties["machineType"] }}
          diskSizeGb: {{ properties["diskSizeGb"] }}
          diskType: pd-standard
          oauthScopes:
          - https://www.googleapis.com/auth/cloud-platform
          labels:
            environment: production
            node-pool: default
          shieldedInstanceConfig:
            enableSecureBoot: true
            enableIntegrityMonitoring: true
          metadata:
            disable-legacy-endpoints: "true"
        management:
          autoUpgrade: {{ properties["enableAutoUpgrade"] }}
          autoRepair: {{ properties["enableAutoRepair"] }}
        autoscaling:
          enabled: {{ properties["enableAutoscaling"] }}
          minNodeCount: {{ properties["minNodeCount"] }}
          maxNodeCount: {{ properties["maxNodeCount"] }}
        upgradeSettings:
          maxSurge: 1
          maxUnavailable: 0
      
      # Maintenance Window
      maintenancePolicy:
        window:
          recurringWindow:
            window:
              startTime: "2024-01-01T03:00:00Z"
              endTime: "2024-01-01T07:00:00Z"
            recurrence: FREQ=WEEKLY;BYDAY=SU

outputs:
- name: clusterName
  value: {{ env["name"] }}
- name: clusterEndpoint
  value: $(ref.{{ env["name"] }}-cluster.endpoint)
- name: clusterCaCertificate
  value: $(ref.{{ env["name"] }}-cluster.masterAuth.clusterCaCertificate)