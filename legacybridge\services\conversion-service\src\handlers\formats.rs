// Format support handlers
use axum::{
    extract::Extension,
    response::<PERSON><PERSON> as Response<PERSON><PERSON>,
    <PERSON><PERSON>,
};
use legacybridge_shared::{
    types::ApiResponse,
    ServiceError, ServiceResult,
};
use serde::{Deserialize, Serialize};
use tracing::info;
use validator::Validate;

use crate::AppState;
use crate::converter::DocumentConverter;

#[derive(Debug, Serialize)]
pub struct SupportedFormats {
    pub input_formats: Vec<FormatInfo>,
    pub output_formats: Vec<FormatInfo>,
}

#[derive(Debug, Serialize)]
pub struct FormatInfo {
    pub format: String,
    pub name: String,
    pub description: String,
    pub extensions: Vec<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct DetectFormatRequest {
    #[validate(length(min = 1))]
    pub content: String, // Base64 encoded content
    pub filename: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct DetectFormatResponse {
    pub detected_format: String,
    pub confidence: f32,
    pub alternatives: Vec<FormatAlternative>,
}

#[derive(Debug, Serialize)]
pub struct FormatAlternative {
    pub format: String,
    pub confidence: f32,
}

/// List supported formats
pub async fn list_supported_formats(
    Extension(_state): Extension<AppState>,
) -> ServiceResult<ResponseJson<ApiResponse<SupportedFormats>>> {
    info!("Listing supported formats");

    let input_formats = vec![
        FormatInfo {
            format: "rtf".to_string(),
            name: "Rich Text Format".to_string(),
            description: "Microsoft Rich Text Format documents".to_string(),
            extensions: vec!["rtf".to_string()],
        },
        FormatInfo {
            format: "md".to_string(),
            name: "Markdown".to_string(),
            description: "Markdown formatted text".to_string(),
            extensions: vec!["md".to_string(), "markdown".to_string()],
        },
        FormatInfo {
            format: "html".to_string(),
            name: "HTML".to_string(),
            description: "HyperText Markup Language".to_string(),
            extensions: vec!["html".to_string(), "htm".to_string()],
        },
        FormatInfo {
            format: "txt".to_string(),
            name: "Plain Text".to_string(),
            description: "Plain text files".to_string(),
            extensions: vec!["txt".to_string(), "text".to_string()],
        },
        FormatInfo {
            format: "doc".to_string(),
            name: "Microsoft Word (Legacy)".to_string(),
            description: "Microsoft Word 97-2003 documents (limited support)".to_string(),
            extensions: vec!["doc".to_string()],
        },
        FormatInfo {
            format: "docx".to_string(),
            name: "Microsoft Word".to_string(),
            description: "Microsoft Word 2007+ documents (limited support)".to_string(),
            extensions: vec!["docx".to_string()],
        },
    ];

    let output_formats = vec![
        FormatInfo {
            format: "md".to_string(),
            name: "Markdown".to_string(),
            description: "Markdown formatted text".to_string(),
            extensions: vec!["md".to_string(), "markdown".to_string()],
        },
        FormatInfo {
            format: "html".to_string(),
            name: "HTML".to_string(),
            description: "HyperText Markup Language".to_string(),
            extensions: vec!["html".to_string(), "htm".to_string()],
        },
        FormatInfo {
            format: "txt".to_string(),
            name: "Plain Text".to_string(),
            description: "Plain text files".to_string(),
            extensions: vec!["txt".to_string(), "text".to_string()],
        },
        FormatInfo {
            format: "json".to_string(),
            name: "JSON".to_string(),
            description: "Structured JSON representation".to_string(),
            extensions: vec!["json".to_string()],
        },
    ];

    let formats = SupportedFormats {
        input_formats,
        output_formats,
    };

    Ok(ResponseJson(ApiResponse::success(formats)))
}

/// Detect document format from content
pub async fn detect_format(
    Extension(state): Extension<AppState>,
    Json(request): Json<DetectFormatRequest>,
) -> ServiceResult<ResponseJson<ApiResponse<DetectFormatResponse>>> {
    info!("Detecting document format");

    // Validate request
    request.validate().map_err(ServiceError::from)?;

    // Decode content
    let content_bytes = base64::decode(&request.content)
        .map_err(|e| ServiceError::BadRequest(format!("Invalid base64 content: {}", e)))?;

    if content_bytes.is_empty() {
        return Err(ServiceError::BadRequest("Content cannot be empty".to_string()));
    }

    // Use the converter to detect format
    let converter = DocumentConverter::new();
    let detected_format = converter.detect_format(&content_bytes)?;

    // Calculate confidence based on detection method
    let confidence = calculate_format_confidence(&content_bytes, &detected_format, &request.filename);

    // Generate alternatives
    let alternatives = generate_format_alternatives(&content_bytes, &detected_format, &request.filename);

    let response = DetectFormatResponse {
        detected_format,
        confidence,
        alternatives,
    };

    Ok(ResponseJson(ApiResponse::success(response)))
}

/// Calculate confidence score for format detection
fn calculate_format_confidence(content: &[u8], detected_format: &str, filename: Option<&String>) -> f32 {
    let mut confidence = 0.5; // Base confidence

    // Check if content matches format expectations
    if let Ok(text) = std::str::from_utf8(content) {
        let trimmed = text.trim();
        
        match detected_format {
            "rtf" => {
                if trimmed.starts_with("{\\rtf") {
                    confidence = 0.95;
                } else {
                    confidence = 0.3;
                }
            }
            "html" => {
                if trimmed.starts_with("<!DOCTYPE html") || 
                   trimmed.starts_with("<html") ||
                   trimmed.contains("<html>") {
                    confidence = 0.9;
                } else if trimmed.contains("<") && trimmed.contains(">") {
                    confidence = 0.7;
                } else {
                    confidence = 0.3;
                }
            }
            "markdown" => {
                let markdown_indicators = [
                    trimmed.contains("# "),
                    trimmed.contains("## "),
                    trimmed.contains("```"),
                    trimmed.contains("**"),
                    trimmed.contains("*"),
                ];
                let indicator_count = markdown_indicators.iter().filter(|&&x| x).count();
                confidence = 0.4 + (indicator_count as f32 * 0.15);
            }
            "text" => {
                // Text is the fallback, so lower confidence unless it's clearly plain text
                confidence = 0.6;
            }
            _ => {}
        }
    }

    // Boost confidence if filename extension matches
    if let Some(filename) = filename {
        if let Some(extension) = filename.split('.').last() {
            let ext_lower = extension.to_lowercase();
            let format_matches = match detected_format {
                "rtf" => ext_lower == "rtf",
                "html" => ext_lower == "html" || ext_lower == "htm",
                "markdown" => ext_lower == "md" || ext_lower == "markdown",
                "text" => ext_lower == "txt" || ext_lower == "text",
                "doc" => ext_lower == "doc",
                "docx" => ext_lower == "docx",
                _ => false,
            };
            
            if format_matches {
                confidence = (confidence + 0.3).min(1.0);
            }
        }
    }

    confidence
}

/// Generate alternative format suggestions
fn generate_format_alternatives(
    content: &[u8],
    detected_format: &str,
    filename: Option<&String>,
) -> Vec<FormatAlternative> {
    let mut alternatives = Vec::new();

    if let Ok(text) = std::str::from_utf8(content) {
        let trimmed = text.trim();

        // Always suggest text as an alternative (unless it's the detected format)
        if detected_format != "text" {
            alternatives.push(FormatAlternative {
                format: "text".to_string(),
                confidence: 0.6,
            });
        }

        // Check for HTML indicators
        if detected_format != "html" && (trimmed.contains("<") && trimmed.contains(">")) {
            let confidence = if trimmed.contains("<!DOCTYPE") || trimmed.contains("<html") {
                0.8
            } else {
                0.4
            };
            alternatives.push(FormatAlternative {
                format: "html".to_string(),
                confidence,
            });
        }

        // Check for Markdown indicators
        if detected_format != "markdown" {
            let markdown_score = [
                trimmed.contains("# "),
                trimmed.contains("```"),
                trimmed.contains("**"),
                trimmed.contains("["),
            ].iter().filter(|&&x| x).count() as f32 * 0.2;
            
            if markdown_score > 0.0 {
                alternatives.push(FormatAlternative {
                    format: "markdown".to_string(),
                    confidence: markdown_score.min(0.8),
                });
            }
        }
    }

    // Sort by confidence descending
    alternatives.sort_by(|a, b| b.confidence.partial_cmp(&a.confidence).unwrap());
    
    // Limit to top 3 alternatives
    alternatives.truncate(3);
    
    alternatives
}
