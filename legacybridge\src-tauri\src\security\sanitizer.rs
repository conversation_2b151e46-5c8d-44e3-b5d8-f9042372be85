// Content Sanitization Module for LegacyBridge
use std::collections::HashMap;
use regex::Regex;
use once_cell::sync::Lazy;

/// Content sanitizer for removing or neutralizing dangerous content
pub struct ContentSanitizer {
    sanitization_rules: HashMap<String, Box<dyn SanitizationRule + Send + Sync>>,
    config: SanitizerConfig,
}

#[derive(Debu<PERSON>, Clone)]
pub struct SanitizerConfig {
    /// Enable aggressive sanitization (removes more content)
    pub aggressive_mode: bool,
    
    /// Preserve formatting when possible
    pub preserve_formatting: bool,
    
    /// Log sanitization actions
    pub log_actions: bool,
    
    /// Maximum content size to process (bytes)
    pub max_content_size: usize,
}

impl Default for SanitizerConfig {
    fn default() -> Self {
        Self {
            aggressive_mode: false,
            preserve_formatting: true,
            log_actions: true,
            max_content_size: 100 * 1024 * 1024, // 100MB
        }
    }
}

#[derive(Debug, <PERSON><PERSON>)]
pub struct SanitizationResult {
    /// Sanitized content
    pub content: Vec<u8>,
    
    /// Number of modifications made
    pub modifications_count: usize,
    
    /// Types of threats neutralized
    pub neutralized_threats: Vec<String>,
    
    /// Warning messages
    pub warnings: Vec<String>,
    
    /// Whether content was truncated
    pub truncated: bool,
}

pub trait SanitizationRule: Send + Sync {
    fn sanitize(&self, content: &[u8], config: &SanitizerConfig) -> Result<Vec<u8>, SanitizationError>;
    fn threat_type(&self) -> &str;
}

impl ContentSanitizer {
    pub fn new(config: SanitizerConfig) -> Self {
        let mut sanitization_rules: HashMap<String, Box<dyn SanitizationRule + Send + Sync>> = HashMap::new();
        
        // Add default sanitization rules
        sanitization_rules.insert("script_injection".to_string(), Box::new(ScriptInjectionSanitizer));
        sanitization_rules.insert("path_traversal".to_string(), Box::new(PathTraversalSanitizer));
        sanitization_rules.insert("rtf_malicious".to_string(), Box::new(RtfMaliciousSanitizer));
        sanitization_rules.insert("xml_entity".to_string(), Box::new(XmlEntitySanitizer));
        sanitization_rules.insert("buffer_overflow".to_string(), Box::new(BufferOverflowSanitizer));
        sanitization_rules.insert("embedded_objects".to_string(), Box::new(EmbeddedObjectSanitizer));
        
        Self {
            sanitization_rules,
            config,
        }
    }
    
    /// Sanitize content based on detected threats
    pub fn sanitize_content(
        &self,
        content: &[u8],
        format: &str,
        detected_threats: &[crate::security::validator::SecurityThreat],
    ) -> SanitizationResult {
        let mut result = SanitizationResult {
            content: content.to_vec(),
            modifications_count: 0,
            neutralized_threats: Vec::new(),
            warnings: Vec::new(),
            truncated: false,
        };
        
        // Check content size
        if content.len() > self.config.max_content_size {
            result.content.truncate(self.config.max_content_size);
            result.truncated = true;
            result.warnings.push(format!(
                "Content truncated from {} to {} bytes",
                content.len(),
                self.config.max_content_size
            ));
        }
        
        // Apply format-specific sanitization
        result.content = match format {
            "rtf" => self.sanitize_rtf(&result.content, detected_threats, &mut result),
            "doc" | "docx" => self.sanitize_office(&result.content, detected_threats, &mut result),
            "pdf" => self.sanitize_pdf(&result.content, detected_threats, &mut result),
            "xml" | "html" => self.sanitize_xml(&result.content, detected_threats, &mut result),
            _ => self.sanitize_generic(&result.content, detected_threats, &mut result),
        };
        
        // Apply threat-specific sanitization
        for threat in detected_threats {
            if let Some(rule) = self.get_rule_for_threat(threat) {
                match rule.sanitize(&result.content, &self.config) {
                    Ok(sanitized) => {
                        if sanitized != result.content {
                            result.content = sanitized;
                            result.modifications_count += 1;
                            result.neutralized_threats.push(rule.threat_type().to_string());
                        }
                    }
                    Err(e) => {
                        result.warnings.push(format!("Sanitization error for {}: {}", rule.threat_type(), e));
                    }
                }
            }
        }
        
        if self.config.log_actions && result.modifications_count > 0 {
            tracing::info!(
                "Sanitized content: {} modifications, neutralized threats: {:?}",
                result.modifications_count,
                result.neutralized_threats
            );
        }
        
        result
    }
    
    fn get_rule_for_threat(&self, threat: &crate::security::validator::SecurityThreat) -> Option<&Box<dyn SanitizationRule + Send + Sync>> {
        use crate::security::validator::ThreatType;
        
        match threat.threat_type {
            ThreatType::ScriptInjection => self.sanitization_rules.get("script_injection"),
            ThreatType::PathTraversal => self.sanitization_rules.get("path_traversal"),
            ThreatType::XmlBomb => self.sanitization_rules.get("xml_entity"),
            ThreatType::BufferOverflow => self.sanitization_rules.get("buffer_overflow"),
            ThreatType::MaliciousContent => match threat.description.as_str() {
                s if s.contains("RTF") => self.sanitization_rules.get("rtf_malicious"),
                s if s.contains("object") => self.sanitization_rules.get("embedded_objects"),
                _ => None,
            },
            _ => None,
        }
    }
    
    fn sanitize_rtf(&self, content: &[u8], _threats: &[crate::security::validator::SecurityThreat], result: &mut SanitizationResult) -> Vec<u8> {
        let content_str = String::from_utf8_lossy(content);
        
        // Remove potentially dangerous RTF control words
        static DANGEROUS_RTF_PATTERNS: Lazy<Vec<(&str, &str)>> = Lazy::new(|| vec![
            (r"\\object", "\\object_removed"),
            (r"\\objdata\s*[0-9a-fA-F]+", "\\objdata_removed"),
            (r"\\field", "\\field_removed"),
            (r"\\fldinst", "\\fldinst_removed"),
            (r"\\fldrslt", "\\fldrslt_removed"),
            (r"\\datafield", "\\datafield_removed"),
            (r"\\do", "\\do_removed"),
            (r"\\sv", "\\sv_removed"), // Shape vertex
            (r"\\sp", "\\sp_removed"), // Shape property
        ]);
        
        let mut sanitized = content_str.to_string();
        
        for (pattern, replacement) in DANGEROUS_RTF_PATTERNS.iter() {
            let re = Regex::new(pattern).unwrap();
            let before_len = sanitized.len();
            sanitized = re.replace_all(&sanitized, *replacement).to_string();
            if sanitized.len() != before_len {
                result.modifications_count += 1;
            }
        }
        
        // Balance braces if needed
        let open_braces = sanitized.matches('{').count();
        let close_braces = sanitized.matches('}').count();
        
        if open_braces != close_braces {
            result.warnings.push(format!(
                "RTF brace imbalance detected: {} open, {} close",
                open_braces, close_braces
            ));
            
            // Add missing closing braces
            while sanitized.matches('{').count() > sanitized.matches('}').count() {
                sanitized.push('}');
                result.modifications_count += 1;
            }
        }
        
        sanitized.into_bytes()
    }
    
    fn sanitize_office(&self, content: &[u8], _threats: &[crate::security::validator::SecurityThreat], result: &mut SanitizationResult) -> Vec<u8> {
        // For binary Office formats, we can't easily sanitize without corrupting the file
        // In production, you would use a proper Office file parser
        result.warnings.push("Binary Office format sanitization limited".to_string());
        content.to_vec()
    }
    
    fn sanitize_pdf(&self, content: &[u8], _threats: &[crate::security::validator::SecurityThreat], result: &mut SanitizationResult) -> Vec<u8> {
        let content_str = String::from_utf8_lossy(content);
        
        // Remove JavaScript from PDF
        static PDF_JS_PATTERN: Lazy<Regex> = Lazy::new(|| 
            Regex::new(r"/JavaScript\s*\([^)]*\)").unwrap()
        );
        
        let sanitized = PDF_JS_PATTERN.replace_all(&content_str, "/JavaScript_removed").to_string();
        
        if sanitized != content_str {
            result.modifications_count += 1;
            result.neutralized_threats.push("PDF JavaScript".to_string());
        }
        
        sanitized.into_bytes()
    }
    
    fn sanitize_xml(&self, content: &[u8], _threats: &[crate::security::validator::SecurityThreat], result: &mut SanitizationResult) -> Vec<u8> {
        let content_str = String::from_utf8_lossy(content);
        
        // Remove dangerous XML entities
        static XML_ENTITY_PATTERN: Lazy<Regex> = Lazy::new(|| 
            Regex::new(r"<!ENTITY[^>]*>").unwrap()
        );
        
        // Remove script tags
        static SCRIPT_TAG_PATTERN: Lazy<Regex> = Lazy::new(|| 
            Regex::new(r"(?i)<script[^>]*>.*?</script>").unwrap()
        );
        
        let mut sanitized = XML_ENTITY_PATTERN.replace_all(&content_str, "<!-- ENTITY removed -->").to_string();
        sanitized = SCRIPT_TAG_PATTERN.replace_all(&sanitized, "<!-- script removed -->").to_string();
        
        if sanitized != content_str {
            result.modifications_count += 1;
            result.neutralized_threats.push("XML entities/scripts".to_string());
        }
        
        sanitized.into_bytes()
    }
    
    fn sanitize_generic(&self, content: &[u8], _threats: &[crate::security::validator::SecurityThreat], _result: &mut SanitizationResult) -> Vec<u8> {
        // Generic sanitization for unknown formats
        content.to_vec()
    }
}

// Specific sanitization rules
struct ScriptInjectionSanitizer;

impl SanitizationRule for ScriptInjectionSanitizer {
    fn sanitize(&self, content: &[u8], _config: &SanitizerConfig) -> Result<Vec<u8>, SanitizationError> {
        let content_str = String::from_utf8_lossy(content);
        
        // Remove script tags and javascript: URLs
        let sanitized = content_str
            .replace("<script", "&lt;script")
            .replace("javascript:", "javascript_blocked:")
            .replace("vbscript:", "vbscript_blocked:")
            .replace("onload=", "onload_blocked=")
            .replace("onerror=", "onerror_blocked=")
            .replace("onclick=", "onclick_blocked=");
        
        Ok(sanitized.into_bytes())
    }
    
    fn threat_type(&self) -> &str {
        "script_injection"
    }
}

struct PathTraversalSanitizer;

impl SanitizationRule for PathTraversalSanitizer {
    fn sanitize(&self, content: &[u8], _config: &SanitizerConfig) -> Result<Vec<u8>, SanitizationError> {
        let content_str = String::from_utf8_lossy(content);
        
        // Remove path traversal patterns
        let sanitized = content_str
            .replace("../", "")
            .replace("..\\", "")
            .replace("/..", "")
            .replace("\\..", "");
        
        Ok(sanitized.into_bytes())
    }
    
    fn threat_type(&self) -> &str {
        "path_traversal"
    }
}

struct RtfMaliciousSanitizer;

impl SanitizationRule for RtfMaliciousSanitizer {
    fn sanitize(&self, content: &[u8], config: &SanitizerConfig) -> Result<Vec<u8>, SanitizationError> {
        let content_str = String::from_utf8_lossy(content);
        
        if config.aggressive_mode {
            // Remove all RTF control words except basic formatting
            static ALLOWED_RTF_CONTROLS: Lazy<Vec<&str>> = Lazy::new(|| vec![
                "\\rtf", "\\ansi", "\\deff", "\\fonttbl", "\\colortbl", 
                "\\b", "\\i", "\\ul", "\\par", "\\line", "\\tab"
            ]);
            
            let mut sanitized = content_str.to_string();
            let re = Regex::new(r"\\[a-z]+").unwrap();
            
            sanitized = re.replace_all(&sanitized, |caps: &regex::Captures| {
                let control = &caps[0];
                if ALLOWED_RTF_CONTROLS.iter().any(|&allowed| control.starts_with(allowed)) {
                    control.to_string()
                } else {
                    format!("\\removed_{}", &control[1..])
                }
            }).to_string();
            
            Ok(sanitized.into_bytes())
        } else {
            // Just remove known dangerous controls
            Ok(content.to_vec())
        }
    }
    
    fn threat_type(&self) -> &str {
        "rtf_malicious"
    }
}

struct XmlEntitySanitizer;

impl SanitizationRule for XmlEntitySanitizer {
    fn sanitize(&self, content: &[u8], _config: &SanitizerConfig) -> Result<Vec<u8>, SanitizationError> {
        let content_str = String::from_utf8_lossy(content);
        
        // Remove all entity declarations
        let entity_re = Regex::new(r"<!ENTITY[^>]*>").unwrap();
        let sanitized = entity_re.replace_all(&content_str, "").to_string();
        
        Ok(sanitized.into_bytes())
    }
    
    fn threat_type(&self) -> &str {
        "xml_entity"
    }
}

struct BufferOverflowSanitizer;

impl SanitizationRule for BufferOverflowSanitizer {
    fn sanitize(&self, content: &[u8], _config: &SanitizerConfig) -> Result<Vec<u8>, SanitizationError> {
        let content_str = String::from_utf8_lossy(content);
        
        // Remove excessive repetitions that could cause buffer overflows
        let repetition_re = Regex::new(r"(.)\1{1000,}").unwrap();
        let sanitized = repetition_re.replace_all(&content_str, "$1$1$1...[truncated]").to_string();
        
        Ok(sanitized.into_bytes())
    }
    
    fn threat_type(&self) -> &str {
        "buffer_overflow"
    }
}

struct EmbeddedObjectSanitizer;

impl SanitizationRule for EmbeddedObjectSanitizer {
    fn sanitize(&self, content: &[u8], config: &SanitizerConfig) -> Result<Vec<u8>, SanitizationError> {
        if config.aggressive_mode {
            // In aggressive mode, remove all embedded objects
            let content_str = String::from_utf8_lossy(content);
            let object_re = Regex::new(r"<object[^>]*>.*?</object>").unwrap();
            let sanitized = object_re.replace_all(&content_str, "[embedded object removed]").to_string();
            Ok(sanitized.into_bytes())
        } else {
            // In normal mode, just return as-is
            Ok(content.to_vec())
        }
    }
    
    fn threat_type(&self) -> &str {
        "embedded_objects"
    }
}

#[derive(Debug)]
pub enum SanitizationError {
    InvalidContent(String),
    ProcessingError(String),
    ConfigError(String),
}

impl std::fmt::Display for SanitizationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SanitizationError::InvalidContent(msg) => write!(f, "Invalid content: {}", msg),
            SanitizationError::ProcessingError(msg) => write!(f, "Processing error: {}", msg),
            SanitizationError::ConfigError(msg) => write!(f, "Configuration error: {}", msg),
        }
    }
}

impl std::error::Error for SanitizationError {}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_script_injection_sanitization() {
        let config = SanitizerConfig::default();
        let sanitizer = ContentSanitizer::new(config);
        
        let malicious_content = b"<script>alert('XSS')</script>Hello javascript:alert('XSS')";
        let threats = vec![]; // Empty threats for testing
        
        let result = sanitizer.sanitize_content(malicious_content, "html", &threats);
        
        let sanitized_str = String::from_utf8_lossy(&result.content);
        assert!(!sanitized_str.contains("<script"));
        assert!(!sanitized_str.contains("javascript:"));
    }
    
    #[test]
    fn test_rtf_sanitization() {
        let config = SanitizerConfig::default();
        let sanitizer = ContentSanitizer::new(config);
        
        let malicious_rtf = b"{\\rtf1\\ansi\\deff0 {\\object\\objdata 41424344} Hello World}";
        let threats = vec![]; // Empty threats for testing
        
        let result = sanitizer.sanitize_content(malicious_rtf, "rtf", &threats);
        
        let sanitized_str = String::from_utf8_lossy(&result.content);
        assert!(sanitized_str.contains("\\object_removed"));
        assert!(result.modifications_count > 0);
    }
}