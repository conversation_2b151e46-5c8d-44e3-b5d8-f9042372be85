imports:
- path: network.jinja
- path: gke-cluster.jinja
- path: cloud-sql.jinja
- path: memorystore.jinja
- path: storage.jinja
- path: container-registry.jinja

resources:
# VPC Network
- name: legacybridge-network
  type: network.jinja
  properties:
    region: us-central1
    vpcCidr: 10.0.0.0/16
    subnetCidrs:
      gke: 10.0.1.0/24
      services: 10.0.2.0/24
      pods: 10.0.4.0/22

# GKE Cluster
- name: legacybridge-gke
  type: gke-cluster.jinja
  properties:
    zone: us-central1-a
    initialNodeCount: 3
    minNodeCount: 2
    maxNodeCount: 20
    machineType: n1-standard-2
    diskSizeGb: 100
    network: $(ref.legacybridge-network.networkName)
    subnetwork: $(ref.legacybridge-network.gkeSubnet)
    clusterVersion: "1.28"
    enableAutoscaling: true
    enableAutoUpgrade: true
    enableAutoRepair: true
    enableStackdriverLogging: true
    enableStackdriverMonitoring: true
    addonsConfig:
      httpLoadBalancing:
        disabled: false
      horizontalPodAutoscaling:
        disabled: false
      kubernetesDashboard:
        disabled: true
      networkPolicyConfig:
        disabled: false

# Cloud SQL PostgreSQL
- name: legacybridge-postgres
  type: cloud-sql.jinja
  properties:
    region: us-central1
    tier: db-n1-standard-2
    databaseVersion: POSTGRES_15
    diskSize: 100
    diskType: PD_SSD
    backupEnabled: true
    highAvailability: true
    maintenanceWindow:
      day: 7
      hour: 3
    authorizedNetworks:
      - name: gke-cluster
        value: $(ref.legacybridge-network.gkeSubnetCidr)

# Memorystore Redis
- name: legacybridge-redis
  type: memorystore.jinja
  properties:
    region: us-central1
    tier: STANDARD_HA
    memorySizeGb: 5
    redisVersion: REDIS_6_X
    network: $(ref.legacybridge-network.networkName)
    authorizedNetwork: $(ref.legacybridge-network.vpcNetwork)

# Cloud Storage
- name: legacybridge-storage
  type: storage.jinja
  properties:
    location: US
    storageClass: STANDARD
    versioning: true
    lifecycleRules:
      - action:
          type: Delete
        condition:
          age: 365
          isLive: false
      - action:
          type: SetStorageClass
          storageClass: NEARLINE
        condition:
          age: 30
          isLive: true

# Container Registry
- name: legacybridge-gcr
  type: container-registry.jinja
  properties:
    location: us

outputs:
- name: gkeClusterName
  value: $(ref.legacybridge-gke.clusterName)
- name: gkeClusterEndpoint
  value: $(ref.legacybridge-gke.clusterEndpoint)
- name: postgresInstanceName
  value: $(ref.legacybridge-postgres.instanceName)
- name: postgresConnectionName
  value: $(ref.legacybridge-postgres.connectionName)
- name: redisHost
  value: $(ref.legacybridge-redis.host)
- name: redisPort
  value: $(ref.legacybridge-redis.port)
- name: storageBucket
  value: $(ref.legacybridge-storage.bucketName)
- name: containerRegistry
  value: $(ref.legacybridge-gcr.registryUrl)