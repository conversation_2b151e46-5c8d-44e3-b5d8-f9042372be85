{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"environment": {"type": "string", "defaultValue": "production", "allowedValues": ["development", "staging", "production"], "metadata": {"description": "Environment name"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Azure region for resources"}}, "vnetAddressPrefix": {"type": "string", "defaultValue": "10.0.0.0/16", "metadata": {"description": "Address prefix for virtual network"}}, "aksNodeCount": {"type": "int", "defaultValue": 3, "minValue": 1, "maxValue": 100, "metadata": {"description": "Number of AKS nodes"}}, "aksNodeVMSize": {"type": "string", "defaultValue": "Standard_DS2_v2", "metadata": {"description": "VM size for AKS nodes"}}, "postgresqlVersion": {"type": "string", "defaultValue": "15", "allowedValues": ["13", "14", "15"], "metadata": {"description": "PostgreSQL version"}}, "postgresqlSkuName": {"type": "string", "defaultValue": "GP_Gen5_2", "metadata": {"description": "PostgreSQL SKU name"}}, "administratorLogin": {"type": "string", "defaultValue": "<PERSON><PERSON><PERSON>", "metadata": {"description": "Administrator username for PostgreSQL"}}, "administratorLoginPassword": {"type": "securestring", "metadata": {"description": "Administrator password for PostgreSQL"}}}, "variables": {"baseName": "[concat('legacybridge-', parameters('environment'))]", "vnetName": "[concat(variables('baseName'), '-vnet')]", "aksName": "[concat(variables('baseName'), '-aks')]", "postgresqlName": "[concat(variables('baseName'), '-postgres')]", "redisName": "[concat(variables('baseName'), '-redis')]", "storageAccountName": "[concat('legacybridge', parameters('environment'), uniqueString(resourceGroup().id))]", "keyVaultName": "[concat(variables('baseName'), '-kv')]", "containerRegistryName": "[concat('legacybridge', parameters('environment'), uniqueString(resourceGroup().id))]", "logAnalyticsName": "[concat(variables('baseName'), '-logs')]", "appInsightsName": "[concat(variables('baseName'), '-insights')]"}, "resources": [{"type": "Microsoft.Network/virtualNetworks", "apiVersion": "2023-04-01", "name": "[variables('vnetName')]", "location": "[parameters('location')]", "properties": {"addressSpace": {"addressPrefixes": ["[parameters('vnetAddressPrefix')]"]}, "subnets": [{"name": "aks-subnet", "properties": {"addressPrefix": "********/24", "serviceEndpoints": [{"service": "Microsoft.Sql"}, {"service": "Microsoft.Storage"}, {"service": "Microsoft.KeyVault"}]}}, {"name": "postgres-subnet", "properties": {"addressPrefix": "********/24", "delegations": [{"name": "postgres-delegation", "properties": {"serviceName": "Microsoft.DBforPostgreSQL/flexibleServers"}}]}}, {"name": "redis-subnet", "properties": {"addressPrefix": "********/24"}}]}}, {"type": "Microsoft.OperationalInsights/workspaces", "apiVersion": "2022-10-01", "name": "[variables('logAnalyticsName')]", "location": "[parameters('location')]", "properties": {"sku": {"name": "PerGB2018"}, "retentionInDays": 30}}, {"type": "Microsoft.Insights/components", "apiVersion": "2020-02-02", "name": "[variables('appInsightsName')]", "location": "[parameters('location')]", "kind": "web", "properties": {"Application_Type": "web", "WorkspaceResourceId": "[resourceId('Microsoft.OperationalInsights/workspaces', variables('logAnalyticsName'))]"}, "dependsOn": ["[resourceId('Microsoft.OperationalInsights/workspaces', variables('logAnalyticsName'))]"]}, {"type": "Microsoft.ContainerRegistry/registries", "apiVersion": "2023-01-01-preview", "name": "[variables('containerRegistryName')]", "location": "[parameters('location')]", "sku": {"name": "Standard"}, "properties": {"adminUserEnabled": false, "encryption": {"status": "enabled"}, "dataEndpointEnabled": false, "publicNetworkAccess": "Enabled", "networkRuleBypassOptions": "AzureServices"}}, {"type": "Microsoft.ManagedIdentity/userAssignedIdentities", "apiVersion": "2023-01-31", "name": "[concat(variables('baseName'), '-identity')]", "location": "[parameters('location')]"}, {"type": "Microsoft.ContainerService/managedClusters", "apiVersion": "2023-10-01", "name": "[variables('aksName')]", "location": "[parameters('location')]", "identity": {"type": "SystemAssigned"}, "properties": {"dnsPrefix": "[variables('aksName')]", "kubernetesVersion": "1.28.3", "enableRBAC": true, "nodeResourceGroup": "[concat(variables('aksName'), '-nodes')]", "networkProfile": {"networkPlugin": "azure", "networkPolicy": "azure", "loadBalancerSku": "standard", "serviceCidr": "********/16", "dnsServiceIP": "*********", "dockerBridgeCidr": "**********/16"}, "agentPoolProfiles": [{"name": "nodepool1", "count": "[parameters('aksNodeCount')]", "vmSize": "[parameters('aksNodeVMSize')]", "osDiskSizeGB": 100, "osDiskType": "Managed", "vnetSubnetID": "[resourceId('Microsoft.Network/virtualNetworks/subnets', variables('vnetName'), 'aks-subnet')]", "maxPods": 110, "type": "VirtualMachineScaleSets", "mode": "System", "enableAutoScaling": true, "minCount": 2, "maxCount": 20, "orchestratorVersion": "1.28.3", "enableNodePublicIP": false, "tags": {"environment": "[parameters('environment')]"}}], "addonProfiles": {"azureKeyvaultSecretsProvider": {"enabled": true, "config": {"enableSecretRotation": "true"}}, "azurepolicy": {"enabled": true}, "httpApplicationRouting": {"enabled": false}, "omsagent": {"enabled": true, "config": {"logAnalyticsWorkspaceResourceID": "[resourceId('Microsoft.OperationalInsights/workspaces', variables('logAnalyticsName'))]"}}}, "autoUpgradeProfile": {"upgradeChannel": "stable"}}, "dependsOn": ["[resourceId('Microsoft.Network/virtualNetworks', variables('vnetName'))]", "[resourceId('Microsoft.OperationalInsights/workspaces', variables('logAnalyticsName'))]"]}, {"type": "Microsoft.DBforPostgreSQL/flexibleServers", "apiVersion": "2022-12-01", "name": "[variables('postgresqlName')]", "location": "[parameters('location')]", "sku": {"name": "[parameters('postgresqlSkuName')]", "tier": "GeneralPurpose"}, "properties": {"version": "[parameters('postgresqlVersion')]", "administratorLogin": "[parameters('administratorLogin')]", "administratorLoginPassword": "[parameters('administratorLoginPassword')]", "storage": {"storageSizeGB": 128}, "backup": {"backupRetentionDays": 7, "geoRedundantBackup": "Enabled"}, "highAvailability": {"mode": "ZoneRedundant"}, "network": {"delegatedSubnetResourceId": "[resourceId('Microsoft.Network/virtualNetworks/subnets', variables('vnetName'), 'postgres-subnet')]"}}, "dependsOn": ["[resourceId('Microsoft.Network/virtualNetworks', variables('vnetName'))]"]}, {"type": "Microsoft.DBforPostgreSQL/flexibleServers/databases", "apiVersion": "2022-12-01", "name": "[concat(variables('postgresqlName'), '/legacybridge')]", "properties": {"charset": "UTF8", "collation": "en_US.utf8"}, "dependsOn": ["[resourceId('Microsoft.DBforPostgreSQL/flexibleServers', variables('postgresqlName'))]"]}, {"type": "Microsoft.DBforPostgreSQL/flexibleServers/firewallRules", "apiVersion": "2022-12-01", "name": "[concat(variables('postgresqlName'), '/AllowAzureServices')]", "properties": {"startIpAddress": "0.0.0.0", "endIpAddress": "0.0.0.0"}, "dependsOn": ["[resourceId('Microsoft.DBforPostgreSQL/flexibleServers', variables('postgresqlName'))]"]}, {"type": "Microsoft.Cache/redis", "apiVersion": "2023-08-01", "name": "[variables('redisName')]", "location": "[parameters('location')]", "properties": {"sku": {"name": "Standard", "family": "C", "capacity": 1}, "enableNonSslPort": false, "minimumTlsVersion": "1.2", "redisConfiguration": {"maxmemory-policy": "allkeys-lru"}, "subnetId": "[resourceId('Microsoft.Network/virtualNetworks/subnets', variables('vnetName'), 'redis-subnet')]"}, "dependsOn": ["[resourceId('Microsoft.Network/virtualNetworks', variables('vnetName'))]"]}, {"type": "Microsoft.Storage/storageAccounts", "apiVersion": "2023-01-01", "name": "[variables('storageAccountName')]", "location": "[parameters('location')]", "sku": {"name": "Standard_LRS"}, "kind": "StorageV2", "properties": {"accessTier": "Hot", "supportsHttpsTrafficOnly": true, "minimumTlsVersion": "TLS1_2", "encryption": {"services": {"blob": {"enabled": true}, "file": {"enabled": true}}, "keySource": "Microsoft.Storage"}, "networkAcls": {"defaultAction": "<PERSON><PERSON>", "virtualNetworkRules": [{"id": "[resourceId('Microsoft.Network/virtualNetworks/subnets', variables('vnetName'), 'aks-subnet')]", "action": "Allow"}], "bypass": "AzureServices"}}, "dependsOn": ["[resourceId('Microsoft.Network/virtualNetworks', variables('vnetName'))]"]}, {"type": "Microsoft.Storage/storageAccounts/blobServices/containers", "apiVersion": "2023-01-01", "name": "[concat(variables('storageAccountName'), '/default/legacybridge-files')]", "properties": {"publicAccess": "None"}, "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts', variables('storageAccountName'))]"]}, {"type": "Microsoft.KeyVault/vaults", "apiVersion": "2023-07-01", "name": "[variables('keyVaultName')]", "location": "[parameters('location')]", "properties": {"sku": {"family": "A", "name": "standard"}, "tenantId": "[subscription().tenantId]", "enabledForDeployment": false, "enabledForDiskEncryption": false, "enabledForTemplateDeployment": true, "enableSoftDelete": true, "softDeleteRetentionInDays": 90, "enableRbacAuthorization": true, "networkAcls": {"defaultAction": "<PERSON><PERSON>", "virtualNetworkRules": [{"id": "[resourceId('Microsoft.Network/virtualNetworks/subnets', variables('vnetName'), 'aks-subnet')]"}], "bypass": "AzureServices"}}, "dependsOn": ["[resourceId('Microsoft.Network/virtualNetworks', variables('vnetName'))]"]}], "outputs": {"aksClusterName": {"type": "string", "value": "[variables('aksName')]"}, "aksResourceId": {"type": "string", "value": "[resourceId('Microsoft.ContainerService/managedClusters', variables('aksName'))]"}, "containerRegistryName": {"type": "string", "value": "[variables('containerRegistryName')]"}, "containerRegistryLoginServer": {"type": "string", "value": "[reference(resourceId('Microsoft.ContainerRegistry/registries', variables('containerRegistryName'))).loginServer]"}, "postgresqlServerName": {"type": "string", "value": "[variables('postgresqlName')]"}, "postgresqlFQDN": {"type": "string", "value": "[reference(resourceId('Microsoft.DBforPostgreSQL/flexibleServers', variables('postgresqlName'))).fullyQualifiedDomainName]"}, "redisHostName": {"type": "string", "value": "[reference(resourceId('Microsoft.Cache/redis', variables('redisName'))).hostName]"}, "redisPrimaryKey": {"type": "string", "value": "[listKeys(resourceId('Microsoft.Cache/redis', variables('redisName')), '2023-08-01').primaryKey]"}, "storageAccountName": {"type": "string", "value": "[variables('storageAccountName')]"}, "storageAccountKey": {"type": "string", "value": "[listKeys(resourceId('Microsoft.Storage/storageAccounts', variables('storageAccountName')), '2023-01-01').keys[0].value]"}, "keyVaultName": {"type": "string", "value": "[variables('keyVaultName')]"}, "appInsightsInstrumentationKey": {"type": "string", "value": "[reference(resourceId('Microsoft.Insights/components', variables('appInsightsName'))).InstrumentationKey]"}}}