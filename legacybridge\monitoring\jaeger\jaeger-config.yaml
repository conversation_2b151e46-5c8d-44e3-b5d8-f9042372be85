apiVersion: v1
kind: ConfigMap
metadata:
  name: jaeger-config
  namespace: monitoring
data:
  collector.yaml: |
    receivers:
      otlp:
        protocols:
          grpc:
            endpoint: 0.0.0.0:4317
          http:
            endpoint: 0.0.0.0:4318
      jaeger:
        protocols:
          grpc:
            endpoint: 0.0.0.0:14250
          thrift_http:
            endpoint: 0.0.0.0:14268
          thrift_compact:
            endpoint: 0.0.0.0:6831
          thrift_binary:
            endpoint: 0.0.0.0:6832

    processors:
      batch:
        timeout: 1s
        send_batch_size: 1024
      memory_limiter:
        check_interval: 1s
        limit_mib: 512
      resource:
        attributes:
          - key: service.namespace
            value: legacybridge
            action: upsert
          - key: deployment.environment
            from_attribute: env
            action: insert

    exporters:
      jaeger:
        endpoint: jaeger-collector:14250
        tls:
          insecure: true
      prometheus:
        endpoint: 0.0.0.0:8889
        namespace: jaeger
        const_labels:
          service: jaeger

    extensions:
      health_check:
        endpoint: 0.0.0.0:13133
      pprof:
        endpoint: 0.0.0.0:1777
      zpages:
        endpoint: 0.0.0.0:55679

    service:
      extensions: [health_check, pprof, zpages]
      pipelines:
        traces:
          receivers: [otlp, jaeger]
          processors: [memory_limiter, batch, resource]
          exporters: [jaeger]
        metrics:
          receivers: [otlp]
          processors: [memory_limiter, batch]
          exporters: [prometheus]

  sampling.yaml: |
    service_strategies:
      - service: "legacybridge-backend"
        type: adaptive
        max_traces_per_second: 100
        sampling_percentage: 1.0
      - service: "legacybridge-frontend"
        type: probabilistic
        param: 0.1
    default_strategy:
      type: probabilistic
      param: 0.01

  query.yaml: |
    query:
      base-path: /
      static-files: /usr/share/jaeger/ui
      ui-config: /etc/jaeger/ui-config.json
      
  ui-config.json: |
    {
      "dependencies": {
        "menuEnabled": true
      },
      "archiveEnabled": false,
      "tracking": {
        "gaID": null,
        "trackErrors": false
      },
      "menu": [
        {
          "label": "Grafana",
          "items": [
            {
              "label": "LegacyBridge Overview",
              "url": "http://grafana:3000/d/legacybridge-overview"
            },
            {
              "label": "SLO Dashboard",
              "url": "http://grafana:3000/d/legacybridge-slo"
            }
          ]
        }
      ],
      "linkPatterns": [
        {
          "type": "logs",
          "key": "trace_id",
          "url": "http://kibana:5601/app/discover#/?_g=(time:(from:now-1h,to:now))&_a=(query:(match:(trace_id:'#{trace_id}')))",
          "text": "View Logs"
        }
      ]
    }