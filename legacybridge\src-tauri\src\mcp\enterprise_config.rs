use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use std::fs;
use std::env;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnterpriseConfig {
    pub license: LicenseConfig,
    pub organization: OrganizationConfig,
    pub deployment: EnterpriseDeployment,
    pub features: EnterpriseFeatures,
    pub customization: CustomizationConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseConfig {
    pub license_key: String,
    pub license_type: LicenseType,
    pub expiration_date: Option<String>,
    pub max_users: Option<u32>,
    pub max_conversions_per_month: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum LicenseType {
    Trial,
    Standard,
    Professional,
    Enterprise,
    Unlimited,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct OrganizationConfig {
    pub name: String,
    pub domain: Option<String>,
    pub admin_email: String,
    pub support_email: String,
    pub billing_email: Option<String>,
    pub technical_contact: Option<ContactInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContactInfo {
    pub name: String,
    pub email: String,
    pub phone: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnterpriseDeployment {
    pub deployment_id: String,
    pub environment: String,
    pub region: Option<String>,
    pub high_availability: bool,
    pub disaster_recovery: bool,
    pub backup_config: Option<BackupConfig>,
    pub compliance: ComplianceConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackupConfig {
    pub enabled: bool,
    pub schedule: String, // cron expression
    pub retention_days: u32,
    pub backup_location: String,
    pub encryption_key: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceConfig {
    pub data_residency: Vec<String>, // country codes
    pub encryption_at_rest: bool,
    pub encryption_in_transit: bool,
    pub audit_logging: bool,
    pub gdpr_compliant: bool,
    pub hipaa_compliant: bool,
    pub sox_compliant: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnterpriseFeatures {
    pub max_file_size_mb: usize,
    pub concurrent_conversions: usize,
    pub batch_processing: bool,
    pub priority_queue: bool,
    pub custom_formats: Vec<String>,
    pub api_rate_limits: ApiRateLimits,
    pub integrations: IntegrationFeatures,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiRateLimits {
    pub requests_per_minute: u32,
    pub requests_per_hour: u32,
    pub requests_per_day: u32,
    pub burst_size: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrationFeatures {
    pub active_directory: bool,
    pub ldap: bool,
    pub saml: bool,
    pub oauth2: bool,
    pub webhook_notifications: bool,
    pub custom_plugins: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomizationConfig {
    pub branding: BrandingConfig,
    pub ui_customization: bool,
    pub custom_error_pages: bool,
    pub white_label: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrandingConfig {
    pub company_name: String,
    pub logo_url: Option<String>,
    pub primary_color: Option<String>,
    pub secondary_color: Option<String>,
    pub favicon_url: Option<String>,
}

pub struct EnterpriseConfigManager {
    base_path: PathBuf,
    config: Option<EnterpriseConfig>,
}

impl EnterpriseConfigManager {
    pub fn new(base_path: PathBuf) -> Self {
        Self {
            base_path,
            config: None,
        }
    }

    /// Generate MCP configuration for enterprise deployment
    pub fn generate_mcp_config(&self, org_config: &EnterpriseConfig) -> Result<serde_json::Value> {
        let deployment_id = &org_config.deployment.deployment_id;
        
        Ok(serde_json::json!({
            "name": format!("legacybridge-mcp-{}", deployment_id),
            "version": env!("CARGO_PKG_VERSION"),
            "description": format!("LegacyBridge MCP Server - {}", org_config.organization.name),
            "server": {
                "command": self.get_server_command(),
                "args": self.get_server_args(&org_config),
                "env": self.get_server_env(&org_config)
            },
            "capabilities": {
                "resources": true,
                "tools": true,
                "prompts": org_config.features.batch_processing,
                "notifications": org_config.features.integrations.webhook_notifications
            },
            "deployment": self.generate_deployment_config(&org_config)?,
            "resources": self.generate_resources(&org_config),
            "tools": self.generate_tools(&org_config),
            "enterprise": {
                "deployment_id": deployment_id,
                "organization": org_config.organization.name.clone(),
                "license_type": org_config.license.license_type.clone(),
                "features": org_config.features.clone()
            }
        }))
    }

    fn get_server_command(&self) -> String {
        if cfg!(windows) {
            "legacybridge-mcp.exe".to_string()
        } else {
            "legacybridge-mcp".to_string()
        }
    }

    fn get_server_args(&self, config: &EnterpriseConfig) -> Vec<String> {
        let mut args = vec!["server".to_string()];
        
        if config.deployment.high_availability {
            args.push("--ha".to_string());
        }
        
        if let Some(region) = &config.deployment.region {
            args.push("--region".to_string());
            args.push(region.clone());
        }
        
        args.push("--config".to_string());
        args.push(self.get_config_path(&config).to_string_lossy().to_string());
        
        args
    }

    fn get_server_env(&self, config: &EnterpriseConfig) -> serde_json::Value {
        let mut env = serde_json::json!({
            "RUST_LOG": if config.deployment.environment == "production" { "warn" } else { "info" },
            "MCP_ENVIRONMENT": &config.deployment.environment,
            "MCP_DEPLOYMENT_ID": &config.deployment.deployment_id,
            "MCP_ORGANIZATION": &config.organization.name,
        });

        if config.compliance.audit_logging {
            env["MCP_AUDIT_LOG"] = serde_json::json!("true");
            env["MCP_AUDIT_LOG_PATH"] = serde_json::json!("/var/log/legacybridge/audit.log");
        }

        env
    }

    fn generate_deployment_config(&self, config: &EnterpriseConfig) -> Result<serde_json::Value> {
        Ok(serde_json::json!({
            "server": {
                "protocol": if config.deployment.high_availability { "websocket" } else { "http" },
                "host": "0.0.0.0",
                "port": 443,
                "workers": num_cpus::get() * 2,
            },
            "security": {
                "enable_authentication": true,
                "enable_tls": true,
                "tls_cert": "/etc/legacybridge/certs/server.crt",
                "tls_key": "/etc/legacybridge/certs/server.key",
                "rate_limiting": {
                    "enabled": true,
                    "requests_per_minute": config.features.api_rate_limits.requests_per_minute,
                    "burst_size": config.features.api_rate_limits.burst_size
                }
            },
            "performance": {
                "max_concurrent_requests": config.features.concurrent_conversions,
                "cache_size": 1024, // MB
                "enable_compression": true
            },
            "monitoring": {
                "enable_telemetry": true,
                "enable_health_check": true,
                "audit_logging": config.compliance.audit_logging
            }
        }))
    }

    fn generate_resources(&self, config: &EnterpriseConfig) -> Vec<serde_json::Value> {
        let mut resources = vec![
            serde_json::json!({
                "name": "supported-formats",
                "description": "List of supported file formats for conversion",
                "uri": "legacy://formats",
                "mimeType": "application/json"
            }),
            serde_json::json!({
                "name": "conversion-stats",
                "description": "Real-time conversion statistics and metrics",
                "uri": "legacy://stats",
                "mimeType": "application/json"
            }),
            serde_json::json!({
                "name": "license-info",
                "description": "License and usage information",
                "uri": "legacy://license",
                "mimeType": "application/json"
            }),
        ];

        if config.compliance.audit_logging {
            resources.push(serde_json::json!({
                "name": "audit-log",
                "description": "Audit log entries",
                "uri": "legacy://audit",
                "mimeType": "application/json"
            }));
        }

        resources
    }

    fn generate_tools(&self, config: &EnterpriseConfig) -> Vec<serde_json::Value> {
        let mut tools = vec![
            serde_json::json!({
                "name": "convert",
                "description": "Convert files between legacy formats",
                "parameters": {
                    "source_format": {
                        "type": "string",
                        "description": "Source file format"
                    },
                    "target_format": {
                        "type": "string",
                        "description": "Target file format"
                    },
                    "file_path": {
                        "type": "string",
                        "description": "Path to the file to convert"
                    },
                    "options": {
                        "type": "object",
                        "description": "Additional conversion options"
                    }
                }
            }),
        ];

        if config.features.batch_processing {
            tools.push(serde_json::json!({
                "name": "batch_convert",
                "description": "Convert multiple files in batch",
                "parameters": {
                    "source_format": {
                        "type": "string",
                        "description": "Source file format"
                    },
                    "target_format": {
                        "type": "string",
                        "description": "Target file format"
                    },
                    "input_directory": {
                        "type": "string",
                        "description": "Directory containing files to convert"
                    },
                    "output_directory": {
                        "type": "string",
                        "description": "Directory for converted files"
                    },
                    "recursive": {
                        "type": "boolean",
                        "description": "Process subdirectories recursively"
                    }
                }
            }));
        }

        if config.features.priority_queue {
            tools.push(serde_json::json!({
                "name": "priority_convert",
                "description": "Convert with priority queuing",
                "parameters": {
                    "priority": {
                        "type": "string",
                        "enum": ["low", "normal", "high", "urgent"],
                        "description": "Conversion priority"
                    },
                    "source_format": {
                        "type": "string",
                        "description": "Source file format"
                    },
                    "target_format": {
                        "type": "string",
                        "description": "Target file format"
                    },
                    "file_path": {
                        "type": "string",
                        "description": "Path to the file to convert"
                    }
                }
            }));
        }

        tools
    }

    fn get_config_path(&self, config: &EnterpriseConfig) -> PathBuf {
        self.base_path
            .join("deployments")
            .join(&config.deployment.deployment_id)
            .join("config.toml")
    }

    /// Initialize enterprise configuration for a new deployment
    pub fn initialize_deployment(&mut self, org_name: &str, license_key: &str) -> Result<EnterpriseConfig> {
        let deployment_id = Uuid::new_v4().to_string();
        
        let config = EnterpriseConfig {
            license: LicenseConfig {
                license_key: license_key.to_string(),
                license_type: LicenseType::Enterprise,
                expiration_date: None,
                max_users: None,
                max_conversions_per_month: None,
            },
            organization: OrganizationConfig {
                name: org_name.to_string(),
                domain: None,
                admin_email: format!("admin@{}.com", org_name.to_lowercase().replace(" ", "")),
                support_email: format!("support@{}.com", org_name.to_lowercase().replace(" ", "")),
                billing_email: None,
                technical_contact: None,
            },
            deployment: EnterpriseDeployment {
                deployment_id,
                environment: "production".to_string(),
                region: None,
                high_availability: true,
                disaster_recovery: true,
                backup_config: Some(BackupConfig {
                    enabled: true,
                    schedule: "0 2 * * *".to_string(), // Daily at 2 AM
                    retention_days: 30,
                    backup_location: "/var/backups/legacybridge".to_string(),
                    encryption_key: Some(Uuid::new_v4().to_string()),
                }),
                compliance: ComplianceConfig {
                    data_residency: vec![],
                    encryption_at_rest: true,
                    encryption_in_transit: true,
                    audit_logging: true,
                    gdpr_compliant: true,
                    hipaa_compliant: false,
                    sox_compliant: false,
                },
            },
            features: EnterpriseFeatures {
                max_file_size_mb: 500,
                concurrent_conversions: 50,
                batch_processing: true,
                priority_queue: true,
                custom_formats: vec![],
                api_rate_limits: ApiRateLimits {
                    requests_per_minute: 1000,
                    requests_per_hour: 50000,
                    requests_per_day: 1000000,
                    burst_size: 100,
                },
                integrations: IntegrationFeatures {
                    active_directory: true,
                    ldap: true,
                    saml: true,
                    oauth2: true,
                    webhook_notifications: true,
                    custom_plugins: true,
                },
            },
            customization: CustomizationConfig {
                branding: BrandingConfig {
                    company_name: org_name.to_string(),
                    logo_url: None,
                    primary_color: None,
                    secondary_color: None,
                    favicon_url: None,
                },
                ui_customization: true,
                custom_error_pages: true,
                white_label: true,
            },
        };

        self.config = Some(config.clone());
        self.save_config(&config)?;
        
        Ok(config)
    }

    /// Save enterprise configuration
    fn save_config(&self, config: &EnterpriseConfig) -> Result<()> {
        let config_dir = self.base_path
            .join("deployments")
            .join(&config.deployment.deployment_id);
        
        fs::create_dir_all(&config_dir)?;
        
        let config_path = config_dir.join("enterprise.json");
        let config_json = serde_json::to_string_pretty(config)?;
        fs::write(config_path, config_json)?;
        
        // Generate and save MCP config
        let mcp_config = self.generate_mcp_config(config)?;
        let mcp_path = config_dir.join(".mcp.json");
        let mcp_json = serde_json::to_string_pretty(&mcp_config)?;
        fs::write(mcp_path, mcp_json)?;
        
        Ok(())
    }

    /// Load enterprise configuration
    pub fn load_config(&mut self, deployment_id: &str) -> Result<EnterpriseConfig> {
        let config_path = self.base_path
            .join("deployments")
            .join(deployment_id)
            .join("enterprise.json");
        
        let config_json = fs::read_to_string(config_path)
            .context("Failed to read enterprise configuration")?;
        
        let config: EnterpriseConfig = serde_json::from_str(&config_json)
            .context("Failed to parse enterprise configuration")?;
        
        self.config = Some(config.clone());
        Ok(config)
    }

    /// Validate license key
    pub fn validate_license(&self, license_key: &str) -> Result<bool> {
        // In production, this would validate against a license server
        // For now, we'll do basic validation
        if license_key.len() < 16 {
            return Ok(false);
        }
        
        // Check format (simplified)
        let parts: Vec<&str> = license_key.split('-').collect();
        if parts.len() != 4 {
            return Ok(false);
        }
        
        Ok(true)
    }
}