// DLL Builder Configuration Types
// Defines all types and interfaces for the DLL Builder Studio

export type Architecture = 'x86' | 'x64';
export type OptimizationLevel = 'debug' | 'release' | 'size';
export type BuildStage = 'preparing' | 'compiling' | 'linking' | 'packaging' | 'completed' | 'failed';
export type TestSuite = 'compatibility' | 'performance' | 'integration' | 'security';
export type PackageFormat = 'zip' | 'tar' | 'msi' | 'nsis';

export interface DLLConfiguration {
  architectures: Architecture[];
  optimization: OptimizationLevel;
  includeDebugSymbols: boolean;
  staticLinking: boolean;
  includedFormats: string[];
  outputDirectory: string;
  generateIntegrationCode: {
    vb6: boolean;
    vfp9: boolean;
    csharp: boolean;
    python: boolean;
  };
  customOptions: Record<string, string>;
  buildMetadata: {
    version: string;
    company: string;
    description: string;
    copyright: string;
  };
}

export interface BuildStatus {
  stage: BuildStage;
  currentStep: string;
  progress: number;
  success?: boolean;
  error?: string;
  outputFiles: string[];
  logs: string[];
  startTime: Date;
  endTime?: Date;
  buildId: string;
}

export interface TestResult {
  testName: string;
  platform: string;
  passed: boolean;
  skipped?: boolean;
  duration: number;
  details?: string;
  error?: string;
  category?: 'compatibility' | 'performance' | 'integration' | 'security';
}

export interface DeploymentPackage {
  fileName: string;
  filePath: string;
  fileSize: number;
  format: PackageFormat;
  includedFiles: string[];
  downloadUrl: string;
  checksum: string;
  createdAt: Date;
  metadata: {
    version: string;
    architecture: Architecture[];
    includedFormats: string[];
  };
}

export interface BuildOptions {
  cleanBuild?: boolean;
  verbose?: boolean;
  parallel?: boolean;
  threads?: number;
  timeout?: number;
}

export interface TestOptions {
  skipFailedTests?: boolean;
  runInParallel?: boolean;
  generateReport?: boolean;
  reportFormat?: 'html' | 'json' | 'xml';
}

export interface PackageOptions {
  signPackage?: boolean;
  certificatePath?: string;
  compressionLevel?: 'none' | 'fast' | 'normal' | 'maximum';
  includeReadme?: boolean;
  includeLicense?: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface BuildProgress {
  stage: BuildStage;
  percentage: number;
  message: string;
  details?: {
    filesProcessed?: number;
    totalFiles?: number;
    currentFile?: string;
    estimatedTimeRemaining?: number;
  };
}

export interface IntegrationCode {
  language: 'vb6' | 'vfp9' | 'csharp' | 'python';
  code: string;
  fileName: string;
  documentation: string;
  examples: string[];
}

export interface PerformanceMetrics {
  buildTime: number;
  dllSize: number;
  memoryUsage: number;
  conversionSpeed: number;
  throughput: number;
}

export interface SecurityCheckResult {
  check: string;
  passed: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
  details: string;
  recommendation?: string;
}

export interface BuildArtifact {
  type: 'dll' | 'lib' | 'header' | 'documentation' | 'example';
  path: string;
  size: number;
  checksum: string;
  architecture?: Architecture;
}

export interface ErrorLog {
  timestamp: Date;
  level: 'error' | 'warning' | 'info';
  message: string;
  stack?: string;
  context?: Record<string, any>;
}

export interface PackageConfiguration {
  format: PackageFormat;
  includeDocs: boolean;
  includeExamples: boolean;
  includeSource: boolean;
  createInstaller: boolean;
  packageName: string;
  version: string;
  description: string;
  author: string;
  license: string;
}

export interface PackageValidation {
  isValid: boolean;
  errors: string[];
}

// Default configurations
export const DEFAULT_DLL_CONFIG: DLLConfiguration = {
  architectures: ['x86'],
  optimization: 'release',
  includeDebugSymbols: false,
  staticLinking: true,
  includedFormats: ['rtf', 'doc', 'wordperfect', 'lotus123', 'dbase'],
  outputDirectory: './dist/dll',
  generateIntegrationCode: {
    vb6: true,
    vfp9: true,
    csharp: false,
    python: false
  },
  customOptions: {},
  buildMetadata: {
    version: '2.0.0',
    company: 'LegacyBridge',
    description: 'Document conversion library for legacy systems',
    copyright: '© 2024 LegacyBridge'
  }
};

// Helper functions
export function isValidArchitecture(arch: string): arch is Architecture {
  return arch === 'x86' || arch === 'x64';
}

export function isValidOptimizationLevel(level: string): level is OptimizationLevel {
  return level === 'debug' || level === 'release' || level === 'size';
}

export function getArchitectureDisplay(arch: Architecture): string {
  return arch === 'x86' ? '32-bit' : '64-bit';
}

export function getBuildStageDisplay(stage: BuildStage): string {
  const displays: Record<BuildStage, string> = {
    preparing: 'Preparing Build',
    compiling: 'Compiling Sources',
    linking: 'Linking Libraries',
    packaging: 'Creating Package',
    completed: 'Build Complete',
    failed: 'Build Failed'
  };
  return displays[stage];
}

export function getTestSuiteDisplay(suite: TestSuite): string {
  const displays: Record<TestSuite, string> = {
    compatibility: 'Platform Compatibility',
    performance: 'Performance Benchmarks',
    integration: 'Integration Tests',
    security: 'Security Validation'
  };
  return displays[suite];
}

export function getPackageFormatExtension(format: PackageFormat): string {
  const extensions: Record<PackageFormat, string> = {
    zip: '.zip',
    tar: '.tar.gz',
    msi: '.msi',
    nsis: '.exe'
  };
  return extensions[format];
}