// Conversion Service for LegacyBridge
// Handles document conversion with Redis queue and async processing

mod handlers;
mod middleware;
mod service;
mod converter;
mod queue;

use axum::{
    extract::Extension,
    http::{HeaderValue, Method},
    routing::{get, post},
    Router,
};
use legacybridge_shared::{
    config::ServiceConfig,
    database::DatabaseManager,
    cache::CacheManager,
    metrics::ServiceMetrics,
    events::EventPublisher,
    ServiceResult,
};
use prometheus::Registry;
use std::{sync::Arc, time::Duration};
use tower::ServiceBuilder;
use tower_http::{
    cors::CorsLayer,
    trace::TraceLayer,
    timeout::TimeoutLayer,
    request_id::{MakeRequestUuid, PropagateRequestIdLayer, SetRequestIdLayer},
};
use tracing::{info, error};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

use crate::queue::JobQueue;
use crate::service::ConversionService;

// Application state
#[derive(Clone)]
pub struct AppState {
    pub db: Arc<DatabaseManager>,
    pub cache: Arc<CacheManager>,
    pub metrics: Arc<ServiceMetrics>,
    pub event_publisher: Arc<EventPublisher>,
    pub job_queue: Arc<JobQueue>,
    pub conversion_service: Arc<ConversionService>,
    pub config: Arc<ServiceConfig>,
}

#[tokio::main]
async fn main() -> ServiceResult<()> {
    // Load configuration
    dotenvy::dotenv().ok();
    let config = Arc::new(ServiceConfig::conversion_service_config());
    config.validate().map_err(|e| legacybridge_shared::ServiceError::Configuration(e))?;

    // Initialize tracing
    init_tracing(&config.service.name)?;

    info!(
        service = %config.service.name,
        version = %config.service.version,
        port = %config.service.port,
        "Starting conversion service"
    );

    // Initialize database
    let db = Arc::new(DatabaseManager::new(&config.database.url).await?);
    info!("Database connection established");

    // Initialize cache
    let cache = Arc::new(CacheManager::new(
        &config.redis.url,
        Duration::from_secs(3600),
    )?);
    info!("Redis cache connection established");

    // Initialize metrics
    let registry = Registry::new();
    let metrics = Arc::new(ServiceMetrics::new(&config.service.name, &registry)?);

    // Initialize event publisher
    let event_publisher = Arc::new(EventPublisher::new(&config.redis.url)?);

    // Initialize job queue
    let job_queue = Arc::new(JobQueue::new(&config.redis.url).await?);
    info!("Job queue initialized");

    // Initialize conversion service
    let conversion_service = Arc::new(ConversionService::new(
        db.clone(),
        cache.clone(),
        job_queue.clone(),
        event_publisher.clone(),
        metrics.clone(),
    ));

    // Create application state
    let state = AppState {
        db,
        cache,
        metrics,
        event_publisher,
        job_queue,
        conversion_service,
        config: config.clone(),
    };

    // Start job processor in background
    let processor_state = state.clone();
    tokio::spawn(async move {
        info!("Starting job processor");
        if let Err(e) = processor_state.conversion_service.start_job_processor().await {
            error!(error = %e, "Job processor failed");
        }
    });

    // Build the application router
    let app = create_router(state.clone()).await?;

    // Start metrics server
    let metrics_app = Router::new()
        .route("/metrics", get(|| async move {
            use prometheus::Encoder;
            let encoder = prometheus::TextEncoder::new();
            let metric_families = registry.gather();
            match encoder.encode_to_string(&metric_families) {
                Ok(output) => output,
                Err(e) => {
                    error!("Failed to encode metrics: {}", e);
                    String::new()
                }
            }
        }))
        .route("/health", get(handlers::health::health_check))
        .layer(Extension(state.clone()));

    // Start metrics server in background
    let metrics_port = config.metrics.port;
    tokio::spawn(async move {
        let listener = tokio::net::TcpListener::bind(format!("0.0.0.0:{}", metrics_port))
            .await
            .expect("Failed to bind metrics server");
        
        info!("Metrics server listening on port {}", metrics_port);
        
        axum::serve(listener, metrics_app)
            .await
            .expect("Metrics server failed");
    });

    // Start main application server
    let listener = tokio::net::TcpListener::bind(format!("{}:{}", config.service.host, config.service.port))
        .await
        .map_err(|e| legacybridge_shared::ServiceError::Internal(format!("Failed to bind server: {}", e)))?;

    info!(
        "Conversion service listening on {}:{}",
        config.service.host, config.service.port
    );

    axum::serve(listener, app)
        .await
        .map_err(|e| legacybridge_shared::ServiceError::Internal(format!("Server error: {}", e)))?;

    Ok(())
}

async fn create_router(state: AppState) -> ServiceResult<Router> {
    // CORS configuration
    let cors = CorsLayer::new()
        .allow_origin("*".parse::<HeaderValue>().unwrap())
        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE, Method::OPTIONS])
        .allow_headers(tower_http::cors::Any);

    // Request ID layer
    let request_id_layer = ServiceBuilder::new()
        .layer(SetRequestIdLayer::x_request_id(MakeRequestUuid))
        .layer(PropagateRequestIdLayer::x_request_id());

    // Build the router
    let router = Router::new()
        // Conversion routes
        .route("/api/v1/convert", post(handlers::conversion::convert_document))
        .route("/api/v1/convert/:job_id", get(handlers::conversion::get_conversion_status))
        .route("/api/v1/convert/:job_id/result", get(handlers::conversion::get_conversion_result))
        .route("/api/v1/convert/:job_id/cancel", post(handlers::conversion::cancel_conversion))
        
        // Job management routes
        .route("/api/v1/jobs", get(handlers::jobs::list_jobs))
        .route("/api/v1/jobs/:job_id", get(handlers::jobs::get_job))
        .route("/api/v1/jobs/:job_id", post(handlers::jobs::retry_job))
        
        // Format support routes
        .route("/api/v1/formats", get(handlers::formats::list_supported_formats))
        .route("/api/v1/formats/detect", post(handlers::formats::detect_format))
        
        // Health and status
        .route("/health", get(handlers::health::health_check))
        .route("/ready", get(handlers::health::readiness_check))
        .route("/status", get(handlers::health::status))
        
        // Add middleware
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(TimeoutLayer::new(Duration::from_secs(30)))
                .layer(cors)
                .layer(request_id_layer)
                .layer(middleware::metrics::MetricsMiddleware::new())
                .layer(Extension(state))
        );

    Ok(router)
}

fn init_tracing(service_name: &str) -> ServiceResult<()> {
    // Initialize Jaeger tracer
    let tracer = opentelemetry_jaeger::new_agent_pipeline()
        .with_service_name(service_name)
        .install_simple()
        .map_err(|e| legacybridge_shared::ServiceError::Internal(format!("Failed to initialize tracer: {}", e)))?;

    let opentelemetry = tracing_opentelemetry::layer().with_tracer(tracer);

    // Initialize tracing subscriber
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "conversion_service=debug,tower_http=debug,axum::rejection=trace".into()),
        )
        .with(tracing_subscriber::fmt::layer().json())
        .with(opentelemetry)
        .init();

    Ok(())
}
