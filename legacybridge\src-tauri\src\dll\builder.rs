use super::{<PERSON>ll<PERSON><PERSON><PERSON>, DllResult};
use std::path::{Path, PathBuf};
use std::process::Command;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildConfig {
    pub source_files: Vec<PathBuf>,
    pub output_name: String,
    pub architecture: Architecture,
    pub optimization: OptimizationLevel,
    pub debug_symbols: bool,
    pub static_linking: bool,
    pub export_definitions: Option<PathBuf>,
    pub include_dirs: Vec<PathBuf>,
    pub library_dirs: Vec<PathBuf>,
    pub libraries: Vec<String>,
    pub defines: Vec<String>,
    pub compiler_flags: Vec<String>,
    pub linker_flags: Vec<String>,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum Architecture {
    X86,
    X64,
    Both,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum OptimizationLevel {
    Debug,
    Release,
    Size,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BuildResult {
    pub dll_path: PathBuf,
    pub lib_path: Option<PathBuf>,
    pub header_path: Option<PathBuf>,
    pub architecture: String,
    pub size: u64,
    pub exports: Vec<String>,
    pub build_time: std::time::Duration,
}

pub struct DllBuilder {
    config: BuildConfig,
    work_dir: PathBuf,
}

impl DllBuilder {
    pub fn new(config: BuildConfig) -> Self {
        Self {
            config,
            work_dir: std::env::temp_dir().join("legacybridge_dll_build"),
        }
    }
    
    pub async fn build(&self) -> DllResult<BuildResult> {
        let start_time = std::time::Instant::now();
        
        // Create work directory
        std::fs::create_dir_all(&self.work_dir)?;
        
        // Build based on architecture
        match self.config.architecture {
            Architecture::X86 => self.build_arch("x86").await,
            Architecture::X64 => self.build_arch("x64").await,
            Architecture::Both => {
                // Build both architectures
                let x86_result = self.build_arch("x86").await?;
                let x64_result = self.build_arch("x64").await?;
                
                // Return x86 as primary (for VB6/VFP9 compatibility)
                Ok(BuildResult {
                    build_time: start_time.elapsed(),
                    ..x86_result
                })
            }
        }
    }
    
    async fn build_arch(&self, arch: &str) -> DllResult<BuildResult> {
        println!("🔨 Building {} DLL...", arch);
        
        // Prepare build directory
        let build_dir = self.work_dir.join(arch);
        std::fs::create_dir_all(&build_dir)?;
        
        // Copy source files
        for source in &self.config.source_files {
            let dest = build_dir.join(source.file_name().unwrap());
            std::fs::copy(source, dest)?;
        }
        
        // Generate build script based on platform
        let build_script = if cfg!(windows) {
            self.generate_windows_build_script(arch, &build_dir)?
        } else {
            self.generate_unix_build_script(arch, &build_dir)?
        };
        
        // Execute build
        let output = Command::new(&build_script)
            .current_dir(&build_dir)
            .output()?;
        
        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(DllError::CompilationError(stderr.to_string()));
        }
        
        // Find output DLL
        let dll_name = format!("{}.dll", self.config.output_name);
        let dll_path = build_dir.join(&dll_name);
        
        if !dll_path.exists() {
            return Err(DllError::BuildError(format!("DLL not found: {}", dll_path.display())));
        }
        
        // Get DLL info
        let metadata = std::fs::metadata(&dll_path)?;
        let exports = self.extract_exports(&dll_path)?;
        
        Ok(BuildResult {
            dll_path,
            lib_path: Some(build_dir.join(format!("{}.lib", self.config.output_name))),
            header_path: Some(build_dir.join(format!("{}.h", self.config.output_name))),
            architecture: arch.to_string(),
            size: metadata.len(),
            exports,
            build_time: std::time::Duration::default(),
        })
    }
    
    fn generate_windows_build_script(&self, arch: &str, build_dir: &Path) -> DllResult<PathBuf> {
        let script_path = build_dir.join("build.bat");
        
        let mut script = String::new();
        script.push_str("@echo off\n");
        script.push_str("setlocal\n\n");
        
        // Setup Visual Studio environment
        let vs_arch = if arch == "x86" { "x86" } else { "x64" };
        script.push_str(&format!(
            "call \"C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\Build\\vcvarsall.bat\" {}\n",
            vs_arch
        ));
        
        // Compiler flags
        let mut cl_flags = vec!["/nologo", "/W3", "/WX-", "/diagnostics:column", "/sdl", "/FC"];
        
        // Optimization
        match self.config.optimization {
            OptimizationLevel::Debug => {
                cl_flags.extend(&["/Od", "/MDd", "/RTC1"]);
            }
            OptimizationLevel::Release => {
                cl_flags.extend(&["/O2", "/MD", "/GL"]);
            }
            OptimizationLevel::Size => {
                cl_flags.extend(&["/O1", "/MD", "/GL"]);
            }
        }
        
        if self.config.debug_symbols {
            cl_flags.push("/Zi");
        }
        
        // Defines
        cl_flags.push("/D_WINDOWS");
        cl_flags.push("/D_USRDLL");
        cl_flags.push("/D_WINDLL");
        
        // Build formatted flags into vectors to avoid lifetime issues
        let define_flags: Vec<String> = self.config.defines.iter()
            .map(|define| format!("/D{}", define))
            .collect();
        
        let include_flags: Vec<String> = self.config.include_dirs.iter()
            .map(|inc| format!("/I\"{}\"", inc.display()))
            .collect();
        
        // Add flags
        for flag in &define_flags {
            cl_flags.push(flag);
        }
        
        for flag in &include_flags {
            cl_flags.push(flag);
        }
        
        // Custom compiler flags
        for flag in &self.config.compiler_flags {
            cl_flags.push(flag);
        }
        
        // Source files
        let sources: Vec<String> = self.config.source_files
            .iter()
            .map(|p| format!("\"{}\"", p.file_name().unwrap().to_string_lossy()))
            .collect();
        
        // Linker flags
        let mut link_flags = vec!["/DLL", "/NOLOGO"];
        
        if arch == "x86" {
            link_flags.push("/MACHINE:X86");
            link_flags.push("/SAFESEH:NO");
        } else {
            link_flags.push("/MACHINE:X64");
        }
        
        // Build formatted link flags to avoid lifetime issues
        let mut formatted_link_flags: Vec<String> = Vec::new();
        
        if let Some(def_file) = &self.config.export_definitions {
            formatted_link_flags.push(format!("/DEF:\"{}\"", def_file.display()));
        }
        
        // Library directories
        for lib_dir in &self.config.library_dirs {
            formatted_link_flags.push(format!("/LIBPATH:\"{}\"", lib_dir.display()));
        }
        
        // Libraries
        for lib in &self.config.libraries {
            formatted_link_flags.push(format!("{}.lib", lib));
        }
        
        // Output name
        formatted_link_flags.push(format!("/OUT:\"{}.dll\"", self.config.output_name));
        formatted_link_flags.push(format!("/IMPLIB:\"{}.lib\"", self.config.output_name));
        
        if self.config.debug_symbols {
            formatted_link_flags.push(format!("/PDB:\"{}.pdb\"", self.config.output_name));
        }
        
        // Add formatted flags
        for flag in &formatted_link_flags {
            link_flags.push(flag);
        }
        
        // Custom linker flags
        for flag in &self.config.linker_flags {
            link_flags.push(flag);
        }
        
        // Build command
        script.push_str(&format!(
            "cl {} {} /link {}\n",
            cl_flags.join(" "),
            sources.join(" "),
            link_flags.join(" ")
        ));
        
        script.push_str("\nif %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%\n");
        script.push_str("echo Build completed successfully!\n");
        
        std::fs::write(&script_path, script)?;
        Ok(script_path)
    }
    
    fn generate_unix_build_script(&self, arch: &str, build_dir: &Path) -> DllResult<PathBuf> {
        let script_path = build_dir.join("build.sh");
        
        let mut script = String::new();
        script.push_str("#!/bin/bash\n");
        script.push_str("set -e\n\n");
        
        // Compiler selection
        let compiler = if arch == "x86" {
            "i686-w64-mingw32-gcc"
        } else {
            "x86_64-w64-mingw32-gcc"
        };
        
        // Compiler flags
        let mut gcc_flags = vec!["-Wall", "-Wextra", "-shared", "-fPIC"];
        
        // Optimization
        match self.config.optimization {
            OptimizationLevel::Debug => {
                gcc_flags.extend(&["-O0", "-g"]);
            }
            OptimizationLevel::Release => {
                gcc_flags.extend(&["-O2"]);
            }
            OptimizationLevel::Size => {
                gcc_flags.extend(&["-Os"]);
            }
        }
        
        if !self.config.debug_symbols && self.config.optimization != OptimizationLevel::Debug {
            gcc_flags.push("-s");
        }
        
        // Defines
        gcc_flags.push("-DBUILDING_DLL");
        
        // Build formatted flags to avoid lifetime issues
        let define_flags: Vec<String> = self.config.defines.iter()
            .map(|define| format!("-D{}", define))
            .collect();
        
        let include_flags: Vec<String> = self.config.include_dirs.iter()
            .map(|inc| format!("-I\"{}\"", inc.display()))
            .collect();
        
        let lib_dir_flags: Vec<String> = self.config.library_dirs.iter()
            .map(|lib_dir| format!("-L\"{}\"", lib_dir.display()))
            .collect();
        
        // Add formatted flags
        for flag in &define_flags {
            gcc_flags.push(flag);
        }
        
        for flag in &include_flags {
            gcc_flags.push(flag);
        }
        
        for flag in &lib_dir_flags {
            gcc_flags.push(flag);
        }
        
        // Source files
        let sources: Vec<String> = self.config.source_files
            .iter()
            .map(|p| format!("\"{}\"", p.file_name().unwrap().to_string_lossy()))
            .collect();
        
        // Output
        gcc_flags.push("-o");
        let output_name = format!("\"{}.dll\"", self.config.output_name);
        gcc_flags.push(&output_name);
        
        // Libraries
        let lib_flags: Vec<String> = self.config.libraries.iter()
            .map(|lib| format!("-l{}", lib))
            .collect();
        
        for flag in &lib_flags {
            gcc_flags.push(flag);
        }
        
        // Export definition file
        if let Some(def_file) = &self.config.export_definitions {
            let def_file_str = format!("\"{}\"", def_file.display());
            gcc_flags.push(&def_file_str);
        }
        
        // Custom flags
        for flag in &self.config.compiler_flags {
            gcc_flags.push(flag);
        }
        
        // Build command
        script.push_str(&format!(
            "{} {} {}\n",
            compiler,
            gcc_flags.join(" "),
            sources.join(" ")
        ));
        
        script.push_str("\necho \"Build completed successfully!\"\n");
        
        std::fs::write(&script_path, script)?;
        
        // Make executable
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = std::fs::metadata(&script_path)?.permissions();
            perms.set_mode(0o755);
            std::fs::set_permissions(&script_path, perms)?;
        }
        
        Ok(script_path)
    }
    
    fn extract_exports(&self, dll_path: &Path) -> DllResult<Vec<String>> {
        if cfg!(windows) {
            // Use dumpbin on Windows
            let output = Command::new("dumpbin")
                .args(&["/EXPORTS", dll_path.to_str().unwrap()])
                .output()
                .map_err(|e| DllError::InspectionError(format!("Failed to run dumpbin: {}", e)))?;
            
            if output.status.success() {
                let stdout = String::from_utf8_lossy(&output.stdout);
                let exports = self.parse_dumpbin_output(&stdout);
                Ok(exports)
            } else {
                Ok(vec![])
            }
        } else {
            // Use objdump on Unix
            let output = Command::new("objdump")
                .args(&["-p", dll_path.to_str().unwrap()])
                .output()
                .map_err(|e| DllError::InspectionError(format!("Failed to run objdump: {}", e)))?;
            
            if output.status.success() {
                let stdout = String::from_utf8_lossy(&output.stdout);
                let exports = self.parse_objdump_output(&stdout);
                Ok(exports)
            } else {
                Ok(vec![])
            }
        }
    }
    
    fn parse_dumpbin_output(&self, output: &str) -> Vec<String> {
        let mut exports = Vec::new();
        let mut in_exports = false;
        
        for line in output.lines() {
            if line.contains("ordinal hint RVA      name") {
                in_exports = true;
                continue;
            }
            
            if in_exports && line.trim().is_empty() {
                break;
            }
            
            if in_exports {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 4 {
                    exports.push(parts[3].to_string());
                }
            }
        }
        
        exports
    }
    
    fn parse_objdump_output(&self, output: &str) -> Vec<String> {
        let mut exports = Vec::new();
        let mut in_export_table = false;
        
        for line in output.lines() {
            if line.contains("Export Table:") {
                in_export_table = true;
                continue;
            }
            
            if in_export_table && line.starts_with("\t[") {
                if let Some(name_start) = line.rfind(' ') {
                    let name = line[name_start + 1..].trim();
                    if !name.is_empty() {
                        exports.push(name.to_string());
                    }
                }
            }
            
            if in_export_table && !line.starts_with("\t") && !line.contains("Export Table:") {
                break;
            }
        }
        
        exports
    }
}

impl Default for BuildConfig {
    fn default() -> Self {
        Self {
            source_files: vec![],
            output_name: "legacybridge".to_string(),
            architecture: Architecture::X86,
            optimization: OptimizationLevel::Release,
            debug_symbols: false,
            static_linking: false,
            export_definitions: None,
            include_dirs: vec![],
            library_dirs: vec![],
            libraries: vec![],
            defines: vec![],
            compiler_flags: vec![],
            linker_flags: vec![],
        }
    }
}