#!/bin/bash

# Deploy Monitoring Stack for LegacyBridge
# This script deploys the complete monitoring infrastructure

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MONITORING_DIR="$(dirname "$SCRIPT_DIR")"
NAMESPACE="${NAMESPACE:-monitoring}"
ENVIRONMENT="${ENVIRONMENT:-production}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $*"
}

error() {
    echo -e "${RED}[ERROR]${NC} $*" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        error "kubectl is not installed"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Check helm (optional but recommended)
    if command -v helm &> /dev/null; then
        log "Helm is available"
        HELM_AVAILABLE=true
    else
        warning "Helm is not installed - will use kubectl apply"
        HELM_AVAILABLE=false
    fi
}

# Create namespace
create_namespace() {
    log "Creating namespace: $NAMESPACE"
    
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
    
    # Label namespace for monitoring
    kubectl label namespace "$NAMESPACE" \
        monitoring=enabled \
        environment="$ENVIRONMENT" \
        --overwrite
}

# Deploy Prometheus
deploy_prometheus() {
    log "Deploying Prometheus..."
    
    # Create Prometheus RBAC
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus
  namespace: $NAMESPACE
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs: ["get", "list", "watch"]
- apiGroups:
  - extensions
  resources:
  - ingresses
  verbs: ["get", "list", "watch"]
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus
subjects:
- kind: ServiceAccount
  name: prometheus
  namespace: $NAMESPACE
EOF
    
    # Apply Prometheus configuration
    kubectl apply -f "$MONITORING_DIR/prometheus/prometheus.yml" \
        --namespace="$NAMESPACE"
    
    # Apply alert rules
    kubectl create configmap prometheus-alerts \
        --from-file="$MONITORING_DIR/prometheus/alert_rules.yml" \
        --from-file="$MONITORING_DIR/prometheus/slo_rules.yml" \
        --from-file="$MONITORING_DIR/prometheus/recording_rules.yml" \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # Deploy Prometheus StatefulSet
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: prometheus
  namespace: $NAMESPACE
spec:
  serviceName: prometheus
  replicas: 2
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      containers:
      - name: prometheus
        image: prom/prometheus:v2.47.0
        args:
          - '--config.file=/etc/prometheus/prometheus.yml'
          - '--storage.tsdb.path=/prometheus'
          - '--storage.tsdb.retention.time=30d'
          - '--web.enable-lifecycle'
          - '--web.route-prefix=/'
        ports:
        - containerPort: 9090
          name: http
        volumeMounts:
        - name: config
          mountPath: /etc/prometheus
        - name: alerts
          mountPath: /etc/prometheus/rules
        - name: storage
          mountPath: /prometheus
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "1"
      volumes:
      - name: config
        configMap:
          name: prometheus-config
      - name: alerts
        configMap:
          name: prometheus-alerts
  volumeClaimTemplates:
  - metadata:
      name: storage
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 100Gi
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: $NAMESPACE
spec:
  ports:
  - port: 9090
    targetPort: 9090
  selector:
    app: prometheus
EOF
}

# Deploy Grafana
deploy_grafana() {
    log "Deploying Grafana..."
    
    # Create Grafana secret
    kubectl create secret generic grafana-credentials \
        --from-literal=admin-user=admin \
        --from-literal=admin-password="$(openssl rand -base64 32)" \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # Apply Grafana provisioning
    kubectl apply -f "$MONITORING_DIR/grafana/provisioning/" \
        --namespace="$NAMESPACE"
    
    # Create dashboard ConfigMap
    kubectl create configmap grafana-dashboards \
        --from-file="$MONITORING_DIR/grafana/dashboards/" \
        --namespace="$NAMESPACE" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # Deploy Grafana
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: $NAMESPACE
spec:
  replicas: 2
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:10.2.0
        env:
        - name: GF_SECURITY_ADMIN_USER
          valueFrom:
            secretKeyRef:
              name: grafana-credentials
              key: admin-user
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-credentials
              key: admin-password
        - name: GF_SERVER_ROOT_URL
          value: "%(protocol)s://%(domain)s:%(http_port)s/grafana/"
        - name: GF_SERVER_SERVE_FROM_SUB_PATH
          value: "true"
        ports:
        - containerPort: 3000
          name: http
        volumeMounts:
        - name: datasources
          mountPath: /etc/grafana/provisioning/datasources
        - name: dashboards-provider
          mountPath: /etc/grafana/provisioning/dashboards
        - name: dashboards
          mountPath: /var/lib/grafana/dashboards
        - name: storage
          mountPath: /var/lib/grafana
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: datasources
        configMap:
          name: grafana-datasources
      - name: dashboards-provider
        configMap:
          name: grafana-dashboard-provider
      - name: dashboards
        configMap:
          name: grafana-dashboards
      - name: storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: $NAMESPACE
spec:
  ports:
  - port: 3000
    targetPort: 3000
  selector:
    app: grafana
EOF
}

# Deploy Jaeger
deploy_jaeger() {
    log "Deploying Jaeger..."
    
    kubectl apply -f "$MONITORING_DIR/jaeger/" \
        --namespace="$NAMESPACE"
}

# Deploy ELK Stack
deploy_elk() {
    log "Deploying ELK Stack..."
    
    # Deploy Elasticsearch
    kubectl apply -f "$MONITORING_DIR/elasticsearch/elasticsearch-config.yaml" \
        --namespace="$NAMESPACE"
    kubectl apply -f "$MONITORING_DIR/elasticsearch/elasticsearch-deployment.yaml" \
        --namespace="$NAMESPACE"
    
    # Wait for Elasticsearch to be ready
    log "Waiting for Elasticsearch to be ready..."
    kubectl wait --for=condition=ready pod -l app=elasticsearch \
        --namespace="$NAMESPACE" \
        --timeout=300s || warning "Elasticsearch pods not ready yet"
    
    # Deploy Logstash
    kubectl apply -f "$MONITORING_DIR/elasticsearch/logstash-config.yaml" \
        --namespace="$NAMESPACE"
    
    # Deploy Kibana
    kubectl apply -f "$MONITORING_DIR/elasticsearch/kibana-config.yaml" \
        --namespace="$NAMESPACE"
}

# Deploy AlertManager
deploy_alertmanager() {
    log "Deploying AlertManager..."
    
    kubectl apply -f "$MONITORING_DIR/alerting/alertmanager-config.yaml" \
        --namespace="$NAMESPACE"
    
    # Deploy AlertManager
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alertmanager
  namespace: $NAMESPACE
spec:
  replicas: 3
  selector:
    matchLabels:
      app: alertmanager
  template:
    metadata:
      labels:
        app: alertmanager
    spec:
      containers:
      - name: alertmanager
        image: prom/alertmanager:v0.26.0
        args:
          - '--config.file=/etc/alertmanager/alertmanager.yml'
          - '--storage.path=/alertmanager'
          - '--cluster.advertise-address=0.0.0.0:9093'
        ports:
        - containerPort: 9093
          name: http
        volumeMounts:
        - name: config
          mountPath: /etc/alertmanager
        - name: storage
          mountPath: /alertmanager
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
      volumes:
      - name: config
        configMap:
          name: alertmanager-config
      - name: storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: alertmanager
  namespace: $NAMESPACE
spec:
  ports:
  - port: 9093
    targetPort: 9093
  selector:
    app: alertmanager
EOF
}

# Create Ingress
create_ingress() {
    log "Creating Ingress for monitoring services..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: monitoring-ingress
  namespace: $NAMESPACE
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /\$2
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - monitoring.legacybridge.com
    secretName: monitoring-tls
  rules:
  - host: monitoring.legacybridge.com
    http:
      paths:
      - path: /prometheus(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: prometheus
            port:
              number: 9090
      - path: /grafana(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: grafana
            port:
              number: 3000
      - path: /kibana(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: kibana
            port:
              number: 5601
      - path: /jaeger(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: jaeger-query
            port:
              number: 16686
      - path: /alertmanager(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: alertmanager
            port:
              number: 9093
EOF
}

# Main deployment
main() {
    log "Starting monitoring stack deployment..."
    
    check_prerequisites
    create_namespace
    
    # Deploy components
    deploy_prometheus
    deploy_grafana
    deploy_jaeger
    deploy_elk
    deploy_alertmanager
    create_ingress
    
    # Get service endpoints
    log "Monitoring stack deployed successfully!"
    log ""
    log "Service URLs (configure your DNS or use port-forward):"
    log "  Prometheus: http://monitoring.legacybridge.com/prometheus"
    log "  Grafana: http://monitoring.legacybridge.com/grafana"
    log "  Kibana: http://monitoring.legacybridge.com/kibana"
    log "  Jaeger: http://monitoring.legacybridge.com/jaeger"
    log "  AlertManager: http://monitoring.legacybridge.com/alertmanager"
    log ""
    log "Grafana admin password:"
    kubectl get secret grafana-credentials -n "$NAMESPACE" -o jsonpath='{.data.admin-password}' | base64 -d
    echo ""
}

# Run main function
main "$@"