pub mod cache;
pub mod memory;
pub mod simd;
pub mod metrics;

use std::sync::Arc;
use tokio::sync::RwLock;

pub use cache::{MultiLevelCache, CacheLevel, CacheStats};
pub use memory::{MemoryPoolManager, MemoryStats};
pub use simd::SimdAccelerator;
pub use metrics::{PerformanceMonitor, MetricsSnapshot};

/// Enterprise-grade performance optimization system
pub struct PerformanceOptimizer {
    cache: Arc<RwLock<MultiLevelCache>>,
    memory_pool: Arc<MemoryPoolManager>,
    simd: Arc<SimdAccelerator>,
    monitor: Arc<PerformanceMonitor>,
}

impl PerformanceOptimizer {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let cache = Arc::new(RwLock::new(MultiLevelCache::new()?));
        let memory_pool = Arc::new(MemoryPoolManager::new());
        let simd = Arc::new(SimdAccelerator::new());
        let monitor = Arc::new(PerformanceMonitor::new());

        Ok(Self {
            cache,
            memory_pool,
            simd,
            monitor,
        })
    }

    pub fn cache(&self) -> Arc<RwLock<MultiLevelCache>> {
        Arc::clone(&self.cache)
    }

    pub fn memory_pool(&self) -> Arc<MemoryPoolManager> {
        Arc::clone(&self.memory_pool)
    }

    pub fn simd(&self) -> Arc<SimdAccelerator> {
        Arc::clone(&self.simd)
    }

    pub fn monitor(&self) -> Arc<PerformanceMonitor> {
        Arc::clone(&self.monitor)
    }

    pub async fn optimize_operation<F, T>(&self, operation_name: &str, operation: F) -> Result<T, Box<dyn std::error::Error>>
    where
        F: FnOnce() -> Result<T, Box<dyn std::error::Error>>,
    {
        let start = std::time::Instant::now();
        
        // Track operation
        self.monitor.track_operation(operation_name);
        
        // Execute with monitoring
        let result = operation();
        
        // Record duration
        self.monitor.record_operation_duration(operation_name, start.elapsed());
        
        result
    }

    pub async fn get_health_status(&self) -> serde_json::Value {
        let metrics = self.monitor.get_current_metrics();
        let cache_stats = self.cache.read().await.get_stats();
        let memory_stats = self.memory_pool.get_stats();

        serde_json::json!({
            "status": "healthy",
            "performance": {
                "cpu_usage": metrics.cpu_usage,
                "memory_usage_mb": metrics.memory_usage_mb,
                "operations_per_second": metrics.operations_per_second,
                "average_latency_ms": metrics.average_latency_ms,
            },
            "cache": {
                "total_requests": cache_stats.total_requests,
                "cache_hits": cache_stats.cache_hits,
                "hit_rate": cache_stats.cache_hit_rate,
            },
            "memory_pool": {
                "total_allocations": memory_stats.total_allocations,
                "pool_hits": memory_stats.pool_hits,
                "active_objects": memory_stats.active_objects,
            }
        })
    }

    pub async fn cleanup(&self) -> Result<(), Box<dyn std::error::Error>> {
        // Clean up cache
        self.cache.write().await.cleanup().await?;
        
        // Clean up memory pools
        self.memory_pool.cleanup();
        
        Ok(())
    }
}

impl Default for PerformanceOptimizer {
    fn default() -> Self {
        Self::new().expect("Failed to create default PerformanceOptimizer")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_performance_optimizer_creation() {
        let optimizer = PerformanceOptimizer::new().unwrap();
        assert!(optimizer.cache.read().await.get_stats().total_requests == 0);
    }

    #[tokio::test]
    async fn test_optimize_operation() {
        let optimizer = PerformanceOptimizer::new().unwrap();
        
        let result = optimizer.optimize_operation("test_op", || {
            Ok::<i32, Box<dyn std::error::Error>>(42)
        }).await.unwrap();
        
        assert_eq!(result, 42);
        
        let metrics = optimizer.monitor.get_current_metrics();
        assert!(metrics.operations_per_second > 0.0);
    }

    #[tokio::test]
    async fn test_health_status() {
        let optimizer = PerformanceOptimizer::new().unwrap();
        let health = optimizer.get_health_status().await;
        
        assert_eq!(health["status"], "healthy");
        assert!(health["performance"].is_object());
        assert!(health["cache"].is_object());
        assert!(health["memory_pool"].is_object());
    }
}