// Job Tracking and Progress Monitoring for LegacyBridge MCP Server
// Provides comprehensive job management and real-time progress updates

use std::sync::Arc;
use tokio::sync::{RwLock, mpsc};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use serde_json::{json, Value as JsonValue};
use futures::stream::{self, StreamExt};
use crate::mcp::types::{ConversionJob, JobStatus, ConversionResult, IntegrationError};
use crate::mcp::websocket::{WebSocketServer, ProgressUpdate, UpdateType};
use crate::conversion::{ConversionEngine, ConversionOptions};
use crate::formats::FormatDetector;

/// Job tracker for managing conversion jobs
pub struct JobTracker {
    /// Active jobs storage
    jobs: Arc<RwLock<HashMap<String, ConversionJob>>>,
    
    /// Job history storage (limited to last 1000 jobs)
    history: Arc<RwLock<Vec<ConversionJob>>>,
    
    /// Progress update channel
    progress_tx: mpsc::Sender<JobProgressEvent>,
    
    /// WebSocket server for real-time updates
    websocket_server: Option<Arc<WebSocketServer>>,
    
    /// Maximum number of historical jobs to keep
    max_history: usize,
}

/// Job progress event
#[derive(Debug, Clone)]
pub struct JobProgressEvent {
    pub job_id: String,
    pub event_type: JobEventType,
    pub timestamp: DateTime<Utc>,
    pub details: JobEventDetails,
}

#[derive(Debug, Clone)]
pub enum JobEventType {
    Created,
    Started,
    Progress,
    FileStarted,
    FileCompleted,
    FileFailed,
    Completed,
    Failed,
    Cancelled,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JobEventDetails {
    pub message: String,
    pub current_file: Option<String>,
    pub progress_percentage: f64,
    pub files_processed: usize,
    pub files_remaining: usize,
    pub error: Option<String>,
    pub metadata: Option<JsonValue>,
}

impl JobTracker {
    /// Create a new job tracker
    pub fn new(websocket_server: Option<Arc<WebSocketServer>>) -> Self {
        let (progress_tx, mut progress_rx) = mpsc::channel::<JobProgressEvent>(1000);
        
        let tracker = Self {
            jobs: Arc::new(RwLock::new(HashMap::new())),
            history: Arc::new(RwLock::new(Vec::new())),
            progress_tx,
            websocket_server,
            max_history: 1000,
        };
        
        // Start progress event handler
        let jobs = tracker.jobs.clone();
        let history = tracker.history.clone();
        let ws_server = tracker.websocket_server.clone();
        
        tokio::spawn(async move {
            while let Some(event) = progress_rx.recv().await {
                Self::handle_progress_event(
                    event,
                    &jobs,
                    &history,
                    &ws_server,
                ).await;
            }
        });
        
        tracker
    }
    
    /// Create a new job
    pub async fn create_job(
        &self,
        job_type: &str,
        total_files: usize,
        metadata: Option<JsonValue>,
    ) -> Result<String, IntegrationError> {
        let job_id = Uuid::new_v4().to_string();
        
        let job = ConversionJob {
            id: job_id.clone(),
            job_type: job_type.to_string(),
            status: JobStatus::Queued,
            total_files,
            processed_files: 0,
            successful_conversions: 0,
            failed_conversions: 0,
            start_time: Utc::now(),
            end_time: None,
            results: Vec::new(),
            metadata,
        };
        
        // Store job
        self.jobs.write().await.insert(job_id.clone(), job);
        
        // Send creation event
        self.send_event(JobProgressEvent {
            job_id: job_id.clone(),
            event_type: JobEventType::Created,
            timestamp: Utc::now(),
            details: JobEventDetails {
                message: format!("Job created: {} files to process", total_files),
                current_file: None,
                progress_percentage: 0.0,
                files_processed: 0,
                files_remaining: total_files,
                error: None,
                metadata: None,
            },
        }).await;
        
        Ok(job_id)
    }
    
    /// Start a job
    pub async fn start_job(&self, job_id: &str) -> Result<(), IntegrationError> {
        let mut jobs = self.jobs.write().await;
        
        if let Some(job) = jobs.get_mut(job_id) {
            job.status = JobStatus::Processing;
            job.start_time = Utc::now();
            
            self.send_event(JobProgressEvent {
                job_id: job_id.to_string(),
                event_type: JobEventType::Started,
                timestamp: Utc::now(),
                details: JobEventDetails {
                    message: "Job started processing".to_string(),
                    current_file: None,
                    progress_percentage: 0.0,
                    files_processed: job.processed_files,
                    files_remaining: job.total_files - job.processed_files,
                    error: None,
                    metadata: None,
                },
            }).await;
            
            Ok(())
        } else {
            Err(IntegrationError::InvalidInput(format!("Job {} not found", job_id)))
        }
    }
    
    /// Update job progress
    pub async fn update_progress(
        &self,
        job_id: &str,
        current_file: Option<String>,
        files_processed: usize,
        successful: usize,
        failed: usize,
    ) -> Result<(), IntegrationError> {
        let mut jobs = self.jobs.write().await;
        
        if let Some(job) = jobs.get_mut(job_id) {
            job.processed_files = files_processed;
            job.successful_conversions = successful;
            job.failed_conversions = failed;
            
            let progress_percentage = (files_processed as f64 / job.total_files as f64) * 100.0;
            
            self.send_event(JobProgressEvent {
                job_id: job_id.to_string(),
                event_type: JobEventType::Progress,
                timestamp: Utc::now(),
                details: JobEventDetails {
                    message: format!("Processing: {:.1}% complete", progress_percentage),
                    current_file,
                    progress_percentage,
                    files_processed,
                    files_remaining: job.total_files - files_processed,
                    error: None,
                    metadata: None,
                },
            }).await;
            
            Ok(())
        } else {
            Err(IntegrationError::InvalidInput(format!("Job {} not found", job_id)))
        }
    }
    
    /// Record file start
    pub async fn file_started(&self, job_id: &str, file_name: &str) -> Result<(), IntegrationError> {
        self.send_event(JobProgressEvent {
            job_id: job_id.to_string(),
            event_type: JobEventType::FileStarted,
            timestamp: Utc::now(),
            details: JobEventDetails {
                message: format!("Started processing: {}", file_name),
                current_file: Some(file_name.to_string()),
                progress_percentage: 0.0,
                files_processed: 0,
                files_remaining: 0,
                error: None,
                metadata: None,
            },
        }).await;
        
        Ok(())
    }
    
    /// Record file completion
    pub async fn file_completed(
        &self,
        job_id: &str,
        file_name: &str,
        result: ConversionResult,
    ) -> Result<(), IntegrationError> {
        let mut jobs = self.jobs.write().await;
        
        if let Some(job) = jobs.get_mut(job_id) {
            job.results.push(result.clone());
            
            self.send_event(JobProgressEvent {
                job_id: job_id.to_string(),
                event_type: JobEventType::FileCompleted,
                timestamp: Utc::now(),
                details: JobEventDetails {
                    message: format!("Completed: {}", file_name),
                    current_file: Some(file_name.to_string()),
                    progress_percentage: 0.0,
                    files_processed: 0,
                    files_remaining: 0,
                    error: None,
                    metadata: Some(json!({
                        "output_size": result.output_size,
                        "conversion_time_ms": result.conversion_time_ms,
                        "quality_score": result.quality_score,
                    })),
                },
            }).await;
            
            Ok(())
        } else {
            Err(IntegrationError::InvalidInput(format!("Job {} not found", job_id)))
        }
    }
    
    /// Record file failure
    pub async fn file_failed(
        &self,
        job_id: &str,
        file_name: &str,
        error: &str,
    ) -> Result<(), IntegrationError> {
        self.send_event(JobProgressEvent {
            job_id: job_id.to_string(),
            event_type: JobEventType::FileFailed,
            timestamp: Utc::now(),
            details: JobEventDetails {
                message: format!("Failed: {}", file_name),
                current_file: Some(file_name.to_string()),
                progress_percentage: 0.0,
                files_processed: 0,
                files_remaining: 0,
                error: Some(error.to_string()),
                metadata: None,
            },
        }).await;
        
        Ok(())
    }
    
    /// Complete a job
    pub async fn complete_job(&self, job_id: &str) -> Result<(), IntegrationError> {
        let mut jobs = self.jobs.write().await;
        
        if let Some(mut job) = jobs.remove(job_id) {
            job.status = JobStatus::Completed;
            job.end_time = Some(Utc::now());
            
            let duration = job.end_time.unwrap().signed_duration_since(job.start_time);
            
            self.send_event(JobProgressEvent {
                job_id: job_id.to_string(),
                event_type: JobEventType::Completed,
                timestamp: Utc::now(),
                details: JobEventDetails {
                    message: format!(
                        "Job completed: {} successful, {} failed in {}s",
                        job.successful_conversions,
                        job.failed_conversions,
                        duration.num_seconds()
                    ),
                    current_file: None,
                    progress_percentage: 100.0,
                    files_processed: job.processed_files,
                    files_remaining: 0,
                    error: None,
                    metadata: Some(json!({
                        "duration_seconds": duration.num_seconds(),
                        "success_rate": (job.successful_conversions as f64 / job.total_files as f64) * 100.0,
                    })),
                },
            }).await;
            
            // Add to history
            self.add_to_history(job).await;
            
            Ok(())
        } else {
            Err(IntegrationError::InvalidInput(format!("Job {} not found", job_id)))
        }
    }
    
    /// Fail a job
    pub async fn fail_job(&self, job_id: &str, error: &str) -> Result<(), IntegrationError> {
        let mut jobs = self.jobs.write().await;
        
        if let Some(mut job) = jobs.remove(job_id) {
            job.status = JobStatus::Failed;
            job.end_time = Some(Utc::now());
            
            self.send_event(JobProgressEvent {
                job_id: job_id.to_string(),
                event_type: JobEventType::Failed,
                timestamp: Utc::now(),
                details: JobEventDetails {
                    message: format!("Job failed: {}", error),
                    current_file: None,
                    progress_percentage: (job.processed_files as f64 / job.total_files as f64) * 100.0,
                    files_processed: job.processed_files,
                    files_remaining: job.total_files - job.processed_files,
                    error: Some(error.to_string()),
                    metadata: None,
                },
            }).await;
            
            // Add to history
            self.add_to_history(job).await;
            
            Ok(())
        } else {
            Err(IntegrationError::InvalidInput(format!("Job {} not found", job_id)))
        }
    }
    
    /// Get job status
    pub async fn get_job(&self, job_id: &str) -> Option<ConversionJob> {
        self.jobs.read().await.get(job_id).cloned()
    }
    
    /// Get all active jobs
    pub async fn get_active_jobs(&self) -> Vec<ConversionJob> {
        self.jobs.read().await.values().cloned().collect()
    }
    
    /// Get job history
    pub async fn get_history(&self, limit: usize) -> Vec<ConversionJob> {
        let history = self.history.read().await;
        let start = if history.len() > limit {
            history.len() - limit
        } else {
            0
        };
        history[start..].to_vec()
    }
    
    /// Send progress event
    async fn send_event(&self, event: JobProgressEvent) {
        let _ = self.progress_tx.send(event).await;
    }
    
    /// Handle progress event
    async fn handle_progress_event(
        event: JobProgressEvent,
        jobs: &Arc<RwLock<HashMap<String, ConversionJob>>>,
        history: &Arc<RwLock<Vec<ConversionJob>>>,
        ws_server: &Option<Arc<WebSocketServer>>,
    ) {
        // Send WebSocket update if server is available
        if let Some(ws) = ws_server {
            let update_type = match event.event_type {
                JobEventType::Created | JobEventType::Started => UpdateType::JobStarted,
                JobEventType::Progress => UpdateType::JobProgress,
                JobEventType::FileStarted => UpdateType::FileStarted,
                JobEventType::FileCompleted => UpdateType::FileCompleted,
                JobEventType::FileFailed => UpdateType::FileFailed,
                JobEventType::Completed => UpdateType::JobCompleted,
                JobEventType::Failed | JobEventType::Cancelled => UpdateType::JobFailed,
            };
            
            let update = ProgressUpdate {
                job_id: event.job_id,
                update_type,
                timestamp: event.timestamp,
                data: json!({
                    "message": event.details.message,
                    "current_file": event.details.current_file,
                    "progress_percentage": event.details.progress_percentage,
                    "files_processed": event.details.files_processed,
                    "files_remaining": event.details.files_remaining,
                    "error": event.details.error,
                    "metadata": event.details.metadata,
                }),
            };
            
            ws.send_progress_update(update).await;
        }
    }
    
    /// Add job to history
    async fn add_to_history(&self, job: ConversionJob) {
        let mut history = self.history.write().await;
        history.push(job);
        
        // Limit history size
        if history.len() > self.max_history {
            let remove_count = history.len() - self.max_history;
            history.drain(0..remove_count);
        }
    }
}

// Extension methods for the MCP server
impl crate::mcp::server::LegacyBridgeMcpServer {
    /// Process a batch conversion job with progress tracking
    pub async fn process_batch_job(
        &self,
        job_id: String,
        files: Vec<JsonValue>,
        output_format: String,
        parallel_jobs: usize,
        continue_on_error: bool,
    ) -> Result<(), IntegrationError> {
        // Get job tracker (would need to add to server struct)
        // let tracker = &self.job_tracker;
        
        // Start the job
        // tracker.start_job(&job_id).await?;
        
        // Process files in parallel batches
        let semaphore = Arc::new(tokio::sync::Semaphore::new(parallel_jobs));
        let mut tasks = Vec::new();
        
        for (index, file) in files.iter().enumerate() {
            let file_content = file["content"].as_str()
                .ok_or_else(|| IntegrationError::InvalidInput("Missing file content".to_string()))?;
            let file_name = file["filename"].as_str()
                .ok_or_else(|| IntegrationError::InvalidInput("Missing filename".to_string()))?;
            
            let job_id_clone = job_id.clone();
            let output_format_clone = output_format.clone();
            let conversion_engine = self.conversion_engine.clone();
            let format_detector = self.format_detector.clone();
            let semaphore_clone = semaphore.clone();
            
            let task = tokio::spawn(async move {
                // Acquire semaphore permit
                let _permit = semaphore_clone.acquire().await.unwrap();
                
                // Process file
                let result = Self::process_single_file(
                    &conversion_engine,
                    &format_detector,
                    file_content,
                    file_name,
                    &output_format_clone,
                ).await;
                
                (index, file_name.to_string(), result)
            });
            
            tasks.push(task);
        }
        
        // Collect results
        let mut successful = 0;
        let mut failed = 0;
        
        for task in tasks {
            match task.await {
                Ok((index, file_name, result)) => {
                    match result {
                        Ok(conversion_result) => {
                            successful += 1;
                            // tracker.file_completed(&job_id, &file_name, conversion_result).await?;
                        }
                        Err(e) => {
                            failed += 1;
                            // tracker.file_failed(&job_id, &file_name, &e.to_string()).await?;
                            
                            if !continue_on_error {
                                // tracker.fail_job(&job_id, &e.to_string()).await?;
                                return Err(e);
                            }
                        }
                    }
                    
                    // Update progress
                    let processed = successful + failed;
                    // tracker.update_progress(&job_id, None, processed, successful, failed).await?;
                }
                Err(e) => {
                    failed += 1;
                    if !continue_on_error {
                        // tracker.fail_job(&job_id, &e.to_string()).await?;
                        return Err(IntegrationError::InternalError(e.to_string()));
                    }
                }
            }
        }
        
        // Complete the job
        // tracker.complete_job(&job_id).await?;
        
        Ok(())
    }
    
    /// Process a single file
    async fn process_single_file(
        conversion_engine: &Arc<ConversionEngine>,
        format_detector: &Arc<FormatDetector>,
        file_content: &str,
        file_name: &str,
        output_format: &str,
    ) -> Result<ConversionResult, IntegrationError> {
        let start_time = std::time::Instant::now();
        
        // Decode content
        let content = base64::decode(file_content)
            .map_err(|e| IntegrationError::InvalidInput(format!("Base64 decode error: {}", e)))?;
        
        // Detect format
        let detection = format_detector.detect_format_from_bytes(&content)
            .map_err(|e| IntegrationError::InternalError(format!("Format detection failed: {}", e)))?;
        
        // Convert
        let options = ConversionOptions::default();
        let converted = conversion_engine.convert(
            &content,
            &detection.format.id,
            output_format,
            &options,
        ).await
        .map_err(|e| IntegrationError::InternalError(format!("Conversion failed: {}", e)))?;
        
        let duration = start_time.elapsed();
        
        Ok(ConversionResult {
            filename: file_name.to_string(),
            input_format: detection.format.id,
            output_format: output_format.to_string(),
            success: true,
            error: None,
            output_content: Some(base64::encode(&converted)),
            input_size: content.len(),
            output_size: converted.len(),
            conversion_time_ms: duration.as_millis() as u64,
            quality_score: Some(8), // Would calculate based on conversion
        })
    }
}