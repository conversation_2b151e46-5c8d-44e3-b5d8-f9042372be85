use super::{<PERSON>ll<PERSON><PERSON>r, DllR<PERSON>ult};
use std::path::{Path, PathBuf};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct GeneratorConfig {
    pub dll_path: PathBuf,
    pub output_dir: PathBuf,
    pub language: TargetLanguage,
    pub include_examples: bool,
    pub include_error_handling: bool,
    pub include_documentation: bool,
    pub template_path: Option<PathBuf>,
    pub namespace: Option<String>,
    pub class_name: Option<String>,
}

#[derive(Debug, <PERSON>lone, Copy, Serialize, Deserialize)]
pub enum TargetLanguage {
    Vb6,
    Vfp9,
    C,
    Cpp,
    Python,
    Csharp,
    Javascript,
    Typescript,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GeneratorResult {
    pub generated_files: Vec<GeneratedFile>,
    pub total_functions: usize,
    pub total_constants: usize,
    pub total_types: usize,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GeneratedFile {
    pub path: <PERSON><PERSON>uf,
    pub language: TargetLanguage,
    pub file_type: FileType,
    pub size: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FileType {
    SourceCode,
    Header,
    Documentation,
    Example,
    TypeDefinition,
}

pub struct CodeGenerator {
    config: GeneratorConfig,
    dll_info: DllInfo,
}

#[derive(Debug, Clone)]
struct DllInfo {
    pub exports: Vec<ExportFunction>,
    pub constants: Vec<Constant>,
    pub types: Vec<TypeDef>,
}

#[derive(Debug, Clone)]
struct ExportFunction {
    pub name: String,
    pub ordinal: Option<u32>,
    pub calling_convention: CallingConvention,
    pub return_type: String,
    pub parameters: Vec<Parameter>,
}

#[derive(Debug, Clone)]
struct Parameter {
    pub name: String,
    pub param_type: String,
    pub is_pointer: bool,
    pub is_out: bool,
}

#[derive(Debug, Clone, Copy)]
enum CallingConvention {
    Stdcall,
    Cdecl,
    Fastcall,
}

#[derive(Debug, Clone)]
struct Constant {
    pub name: String,
    pub value: String,
    pub const_type: String,
}

#[derive(Debug, Clone)]
struct TypeDef {
    pub name: String,
    pub definition: String,
}

impl CodeGenerator {
    pub fn new(config: GeneratorConfig) -> DllResult<Self> {
        let dll_info = Self::analyze_dll(&config.dll_path)?;
        
        Ok(Self {
            config,
            dll_info,
        })
    }
    
    pub async fn generate(&self) -> DllResult<GeneratorResult> {
        println!("🔧 Generating {} integration code...", format_language(self.config.language));
        
        // Create output directory
        std::fs::create_dir_all(&self.config.output_dir)?;
        
        let mut generated_files = Vec::new();
        
        // Generate based on language
        match self.config.language {
            TargetLanguage::Vb6 => generated_files.extend(self.generate_vb6().await?),
            TargetLanguage::Vfp9 => generated_files.extend(self.generate_vfp9().await?),
            TargetLanguage::C => generated_files.extend(self.generate_c().await?),
            TargetLanguage::Cpp => generated_files.extend(self.generate_cpp().await?),
            TargetLanguage::Python => generated_files.extend(self.generate_python().await?),
            TargetLanguage::Csharp => generated_files.extend(self.generate_csharp().await?),
            TargetLanguage::Javascript => generated_files.extend(self.generate_javascript().await?),
            TargetLanguage::Typescript => generated_files.extend(self.generate_typescript().await?),
        }
        
        Ok(GeneratorResult {
            generated_files,
            total_functions: self.dll_info.exports.len(),
            total_constants: self.dll_info.constants.len(),
            total_types: self.dll_info.types.len(),
        })
    }
    
    fn analyze_dll(dll_path: &Path) -> DllResult<DllInfo> {
        // For now, return mock data
        // In a real implementation, this would parse the DLL exports
        Ok(DllInfo {
            exports: vec![
                ExportFunction {
                    name: "Initialize".to_string(),
                    ordinal: Some(1),
                    calling_convention: CallingConvention::Stdcall,
                    return_type: "int".to_string(),
                    parameters: vec![],
                },
                ExportFunction {
                    name: "GetVersion".to_string(),
                    ordinal: Some(2),
                    calling_convention: CallingConvention::Stdcall,
                    return_type: "int".to_string(),
                    parameters: vec![],
                },
                ExportFunction {
                    name: "ConvertDocument".to_string(),
                    ordinal: Some(3),
                    calling_convention: CallingConvention::Stdcall,
                    return_type: "int".to_string(),
                    parameters: vec![
                        Parameter {
                            name: "inputPath".to_string(),
                            param_type: "const char*".to_string(),
                            is_pointer: true,
                            is_out: false,
                        },
                        Parameter {
                            name: "outputPath".to_string(),
                            param_type: "const char*".to_string(),
                            is_pointer: true,
                            is_out: false,
                        },
                        Parameter {
                            name: "format".to_string(),
                            param_type: "const char*".to_string(),
                            is_pointer: true,
                            is_out: false,
                        },
                    ],
                },
                ExportFunction {
                    name: "GetLastError".to_string(),
                    ordinal: Some(4),
                    calling_convention: CallingConvention::Stdcall,
                    return_type: "int".to_string(),
                    parameters: vec![],
                },
                ExportFunction {
                    name: "Cleanup".to_string(),
                    ordinal: Some(5),
                    calling_convention: CallingConvention::Stdcall,
                    return_type: "int".to_string(),
                    parameters: vec![],
                },
            ],
            constants: vec![
                Constant {
                    name: "SUCCESS".to_string(),
                    value: "0".to_string(),
                    const_type: "int".to_string(),
                },
                Constant {
                    name: "ERROR_INVALID_PARAMETER".to_string(),
                    value: "-1".to_string(),
                    const_type: "int".to_string(),
                },
                Constant {
                    name: "ERROR_FILE_NOT_FOUND".to_string(),
                    value: "-2".to_string(),
                    const_type: "int".to_string(),
                },
            ],
            types: vec![],
        })
    }
    
    async fn generate_vb6(&self) -> DllResult<Vec<GeneratedFile>> {
        let mut files = Vec::new();
        
        // Generate module file
        let module_name = self.config.class_name.as_ref()
            .unwrap_or(&"LegacyBridge".to_string());
        
        let mut content = String::new();
        content.push_str(&format!("Attribute VB_Name = \"{}\"\n", module_name));
        content.push_str("Option Explicit\n\n");
        
        // Add constants
        if !self.dll_info.constants.is_empty() {
            content.push_str("' Constants\n");
            for constant in &self.dll_info.constants {
                content.push_str(&format!("Public Const {} As Long = {}\n", 
                    constant.name, constant.value));
            }
            content.push_str("\n");
        }
        
        // Add function declarations
        content.push_str("' DLL Function Declarations\n");
        let dll_name = self.config.dll_path.file_name().unwrap().to_string_lossy();
        
        for func in &self.dll_info.exports {
            content.push_str(&self.generate_vb6_declare(func, &dll_name));
            content.push_str("\n");
        }
        
        // Add wrapper functions if error handling is enabled
        if self.config.include_error_handling {
            content.push_str("\n' Wrapper Functions with Error Handling\n");
            for func in &self.dll_info.exports {
                content.push_str(&self.generate_vb6_wrapper(func));
                content.push_str("\n");
            }
        }
        
        // Add examples if requested
        if self.config.include_examples {
            content.push_str("\n' Example Usage\n");
            content.push_str(&self.generate_vb6_examples());
        }
        
        let module_path = self.config.output_dir.join(format!("{}.bas", module_name));
        std::fs::write(&module_path, content)?;
        
        files.push(GeneratedFile {
            path: module_path.clone(),
            language: TargetLanguage::Vb6,
            file_type: FileType::SourceCode,
            size: std::fs::metadata(&module_path)?.len(),
        });
        
        // Generate documentation if requested
        if self.config.include_documentation {
            let doc_content = self.generate_vb6_documentation();
            let doc_path = self.config.output_dir.join(format!("{}_README.txt", module_name));
            std::fs::write(&doc_path, doc_content)?;
            
            files.push(GeneratedFile {
                path: doc_path.clone(),
                language: TargetLanguage::Vb6,
                file_type: FileType::Documentation,
                size: std::fs::metadata(&doc_path)?.len(),
            });
        }
        
        Ok(files)
    }
    
    fn generate_vb6_declare(&self, func: &ExportFunction, dll_name: &str) -> String {
        let mut declare = format!("Private Declare Function {} Lib \"{}\"", func.name, dll_name);
        
        if let Some(ordinal) = func.ordinal {
            declare.push_str(&format!(" Alias \"#{}\"", ordinal));
        }
        
        if !func.parameters.is_empty() {
            declare.push_str(" _\n    (");
            let params: Vec<String> = func.parameters.iter().map(|p| {
                let param_type = self.vb6_type(&p.param_type);
                if p.is_out {
                    format!("ByRef {} As {}", p.name, param_type)
                } else {
                    format!("ByVal {} As {}", p.name, param_type)
                }
            }).collect();
            declare.push_str(&params.join(", _\n     "));
            declare.push_str(")");
        } else {
            declare.push_str(" ()");
        }
        
        declare.push_str(&format!(" As {}", self.vb6_type(&func.return_type)));
        declare
    }
    
    fn generate_vb6_wrapper(&self, func: &ExportFunction) -> String {
        let mut wrapper = String::new();
        
        wrapper.push_str(&format!("Public Function {}Safe", func.name));
        
        if !func.parameters.is_empty() {
            wrapper.push_str("(");
            let params: Vec<String> = func.parameters.iter().map(|p| {
                let param_type = self.vb6_type(&p.param_type);
                format!("{} As {}", p.name, param_type)
            }).collect();
            wrapper.push_str(&params.join(", "));
            wrapper.push_str(")");
        } else {
            wrapper.push_str("()");
        }
        
        wrapper.push_str(&format!(" As {}\n", self.vb6_type(&func.return_type)));
        wrapper.push_str("    On Error GoTo ErrorHandler\n");
        wrapper.push_str(&format!("    {}Safe = {}", func.name, func.name));
        
        if !func.parameters.is_empty() {
            wrapper.push_str("(");
            let param_names: Vec<&str> = func.parameters.iter().map(|p| p.name.as_str()).collect();
            wrapper.push_str(&param_names.join(", "));
            wrapper.push_str(")");
        } else {
            wrapper.push_str("()");
        }
        
        wrapper.push_str("\n");
        wrapper.push_str("    Exit Function\n");
        wrapper.push_str("ErrorHandler:\n");
        wrapper.push_str("    ' Handle error appropriately\n");
        wrapper.push_str(&format!("    {}Safe = ERROR_INVALID_PARAMETER\n", func.name));
        wrapper.push_str("    Err.Raise Err.Number, Err.Source, Err.Description\n");
        wrapper.push_str("End Function");
        
        wrapper
    }
    
    fn generate_vb6_examples(&self) -> String {
        let mut examples = String::new();
        
        examples.push_str("Public Sub ExampleUsage()\n");
        examples.push_str("    Dim result As Long\n");
        examples.push_str("    \n");
        examples.push_str("    ' Initialize the library\n");
        examples.push_str("    result = Initialize()\n");
        examples.push_str("    If result <> SUCCESS Then\n");
        examples.push_str("        MsgBox \"Failed to initialize: \" & result\n");
        examples.push_str("        Exit Sub\n");
        examples.push_str("    End If\n");
        examples.push_str("    \n");
        examples.push_str("    ' Convert a document\n");
        examples.push_str("    result = ConvertDocument(\"C:\\input.doc\", \"C:\\output.md\", \"markdown\")\n");
        examples.push_str("    If result = SUCCESS Then\n");
        examples.push_str("        MsgBox \"Conversion successful!\"\n");
        examples.push_str("    Else\n");
        examples.push_str("        MsgBox \"Conversion failed: \" & result\n");
        examples.push_str("    End If\n");
        examples.push_str("    \n");
        examples.push_str("    ' Cleanup\n");
        examples.push_str("    Call Cleanup\n");
        examples.push_str("End Sub\n");
        
        examples
    }
    
    fn generate_vb6_documentation(&self) -> String {
        let mut doc = String::new();
        
        doc.push_str("LegacyBridge DLL Integration for Visual Basic 6\n");
        doc.push_str("==============================================\n\n");
        
        doc.push_str("Installation:\n");
        doc.push_str("1. Copy the DLL to your application directory or System32\n");
        doc.push_str("2. Add the .bas module to your VB6 project\n");
        doc.push_str("3. Ensure any runtime dependencies are installed\n\n");
        
        doc.push_str("Functions:\n");
        doc.push_str("----------\n");
        
        for func in &self.dll_info.exports {
            doc.push_str(&format!("\n{}\n", func.name));
            doc.push_str(&format!("  Returns: {}\n", self.vb6_type(&func.return_type)));
            if !func.parameters.is_empty() {
                doc.push_str("  Parameters:\n");
                for param in &func.parameters {
                    doc.push_str(&format!("    - {} As {} {}\n", 
                        param.name, 
                        self.vb6_type(&param.param_type),
                        if param.is_out { "(Output)" } else { "" }
                    ));
                }
            }
        }
        
        doc.push_str("\nConstants:\n");
        doc.push_str("----------\n");
        for constant in &self.dll_info.constants {
            doc.push_str(&format!("{} = {}\n", constant.name, constant.value));
        }
        
        doc.push_str("\nError Handling:\n");
        doc.push_str("---------------\n");
        doc.push_str("Use the Safe wrapper functions for automatic error handling,\n");
        doc.push_str("or check return values against the defined constants.\n");
        
        doc
    }
    
    fn vb6_type(&self, c_type: &str) -> &'static str {
        match c_type {
            "int" | "long" | "LONG" => "Long",
            "unsigned int" | "DWORD" => "Long",
            "short" | "SHORT" => "Integer",
            "char" | "CHAR" => "Byte",
            "const char*" | "char*" | "LPSTR" | "LPCSTR" => "String",
            "void*" | "LPVOID" => "Long",
            "bool" | "BOOL" => "Boolean",
            "float" => "Single",
            "double" => "Double",
            _ => "Long", // Default to Long for unknown types
        }
    }
    
    async fn generate_vfp9(&self) -> DllResult<Vec<GeneratedFile>> {
        let mut files = Vec::new();
        
        // Generate PRG file
        let prg_name = self.config.class_name.as_ref()
            .unwrap_or(&"LegacyBridge".to_string());
        
        let mut content = String::new();
        content.push_str("* LegacyBridge DLL Integration for Visual FoxPro 9\n");
        content.push_str("* Generated by LegacyBridge CLI\n\n");
        
        // Add constants
        if !self.dll_info.constants.is_empty() {
            content.push_str("* Constants\n");
            for constant in &self.dll_info.constants {
                content.push_str(&format!("#DEFINE {} {}\n", constant.name, constant.value));
            }
            content.push_str("\n");
        }
        
        // Add function declarations
        content.push_str("* DLL Function Declarations\n");
        let dll_name = self.config.dll_path.file_stem().unwrap().to_string_lossy();
        
        for func in &self.dll_info.exports {
            content.push_str(&self.generate_vfp9_declare(func, &dll_name));
            content.push_str("\n");
        }
        
        // Add wrapper class if error handling is enabled
        if self.config.include_error_handling {
            content.push_str(&self.generate_vfp9_class(&prg_name));
        }
        
        // Add examples if requested
        if self.config.include_examples {
            content.push_str("\n* Example Usage\n");
            content.push_str(&self.generate_vfp9_examples());
        }
        
        let prg_path = self.config.output_dir.join(format!("{}.prg", prg_name));
        std::fs::write(&prg_path, content)?;
        
        files.push(GeneratedFile {
            path: prg_path.clone(),
            language: TargetLanguage::Vfp9,
            file_type: FileType::SourceCode,
            size: std::fs::metadata(&prg_path)?.len(),
        });
        
        Ok(files)
    }
    
    fn generate_vfp9_declare(&self, func: &ExportFunction, dll_name: &str) -> String {
        let mut declare = String::new();
        
        declare.push_str(&format!("DECLARE {} {} IN {}.dll", 
            self.vfp9_type(&func.return_type),
            func.name,
            dll_name
        ));
        
        if !func.parameters.is_empty() {
            declare.push_str(" ;");
            for (i, param) in func.parameters.iter().enumerate() {
                if i > 0 {
                    declare.push_str(",");
                }
                declare.push_str(&format!("\n    {} {}", 
                    self.vfp9_type(&param.param_type),
                    if param.is_out { "@" } else { "" }
                ));
                declare.push_str(&param.name);
            }
        }
        
        declare
    }
    
    fn generate_vfp9_class(&self, class_name: &str) -> String {
        let mut class_def = String::new();
        
        class_def.push_str(&format!("\nDEFINE CLASS {} AS Custom\n\n", class_name));
        
        // Add properties
        class_def.push_str("    * Properties\n");
        class_def.push_str("    lInitialized = .F.\n");
        class_def.push_str("    nLastError = 0\n\n");
        
        // Add methods
        for func in &self.dll_info.exports {
            class_def.push_str(&self.generate_vfp9_method(func));
            class_def.push_str("\n");
        }
        
        class_def.push_str("ENDDEFINE\n");
        
        class_def
    }
    
    fn generate_vfp9_method(&self, func: &ExportFunction) -> String {
        let mut method = String::new();
        
        method.push_str(&format!("    PROCEDURE {}\n", func.name));
        
        if !func.parameters.is_empty() {
            method.push_str("        LPARAMETERS ");
            let param_names: Vec<&str> = func.parameters.iter().map(|p| p.name.as_str()).collect();
            method.push_str(&param_names.join(", "));
            method.push_str("\n");
        }
        
        method.push_str("        LOCAL lnResult\n");
        method.push_str("        TRY\n");
        method.push_str(&format!("            lnResult = {}", func.name));
        
        if !func.parameters.is_empty() {
            method.push_str("(");
            let param_names: Vec<&str> = func.parameters.iter().map(|p| p.name.as_str()).collect();
            method.push_str(&param_names.join(", "));
            method.push_str(")");
        } else {
            method.push_str("()");
        }
        
        method.push_str("\n");
        method.push_str("            This.nLastError = 0\n");
        method.push_str("            RETURN lnResult\n");
        method.push_str("        CATCH TO loError\n");
        method.push_str("            This.nLastError = ERROR_INVALID_PARAMETER\n");
        method.push_str("            THROW loError\n");
        method.push_str("        ENDTRY\n");
        method.push_str("    ENDPROC");
        
        method
    }
    
    fn generate_vfp9_examples(&self) -> String {
        let mut examples = String::new();
        
        examples.push_str("PROCEDURE ExampleUsage\n");
        examples.push_str("    LOCAL lnResult\n");
        examples.push_str("    \n");
        examples.push_str("    * Initialize the library\n");
        examples.push_str("    lnResult = Initialize()\n");
        examples.push_str("    IF lnResult != SUCCESS\n");
        examples.push_str("        MESSAGEBOX(\"Failed to initialize: \" + STR(lnResult))\n");
        examples.push_str("        RETURN\n");
        examples.push_str("    ENDIF\n");
        examples.push_str("    \n");
        examples.push_str("    * Convert a document\n");
        examples.push_str("    lnResult = ConvertDocument(\"C:\\input.doc\", \"C:\\output.md\", \"markdown\")\n");
        examples.push_str("    IF lnResult = SUCCESS\n");
        examples.push_str("        MESSAGEBOX(\"Conversion successful!\")\n");
        examples.push_str("    ELSE\n");
        examples.push_str("        MESSAGEBOX(\"Conversion failed: \" + STR(lnResult))\n");
        examples.push_str("    ENDIF\n");
        examples.push_str("    \n");
        examples.push_str("    * Cleanup\n");
        examples.push_str("    = Cleanup()\n");
        examples.push_str("ENDPROC\n");
        
        examples
    }
    
    fn vfp9_type(&self, c_type: &str) -> &'static str {
        match c_type {
            "int" | "long" | "LONG" => "INTEGER",
            "unsigned int" | "DWORD" => "INTEGER",
            "short" | "SHORT" => "SHORT",
            "char" | "CHAR" => "STRING",
            "const char*" | "char*" | "LPSTR" | "LPCSTR" => "STRING",
            "void*" | "LPVOID" => "INTEGER",
            "bool" | "BOOL" => "LOGICAL",
            "float" => "SINGLE",
            "double" => "DOUBLE",
            _ => "INTEGER",
        }
    }
    
    async fn generate_c(&self) -> DllResult<Vec<GeneratedFile>> {
        let mut files = Vec::new();
        
        // Generate header file
        let header_name = format!("{}.h", 
            self.config.class_name.as_ref().unwrap_or(&"legacybridge".to_string()));
        
        let mut header = String::new();
        header.push_str("#ifndef LEGACYBRIDGE_H\n");
        header.push_str("#define LEGACYBRIDGE_H\n\n");
        header.push_str("#ifdef __cplusplus\nextern \"C\" {\n#endif\n\n");
        
        // Add constants
        if !self.dll_info.constants.is_empty() {
            header.push_str("/* Constants */\n");
            for constant in &self.dll_info.constants {
                header.push_str(&format!("#define {} {}\n", constant.name, constant.value));
            }
            header.push_str("\n");
        }
        
        // Add function declarations
        header.push_str("/* Function declarations */\n");
        for func in &self.dll_info.exports {
            header.push_str(&self.generate_c_declaration(func));
            header.push_str(";\n");
        }
        
        header.push_str("\n#ifdef __cplusplus\n}\n#endif\n\n");
        header.push_str("#endif /* LEGACYBRIDGE_H */\n");
        
        let header_path = self.config.output_dir.join(header_name);
        std::fs::write(&header_path, header)?;
        
        files.push(GeneratedFile {
            path: header_path.clone(),
            language: TargetLanguage::C,
            file_type: FileType::Header,
            size: std::fs::metadata(&header_path)?.len(),
        });
        
        // Generate example if requested
        if self.config.include_examples {
            let example = self.generate_c_example();
            let example_path = self.config.output_dir.join("example.c");
            std::fs::write(&example_path, example)?;
            
            files.push(GeneratedFile {
                path: example_path.clone(),
                language: TargetLanguage::C,
                file_type: FileType::Example,
                size: std::fs::metadata(&example_path)?.len(),
            });
        }
        
        Ok(files)
    }
    
    fn generate_c_declaration(&self, func: &ExportFunction) -> String {
        let mut decl = format!("{} {}", func.return_type, func.name);
        
        decl.push('(');
        if func.parameters.is_empty() {
            decl.push_str("void");
        } else {
            let params: Vec<String> = func.parameters.iter().map(|p| {
                format!("{} {}", p.param_type, p.name)
            }).collect();
            decl.push_str(&params.join(", "));
        }
        decl.push(')');
        
        decl
    }
    
    fn generate_c_example(&self) -> String {
        let mut example = String::new();
        
        example.push_str("#include <stdio.h>\n");
        example.push_str("#include <windows.h>\n");
        example.push_str("#include \"legacybridge.h\"\n\n");
        
        example.push_str("int main() {\n");
        example.push_str("    HMODULE hDll;\n");
        example.push_str("    int result;\n\n");
        
        example.push_str("    /* Load the DLL */\n");
        example.push_str("    hDll = LoadLibrary(\"legacybridge.dll\");\n");
        example.push_str("    if (!hDll) {\n");
        example.push_str("        printf(\"Failed to load DLL\\n\");\n");
        example.push_str("        return 1;\n");
        example.push_str("    }\n\n");
        
        example.push_str("    /* Get function pointers and use them */\n");
        example.push_str("    /* ... */\n\n");
        
        example.push_str("    /* Free the DLL */\n");
        example.push_str("    FreeLibrary(hDll);\n");
        example.push_str("    return 0;\n");
        example.push_str("}\n");
        
        example
    }
    
    async fn generate_cpp(&self) -> DllResult<Vec<GeneratedFile>> {
        // Similar to C but with classes
        self.generate_c().await
    }
    
    async fn generate_python(&self) -> DllResult<Vec<GeneratedFile>> {
        let mut files = Vec::new();
        
        // Generate Python wrapper
        let py_name = format!("{}.py", 
            self.config.class_name.as_ref().unwrap_or(&"legacybridge".to_string()));
        
        let mut content = String::new();
        content.push_str("\"\"\"LegacyBridge DLL Python Wrapper\"\"\"\n\n");
        content.push_str("import ctypes\n");
        content.push_str("import os\n");
        content.push_str("from typing import Optional\n\n");
        
        // Add constants
        if !self.dll_info.constants.is_empty() {
            content.push_str("# Constants\n");
            for constant in &self.dll_info.constants {
                content.push_str(&format!("{} = {}\n", constant.name, constant.value));
            }
            content.push_str("\n");
        }
        
        // Create class
        content.push_str("class LegacyBridge:\n");
        content.push_str("    def __init__(self, dll_path: str = \"legacybridge.dll\"):\n");
        content.push_str("        self.dll = ctypes.WinDLL(dll_path)\n");
        content.push_str("        self._setup_functions()\n\n");
        
        content.push_str("    def _setup_functions(self):\n");
        for func in &self.dll_info.exports {
            content.push_str(&self.generate_python_function_setup(func));
        }
        
        // Add wrapper methods
        for func in &self.dll_info.exports {
            content.push_str(&self.generate_python_method(func));
        }
        
        // Add example if requested
        if self.config.include_examples {
            content.push_str(&self.generate_python_example());
        }
        
        let py_path = self.config.output_dir.join(py_name);
        std::fs::write(&py_path, content)?;
        
        files.push(GeneratedFile {
            path: py_path.clone(),
            language: TargetLanguage::Python,
            file_type: FileType::SourceCode,
            size: std::fs::metadata(&py_path)?.len(),
        });
        
        Ok(files)
    }
    
    fn generate_python_function_setup(&self, func: &ExportFunction) -> String {
        let mut setup = String::new();
        
        setup.push_str(&format!("        # {}\n", func.name));
        setup.push_str(&format!("        self.dll.{}.restype = {}\n", 
            func.name, 
            self.python_type(&func.return_type)
        ));
        
        if !func.parameters.is_empty() {
            setup.push_str(&format!("        self.dll.{}.argtypes = [", func.name));
            let arg_types: Vec<String> = func.parameters.iter().map(|p| {
                self.python_type(&p.param_type).to_string()
            }).collect();
            setup.push_str(&arg_types.join(", "));
            setup.push_str("]\n");
        }
        setup.push_str("\n");
        
        setup
    }
    
    fn generate_python_method(&self, func: &ExportFunction) -> String {
        let mut method = String::new();
        
        method.push_str(&format!("\n    def {}(self", func.name.to_lowercase()));
        
        for param in &func.parameters {
            method.push_str(&format!(", {}: {}", 
                param.name, 
                self.python_param_type(&param.param_type)
            ));
        }
        
        method.push_str(") -> int:\n");
        method.push_str(&format!("        \"\"\"Call {} from DLL\"\"\"\n", func.name));
        
        if self.config.include_error_handling {
            method.push_str("        try:\n");
            method.push_str(&format!("            return self.dll.{}", func.name));
        } else {
            method.push_str(&format!("        return self.dll.{}", func.name));
        }
        
        if !func.parameters.is_empty() {
            method.push('(');
            let param_names: Vec<String> = func.parameters.iter().map(|p| {
                if p.param_type.contains("char*") {
                    format!("{}.encode('utf-8')", p.name)
                } else {
                    p.name.clone()
                }
            }).collect();
            method.push_str(&param_names.join(", "));
            method.push(')');
        } else {
            method.push_str("()");
        }
        
        method.push('\n');
        
        if self.config.include_error_handling {
            method.push_str("        except Exception as e:\n");
            method.push_str("            print(f\"Error calling {}: {e}\")\n");
            method.push_str("            return -1\n");
        }
        
        method
    }
    
    fn generate_python_example(&self) -> String {
        let mut example = String::new();
        
        example.push_str("\n\nif __name__ == \"__main__\":\n");
        example.push_str("    # Example usage\n");
        example.push_str("    bridge = LegacyBridge()\n");
        example.push_str("    \n");
        example.push_str("    # Initialize\n");
        example.push_str("    result = bridge.initialize()\n");
        example.push_str("    if result == SUCCESS:\n");
        example.push_str("        print(\"Initialized successfully\")\n");
        example.push_str("    \n");
        example.push_str("    # Convert document\n");
        example.push_str("    result = bridge.convertdocument(\n");
        example.push_str("        r\"C:\\input.doc\",\n");
        example.push_str("        r\"C:\\output.md\",\n");
        example.push_str("        \"markdown\"\n");
        example.push_str("    )\n");
        example.push_str("    \n");
        example.push_str("    if result == SUCCESS:\n");
        example.push_str("        print(\"Conversion successful\")\n");
        example.push_str("    else:\n");
        example.push_str("        print(f\"Conversion failed: {result}\")\n");
        example.push_str("    \n");
        example.push_str("    # Cleanup\n");
        example.push_str("    bridge.cleanup()\n");
        
        example
    }
    
    fn python_type(&self, c_type: &str) -> &'static str {
        match c_type {
            "int" | "long" | "LONG" => "ctypes.c_long",
            "unsigned int" | "DWORD" => "ctypes.c_ulong",
            "short" | "SHORT" => "ctypes.c_short",
            "char" | "CHAR" => "ctypes.c_char",
            "const char*" | "char*" | "LPSTR" | "LPCSTR" => "ctypes.c_char_p",
            "void*" | "LPVOID" => "ctypes.c_void_p",
            "bool" | "BOOL" => "ctypes.c_bool",
            "float" => "ctypes.c_float",
            "double" => "ctypes.c_double",
            _ => "ctypes.c_void_p",
        }
    }
    
    fn python_param_type(&self, c_type: &str) -> &'static str {
        match c_type {
            "const char*" | "char*" | "LPSTR" | "LPCSTR" => "str",
            "int" | "long" | "LONG" | "unsigned int" | "DWORD" => "int",
            "float" => "float",
            "double" => "float",
            "bool" | "BOOL" => "bool",
            _ => "int",
        }
    }
    
    async fn generate_csharp(&self) -> DllResult<Vec<GeneratedFile>> {
        let mut files = Vec::new();
        
        // Generate C# wrapper class
        let cs_name = format!("{}.cs", 
            self.config.class_name.as_ref().unwrap_or(&"LegacyBridge".to_string()));
        
        let mut content = String::new();
        content.push_str("using System;\n");
        content.push_str("using System.Runtime.InteropServices;\n\n");
        
        if let Some(namespace) = &self.config.namespace {
            content.push_str(&format!("namespace {}\n{{\n", namespace));
        }
        
        content.push_str("    /// <summary>\n");
        content.push_str("    /// LegacyBridge DLL wrapper for C#\n");
        content.push_str("    /// </summary>\n");
        content.push_str("    public static class LegacyBridge\n");
        content.push_str("    {\n");
        
        // Add constants
        if !self.dll_info.constants.is_empty() {
            content.push_str("        #region Constants\n\n");
            for constant in &self.dll_info.constants {
                content.push_str(&format!("        public const int {} = {};\n", 
                    constant.name, constant.value));
            }
            content.push_str("\n        #endregion\n\n");
        }
        
        // Add P/Invoke declarations
        content.push_str("        #region P/Invoke Declarations\n\n");
        for func in &self.dll_info.exports {
            content.push_str(&self.generate_csharp_pinvoke(func));
            content.push_str("\n");
        }
        content.push_str("        #endregion\n");
        
        // Add wrapper methods if error handling is enabled
        if self.config.include_error_handling {
            content.push_str("\n        #region Wrapper Methods\n\n");
            for func in &self.dll_info.exports {
                content.push_str(&self.generate_csharp_wrapper(func));
                content.push_str("\n");
            }
            content.push_str("        #endregion\n");
        }
        
        content.push_str("    }\n");
        
        if self.config.namespace.is_some() {
            content.push_str("}\n");
        }
        
        // Add example if requested
        if self.config.include_examples {
            content.push_str(&self.generate_csharp_example());
        }
        
        let cs_path = self.config.output_dir.join(cs_name);
        std::fs::write(&cs_path, content)?;
        
        files.push(GeneratedFile {
            path: cs_path.clone(),
            language: TargetLanguage::Csharp,
            file_type: FileType::SourceCode,
            size: std::fs::metadata(&cs_path)?.len(),
        });
        
        Ok(files)
    }
    
    fn generate_csharp_pinvoke(&self, func: &ExportFunction) -> String {
        let mut pinvoke = String::new();
        
        pinvoke.push_str("        [DllImport(\"legacybridge.dll\", ");
        
        match func.calling_convention {
            CallingConvention::Stdcall => pinvoke.push_str("CallingConvention = CallingConvention.StdCall"),
            CallingConvention::Cdecl => pinvoke.push_str("CallingConvention = CallingConvention.Cdecl"),
            CallingConvention::Fastcall => pinvoke.push_str("CallingConvention = CallingConvention.FastCall"),
        }
        
        if func.parameters.iter().any(|p| p.param_type.contains("char*")) {
            pinvoke.push_str(", CharSet = CharSet.Ansi");
        }
        
        pinvoke.push_str(")]\n");
        pinvoke.push_str(&format!("        private static extern {} {}", 
            self.csharp_type(&func.return_type),
            func.name
        ));
        
        pinvoke.push('(');
        if !func.parameters.is_empty() {
            let params: Vec<String> = func.parameters.iter().map(|p| {
                let param_type = self.csharp_type(&p.param_type);
                let attributes = if p.param_type.contains("char*") && p.is_out {
                    "[Out] "
                } else if p.param_type.contains("char*") {
                    ""
                } else if p.is_out {
                    "out "
                } else {
                    ""
                };
                format!("{}{} {}", attributes, param_type, p.name)
            }).collect();
            pinvoke.push_str(&params.join(", "));
        }
        pinvoke.push_str(");");
        
        pinvoke
    }
    
    fn generate_csharp_wrapper(&self, func: &ExportFunction) -> String {
        let mut wrapper = String::new();
        
        wrapper.push_str(&format!("        public static {} {}Safe", 
            self.csharp_type(&func.return_type),
            func.name
        ));
        
        wrapper.push('(');
        if !func.parameters.is_empty() {
            let params: Vec<String> = func.parameters.iter().map(|p| {
                let param_type = self.csharp_type(&p.param_type);
                let modifier = if p.is_out && !p.param_type.contains("char*") {
                    "out "
                } else {
                    ""
                };
                format!("{}{} {}", modifier, param_type, p.name)
            }).collect();
            wrapper.push_str(&params.join(", "));
        }
        wrapper.push_str(")\n");
        wrapper.push_str("        {\n");
        wrapper.push_str("            try\n");
        wrapper.push_str("            {\n");
        wrapper.push_str(&format!("                return {}", func.name));
        
        if !func.parameters.is_empty() {
            wrapper.push('(');
            let args: Vec<String> = func.parameters.iter().map(|p| {
                if p.is_out && !p.param_type.contains("char*") {
                    format!("out {}", p.name)
                } else {
                    p.name.clone()
                }
            }).collect();
            wrapper.push_str(&args.join(", "));
            wrapper.push(')');
        } else {
            wrapper.push_str("()");
        }
        
        wrapper.push_str(";\n");
        wrapper.push_str("            }\n");
        wrapper.push_str("            catch (Exception ex)\n");
        wrapper.push_str("            {\n");
        wrapper.push_str("                Console.WriteLine($\"Error calling {}: {ex.Message}\");\n");
        wrapper.push_str("                return ERROR_INVALID_PARAMETER;\n");
        wrapper.push_str("            }\n");
        wrapper.push_str("        }");
        
        wrapper
    }
    
    fn generate_csharp_example(&self) -> String {
        let mut example = String::new();
        
        example.push_str("\n\n// Example usage:\n");
        example.push_str("/*\n");
        example.push_str("class Program\n");
        example.push_str("{\n");
        example.push_str("    static void Main()\n");
        example.push_str("    {\n");
        example.push_str("        // Initialize\n");
        example.push_str("        int result = LegacyBridge.Initialize();\n");
        example.push_str("        if (result == LegacyBridge.SUCCESS)\n");
        example.push_str("        {\n");
        example.push_str("            Console.WriteLine(\"Initialized successfully\");\n");
        example.push_str("        }\n");
        example.push_str("\n");
        example.push_str("        // Convert document\n");
        example.push_str("        result = LegacyBridge.ConvertDocument(\n");
        example.push_str("            @\"C:\\input.doc\",\n");
        example.push_str("            @\"C:\\output.md\",\n");
        example.push_str("            \"markdown\"\n");
        example.push_str("        );\n");
        example.push_str("\n");
        example.push_str("        if (result == LegacyBridge.SUCCESS)\n");
        example.push_str("        {\n");
        example.push_str("            Console.WriteLine(\"Conversion successful\");\n");
        example.push_str("        }\n");
        example.push_str("\n");
        example.push_str("        // Cleanup\n");
        example.push_str("        LegacyBridge.Cleanup();\n");
        example.push_str("    }\n");
        example.push_str("}\n");
        example.push_str("*/\n");
        
        example
    }
    
    fn csharp_type(&self, c_type: &str) -> &'static str {
        match c_type {
            "int" | "long" | "LONG" => "int",
            "unsigned int" | "DWORD" => "uint",
            "short" | "SHORT" => "short",
            "char" | "CHAR" => "byte",
            "const char*" | "char*" | "LPSTR" | "LPCSTR" => "string",
            "void*" | "LPVOID" => "IntPtr",
            "bool" | "BOOL" => "bool",
            "float" => "float",
            "double" => "double",
            _ => "IntPtr",
        }
    }
    
    async fn generate_javascript(&self) -> DllResult<Vec<GeneratedFile>> {
        // Generate Node.js FFI wrapper
        let mut files = Vec::new();
        
        let js_name = format!("{}.js", 
            self.config.class_name.as_ref().unwrap_or(&"legacybridge".to_string()));
        
        let mut content = String::new();
        content.push_str("/**\n * LegacyBridge DLL wrapper for Node.js\n");
        content.push_str(" * Requires: npm install ffi-napi ref-napi\n */\n\n");
        
        content.push_str("const ffi = require('ffi-napi');\n");
        content.push_str("const ref = require('ref-napi');\n");
        content.push_str("const path = require('path');\n\n");
        
        // Add constants
        if !self.dll_info.constants.is_empty() {
            content.push_str("// Constants\n");
            for constant in &self.dll_info.constants {
                content.push_str(&format!("const {} = {};\n", constant.name, constant.value));
            }
            content.push_str("\n");
        }
        
        // Define library
        content.push_str("// Load DLL\n");
        content.push_str("const dllPath = path.join(__dirname, 'legacybridge.dll');\n");
        content.push_str("const legacybridge = ffi.Library(dllPath, {\n");
        
        for func in &self.dll_info.exports {
            content.push_str(&self.generate_js_function_def(func));
            content.push_str(",\n");
        }
        
        content.push_str("});\n\n");
        
        // Export wrapper module
        content.push_str("// Export wrapper functions\n");
        content.push_str("module.exports = {\n");
        
        // Add constants to exports
        for constant in &self.dll_info.constants {
            content.push_str(&format!("  {},\n", constant.name));
        }
        
        // Add functions to exports
        for func in &self.dll_info.exports {
            content.push_str(&self.generate_js_wrapper_function(func));
            content.push_str(",\n");
        }
        
        content.push_str("};\n");
        
        // Add example if requested
        if self.config.include_examples {
            content.push_str(&self.generate_js_example());
        }
        
        let js_path = self.config.output_dir.join(js_name);
        std::fs::write(&js_path, content)?;
        
        files.push(GeneratedFile {
            path: js_path.clone(),
            language: TargetLanguage::Javascript,
            file_type: FileType::SourceCode,
            size: std::fs::metadata(&js_path)?.len(),
        });
        
        Ok(files)
    }
    
    fn generate_js_function_def(&self, func: &ExportFunction) -> String {
        let mut def = format!("  '{}': [", func.name);
        
        def.push_str(&format!("'{}', [", self.js_type(&func.return_type)));
        
        if !func.parameters.is_empty() {
            let param_types: Vec<String> = func.parameters.iter().map(|p| {
                format!("'{}'", self.js_type(&p.param_type))
            }).collect();
            def.push_str(&param_types.join(", "));
        }
        
        def.push_str("]]");
        def
    }
    
    fn generate_js_wrapper_function(&self, func: &ExportFunction) -> String {
        let mut wrapper = format!("  {}: function", func.name.to_lowercase());
        
        wrapper.push('(');
        if !func.parameters.is_empty() {
            let param_names: Vec<&str> = func.parameters.iter().map(|p| p.name.as_str()).collect();
            wrapper.push_str(&param_names.join(", "));
        }
        wrapper.push_str(") {\n");
        
        if self.config.include_error_handling {
            wrapper.push_str("    try {\n");
            wrapper.push_str(&format!("      return legacybridge.{}", func.name));
        } else {
            wrapper.push_str(&format!("    return legacybridge.{}", func.name));
        }
        
        wrapper.push('(');
        if !func.parameters.is_empty() {
            let param_names: Vec<&str> = func.parameters.iter().map(|p| p.name.as_str()).collect();
            wrapper.push_str(&param_names.join(", "));
        }
        wrapper.push_str(");\n");
        
        if self.config.include_error_handling {
            wrapper.push_str("    } catch (error) {\n");
            wrapper.push_str("      console.error(`Error calling {}: ${error}`);\n");
            wrapper.push_str("      return -1;\n");
            wrapper.push_str("    }\n");
        }
        
        wrapper.push_str("  }");
        wrapper
    }
    
    fn generate_js_example(&self) -> String {
        let mut example = String::new();
        
        example.push_str("\n\n// Example usage:\n");
        example.push_str("/*\n");
        example.push_str("const bridge = require('./legacybridge');\n\n");
        example.push_str("// Initialize\n");
        example.push_str("let result = bridge.initialize();\n");
        example.push_str("if (result === bridge.SUCCESS) {\n");
        example.push_str("  console.log('Initialized successfully');\n");
        example.push_str("}\n\n");
        example.push_str("// Convert document\n");
        example.push_str("result = bridge.convertdocument(\n");
        example.push_str("  'C:\\\\input.doc',\n");
        example.push_str("  'C:\\\\output.md',\n");
        example.push_str("  'markdown'\n");
        example.push_str(");\n\n");
        example.push_str("if (result === bridge.SUCCESS) {\n");
        example.push_str("  console.log('Conversion successful');\n");
        example.push_str("} else {\n");
        example.push_str("  console.log(`Conversion failed: ${result}`);\n");
        example.push_str("}\n\n");
        example.push_str("// Cleanup\n");
        example.push_str("bridge.cleanup();\n");
        example.push_str("*/\n");
        
        example
    }
    
    fn js_type(&self, c_type: &str) -> &'static str {
        match c_type {
            "int" | "long" | "LONG" => "int",
            "unsigned int" | "DWORD" => "uint",
            "short" | "SHORT" => "short",
            "char" | "CHAR" => "char",
            "const char*" | "char*" | "LPSTR" | "LPCSTR" => "string",
            "void*" | "LPVOID" => "pointer",
            "bool" | "BOOL" => "bool",
            "float" => "float",
            "double" => "double",
            _ => "pointer",
        }
    }
    
    async fn generate_typescript(&self) -> DllResult<Vec<GeneratedFile>> {
        let mut files = Vec::new();
        
        // Generate TypeScript definitions
        let ts_name = format!("{}.d.ts", 
            self.config.class_name.as_ref().unwrap_or(&"legacybridge".to_string()));
        
        let mut content = String::new();
        content.push_str("/**\n * LegacyBridge DLL TypeScript definitions\n */\n\n");
        
        content.push_str("declare module 'legacybridge' {\n");
        
        // Add constants
        if !self.dll_info.constants.is_empty() {
            content.push_str("  // Constants\n");
            for constant in &self.dll_info.constants {
                content.push_str(&format!("  export const {}: number;\n", constant.name));
            }
            content.push_str("\n");
        }
        
        // Add function declarations
        content.push_str("  // Functions\n");
        for func in &self.dll_info.exports {
            content.push_str(&self.generate_ts_function_declaration(func));
            content.push_str(";\n");
        }
        
        content.push_str("}\n");
        
        let ts_path = self.config.output_dir.join(ts_name);
        std::fs::write(&ts_path, content)?;
        
        files.push(GeneratedFile {
            path: ts_path.clone(),
            language: TargetLanguage::Typescript,
            file_type: FileType::TypeDefinition,
            size: std::fs::metadata(&ts_path)?.len(),
        });
        
        // Also generate the JavaScript file
        files.extend(self.generate_javascript().await?);
        
        Ok(files)
    }
    
    fn generate_ts_function_declaration(&self, func: &ExportFunction) -> String {
        let mut decl = format!("  export function {}", func.name.to_lowercase());
        
        decl.push('(');
        if !func.parameters.is_empty() {
            let params: Vec<String> = func.parameters.iter().map(|p| {
                format!("{}: {}", p.name, self.ts_type(&p.param_type))
            }).collect();
            decl.push_str(&params.join(", "));
        }
        decl.push_str("): number");
        
        decl
    }
    
    fn ts_type(&self, c_type: &str) -> &'static str {
        match c_type {
            "int" | "long" | "LONG" | "unsigned int" | "DWORD" | "short" | "SHORT" => "number",
            "char" | "CHAR" => "number",
            "const char*" | "char*" | "LPSTR" | "LPCSTR" => "string",
            "void*" | "LPVOID" => "any",
            "bool" | "BOOL" => "boolean",
            "float" | "double" => "number",
            _ => "any",
        }
    }
}

fn format_language(lang: TargetLanguage) -> &'static str {
    match lang {
        TargetLanguage::Vb6 => "Visual Basic 6",
        TargetLanguage::Vfp9 => "Visual FoxPro 9",
        TargetLanguage::C => "C",
        TargetLanguage::Cpp => "C++",
        TargetLanguage::Python => "Python",
        TargetLanguage::Csharp => "C#",
        TargetLanguage::Javascript => "JavaScript",
        TargetLanguage::Typescript => "TypeScript",
    }
}

impl Default for GeneratorConfig {
    fn default() -> Self {
        Self {
            dll_path: PathBuf::new(),
            output_dir: PathBuf::from("./generated"),
            language: TargetLanguage::Vb6,
            include_examples: true,
            include_error_handling: true,
            include_documentation: true,
            template_path: None,
            namespace: None,
            class_name: None,
        }
    }
}