// File service implementation
use crate::storage::{S3Storage, UploadResult};
use legacybridge_shared::{
    database::DatabaseManager,
    cache::CacheManager,
    metrics::ServiceMetrics,
    events::EventPublisher,
    types::{FileMetadata, DomainEvent},
    ServiceError, ServiceResult,
};
use sha2::{Sha256, Digest};
use std::sync::Arc;
use std::collections::HashMap;
use tracing::{info, warn, error};
use uuid::Uuid;

pub struct FileService {
    db: Arc<DatabaseManager>,
    cache: Arc<CacheManager>,
    storage: Arc<S3Storage>,
    event_publisher: Arc<EventPublisher>,
    metrics: Arc<ServiceMetrics>,
}

impl FileService {
    pub fn new(
        db: Arc<DatabaseManager>,
        cache: Arc<CacheManager>,
        storage: Arc<S3Storage>,
        event_publisher: Arc<EventPublisher>,
        metrics: Arc<ServiceMetrics>,
    ) -> Self {
        Self {
            db,
            cache,
            storage,
            event_publisher,
            metrics,
        }
    }

    /// Upload a file
    pub async fn upload_file(
        &self,
        user_id: Uuid,
        filename: &str,
        content: Vec<u8>,
        content_type: &str,
    ) -> ServiceResult<FileMetadata> {
        // Validate file size (100MB limit)
        const MAX_FILE_SIZE: usize = 100 * 1024 * 1024;
        if content.len() > MAX_FILE_SIZE {
            return Err(ServiceError::BadRequest("File size exceeds maximum limit".to_string()));
        }

        // Generate file ID and S3 key
        let file_id = Uuid::new_v4();
        let s3_key = format!("files/{}/{}/{}", user_id, file_id, filename);

        // Calculate checksum
        let mut hasher = Sha256::new();
        hasher.update(&content);
        let checksum = format!("{:x}", hasher.finalize());

        // Check for duplicate files by checksum
        if let Some(existing_file) = self.find_file_by_checksum(&checksum).await? {
            info!(
                file_id = %file_id,
                existing_file_id = %existing_file.id,
                checksum = %checksum,
                "Duplicate file detected, returning existing file"
            );
            return Ok(existing_file);
        }

        // Upload to S3
        let mut metadata = HashMap::new();
        metadata.insert("user_id".to_string(), user_id.to_string());
        metadata.insert("original_filename".to_string(), filename.to_string());
        metadata.insert("checksum".to_string(), checksum.clone());

        let upload_result = self.storage.upload_file(
            &s3_key,
            content.clone(),
            content_type,
            Some(metadata),
        ).await?;

        // Store metadata in database
        let file_metadata = self.create_file_record(
            file_id,
            user_id,
            filename,
            content_type,
            content.len() as u64,
            &s3_key,
            &checksum,
        ).await?;

        // Update metrics
        self.metrics.files_uploaded_total.inc();
        self.metrics.file_upload_size_bytes.observe(content.len() as f64);

        // Publish event
        let event = DomainEvent::FileUploaded {
            file_id,
            user_id,
            file_size: content.len() as u64,
            timestamp: chrono::Utc::now(),
        };

        if let Err(e) = self.event_publisher.publish(event).await {
            warn!(error = %e, file_id = %file_id, "Failed to publish file uploaded event");
        }

        info!(
            file_id = %file_id,
            user_id = %user_id,
            filename = filename,
            size = content.len(),
            s3_key = %s3_key,
            "File uploaded successfully"
        );

        Ok(file_metadata)
    }

    /// Download a file
    pub async fn download_file(&self, file_id: Uuid, user_id: Option<Uuid>) -> ServiceResult<FileDownload> {
        // Get file metadata from database
        let file_metadata = self.get_file_metadata(file_id).await?
            .ok_or_else(|| ServiceError::NotFound("File not found".to_string()))?;

        // Check permissions
        if let Some(uid) = user_id {
            if file_metadata.user_id != uid {
                // TODO: Check if file is shared with user
                return Err(ServiceError::Forbidden);
            }
        }

        // Download from S3
        let download_result = self.storage.download_file(&file_metadata.s3_key).await?;

        // Update metrics
        self.metrics.files_downloaded_total.inc();

        info!(
            file_id = %file_id,
            user_id = ?user_id,
            size = download_result.content_length,
            "File downloaded successfully"
        );

        Ok(FileDownload {
            metadata: file_metadata,
            content: download_result.content,
            content_type: download_result.content_type,
        })
    }

    /// Delete a file
    pub async fn delete_file(&self, file_id: Uuid, user_id: Uuid) -> ServiceResult<()> {
        // Get file metadata
        let file_metadata = self.get_file_metadata(file_id).await?
            .ok_or_else(|| ServiceError::NotFound("File not found".to_string()))?;

        // Check ownership
        if file_metadata.user_id != user_id {
            return Err(ServiceError::Forbidden);
        }

        // Soft delete in database
        self.soft_delete_file(file_id).await?;

        // Delete from S3 (could be done asynchronously)
        if let Err(e) = self.storage.delete_file(&file_metadata.s3_key).await {
            warn!(
                error = %e,
                file_id = %file_id,
                s3_key = %file_metadata.s3_key,
                "Failed to delete file from S3, marked for cleanup"
            );
        }

        info!(
            file_id = %file_id,
            user_id = %user_id,
            "File deleted successfully"
        );

        Ok(())
    }

    /// List files for a user
    pub async fn list_user_files(
        &self,
        user_id: Uuid,
        limit: i64,
        offset: i64,
    ) -> ServiceResult<Vec<FileMetadata>> {
        self.get_user_files(user_id, limit, offset).await
    }

    /// Get file metadata
    pub async fn get_file_metadata(&self, file_id: Uuid) -> ServiceResult<Option<FileMetadata>> {
        // Try cache first
        let cache_key = format!("file_metadata:{}", file_id);
        if let Ok(Some(cached)) = self.cache.get::<FileMetadata>(&cache_key).await {
            return Ok(Some(cached));
        }

        // Get from database
        let metadata = self.get_file_from_db(file_id).await?;

        // Cache the result
        if let Some(ref meta) = metadata {
            if let Err(e) = self.cache.set(&cache_key, meta).await {
                warn!(error = %e, "Failed to cache file metadata");
            }
        }

        Ok(metadata)
    }

    /// Generate presigned URL for file access
    pub async fn generate_presigned_url(
        &self,
        file_id: Uuid,
        user_id: Uuid,
        expires_in_seconds: u64,
        operation: PresignedOperation,
    ) -> ServiceResult<String> {
        // Get file metadata and check permissions
        let file_metadata = self.get_file_metadata(file_id).await?
            .ok_or_else(|| ServiceError::NotFound("File not found".to_string()))?;

        if file_metadata.user_id != user_id {
            return Err(ServiceError::Forbidden);
        }

        // Generate presigned URL
        let s3_operation = match operation {
            PresignedOperation::Download => crate::storage::PresignedOperation::Get,
            PresignedOperation::Upload => crate::storage::PresignedOperation::Put,
        };

        self.storage.generate_presigned_url(
            &file_metadata.s3_key,
            expires_in_seconds,
            s3_operation,
        ).await
    }

    /// Get storage usage for a user
    pub async fn get_user_storage_usage(&self, user_id: Uuid) -> ServiceResult<StorageUsage> {
        let usage = self.calculate_user_storage_usage(user_id).await?;
        Ok(usage)
    }

    // Private helper methods

    async fn create_file_record(
        &self,
        file_id: Uuid,
        user_id: Uuid,
        name: &str,
        content_type: &str,
        size: u64,
        s3_key: &str,
        checksum: &str,
    ) -> ServiceResult<FileMetadata> {
        let query = r#"
            INSERT INTO files (id, user_id, name, content_type, size, s3_key, checksum, uploaded_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
            RETURNING id, user_id, name, content_type, size, s3_key, checksum, uploaded_at, deleted_at
        "#;

        let row = sqlx::query(query)
            .bind(file_id)
            .bind(user_id)
            .bind(name)
            .bind(content_type)
            .bind(size as i64)
            .bind(s3_key)
            .bind(checksum)
            .fetch_one(self.db.pool())
            .await?;

        Ok(FileMetadata {
            id: row.get("id"),
            user_id: row.get("user_id"),
            name: row.get("name"),
            content_type: row.get("content_type"),
            size: row.get::<i64, _>("size") as u64,
            s3_key: row.get("s3_key"),
            checksum: row.get("checksum"),
            uploaded_at: row.get("uploaded_at"),
            deleted_at: row.get("deleted_at"),
        })
    }

    async fn get_file_from_db(&self, file_id: Uuid) -> ServiceResult<Option<FileMetadata>> {
        let query = r#"
            SELECT id, user_id, name, content_type, size, s3_key, checksum, uploaded_at, deleted_at
            FROM files
            WHERE id = $1 AND deleted_at IS NULL
        "#;

        let row = sqlx::query(query)
            .bind(file_id)
            .fetch_optional(self.db.pool())
            .await?;

        if let Some(row) = row {
            Ok(Some(FileMetadata {
                id: row.get("id"),
                user_id: row.get("user_id"),
                name: row.get("name"),
                content_type: row.get("content_type"),
                size: row.get::<i64, _>("size") as u64,
                s3_key: row.get("s3_key"),
                checksum: row.get("checksum"),
                uploaded_at: row.get("uploaded_at"),
                deleted_at: row.get("deleted_at"),
            }))
        } else {
            Ok(None)
        }
    }

    async fn find_file_by_checksum(&self, checksum: &str) -> ServiceResult<Option<FileMetadata>> {
        let query = r#"
            SELECT id, user_id, name, content_type, size, s3_key, checksum, uploaded_at, deleted_at
            FROM files
            WHERE checksum = $1 AND deleted_at IS NULL
            LIMIT 1
        "#;

        let row = sqlx::query(query)
            .bind(checksum)
            .fetch_optional(self.db.pool())
            .await?;

        if let Some(row) = row {
            Ok(Some(FileMetadata {
                id: row.get("id"),
                user_id: row.get("user_id"),
                name: row.get("name"),
                content_type: row.get("content_type"),
                size: row.get::<i64, _>("size") as u64,
                s3_key: row.get("s3_key"),
                checksum: row.get("checksum"),
                uploaded_at: row.get("uploaded_at"),
                deleted_at: row.get("deleted_at"),
            }))
        } else {
            Ok(None)
        }
    }

    async fn soft_delete_file(&self, file_id: Uuid) -> ServiceResult<()> {
        let query = "UPDATE files SET deleted_at = NOW() WHERE id = $1";
        sqlx::query(query)
            .bind(file_id)
            .execute(self.db.pool())
            .await?;
        Ok(())
    }

    async fn get_user_files(
        &self,
        user_id: Uuid,
        limit: i64,
        offset: i64,
    ) -> ServiceResult<Vec<FileMetadata>> {
        let query = r#"
            SELECT id, user_id, name, content_type, size, s3_key, checksum, uploaded_at, deleted_at
            FROM files
            WHERE user_id = $1 AND deleted_at IS NULL
            ORDER BY uploaded_at DESC
            LIMIT $2 OFFSET $3
        "#;

        let rows = sqlx::query(query)
            .bind(user_id)
            .bind(limit)
            .bind(offset)
            .fetch_all(self.db.pool())
            .await?;

        let files = rows.into_iter().map(|row| FileMetadata {
            id: row.get("id"),
            user_id: row.get("user_id"),
            name: row.get("name"),
            content_type: row.get("content_type"),
            size: row.get::<i64, _>("size") as u64,
            s3_key: row.get("s3_key"),
            checksum: row.get("checksum"),
            uploaded_at: row.get("uploaded_at"),
            deleted_at: row.get("deleted_at"),
        }).collect();

        Ok(files)
    }

    async fn calculate_user_storage_usage(&self, user_id: Uuid) -> ServiceResult<StorageUsage> {
        let query = r#"
            SELECT 
                COUNT(*) as file_count,
                COALESCE(SUM(size), 0) as total_size
            FROM files
            WHERE user_id = $1 AND deleted_at IS NULL
        "#;

        let row = sqlx::query(query)
            .bind(user_id)
            .fetch_one(self.db.pool())
            .await?;

        Ok(StorageUsage {
            user_id,
            file_count: row.get::<i64, _>("file_count") as u64,
            total_size: row.get::<i64, _>("total_size") as u64,
            quota: 1024 * 1024 * 1024, // 1GB default quota
        })
    }
}

#[derive(Debug)]
pub struct FileDownload {
    pub metadata: FileMetadata,
    pub content: Vec<u8>,
    pub content_type: String,
}

#[derive(Debug, serde::Serialize)]
pub struct StorageUsage {
    pub user_id: Uuid,
    pub file_count: u64,
    pub total_size: u64,
    pub quota: u64,
}

#[derive(Debug)]
pub enum PresignedOperation {
    Download,
    Upload,
}
