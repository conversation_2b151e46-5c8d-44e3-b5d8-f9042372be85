#!/bin/bash

# Health Check Script for Monitoring Stack
# Verifies that all monitoring components are healthy

set -euo pipefail

NAMESPACE="${NAMESPACE:-monitoring}"
TIMEOUT="${TIMEOUT:-30}"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Service endpoints
declare -A SERVICES=(
    ["prometheus"]="http://prometheus:9090/-/healthy"
    ["grafana"]="http://grafana:3000/api/health"
    ["elasticsearch"]="http://elasticsearch:9200/_cluster/health"
    ["kibana"]="http://kibana:5601/kibana/api/status"
    ["jaeger"]="http://jaeger-query:16687/"
    ["alertmanager"]="http://alertmanager:9093/-/healthy"
)

check_service() {
    local service=$1
    local url=$2
    local response
    
    echo -n "Checking $service... "
    
    if response=$(kubectl exec -n "$NAMESPACE" deployment/curl-test -- \
        curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 "$url" 2>/dev/null); then
        if [[ "$response" =~ ^(200|204)$ ]]; then
            echo -e "${GREEN}✓ Healthy${NC}"
            return 0
        else
            echo -e "${RED}✗ Unhealthy (HTTP $response)${NC}"
            return 1
        fi
    else
        echo -e "${RED}✗ Unreachable${NC}"
        return 1
    fi
}

check_pods() {
    local app=$1
    local ready_count
    local total_count
    
    echo -n "Checking $app pods... "
    
    if ! kubectl get pods -n "$NAMESPACE" -l "app=$app" &>/dev/null; then
        echo -e "${RED}✗ No pods found${NC}"
        return 1
    fi
    
    ready_count=$(kubectl get pods -n "$NAMESPACE" -l "app=$app" -o json | \
        jq '[.items[] | select(.status.phase == "Running" and .status.conditions[] | select(.type == "Ready" and .status == "True"))] | length')
    total_count=$(kubectl get pods -n "$NAMESPACE" -l "app=$app" -o json | jq '.items | length')
    
    if [[ "$ready_count" -eq "$total_count" ]] && [[ "$total_count" -gt 0 ]]; then
        echo -e "${GREEN}✓ $ready_count/$total_count pods ready${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠ $ready_count/$total_count pods ready${NC}"
        return 1
    fi
}

check_metrics() {
    echo -n "Checking Prometheus metrics... "
    
    local query="up{job=~\"legacybridge-.*\"}"
    local result
    
    if result=$(kubectl exec -n "$NAMESPACE" deployment/prometheus -- \
        wget -qO- "http://localhost:9090/api/v1/query?query=$query" 2>/dev/null | \
        jq -r '.data.result[] | "\(.metric.job): \(.value[1])"'); then
        echo -e "${GREEN}✓${NC}"
        echo "$result" | while read -r line; do
            echo "  $line"
        done
        return 0
    else
        echo -e "${RED}✗ Failed to query metrics${NC}"
        return 1
    fi
}

check_traces() {
    echo -n "Checking Jaeger traces... "
    
    local service="legacybridge-backend"
    local result
    
    if result=$(kubectl exec -n "$NAMESPACE" deployment/jaeger -- \
        wget -qO- "http://localhost:16686/api/services" 2>/dev/null | \
        jq -r '.data[]' | grep -q "$service"); then
        echo -e "${GREEN}✓ Service traces found${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠ No traces for $service yet${NC}"
        return 1
    fi
}

check_logs() {
    echo -n "Checking log ingestion... "
    
    local index="legacybridge-*"
    local count
    
    if count=$(kubectl exec -n "$NAMESPACE" deployment/elasticsearch -- \
        curl -s "http://localhost:9200/$index/_count" 2>/dev/null | \
        jq -r '.count // 0'); then
        if [[ "$count" -gt 0 ]]; then
            echo -e "${GREEN}✓ $count log entries${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠ No logs indexed yet${NC}"
            return 1
        fi
    else
        echo -e "${RED}✗ Failed to query logs${NC}"
        return 1
    fi
}

check_alerts() {
    echo -n "Checking active alerts... "
    
    local alerts
    
    if alerts=$(kubectl exec -n "$NAMESPACE" deployment/prometheus -- \
        wget -qO- "http://localhost:9090/api/v1/alerts" 2>/dev/null | \
        jq -r '.data.alerts | length'); then
        if [[ "$alerts" -eq 0 ]]; then
            echo -e "${GREEN}✓ No active alerts${NC}"
        else
            echo -e "${YELLOW}⚠ $alerts active alerts${NC}"
            kubectl exec -n "$NAMESPACE" deployment/prometheus -- \
                wget -qO- "http://localhost:9090/api/v1/alerts" 2>/dev/null | \
                jq -r '.data.alerts[] | "  - \(.labels.alertname): \(.state)"'
        fi
        return 0
    else
        echo -e "${RED}✗ Failed to query alerts${NC}"
        return 1
    fi
}

# Deploy curl test pod if not exists
deploy_curl_pod() {
    if ! kubectl get deployment -n "$NAMESPACE" curl-test &>/dev/null; then
        kubectl create deployment curl-test --image=curlimages/curl:latest -n "$NAMESPACE" -- sleep infinity
        kubectl wait --for=condition=available --timeout=60s deployment/curl-test -n "$NAMESPACE"
    fi
}

main() {
    echo "=== Monitoring Stack Health Check ==="
    echo "Namespace: $NAMESPACE"
    echo ""
    
    # Deploy helper pod
    deploy_curl_pod
    
    # Check pods
    echo "Pod Status:"
    for app in prometheus grafana elasticsearch kibana jaeger alertmanager; do
        check_pods "$app" || true
    done
    echo ""
    
    # Check services
    echo "Service Health:"
    for service in "${!SERVICES[@]}"; do
        check_service "$service" "${SERVICES[$service]}" || true
    done
    echo ""
    
    # Check functionality
    echo "Functional Checks:"
    check_metrics || true
    check_traces || true
    check_logs || true
    check_alerts || true
    echo ""
    
    echo "=== Health Check Complete ==="
}

main "$@"