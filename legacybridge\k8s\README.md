# LegacyBridge Kubernetes Deployment

This directory contains all Kubernetes manifests for deploying LegacyBridge to a production Kubernetes cluster.

## Prerequisites

- Kubernetes cluster v1.28+
- kubectl configured with cluster access
- cert-manager installed for SSL/TLS certificates
- NGINX Ingress Controller installed
- Prometheus Operator (optional, for metrics)

## Files Overview

- `namespace.yaml` - Creates the legacybridge namespace
- `rbac.yaml` - ServiceAccount and RBAC permissions
- `configmap.yaml` - Application configuration
- `secrets.yaml` - Sensitive credentials (MUST BE UPDATED)
- `pvc.yaml` - Persistent volume claims for data storage
- `deployment.yaml` - Backend and frontend deployments
- `service.yaml` - Service definitions for load balancing
- `ingress.yaml` - Ingress rules for external access
- `hpa.yaml` - Horizontal Pod Autoscaler configurations
- `pdb.yaml` - Pod Disruption Budgets for high availability
- `network-policy.yaml` - Network security policies
- `cert-issuer.yaml` - Let's Encrypt certificate issuers
- `kustomization.yaml` - Kustomize configuration

## Deployment Steps

1. **Update Secrets**
   ```bash
   # Edit secrets.yaml and replace all CHANGE_ME values
   # Or create a secrets.env file with actual values
   ```

2. **Deploy using kubectl**
   ```bash
   # Deploy all resources
   kubectl apply -k .
   
   # Or deploy individually
   kubectl apply -f namespace.yaml
   kubectl apply -f rbac.yaml
   kubectl apply -f configmap.yaml
   kubectl apply -f secrets.yaml
   kubectl apply -f pvc.yaml
   kubectl apply -f deployment.yaml
   kubectl apply -f service.yaml
   kubectl apply -f ingress.yaml
   kubectl apply -f hpa.yaml
   kubectl apply -f pdb.yaml
   kubectl apply -f network-policy.yaml
   kubectl apply -f cert-issuer.yaml
   ```

3. **Verify Deployment**
   ```bash
   # Check pods
   kubectl get pods -n legacybridge
   
   # Check services
   kubectl get svc -n legacybridge
   
   # Check ingress
   kubectl get ingress -n legacybridge
   
   # Check HPA
   kubectl get hpa -n legacybridge
   ```

## Important Security Notes

1. **Secrets Management**
   - Never commit actual secrets to git
   - Use a secrets management tool (e.g., Sealed Secrets, External Secrets)
   - Rotate secrets regularly

2. **Network Policies**
   - Review and adjust network policies based on your cluster setup
   - Ensure monitoring namespace labels match your setup

3. **RBAC**
   - The provided RBAC is minimal; adjust based on requirements
   - Follow principle of least privilege

## Scaling and Performance

- Backend scales from 3 to 20 pods based on CPU/memory/requests
- Frontend scales from 2 to 10 pods
- PodDisruptionBudgets ensure high availability during updates

## Monitoring

- Metrics are exposed on port 9090 for Prometheus scraping
- Configure Prometheus ServiceMonitor for automatic discovery

## Troubleshooting

```bash
# Check pod logs
kubectl logs -n legacybridge deployment/legacybridge-backend

# Describe pods for events
kubectl describe pod -n legacybridge

# Check HPA status
kubectl describe hpa -n legacybridge

# Check ingress status
kubectl describe ingress -n legacybridge legacybridge-ingress
```