// Test security module compilation
use legacybridge::security::{
    init_security,
    EnhancedInputValidator,
    SecurityLimits,
    ValidationContext,
    SecurityLimitsEnforcer,
};
use chrono::Utc;

#[tokio::main]
async fn main() {
    println!("Testing security module...");
    
    // Initialize security
    let (validator, enforcer) = init_security().expect("Failed to initialize security");
    
    // Test validation
    let test_content = b"Test content";
    let context = ValidationContext {
        source_ip: Some("127.0.0.1".to_string()),
        user_agent: Some("Test Agent".to_string()),
        request_id: "test-123".to_string(),
        timestamp: Utc::now(),
    };
    
    let result = validator.validate_input(test_content, "txt", &context);
    
    println!("Validation result: {:?}", result.is_valid);
    println!("Errors: {:?}", result.errors.len());
    println!("Warnings: {:?}", result.warnings.len());
    println!("Detected threats: {:?}", result.detected_threats.len());
    
    // Test rate limiting
    match enforcer.check_limits("test-client", test_content.len()).await {
        Ok(_permit) => println!("Rate limit check passed"),
        Err(e) => println!("Rate limit check failed: {:?}", e),
    }
    
    println!("Security module test completed!");
}