// Common types used across all microservices
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

/// User information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct User {
    pub id: Uuid,
    pub username: String,
    pub email: String,
    pub roles: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_login_at: Option<DateTime<Utc>>,
    pub is_active: bool,
}

/// File metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileMetadata {
    pub id: Uuid,
    pub user_id: Uuid,
    pub name: String,
    pub content_type: String,
    pub size: u64,
    pub s3_key: String,
    pub checksum: Option<String>,
    pub uploaded_at: DateTime<Utc>,
    pub deleted_at: Option<DateTime<Utc>>,
}

/// Conversion job status
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub enum JobStatus {
    Queued,
    Processing,
    Completed,
    Failed,
    Cancelled,
}

/// Conversion job
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ConversionJob {
    pub id: String,
    pub user_id: Uuid,
    pub input_file_id: Option<Uuid>,
    pub output_file_id: Option<Uuid>,
    pub input_format: String,
    pub output_format: String,
    pub status: JobStatus,
    pub options: serde_json::Value,
    pub created_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
    pub processing_time_ms: Option<i32>,
    pub error_message: Option<String>,
    pub result_metadata: Option<serde_json::Value>,
}

/// Conversion request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversionRequest {
    pub input_format: String,
    pub output_format: String,
    pub content: String, // Base64 encoded
    pub options: Option<serde_json::Value>,
}

/// Conversion response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversionResponse {
    pub job_id: String,
    pub status: String,
    pub estimated_completion: Option<DateTime<Utc>>,
}

/// Conversion result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversionResult {
    pub content: String,
    pub metadata: HashMap<String, serde_json::Value>,
    pub warnings: Vec<String>,
    pub processing_time_ms: u64,
}

/// Job status response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JobStatusResponse {
    pub job_id: String,
    pub status: JobStatus,
    pub progress: Option<f64>,
    pub created_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub error: Option<String>,
}

/// Health check response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthResponse {
    pub status: String,
    pub version: String,
    pub uptime: u64,
    pub dependencies: HashMap<String, String>,
}

/// API response wrapper
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub timestamp: DateTime<Utc>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            timestamp: Utc::now(),
        }
    }

    pub fn error(message: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message),
            timestamp: Utc::now(),
        }
    }
}

/// Pagination parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationParams {
    pub page: Option<u32>,
    pub limit: Option<u32>,
}

impl Default for PaginationParams {
    fn default() -> Self {
        Self {
            page: Some(1),
            limit: Some(20),
        }
    }
}

/// Paginated response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub items: Vec<T>,
    pub total: u64,
    pub page: u32,
    pub limit: u32,
    pub total_pages: u32,
}

/// Service metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceMetrics {
    pub requests_total: u64,
    pub requests_per_second: f64,
    pub average_response_time_ms: f64,
    pub error_rate: f64,
    pub active_connections: u32,
}

/// Event types for inter-service communication
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum DomainEvent {
    UserAuthenticated {
        user_id: Uuid,
        timestamp: DateTime<Utc>,
    },
    ConversionStarted {
        job_id: String,
        user_id: Uuid,
        input_format: String,
        output_format: String,
        timestamp: DateTime<Utc>,
    },
    ConversionCompleted {
        job_id: String,
        user_id: Uuid,
        success: bool,
        processing_time_ms: u64,
        timestamp: DateTime<Utc>,
    },
    FileUploaded {
        file_id: Uuid,
        user_id: Uuid,
        file_size: u64,
        timestamp: DateTime<Utc>,
    },
}
