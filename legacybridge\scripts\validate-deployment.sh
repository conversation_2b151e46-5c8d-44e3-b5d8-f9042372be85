#!/bin/bash

# LegacyBridge Deployment Validation Script
# This script validates all deployment configurations

set -e

echo "=== LegacyBridge Deployment Validation ==="
echo

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Track validation results
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNINGS=0

# Function to check file exists
check_file() {
    local file=$1
    local description=$2
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓${NC} $description exists: $file"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}✗${NC} $description missing: $file"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# Function to check directory exists
check_dir() {
    local dir=$1
    local description=$2
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -d "$dir" ]; then
        echo -e "${GREEN}✓${NC} $description exists: $dir"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}✗${NC} $description missing: $dir"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# Function to validate YAML/JSON syntax (basic check)
check_syntax() {
    local file=$1
    local type=$2
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ ! -f "$file" ]; then
        echo -e "${RED}✗${NC} Cannot validate $file - file not found"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
    
    # Basic syntax check - looking for common issues
    if grep -q '[[:space:]]$' "$file"; then
        echo -e "${YELLOW}⚠${NC} $file has trailing whitespace"
        WARNINGS=$((WARNINGS + 1))
    fi
    
    if [ "$type" = "yaml" ]; then
        # Check for tabs (YAML doesn't allow tabs)
        if grep -q $'\t' "$file"; then
            echo -e "${RED}✗${NC} $file contains tabs (YAML requires spaces)"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            return 1
        fi
    fi
    
    echo -e "${GREEN}✓${NC} Basic syntax check passed: $file"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
    return 0
}

echo "1. Checking Docker Configuration..."
echo "===================================="
check_file "Dockerfile.frontend" "Frontend Dockerfile"
check_file "Dockerfile.backend" "Backend Dockerfile"
check_file "docker-compose.yml" "Docker Compose configuration"
check_dir "docker/config" "Docker config directory"
echo

echo "2. Checking Kubernetes Manifests..."
echo "==================================="
check_file "k8s/namespace.yaml" "Namespace manifest"
check_file "k8s/deployment.yaml" "Deployment manifest"
check_file "k8s/service.yaml" "Service manifest"
check_file "k8s/ingress.yaml" "Ingress manifest"
check_file "k8s/configmap.yaml" "ConfigMap manifest"
check_file "k8s/secrets.yaml" "Secrets manifest"
check_file "k8s/hpa.yaml" "HPA manifest"
check_file "k8s/rbac.yaml" "RBAC manifest"
check_file "k8s/pvc.yaml" "PVC manifest"
check_file "k8s/network-policy.yaml" "Network Policy manifest"

# Validate YAML syntax for K8s files
for file in k8s/*.yaml; do
    if [ -f "$file" ]; then
        check_syntax "$file" "yaml"
    fi
done
echo

echo "3. Checking Cloud Infrastructure Templates..."
echo "============================================="
check_file "cloud/aws/legacybridge-infrastructure.yaml" "AWS CloudFormation template"
check_file "cloud/azure/legacybridge-infrastructure.json" "Azure ARM template"
check_file "cloud/gcp/legacybridge-infrastructure.yaml" "GCP Deployment Manager template"
check_file "cloud/terraform/main.tf" "Terraform configuration"
echo

echo "4. Checking CI/CD Configuration..."
echo "==================================="
check_file "../.github/workflows/production-deploy.yml" "Production deployment workflow"
check_file "../.github/workflows/ci.yml" "CI workflow"
check_file "../.github/workflows/deploy.yml" "Deploy workflow"
check_file "ci-cd/scripts/deploy.sh" "Deployment script"
check_file "ci-cd/scripts/rollback.sh" "Rollback script"
check_file "ci-cd/scripts/run-tests.sh" "Test runner script"
check_file "ci-cd/scripts/security-scan.sh" "Security scan script"
echo

echo "5. Checking Monitoring Configuration..."
echo "======================================="
check_file "monitoring/prometheus.yml" "Prometheus configuration"
check_file "monitoring/prometheus/prometheus.yml" "Prometheus detailed config"
check_file "monitoring/prometheus/alert_rules.yml" "Alert rules"
check_file "monitoring/prometheus/recording_rules.yml" "Recording rules"
check_file "monitoring/prometheus/slo_rules.yml" "SLO rules"
check_file "monitoring/grafana/dashboards/legacybridge-overview.json" "Grafana dashboard"
check_file "monitoring/grafana/dashboards/legacybridge-slo.json" "SLO dashboard"
check_file "monitoring/elasticsearch/elasticsearch-config.yaml" "Elasticsearch config"
check_file "monitoring/jaeger/jaeger-config.yaml" "Jaeger config"
check_file "monitoring/alerting/alertmanager-config.yaml" "AlertManager config"
check_file "monitoring/docker-compose.monitoring.yml" "Monitoring Docker Compose"
check_file "monitoring/scripts/deploy-monitoring.sh" "Monitoring deployment script"
check_file "monitoring/scripts/health-check.sh" "Health check script"
echo

echo "6. Checking Documentation..."
echo "============================"
check_file "README.md" "Main README"
check_file "ENTERPRISE_DEPLOYMENT_GUIDE.md" "Enterprise deployment guide"
check_file "k8s/README.md" "Kubernetes documentation"
check_file "cloud/README.md" "Cloud infrastructure documentation"
check_file "monitoring/README.md" "Monitoring documentation"
check_file "ci-cd/README.md" "CI/CD documentation"
echo

echo "7. Checking Scripts and Tools..."
echo "================================"
check_file "scripts/init-db.sql" "Database initialization script"

# Check script executability
echo
echo "Checking script permissions..."
for script in ci-cd/scripts/*.sh monitoring/scripts/*.sh; do
    if [ -f "$script" ]; then
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        if [ -x "$script" ]; then
            echo -e "${GREEN}✓${NC} $script is executable"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        else
            echo -e "${YELLOW}⚠${NC} $script is not executable (run: chmod +x $script)"
            WARNINGS=$((WARNINGS + 1))
        fi
    fi
done
echo

echo "8. Checking Production Readiness..."
echo "==================================="
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if [ -f "docker/config/production.toml" ]; then
    echo -e "${GREEN}✓${NC} Production configuration exists"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${YELLOW}⚠${NC} Production configuration missing - create docker/config/production.toml"
    WARNINGS=$((WARNINGS + 1))
fi

# Check for secrets management
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if grep -q "TODO\|FIXME\|XXX" k8s/secrets.yaml 2>/dev/null; then
    echo -e "${YELLOW}⚠${NC} Secrets manifest contains TODO/FIXME markers"
    WARNINGS=$((WARNINGS + 1))
else
    echo -e "${GREEN}✓${NC} Secrets manifest appears complete"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
fi

# Check for hardcoded passwords
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if grep -r "password\s*[:=]\s*[\"'].*[\"']" --include="*.yaml" --include="*.yml" --include="*.json" k8s/ cloud/ 2>/dev/null | grep -v -E "(secretKeyRef|valueFrom|example|template)" > /dev/null; then
    echo -e "${RED}✗${NC} Found hardcoded passwords in configuration files"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
else
    echo -e "${GREEN}✓${NC} No hardcoded passwords found"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
fi

echo
echo "=========================================="
echo "Validation Summary"
echo "=========================================="
echo -e "Total Checks:    $TOTAL_CHECKS"
echo -e "Passed:          ${GREEN}$PASSED_CHECKS${NC}"
echo -e "Failed:          ${RED}$FAILED_CHECKS${NC}"
echo -e "Warnings:        ${YELLOW}$WARNINGS${NC}"
echo

if [ $FAILED_CHECKS -eq 0 ]; then
    if [ $WARNINGS -eq 0 ]; then
        echo -e "${GREEN}✓ All deployment validation checks passed!${NC}"
        exit 0
    else
        echo -e "${YELLOW}⚠ Validation completed with warnings${NC}"
        exit 0
    fi
else
    echo -e "${RED}✗ Deployment validation failed!${NC}"
    echo "Please fix the issues above before proceeding with deployment."
    exit 1
fi