{"dashboard": {"id": null, "uid": "legacybridge-slo", "title": "LegacyBridge SLO Dashboard", "tags": ["legacybridge", "slo", "sla"], "style": "dark", "timezone": "browser", "refresh": "1m", "time": {"from": "now-24h", "to": "now"}, "editable": true, "graphTooltip": 1, "panels": [{"id": 1, "title": "API Availability SLO (Target: 99.9%)", "type": "gauge", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0}, "targets": [{"expr": "slo:monthly:api_availability * 100", "legendFormat": "Monthly Availability", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 99.5}, {"color": "green", "value": 99.9}]}, "min": 98, "max": 100}}}, {"id": 2, "title": "API Latency SLO (P95 < 500ms)", "type": "gauge", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 0}, "targets": [{"expr": "slo:monthly:api_latency_p95 * 1000", "legendFormat": "Monthly P95 Latency", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ms", "color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 400}, {"color": "red", "value": 500}]}, "min": 0, "max": 1000}}}, {"id": 3, "title": "Conversion Success Rate SLO (Target: 99%)", "type": "gauge", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 0}, "targets": [{"expr": "slo:monthly:conversion_success_rate * 100", "legendFormat": "Monthly Success Rate", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 98}, {"color": "green", "value": 99}]}, "min": 95, "max": 100}}}, {"id": 4, "title": "API Error Budget Remaining", "type": "stat", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}, "targets": [{"expr": "slo:error_budget:api_availability * 100", "legendFormat": "Error Budget", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 20}, {"color": "green", "value": 50}]}}}}, {"id": 5, "title": "Conversion Error Budget Remaining", "type": "stat", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 8}, "targets": [{"expr": "slo:error_budget:conversion_success * 100", "legendFormat": "Error Budget", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 20}, {"color": "green", "value": 50}]}}}}, {"id": 6, "title": "SLO Compliance Over Time", "type": "timeseries", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 8}, "targets": [{"expr": "avg_over_time(slo:api_availability:rate5m[1h]) * 100", "legendFormat": "API Availability", "refId": "A"}, {"expr": "avg_over_time(slo:conversion_success_rate:rate5m[1h]) * 100", "legendFormat": "Conversion Success", "refId": "B"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "palette-classic"}, "custom": {"lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10, "showPoints": "never"}}}}, {"id": 7, "title": "SLI Breakdown - Request Rate by Status", "type": "piechart", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 16}, "targets": [{"expr": "sum(rate(http_requests_total{job=\"legacybridge-backend\"}[5m])) by (status)", "legendFormat": "{{ status }}", "refId": "A"}], "options": {"pieType": "donut", "displayLabels": ["name", "percent"], "legendDisplayMode": "table", "legendPlacement": "right"}}, {"id": 8, "title": "Latency Distribution", "type": "heatmap", "gridPos": {"h": 8, "w": 16, "x": 8, "y": 16}, "targets": [{"expr": "sum(rate(http_request_duration_seconds_bucket{job=\"legacybridge-backend\"}[5m])) by (le)", "format": "heatmap", "legendFormat": "{{ le }}", "refId": "A"}], "options": {"calculate": false, "cellGap": 1, "cellValues": {"unit": "short"}, "color": {"scheme": "Spectral"}}}]}}