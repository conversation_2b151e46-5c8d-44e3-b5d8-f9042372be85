trigger:
  branches:
    include:
      - main
      - develop
  tags:
    include:
      - v*

pr:
  branches:
    include:
      - main
      - develop

resources:
  containers:
    - container: rust
      image: rust:1.75
    - container: node
      image: node:18
    - container: trivy
      image: aquasec/trivy:latest

variables:
  - group: legacybridge-secrets
  - name: DOCKER_BUILDKIT
    value: '1'
  - name: REGISTRY
    value: 'legacybridge.azurecr.io'
  - name: IMAGE_NAME
    value: 'legacybridge'
  - name: NODE_VERSION
    value: '18'
  - name: RUST_VERSION
    value: '1.75'
  - name: isMain
    value: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
  - name: isTag
    value: $[startsWith(variables['Build.SourceBranch'], 'refs/tags/v')]

stages:
  - stage: SecurityScan
    displayName: 'Security Scanning'
    jobs:
      - job: TrivyScan
        displayName: 'Trivy Security Scan'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: CmdLine@2
            displayName: 'Run Trivy scan'
            inputs:
              script: |
                docker run --rm -v $(Build.SourcesDirectory):/src \
                  aquasec/trivy:latest fs --severity HIGH,CRITICAL \
                  --format json --output /src/trivy-results.json /src
                
                docker run --rm -v $(Build.SourcesDirectory):/src \
                  aquasec/trivy:latest fs --severity HIGH,CRITICAL \
                  --format table /src
          
          - task: PublishBuildArtifacts@1
            displayName: 'Publish Trivy results'
            inputs:
              pathToPublish: 'trivy-results.json'
              artifactName: 'security-scan-results'

      - job: DependencyCheck
        displayName: 'OWASP Dependency Check'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: dependency-check-build-task@6
            inputs:
              projectName: 'LegacyBridge'
              scanPath: '$(Build.SourcesDirectory)'
              format: 'ALL'
              additionalArguments: '--enableRetired --enableExperimental'
          
          - task: PublishTestResults@2
            inputs:
              testResultsFormat: 'JUnit'
              testResultsFiles: '**/dependency-check-junit.xml'
              testRunTitle: 'Dependency Check Results'

      - job: CredScan
        displayName: 'Credential Scanner'
        pool:
          vmImage: 'windows-latest'
        steps:
          - task: CredScan@3
            inputs:
              outputFormat: 'pre'
              scanFolder: '$(Build.SourcesDirectory)'
          
          - task: PostAnalysis@2
            inputs:
              CredScan: true

  - stage: QualityAnalysis
    displayName: 'Code Quality Analysis'
    dependsOn: []
    jobs:
      - job: Frontend
        displayName: 'Frontend Quality Checks'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '$(NODE_VERSION)'
          
          - task: Npm@1
            displayName: 'Install dependencies'
            inputs:
              command: 'ci'
          
          - task: Npm@1
            displayName: 'Run ESLint'
            inputs:
              command: 'custom'
              customCommand: 'run lint'
          
          - task: Npm@1
            displayName: 'TypeScript type check'
            inputs:
              command: 'custom'
              customCommand: 'run typecheck'
          
          - task: Npm@1
            displayName: 'Check formatting'
            inputs:
              command: 'custom'
              customCommand: 'run format:check'

      - job: Backend
        displayName: 'Backend Quality Checks'
        pool:
          vmImage: 'ubuntu-latest'
        container: rust
        steps:
          - script: |
              rustup component add rustfmt clippy
              cd src-tauri
              cargo fmt -- --check
              cargo clippy -- -D warnings
            displayName: 'Rust linting and formatting'

      - job: SonarCloud
        displayName: 'SonarCloud Analysis'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: SonarCloudPrepare@1
            inputs:
              SonarCloud: 'SonarCloud'
              organization: 'legacybridge'
              scannerMode: 'CLI'
              configMode: 'manual'
              cliProjectKey: 'legacybridge'
              cliProjectName: 'LegacyBridge'
              cliSources: '.'
          
          - task: SonarCloudAnalyze@1
          
          - task: SonarCloudPublish@1
            inputs:
              pollingTimeoutSec: '300'

  - stage: Test
    displayName: 'Testing'
    dependsOn: [SecurityScan, QualityAnalysis]
    jobs:
      - job: UnitTests
        displayName: 'Unit Tests'
        strategy:
          matrix:
            frontend:
              component: 'frontend'
              pool: 'ubuntu-latest'
            backend:
              component: 'backend'
              pool: 'ubuntu-latest'
        pool:
          vmImage: $(pool)
        steps:
          - task: NodeTool@0
            condition: eq(variables['component'], 'frontend')
            inputs:
              versionSpec: '$(NODE_VERSION)'
          
          - task: Npm@1
            condition: eq(variables['component'], 'frontend')
            displayName: 'Install dependencies'
            inputs:
              command: 'ci'
          
          - task: Npm@1
            condition: eq(variables['component'], 'frontend')
            displayName: 'Run unit tests'
            inputs:
              command: 'custom'
              customCommand: 'run test:ci'
          
          - task: PublishTestResults@2
            condition: eq(variables['component'], 'frontend')
            inputs:
              testResultsFormat: 'JUnit'
              testResultsFiles: '**/junit.xml'
              testRunTitle: 'Frontend Unit Tests'
          
          - task: PublishCodeCoverageResults@1
            condition: eq(variables['component'], 'frontend')
            inputs:
              codeCoverageTool: 'Cobertura'
              summaryFileLocation: '$(System.DefaultWorkingDirectory)/coverage/cobertura-coverage.xml'
          
          - script: |
              cd src-tauri
              cargo test --all-features
            condition: eq(variables['component'], 'backend')
            displayName: 'Run Rust tests'

      - job: IntegrationTests
        displayName: 'Integration Tests'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: DockerCompose@0
            displayName: 'Run integration tests'
            inputs:
              containerregistrytype: 'Azure Container Registry'
              azureSubscription: 'Azure-Subscription'
              dockerComposeFile: 'docker-compose.test.yml'
              action: 'Run a Docker Compose command'
              dockerComposeCommand: 'up --build --abort-on-container-exit'
          
          - task: DockerCompose@0
            displayName: 'Cleanup'
            condition: always()
            inputs:
              dockerComposeFile: 'docker-compose.test.yml'
              action: 'Run a Docker Compose command'
              dockerComposeCommand: 'down'

      - job: E2ETests
        displayName: 'E2E Tests'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '$(NODE_VERSION)'
          
          - task: Npm@1
            displayName: 'Install dependencies'
            inputs:
              command: 'ci'
          
          - script: npx playwright install --with-deps
            displayName: 'Install Playwright browsers'
          
          - task: Npm@1
            displayName: 'Run E2E tests'
            inputs:
              command: 'custom'
              customCommand: 'run test:e2e:ci'
          
          - task: PublishTestResults@2
            condition: always()
            inputs:
              testResultsFormat: 'JUnit'
              testResultsFiles: '**/playwright-results.xml'
              testRunTitle: 'E2E Test Results'
          
          - task: PublishBuildArtifacts@1
            condition: always()
            inputs:
              pathToPublish: 'playwright-report'
              artifactName: 'playwright-report'

  - stage: Build
    displayName: 'Build Docker Images'
    dependsOn: [Test]
    condition: and(succeeded(), or(eq(variables.isMain, true), eq(variables.isTag, true)))
    jobs:
      - job: BuildImages
        displayName: 'Build and Push Images'
        strategy:
          matrix:
            frontend:
              component: 'frontend'
            backend:
              component: 'backend'
            cli:
              component: 'cli'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: Docker@2
            displayName: 'Build Docker image'
            inputs:
              containerRegistry: 'ACR-Connection'
              repository: '$(IMAGE_NAME)/$(component)'
              command: 'build'
              Dockerfile: 'Dockerfile.$(component)'
              buildContext: '$(Build.SourcesDirectory)'
              arguments: '--build-arg VERSION=$(Build.SourceVersion)'
              tags: |
                $(Build.BuildId)
                $(Build.SourceBranchName)
                latest
          
          - script: |
              docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
                aquasec/trivy:latest image \
                --severity HIGH,CRITICAL \
                --format json \
                --output trivy-$(component)-results.json \
                $(REGISTRY)/$(IMAGE_NAME)/$(component):$(Build.BuildId)
            displayName: 'Scan Docker image'
          
          - task: Docker@2
            displayName: 'Push Docker image'
            inputs:
              containerRegistry: 'ACR-Connection'
              repository: '$(IMAGE_NAME)/$(component)'
              command: 'push'
              tags: |
                $(Build.BuildId)
                $(Build.SourceBranchName)
                latest

  - stage: DeployStaging
    displayName: 'Deploy to Staging'
    dependsOn: [Build]
    condition: and(succeeded(), eq(variables.isMain, true))
    jobs:
      - deployment: DeployToStaging
        displayName: 'Deploy to Staging AKS'
        environment: 'staging'
        pool:
          vmImage: 'ubuntu-latest'
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                
                - task: KubernetesManifest@0
                  displayName: 'Create namespace'
                  inputs:
                    action: 'deploy'
                    kubernetesServiceConnection: 'AKS-Staging'
                    namespace: 'legacybridge-staging'
                    manifests: 'k8s/namespace.yaml'
                
                - task: KubernetesManifest@0
                  displayName: 'Deploy ConfigMaps and Secrets'
                  inputs:
                    action: 'deploy'
                    kubernetesServiceConnection: 'AKS-Staging'
                    namespace: 'legacybridge-staging'
                    manifests: |
                      k8s/configmap.yaml
                      k8s/secrets.yaml
                
                - task: KubernetesManifest@0
                  displayName: 'Deploy application'
                  inputs:
                    action: 'deploy'
                    kubernetesServiceConnection: 'AKS-Staging'
                    namespace: 'legacybridge-staging'
                    manifests: |
                      k8s/deployment.yaml
                      k8s/service.yaml
                      k8s/ingress.yaml
                      k8s/hpa.yaml
                    containers: |
                      $(REGISTRY)/$(IMAGE_NAME)/frontend:$(Build.BuildId)
                      $(REGISTRY)/$(IMAGE_NAME)/backend:$(Build.BuildId)
                
                - task: Kubernetes@1
                  displayName: 'Wait for rollout'
                  inputs:
                    connectionType: 'Kubernetes Service Connection'
                    kubernetesServiceEndpoint: 'AKS-Staging'
                    namespace: 'legacybridge-staging'
                    command: 'rollout'
                    arguments: 'status deployment/legacybridge-backend --timeout=600s'

      - job: SmokeTests
        displayName: 'Smoke Tests - Staging'
        dependsOn: DeployToStaging
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '$(NODE_VERSION)'
          
          - task: Npm@1
            displayName: 'Install dependencies'
            inputs:
              command: 'ci'
          
          - task: Npm@1
            displayName: 'Run smoke tests'
            inputs:
              command: 'custom'
              customCommand: 'run test:smoke -- --env staging'

  - stage: DeployProduction
    displayName: 'Deploy to Production'
    dependsOn: [DeployStaging]
    condition: and(succeeded(), eq(variables.isTag, true))
    jobs:
      - deployment: ApprovalGate
        displayName: 'Production Approval'
        environment: 'production'
        pool:
          vmImage: 'ubuntu-latest'
        strategy:
          runOnce:
            deploy:
              steps:
                - script: echo "Approved for production deployment"

      - deployment: DeployToProduction
        displayName: 'Deploy to Production AKS'
        dependsOn: ApprovalGate
        environment: 'production'
        pool:
          vmImage: 'ubuntu-latest'
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                
                - script: |
                    VERSION=$(echo $(Build.SourceBranch) | sed 's/refs\/tags\/v//')
                    echo "##vso[task.setvariable variable=VERSION]$VERSION"
                  displayName: 'Extract version'
                
                - task: KubernetesManifest@0
                  displayName: 'Blue-Green deployment'
                  inputs:
                    action: 'deploy'
                    kubernetesServiceConnection: 'AKS-Production'
                    namespace: 'legacybridge'
                    manifests: |
                      k8s/deployment.yaml
                    containers: |
                      $(REGISTRY)/$(IMAGE_NAME)/frontend:$(Build.BuildId)
                      $(REGISTRY)/$(IMAGE_NAME)/backend:$(Build.BuildId)
                    strategy: 'blue-green'
                    trafficSplitMethod: 'smi'
                    percentage: 10
                
                - task: Delay@1
                  displayName: 'Monitor canary for 10 minutes'
                  inputs:
                    delayForMinutes: '10'
                
                - task: KubernetesManifest@0
                  displayName: 'Promote to production'
                  inputs:
                    action: 'promote'
                    kubernetesServiceConnection: 'AKS-Production'
                    namespace: 'legacybridge'
                    manifests: |
                      k8s/deployment.yaml
                    strategy: 'blue-green'

      - job: PostDeploymentTests
        displayName: 'Post-Deployment Verification'
        dependsOn: DeployToProduction
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '$(NODE_VERSION)'
          
          - task: Npm@1
            displayName: 'Install dependencies'
            inputs:
              command: 'ci'
          
          - task: Npm@1
            displayName: 'Run health checks'
            inputs:
              command: 'custom'
              customCommand: 'run test:health-check -- --env production'
          
          - task: AzureMonitor@1
            displayName: 'Create deployment marker'
            inputs:
              connectedServiceNameARM: 'Azure-Subscription'
              ResourceGroupName: 'legacybridge-prod'
              actionType: 'Deployment Marker'
              deploymentName: 'v$(VERSION)'

      - job: Cleanup
        displayName: 'Cleanup old versions'
        dependsOn: PostDeploymentTests
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: Kubernetes@1
            displayName: 'Cleanup old deployments'
            inputs:
              connectionType: 'Kubernetes Service Connection'
              kubernetesServiceEndpoint: 'AKS-Production'
              namespace: 'legacybridge'
              command: 'delete'
              arguments: |
                deployment -l app=legacybridge --field-selector metadata.name!=legacybridge-backend-$(VERSION),metadata.name!=legacybridge-frontend-$(VERSION) --ignore-not-found=true

  - stage: Notifications
    displayName: 'Notifications'
    dependsOn: [DeployStaging, DeployProduction]
    condition: always()
    jobs:
      - job: NotifyTeams
        displayName: 'Notify Teams'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: PowerShell@2
            displayName: 'Send Teams notification'
            inputs:
              targetType: 'inline'
              script: |
                $status = "$(Agent.JobStatus)"
                $color = if ($status -eq "Succeeded") { "0078D7" } else { "E81123" }
                $emoji = if ($status -eq "Succeeded") { "✅" } else { "❌" }
                
                $body = @{
                  "@type" = "MessageCard"
                  "@context" = "http://schema.org/extensions"
                  "summary" = "LegacyBridge Deployment"
                  "themeColor" = $color
                  "sections" = @(
                    @{
                      "activityTitle" = "$emoji Deployment $status"
                      "facts" = @(
                        @{
                          "name" = "Project"
                          "value" = "LegacyBridge"
                        },
                        @{
                          "name" = "Build"
                          "value" = "$(Build.BuildNumber)"
                        },
                        @{
                          "name" = "Branch"
                          "value" = "$(Build.SourceBranchName)"
                        },
                        @{
                          "name" = "Commit"
                          "value" = "$(Build.SourceVersion)"
                        }
                      )
                    }
                  )
                  "potentialAction" = @(
                    @{
                      "@type" = "OpenUri"
                      "name" = "View Pipeline"
                      "targets" = @(
                        @{
                          "os" = "default"
                          "uri" = "$(System.TeamFoundationCollectionUri)$(System.TeamProject)/_build/results?buildId=$(Build.BuildId)"
                        }
                      )
                    }
                  )
                } | ConvertTo-Json -Depth 10
                
                Invoke-RestMethod -Uri "$(TeamsWebhook)" -Method Post -Body $body -ContentType "application/json"