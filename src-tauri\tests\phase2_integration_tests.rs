// Phase 2 Integration Tests
// Comprehensive tests for all Phase 2 performance optimizations

use legacybridge::memory::{BoundedStringCache, get_cached_string, get_cache_stats};
use legacybridge::processing::ZeroCopyProcessor;
use legacybridge::simd::{SimdStringProcessor, get_simd_info};
use legacybridge::concurrency::{WorkStealingThreadPool, BatchConversionProcessor, ConversionRequest};
use std::time::{Duration, Instant};
use std::sync::{Arc, atomic::{AtomicUsize, Ordering}};

#[test]
fn test_bounded_string_cache_memory_stability() {
    let mut cache = BoundedStringCache::new(1000);
    
    // Fill cache with many strings
    for i in 0..2000 {
        let key = format!("test_string_{}", i);
        cache.get_or_insert(&key);
    }
    
    let stats = cache.get_stats();
    
    // Cache should not exceed max size
    assert!(stats.size <= 1000, "Cache size {} exceeds limit 1000", stats.size);
    
    // Memory usage should be reasonable
    assert!(stats.memory_usage < 1024 * 1024, "Memory usage {} too high", stats.memory_usage);
    
    println!("Cache stats: {:?}", stats);
}

#[test]
fn test_zero_copy_efficiency() {
    let processor = ZeroCopyProcessor::new();
    
    // Test zero-copy for clean text
    let clean_text = "This is clean text without special characters";
    let result = processor.normalize_whitespace(clean_text);
    
    // Should be zero-copy (borrowed)
    match result {
        std::borrow::Cow::Borrowed(_) => {
            println!("✓ Zero-copy optimization working for clean text");
        }
        std::borrow::Cow::Owned(_) => {
            panic!("Expected zero-copy for clean text");
        }
    }
    
    // Test allocation for dirty text
    let dirty_text = "Text  with\t\tmultiple\r\nwhitespace\t issues";
    let result = processor.normalize_whitespace(dirty_text);
    
    // Should allocate (owned)
    match result {
        std::borrow::Cow::Owned(_) => {
            println!("✓ Allocation only when necessary for dirty text");
        }
        std::borrow::Cow::Borrowed(_) => {
            panic!("Expected allocation for dirty text");
        }
    }
}

#[test]
fn test_simd_performance() {
    let processor = SimdStringProcessor::new();
    let info = get_simd_info();
    
    println!("SIMD Info: {:?}", info);
    
    let test_text = "Text with {many} \\control\\ characters and {more} braces".repeat(100);
    
    // Benchmark SIMD vs scalar
    let iterations = 100;
    
    // SIMD processing
    let start = Instant::now();
    for _ in 0..iterations {
        let _ = processor.vectorized_escape_processing(&test_text);
    }
    let simd_duration = start.elapsed();
    
    // Scalar processing (fallback)
    let start = Instant::now();
    for _ in 0..iterations {
        let _ = processor.scalar_escape_processing(&test_text);
    }
    let scalar_duration = start.elapsed();
    
    println!("SIMD duration: {:?}", simd_duration);
    println!("Scalar duration: {:?}", scalar_duration);
    
    if info.simd_available {
        let speedup = scalar_duration.as_nanos() as f64 / simd_duration.as_nanos() as f64;
        println!("SIMD speedup: {:.2}x", speedup);
        
        // Should have some performance improvement
        assert!(speedup >= 1.0, "SIMD should not be slower than scalar");
    }
}

#[test]
fn test_work_stealing_thread_pool() {
    let pool = WorkStealingThreadPool::new(4);
    let counter = Arc::new(AtomicUsize::new(0));
    
    // Submit tasks with varying workloads
    let tasks: Vec<_> = (0..100).map(|i| {
        let counter = Arc::clone(&counter);
        move || {
            // Simulate variable work
            std::thread::sleep(Duration::from_millis(if i % 10 == 0 { 5 } else { 1 }));
            counter.fetch_add(1, Ordering::SeqCst);
        }
    }).collect();
    
    let start = Instant::now();
    pool.execute_batch(tasks);
    
    // Wait for completion
    while counter.load(Ordering::SeqCst) < 100 {
        std::thread::sleep(Duration::from_millis(1));
    }
    
    let duration = start.elapsed();
    println!("Work-stealing pool completed 100 tasks in {:?}", duration);
    
    // Should complete reasonably quickly with work stealing
    assert!(duration < Duration::from_secs(2), "Tasks took too long: {:?}", duration);
    
    let stats = pool.get_stats();
    println!("Pool stats: {:?}", stats);
}

#[tokio::test]
async fn test_batch_conversion_performance() {
    let processor = BatchConversionProcessor::new(num_cpus::get());
    
    // Create test conversion requests
    let requests: Vec<ConversionRequest> = (0..50)
        .map(|i| ConversionRequest {
            content: format!("# Test Document {}\n\nThis is test content with **bold** and *italic* text.", i),
            input_format: "md".to_string(),
            output_format: "rtf".to_string(),
            file_id: format!("file_{}", i),
        })
        .collect();
    
    let start = Instant::now();
    let results = processor.process_batch(requests).await;
    let duration = start.elapsed();
    
    println!("Batch conversion of 50 files completed in {:?}", duration);
    
    // Verify all conversions
    assert_eq!(results.len(), 50);
    
    let success_count = results.iter().filter(|r| r.is_ok()).count();
    println!("Successful conversions: {}/50", success_count);
    
    // Should have high success rate
    assert!(success_count >= 45, "Too many conversion failures");
    
    // Should complete within reasonable time
    assert!(duration < Duration::from_secs(10), "Batch conversion too slow: {:?}", duration);
    
    let stats = processor.get_pool_stats();
    println!("Batch processor stats: {:?}", stats);
}

#[test]
fn test_memory_usage_stability() {
    // Test memory stability over multiple operations
    let processor = ZeroCopyProcessor::new();
    let mut cache = BoundedStringCache::new(500);
    
    let initial_stats = cache.get_stats();
    
    // Perform many operations
    for i in 0..1000 {
        let text = format!("Test document {} with some content", i);
        
        // Use cache
        let cached = cache.get_or_insert(&text);
        
        // Process text
        let _processed = processor.normalize_whitespace(&text);
        let _escaped = processor.escape_rtf_text(&text);
        
        // Verify cache is working
        assert!(!cached.is_empty());
    }
    
    let final_stats = cache.get_stats();
    
    println!("Initial cache stats: {:?}", initial_stats);
    println!("Final cache stats: {:?}", final_stats);
    
    // Memory should be bounded
    assert!(final_stats.size <= 500, "Cache size exceeded limit");
    assert!(final_stats.memory_usage < 1024 * 1024, "Memory usage too high");
    
    // Should have good cache utilization
    assert!(final_stats.total_access_count > 1000, "Cache not being used effectively");
}

#[test]
fn test_performance_targets() {
    // Test realistic performance targets from CURSOR-08
    let test_cases = vec![
        ("tiny", "Hello World", 20000),           // 20k ops/sec for tiny docs
        ("small", "# Test\n\nContent".repeat(50), 5000),  // 5k ops/sec for small docs
        ("medium", "# Test\n\nContent".repeat(500), 1000), // 1k ops/sec for medium docs
    ];
    
    for (category, content, target_ops_per_sec) in test_cases {
        let iterations = 100;
        let start = Instant::now();
        
        for _ in 0..iterations {
            // Simulate conversion (would use actual conversion in real test)
            let _result = legacybridge::conversion::rtf_to_markdown(content);
        }
        
        let duration = start.elapsed();
        let ops_per_sec = iterations as f64 / duration.as_secs_f64();
        
        println!("{} documents: {:.0} ops/sec (target: {})", category, ops_per_sec, target_ops_per_sec);
        
        // Should meet at least 50% of target performance
        let min_acceptable = target_ops_per_sec as f64 * 0.5;
        assert!(ops_per_sec >= min_acceptable, 
            "{} performance too low: {:.0} < {:.0}", category, ops_per_sec, min_acceptable);
    }
}

#[test]
fn test_global_cache_integration() {
    // Test global cache functions
    let test_strings = vec![
        "Common string 1",
        "Common string 2", 
        "Common string 1", // Duplicate
        "Another string",
        "Common string 2", // Duplicate
    ];
    
    let mut cached_strings = Vec::new();
    
    for s in &test_strings {
        let cached = get_cached_string(s);
        cached_strings.push(cached);
    }
    
    // Verify string interning is working
    assert!(Arc::ptr_eq(&cached_strings[0], &cached_strings[2])); // Same string should be same Arc
    assert!(Arc::ptr_eq(&cached_strings[1], &cached_strings[4])); // Same string should be same Arc
    
    let stats = get_cache_stats();
    println!("Global cache stats: {:?}", stats);
    
    // Should have fewer unique strings than total requests
    assert!(stats.size < test_strings.len());
}

// Performance regression test
#[test]
fn test_no_performance_regression() {
    let test_content = "# Performance Test\n\nThis is a test document for performance regression testing.";
    let iterations = 50;
    let mut durations = Vec::new();
    
    // Warmup
    for _ in 0..5 {
        let _ = legacybridge::conversion::rtf_to_markdown(test_content);
    }
    
    // Measure performance
    for _ in 0..iterations {
        let start = Instant::now();
        let _result = legacybridge::conversion::rtf_to_markdown(test_content);
        durations.push(start.elapsed());
    }
    
    let avg_duration = durations.iter().sum::<Duration>() / iterations as u32;
    let max_duration = durations.iter().max().unwrap();
    
    println!("Average conversion time: {:?}", avg_duration);
    println!("Max conversion time: {:?}", max_duration);
    
    // Performance assertions
    assert!(avg_duration < Duration::from_millis(10), "Average performance regression");
    assert!(*max_duration < Duration::from_millis(50), "Max performance regression");
}
