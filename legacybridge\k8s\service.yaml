apiVersion: v1
kind: Service
metadata:
  name: legacybridge-backend-service
  namespace: legacybridge
  labels:
    app: legacybridge
    component: backend
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  - port: 8765
    targetPort: 8765
    protocol: TCP
    name: mcp
  selector:
    app: legacybridge
    component: backend

---
apiVersion: v1
kind: Service
metadata:
  name: legacybridge-frontend-service
  namespace: legacybridge
  labels:
    app: legacybridge
    component: frontend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: legacybridge
    component: frontend

---
apiVersion: v1
kind: Service
metadata:
  name: legacybridge-backend-headless
  namespace: legacybridge
  labels:
    app: legacybridge
    component: backend
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: metrics
  selector:
    app: legacybridge
    component: backend