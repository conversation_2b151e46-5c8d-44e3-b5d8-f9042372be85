apiVersion: v1
kind: ConfigMap
metadata:
  name: logstash-config
  namespace: monitoring
data:
  logstash.yml: |
    http.host: "0.0.0.0"
    path.config: /usr/share/logstash/pipeline
    xpack.monitoring.enabled: true
    xpack.monitoring.elasticsearch.hosts: [ "http://elasticsearch:9200" ]
    
  logstash.conf: |
    input {
      # Kubernetes logs from Filebeat
      beats {
        port => 5044
        type => "kubernetes"
      }
      
      # Application logs via TCP
      tcp {
        port => 5000
        codec => json_lines
        type => "application"
      }
      
      # Syslog
      syslog {
        port => 5514
        type => "syslog"
      }
    }
    
    filter {
      # Parse Kubernetes metadata
      if [type] == "kubernetes" {
        # Extract pod metadata from file path
        grok {
          match => {
            "source" => "/var/log/containers/%{DATA:pod_name}_%{DATA:namespace}_%{GREEDYDATA:container_info}.log"
          }
        }
        
        # Parse container info
        grok {
          match => {
            "container_info" => "%{DATA:container_name}-%{DATA:container_id}"
          }
        }
        
        # Parse log format (docker/cri)
        grok {
          match => {
            "message" => "^%{TIMESTAMP_ISO8601:log_timestamp} %{LOGLEVEL:level} %{GREEDYDATA:log_message}"
          }
          overwrite => ["message"]
        }
        
        # Parse JSON logs
        if [log_message] =~ /^\{.*\}$/ {
          json {
            source => "log_message"
            target => "app"
          }
        }
        
        # Add Kubernetes metadata
        mutate {
          add_field => {
            "kubernetes.pod_name" => "%{pod_name}"
            "kubernetes.namespace" => "%{namespace}"
            "kubernetes.container_name" => "%{container_name}"
          }
        }
      }
      
      # Parse application logs
      if [type] == "application" {
        # Extract trace ID for correlation with Jaeger
        if [trace_id] {
          mutate {
            add_field => {
              "tracing.trace_id" => "%{trace_id}"
              "tracing.span_id" => "%{span_id}"
            }
          }
        }
        
        # Parse error stack traces
        if [level] == "ERROR" or [level] == "error" {
          multiline {
            pattern => "^[[:space:]]+(at|\.{3})\b"
            what => "previous"
          }
        }
      }
      
      # Common processing
      date {
        match => [ "timestamp", "ISO8601", "yyyy-MM-dd HH:mm:ss.SSS" ]
        target => "@timestamp"
      }
      
      # GeoIP enrichment for access logs
      if [client_ip] {
        geoip {
          source => "client_ip"
          target => "geoip"
        }
      }
      
      # Add environment metadata
      mutate {
        add_field => {
          "environment" => "${ENVIRONMENT:production}"
          "service" => "legacybridge"
        }
      }
      
      # Remove unnecessary fields
      mutate {
        remove_field => [ "host", "container_info", "log_message" ]
      }
    }
    
    output {
      # Send to Elasticsearch
      elasticsearch {
        hosts => ["elasticsearch:9200"]
        index => "legacybridge-%{type}-%{+YYYY.MM.dd}"
        template_name => "legacybridge"
        template => "/usr/share/logstash/templates/legacybridge-template.json"
        template_overwrite => true
      }
      
      # Send metrics to monitoring
      if [type] == "metrics" {
        statsd {
          host => "statsd"
          port => 8125
          namespace => "legacybridge"
          gauge => {
            "%{metric_name}" => "%{metric_value}"
          }
        }
      }
      
      # Debug output (remove in production)
      # stdout {
      #   codec => rubydebug
      # }
    }
    
  legacybridge-template.json: |
    {
      "index_patterns": ["legacybridge-*"],
      "settings": {
        "number_of_shards": 3,
        "number_of_replicas": 1,
        "index.refresh_interval": "5s",
        "index.lifecycle.name": "legacybridge-ilm-policy",
        "index.lifecycle.rollover_alias": "legacybridge"
      },
      "mappings": {
        "properties": {
          "@timestamp": { "type": "date" },
          "message": { "type": "text" },
          "level": { "type": "keyword" },
          "service": { "type": "keyword" },
          "environment": { "type": "keyword" },
          "trace_id": { "type": "keyword" },
          "span_id": { "type": "keyword" },
          "user_id": { "type": "keyword" },
          "request_id": { "type": "keyword" },
          "method": { "type": "keyword" },
          "path": { "type": "keyword" },
          "status_code": { "type": "integer" },
          "response_time": { "type": "float" },
          "error": {
            "properties": {
              "type": { "type": "keyword" },
              "message": { "type": "text" },
              "stack_trace": { "type": "text" }
            }
          },
          "kubernetes": {
            "properties": {
              "pod_name": { "type": "keyword" },
              "namespace": { "type": "keyword" },
              "container_name": { "type": "keyword" },
              "node_name": { "type": "keyword" }
            }
          },
          "geoip": {
            "properties": {
              "location": { "type": "geo_point" },
              "country_name": { "type": "keyword" },
              "city_name": { "type": "keyword" }
            }
          }
        }
      }
    }