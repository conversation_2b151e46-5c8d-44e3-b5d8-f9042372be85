pipeline {
    agent {
        kubernetes {
            yaml '''
apiVersion: v1
kind: Pod
spec:
  containers:
  - name: node
    image: node:18
    command: ['sleep', '99999']
  - name: rust
    image: rust:1.75
    command: ['sleep', '99999']
  - name: docker
    image: docker:24-dind
    securityContext:
      privileged: true
    volumeMounts:
    - name: docker-sock
      mountPath: /var/run/docker.sock
  - name: kubectl
    image: bitnami/kubectl:latest
    command: ['sleep', '99999']
  - name: trivy
    image: aquasec/trivy:latest
    command: ['sleep', '99999']
  volumes:
  - name: docker-sock
    hostPath:
      path: /var/run/docker.sock
'''
        }
    }

    environment {
        REGISTRY = 'ghcr.io'
        IMAGE_NAME = 'legacybridge'
        NODE_VERSION = '18'
        RUST_VERSION = '1.75'
        DOCKER_BUILDKIT = '1'
        COMPOSE_DOCKER_CLI_BUILD = '1'
    }

    options {
        buildDiscarder(logRotator(numToKeepStr: '30', artifactNumToKeepStr: '10'))
        timeout(time: 2, unit: 'HOURS')
        timestamps()
        ansiColor('xterm')
        disableConcurrentBuilds()
    }

    stages {
        stage('Checkout') {
            steps {
                checkout scm
                script {
                    env.GIT_COMMIT_SHORT = sh(
                        script: "git rev-parse --short HEAD",
                        returnStdout: true
                    ).trim()
                    env.GIT_BRANCH_NAME = sh(
                        script: "git rev-parse --abbrev-ref HEAD",
                        returnStdout: true
                    ).trim()
                    env.BUILD_TAG = "${env.GIT_BRANCH_NAME}-${env.GIT_COMMIT_SHORT}-${env.BUILD_NUMBER}"
                }
            }
        }

        stage('Security Scan') {
            parallel {
                stage('Trivy Scan') {
                    steps {
                        container('trivy') {
                            sh '''
                                trivy fs --severity HIGH,CRITICAL \
                                    --format json \
                                    --output trivy-results.json \
                                    .
                                trivy fs --severity HIGH,CRITICAL \
                                    --format table \
                                    .
                            '''
                            publishHTML(target: [
                                allowMissing: false,
                                alwaysLinkToLastBuild: true,
                                keepAll: true,
                                reportDir: '.',
                                reportFiles: 'trivy-results.json',
                                reportName: 'Trivy Security Report'
                            ])
                        }
                    }
                }

                stage('OWASP Dependency Check') {
                    steps {
                        dependencyCheck additionalArguments: '''
                            --enableRetired 
                            --enableExperimental
                            --format HTML
                            --format JSON
                        ''', odcInstallation: 'OWASP-DC'
                        
                        dependencyCheckPublisher pattern: '**/dependency-check-report.json'
                    }
                }

                stage('SonarQube Analysis') {
                    when {
                        branch 'main'
                    }
                    steps {
                        withSonarQubeEnv('SonarQube') {
                            sh '''
                                sonar-scanner \
                                    -Dsonar.projectKey=legacybridge \
                                    -Dsonar.sources=. \
                                    -Dsonar.host.url=$SONAR_HOST_URL \
                                    -Dsonar.login=$SONAR_AUTH_TOKEN
                            '''
                        }
                    }
                }
            }
        }

        stage('Quality Gate') {
            when {
                branch 'main'
            }
            steps {
                timeout(time: 5, unit: 'MINUTES') {
                    waitForQualityGate abortPipeline: true
                }
            }
        }

        stage('Build & Test') {
            parallel {
                stage('Frontend') {
                    steps {
                        container('node') {
                            sh '''
                                npm ci
                                npm run lint
                                npm run typecheck
                                npm run test:ci
                                npm run test:coverage
                                npm run build
                            '''
                            junit 'junit.xml'
                            publishHTML(target: [
                                allowMissing: false,
                                alwaysLinkToLastBuild: true,
                                keepAll: true,
                                reportDir: 'coverage/lcov-report',
                                reportFiles: 'index.html',
                                reportName: 'Frontend Coverage Report'
                            ])
                        }
                    }
                }

                stage('Backend') {
                    steps {
                        container('rust') {
                            sh '''
                                cd src-tauri
                                cargo fmt -- --check
                                cargo clippy -- -D warnings
                                cargo test --all-features
                                cargo build --release
                            '''
                        }
                    }
                }

                stage('E2E Tests') {
                    steps {
                        container('node') {
                            sh '''
                                npx playwright install
                                npm run test:e2e:ci
                            '''
                            publishHTML(target: [
                                allowMissing: false,
                                alwaysLinkToLastBuild: true,
                                keepAll: true,
                                reportDir: 'playwright-report',
                                reportFiles: 'index.html',
                                reportName: 'E2E Test Report'
                            ])
                        }
                    }
                }
            }
        }

        stage('Build Docker Images') {
            when {
                anyOf {
                    branch 'main'
                    tag pattern: "v\\d+\\.\\d+\\.\\d+", comparator: "REGEXP"
                }
            }
            steps {
                container('docker') {
                    script {
                        docker.withRegistry("https://${REGISTRY}", 'github-registry') {
                            ['frontend', 'backend', 'cli'].each { component ->
                                def image = docker.build(
                                    "${REGISTRY}/${IMAGE_NAME}/${component}:${BUILD_TAG}",
                                    "--file Dockerfile.${component} " +
                                    "--build-arg VERSION=${GIT_COMMIT_SHORT} " +
                                    "."
                                )
                                image.push()
                                image.push('latest')
                                
                                // Scan built image
                                sh """
                                    trivy image \
                                        --severity HIGH,CRITICAL \
                                        --format json \
                                        --output trivy-${component}-results.json \
                                        ${REGISTRY}/${IMAGE_NAME}/${component}:${BUILD_TAG}
                                """
                            }
                        }
                    }
                }
            }
        }

        stage('Deploy to Staging') {
            when {
                branch 'main'
            }
            environment {
                KUBECONFIG = credentials('kubeconfig-staging')
            }
            steps {
                container('kubectl') {
                    sh '''
                        # Apply Kubernetes manifests
                        kubectl apply -f k8s/namespace.yaml
                        kubectl apply -f k8s/configmap.yaml
                        kubectl apply -f k8s/secrets.yaml
                        
                        # Update deployments
                        kubectl set image deployment/legacybridge-backend \
                            backend=${REGISTRY}/${IMAGE_NAME}/backend:${BUILD_TAG} \
                            -n legacybridge
                        
                        kubectl set image deployment/legacybridge-frontend \
                            frontend=${REGISTRY}/${IMAGE_NAME}/frontend:${BUILD_TAG} \
                            -n legacybridge
                        
                        # Wait for rollout
                        kubectl rollout status deployment/legacybridge-backend -n legacybridge
                        kubectl rollout status deployment/legacybridge-frontend -n legacybridge
                    '''
                }
            }
        }

        stage('Smoke Tests - Staging') {
            when {
                branch 'main'
            }
            steps {
                container('node') {
                    sh 'npm run test:smoke -- --env staging'
                }
            }
        }

        stage('Performance Tests') {
            when {
                branch 'main'
            }
            steps {
                container('node') {
                    sh 'npm run test:performance -- --env staging --duration 300'
                    publishHTML(target: [
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'performance-results',
                        reportFiles: 'index.html',
                        reportName: 'Performance Test Report'
                    ])
                }
            }
        }

        stage('Deploy to Production') {
            when {
                tag pattern: "v\\d+\\.\\d+\\.\\d+", comparator: "REGEXP"
            }
            environment {
                KUBECONFIG = credentials('kubeconfig-production')
            }
            input {
                message 'Deploy to production?'
                ok 'Deploy'
                parameters {
                    choice(
                        name: 'DEPLOYMENT_STRATEGY',
                        choices: ['Blue-Green', 'Canary', 'Rolling'],
                        description: 'Choose deployment strategy'
                    )
                    string(
                        name: 'CANARY_PERCENTAGE',
                        defaultValue: '10',
                        description: 'Canary traffic percentage (if canary deployment)'
                    )
                }
            }
            steps {
                container('kubectl') {
                    script {
                        def version = env.TAG_NAME.substring(1)
                        
                        if (params.DEPLOYMENT_STRATEGY == 'Blue-Green') {
                            sh """
                                # Create new deployment
                                kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: legacybridge-backend-${version}
  namespace: legacybridge
spec:
  replicas: 3
  selector:
    matchLabels:
      app: legacybridge
      component: backend
      version: v${version}
  template:
    metadata:
      labels:
        app: legacybridge
        component: backend
        version: v${version}
    spec:
      containers:
      - name: backend
        image: ${REGISTRY}/${IMAGE_NAME}/backend:${BUILD_TAG}
EOF
                                
                                # Wait for deployment
                                kubectl rollout status deployment/legacybridge-backend-${version} -n legacybridge
                                
                                # Switch traffic
                                kubectl patch service legacybridge-backend-service -n legacybridge \
                                    -p '{"spec":{"selector":{"version":"v${version}"}}}'
                            """
                        } else if (params.DEPLOYMENT_STRATEGY == 'Canary') {
                            sh """
                                # Update canary deployment
                                kubectl set image deployment/legacybridge-backend-canary \
                                    backend=${REGISTRY}/${IMAGE_NAME}/backend:${BUILD_TAG} \
                                    -n legacybridge
                                
                                # Configure traffic split
                                kubectl patch service legacybridge-backend-service -n legacybridge \
                                    -p '{"metadata":{"annotations":{"nginx.ingress.kubernetes.io/canary":"true","nginx.ingress.kubernetes.io/canary-weight":"${params.CANARY_PERCENTAGE}"}}}'
                            """
                        } else {
                            sh """
                                # Rolling update
                                kubectl set image deployment/legacybridge-backend \
                                    backend=${REGISTRY}/${IMAGE_NAME}/backend:${BUILD_TAG} \
                                    -n legacybridge
                                
                                kubectl rollout status deployment/legacybridge-backend -n legacybridge
                            """
                        }
                    }
                }
            }
        }

        stage('Health Check - Production') {
            when {
                tag pattern: "v\\d+\\.\\d+\\.\\d+", comparator: "REGEXP"
            }
            steps {
                container('node') {
                    sh 'npm run test:health-check -- --env production'
                }
            }
        }

        stage('Finalize Deployment') {
            when {
                allOf {
                    tag pattern: "v\\d+\\.\\d+\\.\\d+", comparator: "REGEXP"
                    expression { params.DEPLOYMENT_STRATEGY == 'Canary' }
                }
            }
            input {
                message 'Promote canary to full deployment?'
                ok 'Promote'
            }
            steps {
                container('kubectl') {
                    sh '''
                        # Full traffic to new version
                        kubectl patch service legacybridge-backend-service -n legacybridge \
                            -p '{"metadata":{"annotations":{"nginx.ingress.kubernetes.io/canary":"false"}}}'
                        
                        # Scale down canary
                        kubectl scale deployment legacybridge-backend-canary --replicas=0 -n legacybridge
                    '''
                }
            }
        }

        stage('Cleanup') {
            when {
                tag pattern: "v\\d+\\.\\d+\\.\\d+", comparator: "REGEXP"
            }
            steps {
                container('kubectl') {
                    sh '''
                        # Keep last 3 versions
                        kubectl get deployments -n legacybridge -o name | \
                            grep -E "legacybridge-(backend|frontend)-v" | \
                            sort -V | \
                            head -n -6 | \
                            xargs -r kubectl delete -n legacybridge
                    '''
                }
            }
        }
    }

    post {
        always {
            archiveArtifacts artifacts: '''
                trivy-*.json,
                coverage/**/*,
                test-results/**/*,
                performance-results/**/*
            ''', allowEmptyArchive: true
            
            cleanWs()
        }

        success {
            slackSend(
                channel: '#deployments',
                color: 'good',
                message: """
                    ✅ Deployment successful!
                    *Project:* ${env.JOB_NAME}
                    *Build:* ${env.BUILD_NUMBER}
                    *Branch:* ${env.GIT_BRANCH_NAME}
                    *Commit:* ${env.GIT_COMMIT_SHORT}
                """
            )
        }

        failure {
            slackSend(
                channel: '#alerts',
                color: 'danger',
                message: """
                    ❌ Deployment failed!
                    *Project:* ${env.JOB_NAME}
                    *Build:* ${env.BUILD_NUMBER}
                    *Branch:* ${env.GIT_BRANCH_NAME}
                    *URL:* ${env.BUILD_URL}
                """
            )
        }

        unstable {
            slackSend(
                channel: '#deployments',
                color: 'warning',
                message: """
                    ⚠️ Build unstable
                    *Project:* ${env.JOB_NAME}
                    *Build:* ${env.BUILD_NUMBER}
                    Check test results: ${env.BUILD_URL}
                """
            )
        }
    }
}