apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: legacybridge-backend-pdb
  namespace: legacybridge
  labels:
    app: legacybridge
    component: backend
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: legacybridge
      component: backend

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: legacybridge-frontend-pdb
  namespace: legacybridge
  labels:
    app: legacybridge
    component: frontend
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: legacybridge
      component: frontend