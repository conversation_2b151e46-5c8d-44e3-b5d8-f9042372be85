import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 100 },  // Ramp up to 100 users
    { duration: '5m', target: 100 },  // Stay at 100 users
    { duration: '2m', target: 200 },  // Ramp up to 200 users
    { duration: '5m', target: 200 },  // Stay at 200 users
    { duration: '2m', target: 0 },    // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests must complete below 500ms
    http_req_failed: ['rate<0.1'],    // Error rate must be below 10%
    errors: ['rate<0.1'],             // Custom error rate below 10%
  },
};

const BASE_URL = __ENV.BASE_URL || 'http://localhost:3000';

export default function () {
  // Test scenarios
  const scenarios = [
    { name: 'Homepage', url: '/', weight: 0.3 },
    { name: 'API Health', url: '/api/health', weight: 0.2 },
    { name: 'Convert Endpoint', url: '/api/v1/convert', weight: 0.3, method: 'POST' },
    { name: 'Status Check', url: '/api/v1/status', weight: 0.2 },
  ];

  // Select scenario based on weight
  const random = Math.random();
  let accumulator = 0;
  let selectedScenario;

  for (const scenario of scenarios) {
    accumulator += scenario.weight;
    if (random <= accumulator) {
      selectedScenario = scenario;
      break;
    }
  }

  // Execute scenario
  let response;
  const url = `${BASE_URL}${selectedScenario.url}`;

  if (selectedScenario.method === 'POST') {
    // File conversion test
    const payload = JSON.stringify({
      sourceFormat: 'docx',
      targetFormat: 'pdf',
      content: 'SGVsbG8gV29ybGQh', // Base64 "Hello World!"
    });

    const params = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token',
      },
    };

    response = http.post(url, payload, params);
  } else {
    response = http.get(url);
  }

  // Check response
  const checkResult = check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
    'response has body': (r) => r.body && r.body.length > 0,
  });

  errorRate.add(!checkResult);

  // Think time between requests
  sleep(Math.random() * 3 + 1); // 1-4 seconds
}

export function handleSummary(data) {
  return {
    'stdout': textSummary(data, { indent: ' ', enableColors: true }),
    'performance-report.json': JSON.stringify(data),
    'performance-report.html': htmlReport(data),
  };
}

function textSummary(data, options) {
  // Custom text summary
  return `
Performance Test Results
========================
Total Requests: ${data.metrics.http_reqs.values.count}
Failed Requests: ${data.metrics.http_req_failed.values.passes}
Average Response Time: ${data.metrics.http_req_duration.values.avg.toFixed(2)}ms
95th Percentile: ${data.metrics.http_req_duration.values['p(95)'].toFixed(2)}ms
Error Rate: ${(data.metrics.errors.values.rate * 100).toFixed(2)}%
`;
}

function htmlReport(data) {
  // Generate HTML report
  return `
<!DOCTYPE html>
<html>
<head>
    <title>LegacyBridge Performance Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .metric { margin: 10px 0; padding: 10px; background: #f0f0f0; }
        .pass { color: green; }
        .fail { color: red; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #4CAF50; color: white; }
    </style>
</head>
<body>
    <h1>LegacyBridge Performance Test Report</h1>
    <div class="metric">
        <h2>Summary</h2>
        <p>Total Virtual Users: ${options.stages[2].target}</p>
        <p>Test Duration: ${options.stages.reduce((sum, stage) => sum + parseInt(stage.duration), 0)} minutes</p>
        <p>Total Requests: ${data.metrics.http_reqs.values.count}</p>
    </div>
    <div class="metric">
        <h2>Response Times</h2>
        <table>
            <tr>
                <th>Metric</th>
                <th>Value</th>
                <th>Threshold</th>
                <th>Status</th>
            </tr>
            <tr>
                <td>Average</td>
                <td>${data.metrics.http_req_duration.values.avg.toFixed(2)}ms</td>
                <td>-</td>
                <td>-</td>
            </tr>
            <tr>
                <td>95th Percentile</td>
                <td>${data.metrics.http_req_duration.values['p(95)'].toFixed(2)}ms</td>
                <td>&lt; 500ms</td>
                <td class="${data.metrics.http_req_duration.values['p(95)'] < 500 ? 'pass' : 'fail'}">
                    ${data.metrics.http_req_duration.values['p(95)'] < 500 ? 'PASS' : 'FAIL'}
                </td>
            </tr>
        </table>
    </div>
    <div class="metric">
        <h2>Error Rates</h2>
        <table>
            <tr>
                <th>Metric</th>
                <th>Value</th>
                <th>Threshold</th>
                <th>Status</th>
            </tr>
            <tr>
                <td>HTTP Errors</td>
                <td>${(data.metrics.http_req_failed.values.rate * 100).toFixed(2)}%</td>
                <td>&lt; 10%</td>
                <td class="${data.metrics.http_req_failed.values.rate < 0.1 ? 'pass' : 'fail'}">
                    ${data.metrics.http_req_failed.values.rate < 0.1 ? 'PASS' : 'FAIL'}
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
`;
}