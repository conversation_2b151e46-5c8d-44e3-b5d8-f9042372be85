# LegacyBridge Phase 3 Deployment Guide

This guide provides comprehensive instructions for deploying the LegacyBridge microservices architecture in production environments.

## Prerequisites

### System Requirements
- **CPU**: 4+ cores recommended
- **Memory**: 8GB+ RAM recommended
- **Storage**: 50GB+ available space
- **Network**: Stable internet connection for container pulls

### Software Dependencies
- Docker 20.10+
- Docker Compose 2.0+
- Kubernetes 1.24+ (for K8s deployment)
- kubectl configured with cluster access
- Helm 3.0+ (optional, for package management)

## Infrastructure Setup

### 1. Start Core Infrastructure

```bash
# Clone the repository
git clone https://github.com/Beaulewis1977/legacy-bridge.git
cd legacy-bridge/legacybridge/services

# Start infrastructure services
./scripts/start-infrastructure.sh

# Wait for services to be ready (check logs)
docker-compose -f docker-compose.infrastructure.yml logs -f
```

### 2. Configure API Gateway

```bash
# Configure Kong API Gateway
./scripts/setup-kong.sh

# Verify Kong configuration
curl http://localhost:8001/services
```

### 3. Verify Infrastructure Health

```bash
# Check PostgreSQL
docker exec legacybridge-postgres pg_isready -U postgres

# Check Redis
docker exec legacybridge-redis redis-cli ping

# Check MinIO
curl http://localhost:9000/minio/health/live

# Check Kong
curl http://localhost:8001/status
```

## Service Deployment

### Option 1: Development Deployment (Cargo)

```bash
# Terminal 1: Auth Service
cd auth-service
cp .env.example .env
# Edit .env with your configuration
cargo run

# Terminal 2: Conversion Service
cd conversion-service
cp .env.example .env
cargo run

# Terminal 3: File Service
cd file-service
cp .env.example .env
cargo run

# Terminal 4: Job Service
cd job-service
cp .env.example .env
cargo run
```

### Option 2: Docker Deployment

```bash
# Build all service images
docker build -t legacybridge/auth-service:latest ./auth-service
docker build -t legacybridge/conversion-service:latest ./conversion-service
docker build -t legacybridge/file-service:latest ./file-service
docker build -t legacybridge/job-service:latest ./job-service

# Create service network
docker network create legacybridge-services

# Run services
docker run -d --name auth-service \
  --network legacybridge-services \
  -p 3001:3001 \
  -e LEGACYBRIDGE_DATABASE_URL=*********************************************************/legacybridge \
  -e LEGACYBRIDGE_REDIS_URL=redis://legacybridge-redis:6379 \
  legacybridge/auth-service:latest

# Repeat for other services...
```

### Option 3: Kubernetes Deployment

```bash
# Create namespace
kubectl create namespace legacybridge

# Apply configurations
kubectl apply -f k8s/configmaps/ -n legacybridge
kubectl apply -f k8s/secrets/ -n legacybridge
kubectl apply -f k8s/services/ -n legacybridge
kubectl apply -f k8s/deployments/ -n legacybridge

# Check deployment status
kubectl get pods -n legacybridge
kubectl get services -n legacybridge
```

## Configuration

### Environment Variables

Critical production settings that must be changed:

```bash
# Database
LEGACYBRIDGE_DATABASE_URL=************************************/legacybridge

# Redis
LEGACYBRIDGE_REDIS_URL=redis://host:6379

# JWT Secret (MUST CHANGE IN PRODUCTION)
LEGACYBRIDGE_AUTH_JWT_SECRET=your-super-secret-jwt-key-256-bits-minimum

# S3 Storage
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
S3_BUCKET_NAME=your-bucket-name

# Service Environment
LEGACYBRIDGE_SERVICE_ENVIRONMENT=production
```

### Security Configuration

```bash
# Generate secure JWT secret
openssl rand -base64 32

# Create strong database passwords
openssl rand -base64 24

# Configure TLS certificates (recommended)
# Place certificates in appropriate directories
```

## Health Checks and Verification

### Service Health Endpoints

```bash
# Direct service health checks
curl http://localhost:3001/health  # Auth Service
curl http://localhost:3002/health  # Conversion Service
curl http://localhost:3003/health  # File Service
curl http://localhost:3004/health  # Job Service

# Through API Gateway
curl http://localhost:8000/auth/health
curl http://localhost:8000/convert/health
curl http://localhost:8000/files/health
curl http://localhost:8000/jobs/health
```

### Functional Testing

```bash
# Test authentication
curl -X POST http://localhost:8000/auth/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# Test file upload
curl -X POST http://localhost:8000/files/api/v1/files \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@test.txt"

# Test conversion
curl -X POST http://localhost:8000/convert/api/v1/convert \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"input_format":"txt","output_format":"md","content":"SGVsbG8gV29ybGQ="}'
```

## Monitoring Setup

### Prometheus Metrics

```bash
# Access Prometheus
open http://localhost:9090

# Check targets
curl http://localhost:9090/api/v1/targets

# Service metrics endpoints
curl http://localhost:9091/metrics  # Auth Service
curl http://localhost:9092/metrics  # Conversion Service
curl http://localhost:9093/metrics  # File Service
curl http://localhost:9094/metrics  # Job Service
```

### Grafana Dashboards

```bash
# Access Grafana
open http://localhost:3000
# Login: admin/admin

# Import dashboards from monitoring/grafana/dashboards/
```

### Logging

```bash
# Access Kibana
open http://localhost:5601

# View service logs
docker logs legacybridge-auth-service
docker logs legacybridge-conversion-service
```

## Scaling and Performance

### Horizontal Scaling

```bash
# Scale services in Kubernetes
kubectl scale deployment auth-service --replicas=3 -n legacybridge
kubectl scale deployment conversion-service --replicas=5 -n legacybridge

# Scale with Docker Compose
docker-compose -f docker-compose.services.yml up --scale auth-service=3
```

### Performance Tuning

```bash
# Database connection pooling
LEGACYBRIDGE_DATABASE_MAX_CONNECTIONS=50
LEGACYBRIDGE_DATABASE_MIN_CONNECTIONS=10

# Redis connection pooling
LEGACYBRIDGE_REDIS_MAX_CONNECTIONS=20

# File upload limits
MAX_FILE_SIZE_MB=500

# Conversion timeouts
CONVERSION_TIMEOUT_SECONDS=600
```

## Backup and Recovery

### Database Backup

```bash
# Create backup
docker exec legacybridge-postgres pg_dump -U postgres legacybridge > backup.sql

# Restore backup
docker exec -i legacybridge-postgres psql -U postgres legacybridge < backup.sql
```

### File Storage Backup

```bash
# Backup MinIO data
docker exec legacybridge-minio mc mirror /data /backup

# S3 backup (if using AWS)
aws s3 sync s3://your-bucket s3://your-backup-bucket
```

## Troubleshooting

### Common Issues

1. **Services can't connect to database**
   ```bash
   # Check database connectivity
   docker exec legacybridge-postgres pg_isready -U postgres
   
   # Check network connectivity
   docker network ls
   docker network inspect legacybridge-network
   ```

2. **Authentication failures**
   ```bash
   # Verify JWT secret consistency
   echo $LEGACYBRIDGE_AUTH_JWT_SECRET
   
   # Check Redis session storage
   docker exec legacybridge-redis redis-cli keys "session:*"
   ```

3. **File upload failures**
   ```bash
   # Check S3 connectivity
   docker exec legacybridge-minio mc admin info local
   
   # Verify bucket permissions
   docker exec legacybridge-minio mc ls local/legacybridge-files
   ```

### Log Analysis

```bash
# Service-specific logs
docker logs --tail=100 legacybridge-auth-service
docker logs --tail=100 legacybridge-conversion-service

# Infrastructure logs
docker logs --tail=100 legacybridge-postgres
docker logs --tail=100 legacybridge-redis
```

## Security Hardening

### Production Security Checklist

- [ ] Change all default passwords
- [ ] Generate secure JWT secrets
- [ ] Configure TLS/SSL certificates
- [ ] Set up firewall rules
- [ ] Enable audit logging
- [ ] Configure rate limiting
- [ ] Set up monitoring alerts
- [ ] Regular security updates

### Network Security

```bash
# Configure Kong rate limiting
curl -X POST http://localhost:8001/plugins \
  --data "name=rate-limiting" \
  --data "config.minute=100"

# Configure CORS
curl -X POST http://localhost:8001/plugins \
  --data "name=cors" \
  --data "config.origins=https://yourdomain.com"
```

## Maintenance

### Regular Maintenance Tasks

1. **Database maintenance**
   ```bash
   # Vacuum and analyze
   docker exec legacybridge-postgres psql -U postgres -d legacybridge -c "VACUUM ANALYZE;"
   ```

2. **Log rotation**
   ```bash
   # Configure log rotation for Docker
   # Edit /etc/docker/daemon.json
   ```

3. **Security updates**
   ```bash
   # Update container images
   docker pull postgres:15-alpine
   docker pull redis:7-alpine
   ```

This deployment guide provides a comprehensive foundation for running LegacyBridge in production. Adjust configurations based on your specific infrastructure requirements and security policies.
