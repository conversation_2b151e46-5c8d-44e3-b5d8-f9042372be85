{#
Copyright 2024 LegacyBridge
Memorystore Redis Template for Google Cloud Deployment Manager
#}

resources:
# Memorystore Redis Instance
- name: {{ env["name"] }}-redis
  type: redis.v1.instance
  properties:
    parent: projects/{{ env["project"] }}/locations/{{ properties["region"] }}
    instanceId: {{ env["name"] }}
    tier: {{ properties["tier"] }}
    memorySizeGb: {{ properties["memorySizeGb"] }}
    redisVersion: {{ properties["redisVersion"] }}
    authorizedNetwork: {{ properties["authorizedNetwork"] }}
    
    # Redis Configuration
    redisConfigs:
      maxmemory-policy: allkeys-lru
      timeout: "300"
      tcp-keepalive: "60"
      
    # Maintenance Policy
    maintenancePolicy:
      weeklyMaintenanceWindow:
      - day: SUNDAY
        startTime:
          hours: 3
          minutes: 0
        duration: PT1H
    
    # Labels
    labels:
      environment: production
      application: legacybridge

outputs:
- name: host
  value: $(ref.{{ env["name"] }}-redis.host)
- name: port
  value: $(ref.{{ env["name"] }}-redis.port)
- name: currentLocationId
  value: $(ref.{{ env["name"] }}-redis.currentLocationId)
- name: redisVersion
  value: $(ref.{{ env["name"] }}-redis.redisVersion)