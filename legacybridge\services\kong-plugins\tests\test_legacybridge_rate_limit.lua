-- Enterprise-grade test suite for LegacyBridge rate limiting plugin
-- Tests enterprise-scale rate limiting with Redis backend

local helpers = require "spec.helpers"
local cjson = require "cjson"

local PLUGIN_NAME = "legacybridge-rate-limit"

describe(PLUGIN_NAME .. ": (access)", function()
  local client
  local admin_client
  local redis_client

  lazy_setup(function()
    local bp = helpers.get_db_utils(nil, nil, { PLUGIN_NAME })

    -- Create test service
    local service = bp.services:insert({
      protocol = "http",
      host = "httpbin.org",
      port = 80,
      path = "/anything",
      name = "test-service"
    })

    -- Create test route
    local route = bp.routes:insert({
      hosts = { "test.com" },
      service = service,
    })

    -- Add plugin to route with enterprise configuration
    bp.plugins:insert({
      name = PLUGIN_NAME,
      route = { id = route.id },
      config = {
        namespace = "legacybridge-test",
        redis_host = "localhost",
        redis_port = 6379,
        redis_timeout = 1000,
        default_requests_per_minute = 10,  -- Low for testing
        default_requests_per_hour = 100,
        default_requests_per_day = 1000,
        service_limits = {
          ["test-service"] = {
            requests_per_minute = 5,  -- Even lower for specific testing
            requests_per_hour = 50,
            requests_per_day = 500
          }
        },
        admin_limit_multiplier = 10,
        premium_limit_multiplier = 5,
        pro_limit_multiplier = 3,
        enable_user_tier_limits = true
      },
    })

    -- Start Kong with Redis
    assert(helpers.start_kong({
      database = "off",
      nginx_conf = "spec/fixtures/custom_nginx.template",
      plugins = "bundled," .. PLUGIN_NAME,
    }))

    client = helpers.proxy_client()
    admin_client = helpers.admin_client()
    
    -- Initialize Redis client for testing
    local redis = require "resty.redis"
    redis_client = redis:new()
    redis_client:set_timeout(1000)
    assert(redis_client:connect("localhost", 6379))
  end)

  lazy_teardown(function()
    if redis_client then
      redis_client:flushall()  -- Clean up test data
      redis_client:close()
    end
    if client then
      client:close()
    end
    if admin_client then
      admin_client:close()
    end
    helpers.stop_kong()
  end)

  describe("Enterprise Rate Limiting Tests", function()
    
    before_each(function()
      -- Clear Redis between tests
      if redis_client then
        redis_client:flushall()
      end
    end)

    it("should allow requests within rate limit", function()
      for i = 1, 3 do  -- Well within limit of 5/minute
        local res = assert(client:send({
          method = "GET",
          path = "/test",
          headers = {
            host = "test.com",
            ["X-User-ID"] = "test-user-1"
          }
        }))
        assert.response(res).has.status(200)
        assert.response(res).has.header("X-RateLimit-Limit-Minute")
        assert.response(res).has.header("X-RateLimit-Remaining-Minute")
        assert.response(res).has.header("X-RateLimit-Reset-Minute")
        
        -- Verify rate limit headers
        assert.equal("5", res.headers["X-RateLimit-Limit-Minute"])
        local remaining = tonumber(res.headers["X-RateLimit-Remaining-Minute"])
        assert.is_true(remaining >= 0)
      end
    end)

    it("should enforce per-minute rate limits", function()
      local user_id = "test-user-minute"
      
      -- Send requests up to the limit
      for i = 1, 5 do
        local res = assert(client:send({
          method = "GET",
          path = "/test",
          headers = {
            host = "test.com",
            ["X-User-ID"] = user_id
          }
        }))
        assert.response(res).has.status(200)
      end
      
      -- Next request should be rate limited
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = {
          host = "test.com",
          ["X-User-ID"] = user_id
        }
      }))
      assert.response(res).has.status(429)
      local body = assert.response(res).has.jsonbody()
      assert.equal("rate_limit_exceeded", body.error)
      assert.equal("Too many requests per minute", body.message)
      assert.equal(5, body.limit)
      assert.equal("minute", body.window)
    end)

    it("should apply different limits for user tiers", function()
      -- Test admin user (10x multiplier)
      local admin_user = "admin-user"
      for i = 1, 25 do  -- 5 * 10 = 50 requests allowed for admin
        local res = assert(client:send({
          method = "GET",
          path = "/test",
          headers = {
            host = "test.com",
            ["X-User-ID"] = admin_user,
            ["X-User-Roles"] = "admin,user"
          }
        }))
        if i <= 50 then
          assert.response(res).has.status(200)
        else
          assert.response(res).has.status(429)
          break
        end
      end
      
      -- Test premium user (5x multiplier)
      local premium_user = "premium-user"
      for i = 1, 15 do  -- 5 * 5 = 25 requests allowed for premium
        local res = assert(client:send({
          method = "GET",
          path = "/test",
          headers = {
            host = "test.com",
            ["X-User-ID"] = premium_user,
            ["X-User-Roles"] = "premium,user"
          }
        }))
        if i <= 25 then
          assert.response(res).has.status(200)
        else
          assert.response(res).has.status(429)
          break
        end
      end
    end)

    it("should handle IP-based rate limiting when no user ID", function()
      -- Requests without X-User-ID should use IP-based limiting
      for i = 1, 5 do
        local res = assert(client:send({
          method = "GET",
          path = "/test",
          headers = {
            host = "test.com",
            ["X-Forwarded-For"] = "*************"
          }
        }))
        assert.response(res).has.status(200)
      end
      
      -- 6th request should be rate limited
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = {
          host = "test.com",
          ["X-Forwarded-For"] = "*************"
        }
      }))
      assert.response(res).has.status(429)
    end)

    it("should handle Redis connection failures gracefully", function()
      -- Simulate Redis failure by using wrong port
      -- In real test, we'd mock Redis to fail
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = {
          host = "test.com",
          ["X-User-ID"] = "test-user-redis-fail"
        }
      }))
      -- Should fail open (allow request) when Redis is unavailable
      assert.response(res).has.status(200)
    end)

    it("should provide accurate rate limit headers", function()
      local user_id = "test-user-headers"
      
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = {
          host = "test.com",
          ["X-User-ID"] = user_id
        }
      }))
      
      assert.response(res).has.status(200)
      assert.response(res).has.header("X-RateLimit-Limit-Minute")
      assert.response(res).has.header("X-RateLimit-Remaining-Minute")
      assert.response(res).has.header("X-RateLimit-Reset-Minute")
      assert.response(res).has.header("X-RateLimit-Limit-Hour")
      assert.response(res).has.header("X-RateLimit-Remaining-Hour")
      assert.response(res).has.header("X-RateLimit-Reset-Hour")
      
      -- Verify header values
      assert.equal("5", res.headers["X-RateLimit-Limit-Minute"])
      assert.equal("50", res.headers["X-RateLimit-Limit-Hour"])
      
      local remaining_minute = tonumber(res.headers["X-RateLimit-Remaining-Minute"])
      local remaining_hour = tonumber(res.headers["X-RateLimit-Remaining-Hour"])
      assert.is_true(remaining_minute >= 0 and remaining_minute <= 5)
      assert.is_true(remaining_hour >= 0 and remaining_hour <= 50)
    end)

    it("should handle concurrent requests safely", function()
      local user_id = "test-user-concurrent"
      local success_count = 0
      local rate_limited_count = 0
      
      -- Send 10 concurrent requests (more than limit of 5)
      for i = 1, 10 do
        local res = assert(client:send({
          method = "GET",
          path = "/test",
          headers = {
            host = "test.com",
            ["X-User-ID"] = user_id,
            ["X-Request-ID"] = "concurrent-" .. i
          }
        }))
        
        if res.status == 200 then
          success_count = success_count + 1
        elseif res.status == 429 then
          rate_limited_count = rate_limited_count + 1
        end
      end
      
      -- Should have exactly 5 successful requests and 5 rate limited
      assert.equal(5, success_count)
      assert.equal(5, rate_limited_count)
    end)

    it("should reset rate limits after time window", function()
      local user_id = "test-user-reset"
      
      -- Use up the rate limit
      for i = 1, 5 do
        local res = assert(client:send({
          method = "GET",
          path = "/test",
          headers = {
            host = "test.com",
            ["X-User-ID"] = user_id
          }
        }))
        assert.response(res).has.status(200)
      end
      
      -- Next request should be rate limited
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = {
          host = "test.com",
          ["X-User-ID"] = user_id
        }
      }))
      assert.response(res).has.status(429)
      
      -- Wait for next minute window (in real test, we'd mock time)
      -- For now, just verify the rate limit structure is correct
      local body = assert.response(res).has.jsonbody()
      assert.is_number(body.reset_time)
      assert.is_true(body.reset_time > ngx.time())
    end)

  end)

  describe("Enterprise Performance Tests", function()
    
    it("should handle high-volume requests efficiently", function()
      local start_time = ngx.now()
      local request_count = 100
      local user_base = "perf-user-"
      
      for i = 1, request_count do
        -- Use different users to avoid rate limiting
        local user_id = user_base .. (i % 20)  -- 20 different users
        local res = assert(client:send({
          method = "GET",
          path = "/test",
          headers = {
            host = "test.com",
            ["X-User-ID"] = user_id
          }
        }))
        -- Most should succeed (5 requests per user)
        assert.is_true(res.status == 200 or res.status == 429)
      end
      
      local end_time = ngx.now()
      local duration = end_time - start_time
      
      -- Should complete within reasonable time for enterprise performance
      assert.is_true(duration < 10.0, "Performance test took too long: " .. duration .. "s")
    end)

    it("should maintain accuracy under load", function()
      local user_id = "load-test-user"
      local allowed_count = 0
      local denied_count = 0
      
      -- Send exactly 10 requests (double the limit)
      for i = 1, 10 do
        local res = assert(client:send({
          method = "GET",
          path = "/test",
          headers = {
            host = "test.com",
            ["X-User-ID"] = user_id
          }
        }))
        
        if res.status == 200 then
          allowed_count = allowed_count + 1
        elseif res.status == 429 then
          denied_count = denied_count + 1
        end
      end
      
      -- Should have exactly 5 allowed and 5 denied
      assert.equal(5, allowed_count)
      assert.equal(5, denied_count)
    end)

  end)

  describe("Enterprise Security Tests", function()
    
    it("should prevent rate limit bypass attempts", function()
      local user_id = "bypass-test-user"
      
      -- Try various bypass techniques
      local bypass_attempts = {
        { ["X-User-ID"] = user_id, ["X-Forwarded-For"] = "*******" },
        { ["X-User-ID"] = user_id, ["X-Real-IP"] = "*******" },
        { ["X-User-ID"] = user_id .. " ", ["X-Forwarded-For"] = "*******" },  -- Space padding
        { ["X-User-ID"] = string.upper(user_id) },  -- Case variation
      }
      
      local total_allowed = 0
      
      for _, headers in ipairs(bypass_attempts) do
        for i = 1, 6 do  -- Try to exceed limit
          headers.host = "test.com"
          local res = assert(client:send({
            method = "GET",
            path = "/test",
            headers = headers
          }))
          
          if res.status == 200 then
            total_allowed = total_allowed + 1
          end
        end
      end
      
      -- Should not allow more than the legitimate limit
      assert.is_true(total_allowed <= 20)  -- 5 per attempt max
    end)

    it("should handle malicious header injection", function()
      local malicious_headers = {
        ["X-User-ID"] = "test\r\nX-Injected: malicious",
        ["X-User-Roles"] = "admin\r\nX-Evil: header",
      }
      
      local res = assert(client:send({
        method = "GET",
        path = "/test",
        headers = malicious_headers
      }))
      
      -- Should handle gracefully without crashing
      assert.is_true(res.status == 200 or res.status == 429 or res.status == 400)
    end)

  end)

end)
