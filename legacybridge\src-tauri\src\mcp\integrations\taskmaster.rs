// TaskMaster AI MCP Integration for LegacyBridge
// Leverages task management for complex document processing workflows

use serde::{Serialize, Deserialize};
use crate::mcp::client::McpClient;
use crate::mcp::types::IntegrationError;
use serde_json::{json, Value as JsonValue};

pub struct TaskMasterIntegration {
    client: McpClient,
    project_id: String,
}

impl TaskMasterIntegration {
    pub async fn new() -> Result<Self, IntegrationError> {
        let client = McpClient::connect("taskmaster-ai").await?;
        
        // Initialize LegacyBridge project in TaskMaster
        let project_id = Self::initialize_project(&client).await?;
        
        Ok(Self { client, project_id })
    }
    
    /// Create document processing workflow tasks
    pub async fn create_conversion_workflow(
        &self,
        files: Vec<String>,
        target_format: String,
        options: WorkflowOptions,
    ) -> Result<String, IntegrationError> {
        // Create main workflow task
        let workflow_task = self.client.call_tool("add_task", json!({
            "projectRoot": self.project_id,
            "prompt": format!(
                "Document Conversion Workflow: Convert {} files to {} format",
                files.len(),
                target_format
            ),
            "priority": "high",
            "research": true
        })).await?;
        
        let main_task_id = workflow_task["task_id"].as_str()
            .ok_or_else(|| IntegrationError::InvalidResponse("Missing task_id".to_string()))?;
        
        // Create subtasks for each phase
        let phases = vec![
            ("Format Detection", "Detect and analyze input file formats"),
            ("Validation", "Validate file integrity and compatibility"),
            ("Conversion", "Perform batch document conversion"),
            ("Quality Assurance", "Verify conversion quality and completeness"),
            ("Delivery", "Package and deliver converted files"),
        ];
        
        for (phase_name, description) in phases {
            self.client.call_tool("add_subtask", json!({
                "id": main_task_id,
                "projectRoot": self.project_id,
                "title": phase_name,
                "description": description,
                "status": "pending"
            })).await?;
        }
        
        // Create individual file tasks if detailed tracking requested
        if options.detailed_tracking {
            for (index, file) in files.iter().enumerate() {
                self.client.call_tool("add_subtask", json!({
                    "id": main_task_id,
                    "projectRoot": self.project_id,
                    "title": format!("Convert File {}: {}", index + 1, file),
                    "description": format!("Convert {} to {}", file, target_format),
                    "status": "pending"
                })).await?;
            }
        }
        
        Ok(main_task_id.to_string())
    }
    
    /// Update task progress during conversion
    pub async fn update_conversion_progress(
        &self,
        task_id: &str,
        progress: ConversionProgress,
    ) -> Result<(), IntegrationError> {
        let status = match progress.stage {
            ConversionStage::Detection => "in-progress",
            ConversionStage::Validation => "in-progress", 
            ConversionStage::Conversion => "in-progress",
            ConversionStage::QualityCheck => "review",
            ConversionStage::Completed => "done",
            ConversionStage::Failed => "cancelled",
        };
        
        self.client.call_tool("update_task", json!({
            "id": task_id,
            "projectRoot": self.project_id,
            "prompt": format!(
                "Progress Update: {} ({:.1}% complete) - {} files processed, {} successful, {} failed",
                progress.stage.description(),
                progress.completion_percentage,
                progress.files_processed,
                progress.successful_conversions,
                progress.failed_conversions
            )
        })).await?;
        
        self.client.call_tool("set_task_status", json!({
            "id": task_id,
            "status": status,
            "projectRoot": self.project_id
        })).await?;
        
        Ok(())
    }
    
    /// Get workflow status and metrics
    pub async fn get_workflow_status(&self, workflow_id: &str) -> Result<WorkflowStatus, IntegrationError> {
        let tasks = self.client.call_tool("get_tasks", json!({
            "projectRoot": self.project_id,
            "parentId": workflow_id
        })).await?;
        
        let task_list = tasks["tasks"].as_array()
            .ok_or_else(|| IntegrationError::InvalidResponse("Missing tasks array".to_string()))?;
        
        let mut total_tasks = 0;
        let mut completed_tasks = 0;
        let mut failed_tasks = 0;
        let mut in_progress_tasks = 0;
        
        for task in task_list {
            total_tasks += 1;
            match task["status"].as_str() {
                Some("done") => completed_tasks += 1,
                Some("cancelled") => failed_tasks += 1,
                Some("in-progress") => in_progress_tasks += 1,
                _ => {}
            }
        }
        
        Ok(WorkflowStatus {
            workflow_id: workflow_id.to_string(),
            total_tasks,
            completed_tasks,
            failed_tasks,
            in_progress_tasks,
            completion_percentage: if total_tasks > 0 {
                (completed_tasks as f64 / total_tasks as f64) * 100.0
            } else {
                0.0
            },
        })
    }
    
    /// Archive completed workflow
    pub async fn archive_workflow(&self, workflow_id: &str) -> Result<(), IntegrationError> {
        self.client.call_tool("update_task", json!({
            "id": workflow_id,
            "projectRoot": self.project_id,
            "archived": true
        })).await?;
        
        Ok(())
    }
    
    async fn initialize_project(client: &McpClient) -> Result<String, IntegrationError> {
        // Check if LegacyBridge project already exists
        let existing_projects = client.call_tool("get_tasks", json!({
            "projectRoot": "/tmp/legacybridge-taskmaster",
        })).await;
        
        if existing_projects.is_err() {
            // Initialize new project
            client.call_tool("initialize_project", json!({
                "projectRoot": "/tmp/legacybridge-taskmaster",
                "rules": ["cursor", "cline"],
                "storeTasksInGit": false,
                "skipInstall": true
            })).await?;
        }
        
        Ok("/tmp/legacybridge-taskmaster".to_string())
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkflowOptions {
    pub detailed_tracking: bool,
    pub quality_threshold: f64,
    pub auto_retry_failures: bool,
    pub notification_webhooks: Vec<String>,
}

impl Default for WorkflowOptions {
    fn default() -> Self {
        Self {
            detailed_tracking: true,
            quality_threshold: 0.9,
            auto_retry_failures: true,
            notification_webhooks: vec![],
        }
    }
}

#[derive(Debug, Clone)]
pub struct ConversionProgress {
    pub stage: ConversionStage,
    pub completion_percentage: f64,
    pub files_processed: usize,
    pub successful_conversions: usize,
    pub failed_conversions: usize,
    pub current_file: Option<String>,
}

#[derive(Debug, Clone)]
pub enum ConversionStage {
    Detection,
    Validation,
    Conversion,
    QualityCheck,
    Completed,
    Failed,
}

impl ConversionStage {
    pub fn description(&self) -> &str {
        match self {
            Self::Detection => "Detecting file formats",
            Self::Validation => "Validating file integrity",
            Self::Conversion => "Converting documents",
            Self::QualityCheck => "Quality assurance",
            Self::Completed => "Conversion completed",
            Self::Failed => "Conversion failed",
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkflowStatus {
    pub workflow_id: String,
    pub total_tasks: usize,
    pub completed_tasks: usize,
    pub failed_tasks: usize,
    pub in_progress_tasks: usize,
    pub completion_percentage: f64,
}