[package]
name = "auth-service"
version = "0.1.0"
edition = "2021"
description = "Authentication service for LegacyBridge microservices architecture"

[dependencies]
# Shared library
legacybridge-shared = { path = "../shared" }

# Web framework
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace", "timeout", "request-id"] }
hyper = "1.0"

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json", "migrate"] }

# Redis
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }

# UUID and time
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-opentelemetry = "0.21"
opentelemetry = "0.20"
opentelemetry-jaeger = "0.19"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Validation
validator = { version = "0.16", features = ["derive"] }

# JWT and authentication
jsonwebtoken = "9.0"
argon2 = "0.5"
rand = "0.8"

# Configuration
config = "0.13"
dotenvy = "0.15"

# Metrics
prometheus = "0.13"

# HTTP client for service communication
reqwest = { version = "0.11", features = ["json"] }

[dev-dependencies]
tokio-test = "0.4"
testcontainers = "0.15"
axum-test = "14.0"
