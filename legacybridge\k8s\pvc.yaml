apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: legacybridge-data
  namespace: legacybridge
  labels:
    app: legacybridge
    component: storage
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: standard
  resources:
    requests:
      storage: 100Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: legacybridge-cache
  namespace: legacybridge
  labels:
    app: legacybridge
    component: cache-storage
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: fast-ssd
  resources:
    requests:
      storage: 50Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: legacybridge-logs
  namespace: legacybridge
  labels:
    app: legacybridge
    component: logging
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: standard
  resources:
    requests:
      storage: 20Gi