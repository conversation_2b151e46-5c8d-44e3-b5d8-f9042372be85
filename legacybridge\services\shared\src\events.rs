// Event system for inter-service communication
use crate::error::{ServiceError, ServiceResult};
use crate::types::DomainEvent;
use redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::{error, info, warn};

pub struct EventPublisher {
    redis_client: redis::Client,
}

impl EventPublisher {
    pub fn new(redis_url: &str) -> ServiceResult<Self> {
        let redis_client = redis::Client::open(redis_url)?;
        Ok(Self { redis_client })
    }

    pub async fn publish(&self, event: DomainEvent) -> ServiceResult<()> {
        let mut conn = self.redis_client.get_async_connection().await?;
        
        let event_data = serde_json::to_string(&event)?;
        
        let channel = match &event {
            DomainEvent::UserAuthenticated { .. } => "auth_events",
            DomainEvent::ConversionStarted { .. } | DomainEvent::ConversionCompleted { .. } => "conversion_events",
            DomainEvent::FileUploaded { .. } => "file_events",
        };
        
        conn.publish(channel, event_data).await?;
        
        info!(
            event_type = std::any::type_name::<DomainEvent>(),
            channel = channel,
            "Published domain event"
        );
        
        Ok(())
    }

    pub async fn publish_to_channel(&self, channel: &str, message: &str) -> ServiceResult<()> {
        let mut conn = self.redis_client.get_async_connection().await?;
        conn.publish(channel, message).await?;
        Ok(())
    }
}

pub struct EventSubscriber {
    redis_client: redis::Client,
}

impl EventSubscriber {
    pub fn new(redis_url: &str) -> ServiceResult<Self> {
        let redis_client = redis::Client::open(redis_url)?;
        Ok(Self { redis_client })
    }

    pub async fn subscribe<F>(&self, channels: &[&str], handler: F) -> ServiceResult<()> 
    where
        F: Fn(DomainEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>> + Send + Sync + 'static,
    {
        let mut pubsub = self.redis_client.get_async_connection().await?.into_pubsub();
        
        for channel in channels {
            pubsub.subscribe(channel).await?;
            info!(channel = channel, "Subscribed to event channel");
        }

        let mut stream = pubsub.on_message();
        
        while let Some(msg) = stream.next().await {
            let payload: String = msg.get_payload()?;
            
            match serde_json::from_str::<DomainEvent>(&payload) {
                Ok(event) => {
                    if let Err(e) = handler(event) {
                        error!(error = %e, "Event handler error");
                    }
                }
                Err(e) => {
                    warn!(error = %e, payload = payload, "Failed to deserialize event");
                }
            }
        }
        
        Ok(())
    }

    pub async fn subscribe_raw<F>(&self, channels: &[&str], handler: F) -> ServiceResult<()>
    where
        F: Fn(String, String) -> Result<(), Box<dyn std::error::Error + Send + Sync>> + Send + Sync + 'static,
    {
        let mut pubsub = self.redis_client.get_async_connection().await?.into_pubsub();
        
        for channel in channels {
            pubsub.subscribe(channel).await?;
            info!(channel = channel, "Subscribed to raw channel");
        }

        let mut stream = pubsub.on_message();
        
        while let Some(msg) = stream.next().await {
            let channel: String = msg.get_channel_name().to_string();
            let payload: String = msg.get_payload()?;
            
            if let Err(e) = handler(channel, payload) {
                error!(error = %e, "Raw event handler error");
            }
        }
        
        Ok(())
    }
}

// Event bus for local event handling within a service
pub struct EventBus {
    sender: mpsc::UnboundedSender<DomainEvent>,
}

impl EventBus {
    pub fn new() -> (Self, mpsc::UnboundedReceiver<DomainEvent>) {
        let (sender, receiver) = mpsc::unbounded_channel();
        (Self { sender }, receiver)
    }

    pub fn publish(&self, event: DomainEvent) -> ServiceResult<()> {
        self.sender.send(event)
            .map_err(|e| ServiceError::Internal(format!("Failed to publish local event: {}", e)))?;
        Ok(())
    }
}

// Event handler trait for implementing event processors
pub trait EventHandler: Send + Sync {
    async fn handle(&self, event: DomainEvent) -> ServiceResult<()>;
}

// Aggregate event handler that can route events to multiple handlers
pub struct AggregateEventHandler {
    handlers: Vec<Arc<dyn EventHandler>>,
}

impl AggregateEventHandler {
    pub fn new() -> Self {
        Self {
            handlers: Vec::new(),
        }
    }

    pub fn add_handler(&mut self, handler: Arc<dyn EventHandler>) {
        self.handlers.push(handler);
    }

    pub async fn handle_event(&self, event: DomainEvent) -> ServiceResult<()> {
        for handler in &self.handlers {
            if let Err(e) = handler.handle(event.clone()).await {
                error!(
                    error = %e,
                    handler = std::any::type_name_of_val(handler.as_ref()),
                    "Event handler failed"
                );
            }
        }
        Ok(())
    }
}

// Specific event handlers for common scenarios
pub struct LoggingEventHandler;

impl EventHandler for LoggingEventHandler {
    async fn handle(&self, event: DomainEvent) -> ServiceResult<()> {
        match &event {
            DomainEvent::UserAuthenticated { user_id, timestamp } => {
                info!(user_id = %user_id, timestamp = %timestamp, "User authenticated");
            }
            DomainEvent::ConversionStarted { job_id, user_id, input_format, output_format, timestamp } => {
                info!(
                    job_id = job_id,
                    user_id = %user_id,
                    input_format = input_format,
                    output_format = output_format,
                    timestamp = %timestamp,
                    "Conversion started"
                );
            }
            DomainEvent::ConversionCompleted { job_id, user_id, success, processing_time_ms, timestamp } => {
                info!(
                    job_id = job_id,
                    user_id = %user_id,
                    success = success,
                    processing_time_ms = processing_time_ms,
                    timestamp = %timestamp,
                    "Conversion completed"
                );
            }
            DomainEvent::FileUploaded { file_id, user_id, file_size, timestamp } => {
                info!(
                    file_id = %file_id,
                    user_id = %user_id,
                    file_size = file_size,
                    timestamp = %timestamp,
                    "File uploaded"
                );
            }
        }
        Ok(())
    }
}

// Metrics event handler for collecting metrics from events
pub struct MetricsEventHandler {
    metrics: Arc<crate::metrics::ServiceMetrics>,
}

impl MetricsEventHandler {
    pub fn new(metrics: Arc<crate::metrics::ServiceMetrics>) -> Self {
        Self { metrics }
    }
}

impl EventHandler for MetricsEventHandler {
    async fn handle(&self, event: DomainEvent) -> ServiceResult<()> {
        match &event {
            DomainEvent::UserAuthenticated { .. } => {
                self.metrics.auth_events_total.inc();
            }
            DomainEvent::ConversionStarted { .. } => {
                self.metrics.conversion_jobs_started_total.inc();
            }
            DomainEvent::ConversionCompleted { success, processing_time_ms, .. } => {
                if *success {
                    self.metrics.conversion_jobs_completed_total.inc();
                } else {
                    self.metrics.conversion_jobs_failed_total.inc();
                }
                self.metrics.conversion_duration_seconds
                    .observe(*processing_time_ms as f64 / 1000.0);
            }
            DomainEvent::FileUploaded { file_size, .. } => {
                self.metrics.files_uploaded_total.inc();
                self.metrics.file_upload_size_bytes.observe(*file_size as f64);
            }
        }
        Ok(())
    }
}
