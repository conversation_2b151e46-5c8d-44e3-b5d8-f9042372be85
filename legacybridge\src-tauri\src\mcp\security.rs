use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use jsonwebtoken::{<PERSON><PERSON><PERSON><PERSON>, Decoding<PERSON><PERSON>, Enco<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Validation};
use rustls::{Certificate, PrivateKey, ServerConfig};
use std::fs;
use std::sync::Arc;
use tokio_rustls::TlsAcceptor;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub authentication: AuthenticationConfig,
    pub authorization: AuthorizationConfig,
    pub tls: TlsConfig,
    pub rate_limiting: RateLimitingConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthenticationConfig {
    pub enabled: bool,
    pub method: AuthMethod,
    pub jwt_secret: Option<String>,
    pub api_keys: Vec<ApiKey>,
    pub session_timeout: u64, // seconds
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum AuthMethod {
    None,
    Api<PERSON>ey,
    Jwt,
    Basic,
    OAuth2,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ApiKey {
    pub id: String,
    pub key: String,
    pub name: String,
    pub permissions: Vec<String>,
    pub rate_limit: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthorizationConfig {
    pub enabled: bool,
    pub rbac: RbacConfig,
    pub policies: Vec<AuthPolicy>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RbacConfig {
    pub roles: Vec<Role>,
    pub default_role: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Role {
    pub name: String,
    pub permissions: Vec<String>,
    pub inherit_from: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthPolicy {
    pub id: String,
    pub name: String,
    pub rules: Vec<PolicyRule>,
    pub effect: PolicyEffect,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PolicyRule {
    pub resource: String,
    pub actions: Vec<String>,
    pub conditions: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum PolicyEffect {
    Allow,
    Deny,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TlsConfig {
    pub enabled: bool,
    pub cert_path: Option<PathBuf>,
    pub key_path: Option<PathBuf>,
    pub ca_path: Option<PathBuf>,
    pub verify_client: bool,
    pub min_version: TlsVersion,
    pub cipher_suites: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TlsVersion {
    #[serde(rename = "1.2")]
    Tls12,
    #[serde(rename = "1.3")]
    Tls13,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitingConfig {
    pub enabled: bool,
    pub window_size: u64, // seconds
    pub max_requests: u32,
    pub burst_size: u32,
    pub per_client: bool,
}

pub struct SecurityManager {
    config: SecurityConfig,
    jwt_encoding_key: Option<EncodingKey>,
    jwt_decoding_key: Option<DecodingKey>,
    tls_acceptor: Option<Arc<TlsAcceptor>>,
}

impl SecurityManager {
    pub fn new(config: SecurityConfig) -> Result<Self> {
        let mut manager = Self {
            config: config.clone(),
            jwt_encoding_key: None,
            jwt_decoding_key: None,
            tls_acceptor: None,
        };

        // Initialize JWT keys if JWT auth is enabled
        if config.authentication.method == AuthMethod::Jwt {
            if let Some(secret) = &config.authentication.jwt_secret {
                manager.jwt_encoding_key = Some(EncodingKey::from_secret(secret.as_bytes()));
                manager.jwt_decoding_key = Some(DecodingKey::from_secret(secret.as_bytes()));
            }
        }

        // Initialize TLS if enabled
        if config.tls.enabled {
            manager.tls_acceptor = Some(Arc::new(manager.create_tls_acceptor()?));
        }

        Ok(manager)
    }

    fn create_tls_acceptor(&self) -> Result<TlsAcceptor> {
        let cert_path = self.config.tls.cert_path.as_ref()
            .context("TLS cert_path is required when TLS is enabled")?;
        let key_path = self.config.tls.key_path.as_ref()
            .context("TLS key_path is required when TLS is enabled")?;

        let cert_pem = fs::read_to_string(cert_path)
            .context("Failed to read TLS certificate")?;
        let key_pem = fs::read_to_string(key_path)
            .context("Failed to read TLS private key")?;

        let certs = rustls_pemfile::certs(&mut cert_pem.as_bytes())
            .collect::<Result<Vec<_>, _>>()
            .context("Failed to parse certificates")?
            .into_iter()
            .map(Certificate)
            .collect();

        let keys = rustls_pemfile::pkcs8_private_keys(&mut key_pem.as_bytes())
            .collect::<Result<Vec<_>, _>>()
            .context("Failed to parse private keys")?;

        let key = PrivateKey(keys.into_iter().next()
            .context("No private key found")?.secret_bytes_der().to_vec());

        let config = ServerConfig::builder()
            .with_no_client_auth()
            .with_single_cert(certs, key)
            .context("Failed to create TLS config")?;

        Ok(TlsAcceptor::from(Arc::new(config)))
    }

    pub fn authenticate_api_key(&self, key: &str) -> Result<Option<ApiKey>> {
        if !self.config.authentication.enabled {
            return Ok(None);
        }

        for api_key in &self.config.authentication.api_keys {
            if api_key.key == key {
                return Ok(Some(api_key.clone()));
            }
        }

        Ok(None)
    }

    pub fn create_jwt_token(&self, claims: &serde_json::Value) -> Result<String> {
        let encoding_key = self.jwt_encoding_key.as_ref()
            .context("JWT encoding key not initialized")?;

        jsonwebtoken::encode(
            &Header::new(Algorithm::HS256),
            claims,
            encoding_key,
        ).context("Failed to create JWT token")
    }

    pub fn verify_jwt_token(&self, token: &str) -> Result<serde_json::Value> {
        let decoding_key = self.jwt_decoding_key.as_ref()
            .context("JWT decoding key not initialized")?;

        let validation = Validation::new(Algorithm::HS256);
        
        let token_data = jsonwebtoken::decode::<serde_json::Value>(
            token,
            decoding_key,
            &validation,
        ).context("Failed to verify JWT token")?;

        Ok(token_data.claims)
    }

    pub fn authorize(&self, role: &str, resource: &str, action: &str) -> Result<bool> {
        if !self.config.authorization.enabled {
            return Ok(true);
        }

        // Find role
        let role_config = self.config.authorization.rbac.roles.iter()
            .find(|r| r.name == role)
            .or_else(|| {
                self.config.authorization.rbac.roles.iter()
                    .find(|r| r.name == self.config.authorization.rbac.default_role)
            })
            .context("Role not found")?;

        // Check role permissions
        if role_config.permissions.contains(&format!("{}:{}", resource, action)) ||
           role_config.permissions.contains(&"*:*".to_string()) {
            return Ok(true);
        }

        // Check policies
        for policy in &self.config.authorization.policies {
            for rule in &policy.rules {
                if rule.resource == resource && rule.actions.contains(&action.to_string()) {
                    return Ok(policy.effect == PolicyEffect::Allow);
                }
            }
        }

        Ok(false)
    }

    pub fn get_tls_acceptor(&self) -> Option<Arc<TlsAcceptor>> {
        self.tls_acceptor.clone()
    }
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            authentication: AuthenticationConfig {
                enabled: false,
                method: AuthMethod::None,
                jwt_secret: None,
                api_keys: vec![],
                session_timeout: 3600,
            },
            authorization: AuthorizationConfig {
                enabled: false,
                rbac: RbacConfig {
                    roles: vec![
                        Role {
                            name: "admin".to_string(),
                            permissions: vec!["*:*".to_string()],
                            inherit_from: None,
                        },
                        Role {
                            name: "user".to_string(),
                            permissions: vec![
                                "legacy:read".to_string(),
                                "legacy:convert".to_string(),
                            ],
                            inherit_from: None,
                        },
                    ],
                    default_role: "user".to_string(),
                },
                policies: vec![],
            },
            tls: TlsConfig {
                enabled: false,
                cert_path: None,
                key_path: None,
                ca_path: None,
                verify_client: false,
                min_version: TlsVersion::Tls12,
                cipher_suites: vec![],
            },
            rate_limiting: RateLimitingConfig {
                enabled: false,
                window_size: 60,
                max_requests: 100,
                burst_size: 10,
                per_client: true,
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_security_config_default() {
        let config = SecurityConfig::default();
        assert!(!config.authentication.enabled);
        assert!(!config.authorization.enabled);
        assert!(!config.tls.enabled);
        assert!(!config.rate_limiting.enabled);
    }

    #[test]
    fn test_api_key_authentication() {
        let mut config = SecurityConfig::default();
        config.authentication.enabled = true;
        config.authentication.method = AuthMethod::ApiKey;
        config.authentication.api_keys.push(ApiKey {
            id: "test-key".to_string(),
            key: "secret-key-123".to_string(),
            name: "Test API Key".to_string(),
            permissions: vec!["legacy:read".to_string()],
            rate_limit: Some(100),
        });

        let manager = SecurityManager::new(config).unwrap();
        
        let result = manager.authenticate_api_key("secret-key-123").unwrap();
        assert!(result.is_some());
        assert_eq!(result.unwrap().id, "test-key");

        let result = manager.authenticate_api_key("wrong-key").unwrap();
        assert!(result.is_none());
    }

    #[test]
    fn test_authorization() {
        let config = SecurityConfig {
            authentication: Default::default(),
            authorization: AuthorizationConfig {
                enabled: true,
                rbac: RbacConfig {
                    roles: vec![
                        Role {
                            name: "admin".to_string(),
                            permissions: vec!["*:*".to_string()],
                            inherit_from: None,
                        },
                        Role {
                            name: "user".to_string(),
                            permissions: vec!["legacy:read".to_string()],
                            inherit_from: None,
                        },
                    ],
                    default_role: "user".to_string(),
                },
                policies: vec![],
            },
            tls: Default::default(),
            rate_limiting: Default::default(),
        };

        let manager = SecurityManager::new(config).unwrap();

        assert!(manager.authorize("admin", "legacy", "write").unwrap());
        assert!(manager.authorize("user", "legacy", "read").unwrap());
        assert!(!manager.authorize("user", "legacy", "write").unwrap());
    }
}