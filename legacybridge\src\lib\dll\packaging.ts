// DLL Packaging System
// Handles deployment package creation and distribution

import { useCallback } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { 
  BuildStatus,
  TestResult,
  DeploymentPackage,
  PackageFormat,
  PackageOptions,
  ValidationResult,
  Architecture
} from './dll-config';

export interface PackageConfiguration {
  format: PackageFormat;
  includeDocs: boolean;
  includeExamples: boolean;
  includeSource: boolean;
  createInstaller: boolean;
  packageName: string;
  version: string;
  description: string;
  author: string;
  license: string;
}

export interface PackagingEngine {
  createPackage: (
    buildStatus: BuildStatus,
    testResults: TestResult[],
    config: PackageConfiguration,
    onProgress: (progress: number) => void
  ) => Promise<DeploymentPackage>;
  validatePackaging: (
    buildStatus: BuildStatus,
    config: PackageConfiguration
  ) => Promise<ValidationResult>;
  getPackageContents: (packagePath: string) => Promise<string[]>;
  verifyPackageIntegrity: (packagePath: string) => Promise<boolean>;
}

export function usePackaging(): PackagingEngine {
  const validatePackaging = useCallback(async (
    buildStatus: BuildStatus,
    config: PackageConfiguration
  ): Promise<ValidationResult> => {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Check build status
    if (!buildStatus.success) {
      errors.push('Cannot create package from failed build');
    }

    if (buildStatus.outputFiles.length === 0) {
      errors.push('No output files available for packaging');
    }

    // Validate package configuration
    if (!config.packageName || config.packageName.trim() === '') {
      errors.push('Package name is required');
    }

    if (!config.version || !config.version.match(/^\d+\.\d+\.\d+$/)) {
      errors.push('Version must be in format X.Y.Z');
    }

    if (!config.author || config.author.trim() === '') {
      warnings.push('Author information is recommended');
    }

    if (!config.license || config.license.trim() === '') {
      warnings.push('License information is recommended for distribution');
    }

    // Format-specific validation
    if (config.format === 'msi' && process.platform !== 'win32') {
      errors.push('MSI packages can only be created on Windows');
    }

    if (config.createInstaller && (config.format === 'zip' || config.format === 'tar')) {
      warnings.push('Installer creation not available for archive formats');
    }

    // Suggestions
    if (config.includeSource) {
      suggestions.push('Including source code will increase package size significantly');
    }

    if (!config.includeDocs) {
      suggestions.push('Consider including documentation for better user experience');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }, []);

  const createPackage = useCallback(async (
    buildStatus: BuildStatus,
    testResults: TestResult[],
    config: PackageConfiguration,
    onProgress: (progress: number) => void
  ): Promise<DeploymentPackage> => {
    try {
      onProgress(10);

      // Collect files to package
      const filesToPackage = await collectPackageFiles(buildStatus, config);
      onProgress(30);

      // Generate documentation if needed
      if (config.includeDocs) {
        await generateDocumentation(buildStatus, testResults, config);
      }
      onProgress(50);

      // Create the package
      const packageResult = await invoke<{
        path: string;
        size: number;
        checksum: string;
        files: string[];
      }>('create_deployment_package', {
        buildStatus,
        config,
        filesToPackage
      });
      onProgress(80);

      // Create installer if requested
      let installerPath: string | undefined;
      if (config.createInstaller && (config.format === 'msi' || config.format === 'nsis')) {
        installerPath = await invoke<string>('create_installer', {
          packagePath: packageResult.path,
          config
        });
      }
      onProgress(90);

      // Generate download URL (in real app, this would upload to CDN)
      const downloadUrl = `file://${installerPath || packageResult.path}`;

      const deploymentPackage: DeploymentPackage = {
        fileName: `${config.packageName}-${config.version}${getPackageExtension(config.format)}`,
        filePath: installerPath || packageResult.path,
        fileSize: packageResult.size,
        format: config.format,
        includedFiles: packageResult.files,
        downloadUrl,
        checksum: packageResult.checksum,
        createdAt: new Date(),
        metadata: {
          version: config.version,
          architecture: extractArchitectures(buildStatus),
          includedFormats: extractIncludedFormats(buildStatus)
        }
      };

      onProgress(100);
      return deploymentPackage;

    } catch (error) {
      throw new Error(`Package creation failed: ${error}`);
    }
  }, []);

  const getPackageContents = useCallback(async (packagePath: string): Promise<string[]> => {
    try {
      const contents = await invoke<string[]>('list_package_contents', { packagePath });
      return contents;
    } catch (error) {
      throw new Error(`Failed to read package contents: ${error}`);
    }
  }, []);

  const verifyPackageIntegrity = useCallback(async (packagePath: string): Promise<boolean> => {
    try {
      const isValid = await invoke<boolean>('verify_package_integrity', { packagePath });
      return isValid;
    } catch (error) {
      throw new Error(`Package verification failed: ${error}`);
    }
  }, []);

  return {
    createPackage,
    validatePackaging,
    getPackageContents,
    verifyPackageIntegrity
  };
}

// Mock implementation for development
export function useMockPackaging(): PackagingEngine {
  const validatePackaging = useCallback(async (
    buildStatus: BuildStatus,
    config: PackageConfiguration
  ): Promise<ValidationResult> => {
    await new Promise(resolve => setTimeout(resolve, 300));

    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    if (!buildStatus.success) {
      errors.push('Cannot create package from failed build');
    }

    if (!config.packageName) {
      errors.push('Package name is required');
    }

    if (!config.version || !config.version.match(/^\d+\.\d+\.\d+$/)) {
      errors.push('Invalid version format');
    }

    return { isValid: errors.length === 0, errors, warnings, suggestions };
  }, []);

  const createPackage = useCallback(async (
    buildStatus: BuildStatus,
    testResults: TestResult[],
    config: PackageConfiguration,
    onProgress: (progress: number) => void
  ): Promise<DeploymentPackage> => {
    // Simulate packaging process
    const steps = [10, 25, 40, 60, 80, 90, 100];
    for (const progress of steps) {
      await new Promise(resolve => setTimeout(resolve, 500));
      onProgress(progress);
    }

    const mockPackage: DeploymentPackage = {
      fileName: `${config.packageName}-${config.version}${getPackageExtension(config.format)}`,
      filePath: `/dist/packages/${config.packageName}-${config.version}${getPackageExtension(config.format)}`,
      fileSize: 5242880, // 5MB
      format: config.format,
      includedFiles: [
        'legacybridge_x86.dll',
        'legacybridge_x64.dll',
        'README.md',
        'LICENSE.txt',
        'docs/API_REFERENCE.md',
        'docs/INTEGRATION_GUIDE.md',
        'examples/vb6/LegacyBridge.bas',
        'examples/vb6/TestProject.vbp',
        'examples/vfp9/legacybridge.prg',
        'examples/vfp9/test_project.pjx'
      ],
      downloadUrl: `https://downloads.legacybridge.com/${config.packageName}-${config.version}${getPackageExtension(config.format)}`,
      checksum: 'sha256:abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
      createdAt: new Date(),
      metadata: {
        version: config.version,
        architecture: ['x86', 'x64'],
        includedFormats: ['rtf', 'doc', 'wordperfect', 'lotus123', 'dbase']
      }
    };

    return mockPackage;
  }, []);

  const getPackageContents = useCallback(async (packagePath: string): Promise<string[]> => {
    await new Promise(resolve => setTimeout(resolve, 200));
    
    return [
      'legacybridge_x86.dll',
      'legacybridge_x64.dll',
      'include/legacybridge.h',
      'lib/legacybridge_x86.lib',
      'lib/legacybridge_x64.lib',
      'docs/README.md',
      'docs/API_REFERENCE.md',
      'examples/vb6/LegacyBridge.bas',
      'examples/vfp9/legacybridge.prg',
      'LICENSE.txt'
    ];
  }, []);

  const verifyPackageIntegrity = useCallback(async (packagePath: string): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return true; // Mock always returns valid
  }, []);

  return {
    createPackage,
    validatePackaging,
    getPackageContents,
    verifyPackageIntegrity
  };
}

// Helper functions
async function collectPackageFiles(
  buildStatus: BuildStatus,
  config: PackageConfiguration
): Promise<string[]> {
  const files: string[] = [...buildStatus.outputFiles];

  // Add standard files
  files.push('LICENSE.txt', 'README.md');

  if (config.includeExamples) {
    files.push(
      'examples/vb6/LegacyBridge.bas',
      'examples/vb6/TestProject.vbp',
      'examples/vfp9/legacybridge.prg',
      'examples/vfp9/test_project.pjx'
    );
  }

  if (config.includeDocs) {
    files.push(
      'docs/API_REFERENCE.md',
      'docs/INTEGRATION_GUIDE.md',
      'docs/TROUBLESHOOTING.md'
    );
  }

  if (config.includeSource) {
    files.push(
      'src/**/*.rs',
      'Cargo.toml',
      'build.rs'
    );
  }

  return files;
}

async function generateDocumentation(
  buildStatus: BuildStatus,
  testResults: TestResult[],
  config: PackageConfiguration
): Promise<void> {
  // In a real implementation, this would generate comprehensive docs
  await new Promise(resolve => setTimeout(resolve, 1000));
}

function extractArchitectures(buildStatus: BuildStatus): Architecture[] {
  const architectures: Architecture[] = [];
  
  buildStatus.outputFiles.forEach(file => {
    if (file.includes('x86')) architectures.push('x86');
    if (file.includes('x64')) architectures.push('x64');
  });

  return [...new Set(architectures)];
}

function extractIncludedFormats(buildStatus: BuildStatus): string[] {
  // In a real implementation, this would extract from build metadata
  return ['rtf', 'doc', 'wordperfect', 'lotus123', 'dbase'];
}

function getPackageExtension(format: PackageFormat): string {
  const extensions: Record<PackageFormat, string> = {
    zip: '.zip',
    tar: '.tar.gz',
    msi: '.msi',
    nsis: '.exe'
  };
  return extensions[format];
}