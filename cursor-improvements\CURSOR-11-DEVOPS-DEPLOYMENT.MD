# 🚀 PHASE 5: DEVOPS & DEPLOYMENT (WEEKS 15-17)

**Priority:** P2 - Operational Excellence  
**Duration:** 3 Weeks  
**Team Size:** 6-8 DevOps Engineers & Infrastructure Specialists  
**Dependencies:** Phases 1-4 Complete  

---

## 📊 PHASE OVERVIEW

This phase establishes enterprise-grade DevOps practices with unified CI/CD pipelines, Infrastructure as Code, and comprehensive monitoring. The goal is to achieve zero-touch deployment capabilities with robust security and observability.

### 🎯 Phase Success Criteria
- **Unified CI/CD pipeline** replacing multiple disparate systems
- **Infrastructure as Code** with Terraform for all environments
- **Blue-green deployment** with automatic rollback capabilities
- **Comprehensive monitoring** with Prometheus/Grafana stack

### 📋 Phase Deliverables
1. **Unified CI/CD Pipeline** - Single GitLab CI pipeline
2. **Infrastructure as Code** - Terraform for all environments
3. **Container Security** - Hardened images and runtime security
4. **Monitoring Stack** - Prometheus, Grafana, Jaeger, ELK
5. **Deployment Automation** - Zero-touch deployment with rollback

### 🚨 Current DevOps Problems

**Critical Infrastructure Issues:**
```
Component                | Current State        | Enterprise Standard
CI/CD Systems           | 3 Different Systems  | Single Unified Pipeline
Infrastructure Mgmt     | Manual/Inconsistent  | Infrastructure as Code
Container Security      | Basic/Vulnerable     | Hardened & Scanned
Monitoring Coverage     | Limited/Fragmented   | Comprehensive/Unified
Deployment Process      | Manual Steps         | Fully Automated
Rollback Capability     | None                 | <5 minute rollback
```

---

## 🔧 PHASE 5.1: CI/CD PIPELINE (WEEK 15)

**Agent Assignment:** DevOps Engineer + CI/CD Specialist  

### **Subtask 5.1.1: Unified CI/CD Pipeline**

#### **GitLab CI Pipeline Architecture**

1. **Master Pipeline Configuration**
   ```yaml
   # .gitlab-ci.yml
   stages:
     - validate
     - build
     - test
     - security
     - package
     - deploy-staging
     - integration-test
     - deploy-production
     - post-deploy
   
   variables:
     DOCKER_DRIVER: overlay2
     DOCKER_TLS_CERTDIR: "/certs"
     RUST_BACKTRACE: 1
     CARGO_TERM_COLOR: always
     NODE_VERSION: "18"
     TERRAFORM_VERSION: "1.6.0"
     HELM_VERSION: "3.13.0"
   
   # Global before script
   before_script:
     - echo "Pipeline started at $(date)"
     - echo "Commit SHA: $CI_COMMIT_SHA"
     - echo "Branch: $CI_COMMIT_REF_NAME"
   
   # Include common templates
   include:
     - local: '.gitlab-ci/templates/security.yml'
     - local: '.gitlab-ci/templates/rust.yml'
     - local: '.gitlab-ci/templates/node.yml'
     - local: '.gitlab-ci/templates/docker.yml'
     - local: '.gitlab-ci/templates/terraform.yml'
     - local: '.gitlab-ci/templates/kubernetes.yml'
   
   # Validation Stage
   validate:syntax:
     stage: validate
     image: alpine:latest
     script:
       - apk add --no-cache yamllint
       - yamllint .gitlab-ci.yml
       - yamllint -r .gitlab-ci/
       - echo "✅ CI/CD syntax validation passed"
     rules:
       - if: $CI_PIPELINE_SOURCE == "merge_request_event"
       - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
   
   validate:terraform:
     stage: validate
     extends: .terraform-validate
     needs: []
   
   validate:kubernetes:
     stage: validate
     extends: .kubernetes-validate
     needs: []
   
   # Build Stage
   build:rust-services:
     stage: build
     extends: .rust-build
     parallel:
       matrix:
         - SERVICE: [auth-service, conversion-service, file-service]
     artifacts:
       paths:
         - target/release/$SERVICE
         - target/release/deps/
       expire_in: 1 hour
   
   build:frontend:
     stage: build
     extends: .node-build
     script:
       - cd legacybridge
       - npm ci --cache .npm --prefer-offline
       - npm run build:production
       - npm run build:tauri -- --ci
     artifacts:
       paths:
         - legacybridge/dist/
         - legacybridge/src-tauri/target/release/bundle/
       expire_in: 1 hour
   
   # Test Stage
   test:unit:rust:
     stage: test
     extends: .rust-test
     script:
       - cargo test --workspace --verbose
       - cargo test --workspace --doc
     coverage: '/^\d+\.\d+% coverage/'
     artifacts:
       reports:
         coverage_report:
           coverage_format: cobertura
           path: coverage.xml
   
   test:unit:frontend:
     stage: test
     extends: .node-test
     script:
       - cd legacybridge
       - npm run test:unit -- --coverage --watchAll=false
       - npm run test:e2e -- --headless
     artifacts:
       reports:
         junit: legacybridge/junit.xml
         coverage_report:
           coverage_format: cobertura
           path: legacybridge/coverage/cobertura-coverage.xml
   
   test:integration:
     stage: test
     image: docker:latest
     services:
       - docker:dind
       - postgres:13
       - redis:7
     variables:
       POSTGRES_DB: legacybridge_test
       POSTGRES_USER: test
       POSTGRES_PASSWORD: test
       REDIS_URL: redis://redis:6379
       DATABASE_URL: **********************************/legacybridge_test
     script:
       - apk add --no-cache curl
       - docker-compose -f docker-compose.test.yml up -d
       - sleep 30  # Wait for services to start
       - ./scripts/run-integration-tests.sh
     after_script:
       - docker-compose -f docker-compose.test.yml down
     artifacts:
       reports:
         junit: integration-test-results.xml
   
   # Security Stage
   security:sast:
     stage: security
     extends: .security-sast
   
   security:dast:
     stage: security
     extends: .security-dast
     needs: ["build:rust-services", "build:frontend"]
   
   security:dependency-scan:
     stage: security
     extends: .security-dependency-scan
   
   security:container-scan:
     stage: security
     extends: .security-container-scan
     needs: ["package:docker-images"]
   
   # Package Stage
   package:docker-images:
     stage: package
     extends: .docker-build
     parallel:
       matrix:
         - SERVICE: [auth-service, conversion-service, file-service, frontend]
     needs: 
       - job: build:rust-services
         artifacts: true
       - job: build:frontend
         artifacts: true
   
   package:helm-charts:
     stage: package
     extends: .helm-package
     script:
       - helm package helm/legacybridge --version $CI_COMMIT_TAG
       - helm push legacybridge-$CI_COMMIT_TAG.tgz oci://$CI_REGISTRY/helm
     rules:
       - if: $CI_COMMIT_TAG
   
   # Staging Deployment
   deploy:staging:
     stage: deploy-staging
     extends: .deploy-staging
     environment:
       name: staging
       url: https://staging.legacybridge.com
     needs:
       - job: package:docker-images
         artifacts: false
   
   # Integration Testing in Staging
   test:staging-integration:
     stage: integration-test
     extends: .staging-integration-test
     environment:
       name: staging
     needs: ["deploy:staging"]
   
   test:staging-performance:
     stage: integration-test
     extends: .staging-performance-test
     environment:
       name: staging
     needs: ["deploy:staging"]
   
   # Production Deployment
   deploy:production:
     stage: deploy-production
     extends: .deploy-production
     environment:
       name: production
       url: https://api.legacybridge.com
     when: manual
     rules:
       - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
         when: manual
       - if: $CI_COMMIT_TAG
         when: on_success
     needs:
       - job: test:staging-integration
         artifacts: false
       - job: test:staging-performance
         artifacts: false
   
   # Post-deployment validation
   validate:production:
     stage: post-deploy
     extends: .production-validation
     environment:
       name: production
     needs: ["deploy:production"]
   
   # Notify stakeholders
   notify:success:
     stage: post-deploy
     image: alpine:latest
     script:
       - apk add --no-cache curl
       - ./scripts/notify-deployment-success.sh
     rules:
       - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
       - if: $CI_COMMIT_TAG
     needs: ["validate:production"]
   
   notify:failure:
     stage: post-deploy
     image: alpine:latest
     script:
       - ./scripts/notify-deployment-failure.sh
     rules:
       - if: $CI_PIPELINE_SOURCE == "merge_request_event"
         when: on_failure
       - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
         when: on_failure
     needs: []
   ```

2. **Rust Service Build Template**
   ```yaml
   # .gitlab-ci/templates/rust.yml
   .rust-base:
     image: rust:1.75-slim
     before_script:
       - apt-get update && apt-get install -y pkg-config libssl-dev
       - rustup component add clippy rustfmt
       - cargo --version
       - rustc --version
   
   .rust-cache:
     extends: .rust-base
     cache:
       key: rust-$CI_COMMIT_REF_SLUG
       paths:
         - target/
         - $CARGO_HOME/registry/
         - $CARGO_HOME/git/
       policy: pull-push
   
   .rust-build:
     extends: .rust-cache
     script:
       - echo "Building Rust service: $SERVICE"
       - cargo build --release --bin $SERVICE
       - ls -la target/release/
       - strip target/release/$SERVICE
       - ./target/release/$SERVICE --version || echo "Service built successfully"
     artifacts:
       paths:
         - target/release/$SERVICE
       expire_in: 1 hour
   
   .rust-test:
     extends: .rust-cache
     variables:
       CARGO_INCREMENTAL: "0"
       RUSTFLAGS: "-Zprofile -Ccodegen-units=1 -Cinline-threshold=0 -Clink-dead-code -Coverflow-checks=off -Cpanic=abort -Zpanic_abort_tests"
       RUSTDOCFLAGS: "-Zprofile -Ccodegen-units=1 -Cinline-threshold=0 -Clink-dead-code -Coverflow-checks=off -Cpanic=abort -Zpanic_abort_tests"
     script:
       - cargo install grcov
       - cargo test --workspace --verbose
       - grcov . --binary-path ./target/debug/ -s . -t cobertura --branch --ignore-not-existing --ignore "/*" -o coverage.xml
   
   .rust-lint:
     extends: .rust-cache
     script:
       - cargo fmt -- --check
       - cargo clippy --workspace --all-targets --all-features -- -D warnings
       - cargo audit
   ```

3. **Docker Build Template**
   ```yaml
   # .gitlab-ci/templates/docker.yml
   .docker-base:
     image: docker:latest
     services:
       - docker:dind
     variables:
       DOCKER_BUILDKIT: 1
       BUILDX_PLATFORMS: linux/amd64,linux/arm64
   
   .docker-build:
     extends: .docker-base
     before_script:
       - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
       - docker buildx create --use --driver docker-container
     script:
       - |
         if [ "$SERVICE" = "frontend" ]; then
           DOCKERFILE="Dockerfile.frontend"
           CONTEXT="legacybridge/"
         else
           DOCKERFILE="Dockerfile.$SERVICE"
           CONTEXT="."
         fi
       
       - IMAGE_TAG=$CI_REGISTRY_IMAGE/$SERVICE:$CI_COMMIT_SHA
       - LATEST_TAG=$CI_REGISTRY_IMAGE/$SERVICE:latest
       
       # Build multi-platform image
       - docker buildx build 
           --platform $BUILDX_PLATFORMS
           --file $DOCKERFILE
           --tag $IMAGE_TAG
           --tag $LATEST_TAG
           --push
           $CONTEXT
       
       # Security scan
       - docker run --rm -v /var/run/docker.sock:/var/run/docker.sock 
           aquasec/trivy image --exit-code 1 --severity HIGH,CRITICAL $IMAGE_TAG
   
   .docker-scan:
     extends: .docker-base
     script:
       - IMAGE_TAG=$CI_REGISTRY_IMAGE/$SERVICE:$CI_COMMIT_SHA
       - docker pull $IMAGE_TAG
       - docker run --rm -v /var/run/docker.sock:/var/run/docker.sock
           aquasec/trivy image --format json --output $SERVICE-security-report.json $IMAGE_TAG
     artifacts:
       reports:
         container_scanning: $SERVICE-security-report.json
   ```

4. **Deployment Template**
   ```yaml
   # .gitlab-ci/templates/kubernetes.yml
   .kubernetes-base:
     image: 
       name: hashicorp/terraform:1.6
       entrypoint: [""]
     before_script:
       - apk add --no-cache curl kubectl helm
       - curl -LO "https://dl.k8s.io/release/v1.28.0/bin/linux/amd64/kubectl"
       - chmod +x kubectl && mv kubectl /usr/local/bin/
       - helm version
   
   .deploy-staging:
     extends: .kubernetes-base
     script:
       - echo "Deploying to staging environment"
       - kubectl config use-context staging-cluster
       - kubectl create namespace legacybridge-staging --dry-run=client -o yaml | kubectl apply -f -
       
       # Deploy with Helm
       - helm upgrade --install legacybridge-staging ./helm/legacybridge
           --namespace legacybridge-staging
           --values ./helm/values/staging.yaml
           --set image.tag=$CI_COMMIT_SHA
           --set global.environment=staging
           --wait --timeout=10m
       
       # Verify deployment
       - kubectl rollout status deployment/legacybridge-staging -n legacybridge-staging
       - kubectl get pods -n legacybridge-staging
       
       # Run smoke tests
       - ./scripts/smoke-test.sh https://staging.legacybridge.com
   
   .deploy-production:
     extends: .kubernetes-base
     script:
       - echo "Deploying to production environment"
       - kubectl config use-context production-cluster
       
       # Blue-green deployment strategy
       - export CURRENT_COLOR=$(kubectl get service legacybridge-production -o jsonpath='{.spec.selector.color}' 2>/dev/null || echo "blue")
       - export NEW_COLOR=$([ "$CURRENT_COLOR" = "blue" ] && echo "green" || echo "blue")
       
       echo "Current color: $CURRENT_COLOR, New color: $NEW_COLOR"
       
       # Deploy new version
       - helm upgrade --install legacybridge-$NEW_COLOR ./helm/legacybridge
           --namespace legacybridge-production
           --values ./helm/values/production.yaml
           --set image.tag=$CI_COMMIT_SHA
           --set global.environment=production
           --set global.color=$NEW_COLOR
           --wait --timeout=15m
       
       # Health check new deployment
       - kubectl rollout status deployment/legacybridge-$NEW_COLOR -n legacybridge-production
       - ./scripts/health-check.sh https://legacybridge-$NEW_COLOR.internal.com
       
       # Switch traffic
       - kubectl patch service legacybridge-production -p '{"spec":{"selector":{"color":"'$NEW_COLOR'"}}}'
       
       # Final validation
       - sleep 30
       - ./scripts/production-validation.sh https://api.legacybridge.com
       
       # Cleanup old deployment (keep for rollback)
       - echo "Deployment successful. Old $CURRENT_COLOR deployment kept for rollback."
   
   .rollback-production:
     extends: .kubernetes-base
     script:
       - echo "Rolling back production deployment"
       - kubectl config use-context production-cluster
       
       # Determine colors
       - export CURRENT_COLOR=$(kubectl get service legacybridge-production -o jsonpath='{.spec.selector.color}')
       - export ROLLBACK_COLOR=$([ "$CURRENT_COLOR" = "blue" ] && echo "green" || echo "blue")
       
       echo "Rolling back from $CURRENT_COLOR to $ROLLBACK_COLOR"
       
       # Switch traffic back
       - kubectl patch service legacybridge-production -p '{"spec":{"selector":{"color":"'$ROLLBACK_COLOR'"}}}'
       
       # Verify rollback
       - ./scripts/production-validation.sh https://api.legacybridge.com
       
       echo "Rollback completed successfully"
     when: manual
     environment:
       name: production
   ```

**Success Criteria:**
- ✅ Single unified CI/CD pipeline replacing multiple systems
- ✅ Automated testing at every stage
- ✅ Security scanning integrated into pipeline
- ✅ Blue-green deployment with rollback capability

---

### **Subtask 5.1.2: Infrastructure as Code**

#### **Terraform Infrastructure Management**

1. **Main Infrastructure Configuration**
   ```hcl
   # terraform/main.tf
   terraform {
     required_version = ">= 1.6"
     required_providers {
       aws = {
         source  = "hashicorp/aws"
         version = "~> 5.0"
       }
       kubernetes = {
         source  = "hashicorp/kubernetes"
         version = "~> 2.24"
       }
       helm = {
         source  = "hashicorp/helm"
         version = "~> 2.12"
       }
     }
     
     backend "s3" {
       bucket         = "legacybridge-terraform-state"
       key            = "infrastructure/terraform.tfstate"
       region         = "us-west-2"
       encrypt        = true
       dynamodb_table = "terraform-state-lock"
     }
   }
   
   provider "aws" {
     region = var.aws_region
     
     default_tags {
       tags = {
         Project     = "LegacyBridge"
         Environment = var.environment
         ManagedBy   = "Terraform"
         Owner       = "DevOps Team"
       }
     }
   }
   
   # Data sources
   data "aws_availability_zones" "available" {
     state = "available"
   }
   
   data "aws_caller_identity" "current" {}
   
   # Local values
   locals {
     name_prefix = "legacybridge-${var.environment}"
     common_tags = {
       Project     = "LegacyBridge"
       Environment = var.environment
       ManagedBy   = "Terraform"
     }
   }
   
   # VPC and Networking
   module "vpc" {
     source = "./modules/vpc"
     
     name_prefix         = local.name_prefix
     cidr_block         = var.vpc_cidr
     availability_zones = data.aws_availability_zones.available.names
     environment        = var.environment
     
     tags = local.common_tags
   }
   
   # EKS Cluster
   module "eks" {
     source = "./modules/eks"
     
     cluster_name       = "${local.name_prefix}-cluster"
     cluster_version    = var.kubernetes_version
     vpc_id            = module.vpc.vpc_id
     subnet_ids        = module.vpc.private_subnet_ids
     
     node_groups = {
       main = {
         desired_capacity = var.eks_node_desired_capacity
         max_capacity     = var.eks_node_max_capacity
         min_capacity     = var.eks_node_min_capacity
         instance_types   = var.eks_node_instance_types
         capacity_type    = "ON_DEMAND"
         
         k8s_labels = {
           Environment = var.environment
           NodeGroup   = "main"
         }
       }
       
       spot = {
         desired_capacity = var.eks_spot_desired_capacity
         max_capacity     = var.eks_spot_max_capacity
         min_capacity     = var.eks_spot_min_capacity
         instance_types   = var.eks_spot_instance_types
         capacity_type    = "SPOT"
         
         k8s_labels = {
           Environment = var.environment
           NodeGroup   = "spot"
         }
         
         taints = [
           {
             key    = "spot"
             value  = "true"
             effect = "NO_SCHEDULE"
           }
         ]
       }
     }
     
     tags = local.common_tags
   }
   
   # RDS Database
   module "rds" {
     source = "./modules/rds"
     
     identifier     = "${local.name_prefix}-db"
     engine_version = var.postgres_version
     instance_class = var.rds_instance_class
     
     allocated_storage     = var.rds_allocated_storage
     max_allocated_storage = var.rds_max_allocated_storage
     storage_encrypted     = true
     
     db_name  = var.database_name
     username = var.database_username
     
     vpc_id     = module.vpc.vpc_id
     subnet_ids = module.vpc.database_subnet_ids
     
     backup_retention_period = var.rds_backup_retention
     backup_window          = var.rds_backup_window
     maintenance_window     = var.rds_maintenance_window
     
     monitoring_enabled = true
     performance_insights_enabled = true
     
     tags = local.common_tags
   }
   
   # ElastiCache Redis
   module "redis" {
     source = "./modules/elasticache"
     
     cluster_id      = "${local.name_prefix}-redis"
     engine_version  = var.redis_version
     node_type       = var.redis_node_type
     num_cache_nodes = var.redis_num_nodes
     
     vpc_id     = module.vpc.vpc_id
     subnet_ids = module.vpc.cache_subnet_ids
     
     at_rest_encryption_enabled = true
     transit_encryption_enabled = true
     
     tags = local.common_tags
   }
   
   # S3 Buckets
   module "s3" {
     source = "./modules/s3"
     
     bucket_prefix = local.name_prefix
     environment   = var.environment
     
     buckets = {
       uploads = {
         versioning_enabled = true
         lifecycle_rules = [
           {
             id     = "cleanup_old_uploads"
             status = "Enabled"
             expiration = {
               days = 90
             }
           }
         ]
       }
       
       processed = {
         versioning_enabled = true
         lifecycle_rules = [
           {
             id     = "archive_old_files"
             status = "Enabled"
             transition = {
               days          = 30
               storage_class = "STANDARD_IA"
             }
           }
         ]
       }
       
       backups = {
         versioning_enabled = true
         lifecycle_rules = [
           {
             id     = "delete_old_backups"
             status = "Enabled"
             expiration = {
               days = 365
             }
           }
         ]
       }
     }
     
     tags = local.common_tags
   }
   
   # Monitoring and Logging
   module "monitoring" {
     source = "./modules/monitoring"
     
     cluster_name = module.eks.cluster_name
     vpc_id       = module.vpc.vpc_id
     
     prometheus_enabled = true
     grafana_enabled    = true
     jaeger_enabled     = true
     elk_enabled        = true
     
     tags = local.common_tags
   }
   
   # Security
   module "security" {
     source = "./modules/security"
     
     vpc_id     = module.vpc.vpc_id
     cluster_name = module.eks.cluster_name
     
     enable_guardduty     = true
     enable_security_hub  = true
     enable_inspector     = true
     enable_macie         = var.environment == "production"
     
     tags = local.common_tags
   }
   ```

2. **EKS Module**
   ```hcl
   # terraform/modules/eks/main.tf
   resource "aws_eks_cluster" "cluster" {
     name     = var.cluster_name
     version  = var.cluster_version
     role_arn = aws_iam_role.cluster.arn
   
     vpc_config {
       subnet_ids              = var.subnet_ids
       endpoint_private_access = true
       endpoint_public_access  = true
       public_access_cidrs     = var.public_access_cidrs
       
       security_group_ids = [aws_security_group.cluster.id]
     }
   
     encryption_config {
       provider {
         key_arn = aws_kms_key.eks.arn
       }
       resources = ["secrets"]
     }
   
     enabled_cluster_log_types = [
       "api",
       "audit",
       "authenticator",
       "controllerManager",
       "scheduler"
     ]
   
     depends_on = [
       aws_iam_role_policy_attachment.cluster_AmazonEKSClusterPolicy,
       aws_iam_role_policy_attachment.cluster_AmazonEKSVPCResourceController,
       aws_cloudwatch_log_group.cluster,
     ]
   
     tags = var.tags
   }
   
   # Node Groups
   resource "aws_eks_node_group" "node_groups" {
     for_each = var.node_groups
   
     cluster_name    = aws_eks_cluster.cluster.name
     node_group_name = each.key
     node_role_arn   = aws_iam_role.node_group.arn
     subnet_ids      = var.subnet_ids
   
     capacity_type  = each.value.capacity_type
     instance_types = each.value.instance_types
     disk_size      = each.value.disk_size
   
     scaling_config {
       desired_size = each.value.desired_capacity
       max_size     = each.value.max_capacity
       min_size     = each.value.min_capacity
     }
   
     update_config {
       max_unavailable_percentage = 25
     }
   
     labels = each.value.k8s_labels
   
     dynamic "taint" {
       for_each = each.value.taints != null ? each.value.taints : []
       content {
         key    = taint.value.key
         value  = taint.value.value
         effect = taint.value.effect
       }
     }
   
     # Ensure that IAM Role permissions are created before and deleted after EKS Node Group handling.
     depends_on = [
       aws_iam_role_policy_attachment.node_group_AmazonEKSWorkerNodePolicy,
       aws_iam_role_policy_attachment.node_group_AmazonEKS_CNI_Policy,
       aws_iam_role_policy_attachment.node_group_AmazonEC2ContainerRegistryReadOnly,
     ]
   
     tags = var.tags
   }
   
   # Cluster Autoscaler
   resource "kubernetes_cluster_role" "cluster_autoscaler" {
     metadata {
       name = "cluster-autoscaler"
       labels = {
         k8s-addon = "cluster-autoscaler.addons.k8s.io"
         k8s-app   = "cluster-autoscaler"
       }
     }
   
     rule {
       api_groups = [""]
       resources  = ["events", "endpoints"]
       verbs      = ["create", "patch"]
     }
   
     rule {
       api_groups = [""]
       resources  = ["pods/eviction"]
       verbs      = ["create"]
     }
   
     rule {
       api_groups = [""]
       resources  = ["pods/status"]
       verbs      = ["update"]
     }
   
     rule {
       api_groups     = [""]
       resources      = ["endpoints"]
       resource_names = ["cluster-autoscaler"]
       verbs          = ["get", "update"]
     }
   
     rule {
       api_groups = [""]
       resources  = ["nodes"]
       verbs      = ["watch", "list", "get", "update"]
     }
   
     rule {
       api_groups = [""]
       resources  = ["pods", "services", "replicationcontrollers", "persistentvolumeclaims", "persistentvolumes"]
       verbs      = ["watch", "list", "get"]
     }
   
     rule {
       api_groups = ["extensions"]
       resources  = ["replicasets", "daemonsets"]
       verbs      = ["watch", "list", "get"]
     }
   
     rule {
       api_groups = ["policy"]
       resources  = ["poddisruptionbudgets"]
       verbs      = ["watch", "list"]
     }
   
     rule {
       api_groups = ["apps"]
       resources  = ["statefulsets", "replicasets", "daemonsets"]
       verbs      = ["watch", "list", "get"]
     }
   
     rule {
       api_groups = ["storage.k8s.io"]
       resources  = ["storageclasses", "csinodes", "csidrivers", "csistoragecapacities"]
       verbs      = ["watch", "list", "get"]
     }
   
     rule {
       api_groups = ["batch", "extensions"]
       resources  = ["jobs"]
       verbs      = ["get", "list", "watch", "patch"]
     }
   
     rule {
       api_groups = ["coordination.k8s.io"]
       resources  = ["leases"]
       verbs      = ["create"]
     }
   
     rule {
       api_groups     = ["coordination.k8s.io"]
       resource_names = ["cluster-autoscaler"]
       resources      = ["leases"]
       verbs          = ["get", "update"]
     }
   }
   
   # Add-ons
   resource "aws_eks_addon" "addons" {
     for_each = var.cluster_addons
   
     cluster_name             = aws_eks_cluster.cluster.name
     addon_name               = each.key
     addon_version            = each.value.version
     resolve_conflicts        = "OVERWRITE"
     service_account_role_arn = each.value.service_account_role_arn
   
     tags = var.tags
   }
   ```

3. **Environment-Specific Variables**
   ```hcl
   # terraform/environments/production/terraform.tfvars
   aws_region  = "us-west-2"
   environment = "production"
   
   # VPC Configuration
   vpc_cidr = "10.0.0.0/16"
   
   # EKS Configuration
   kubernetes_version = "1.28"
   
   eks_node_desired_capacity = 3
   eks_node_max_capacity     = 10
   eks_node_min_capacity     = 3
   eks_node_instance_types   = ["t3.large", "t3.xlarge"]
   
   eks_spot_desired_capacity = 2
   eks_spot_max_capacity     = 20
   eks_spot_min_capacity     = 0
   eks_spot_instance_types   = ["t3.medium", "t3.large", "c5.large"]
   
   # Database Configuration
   postgres_version      = "15.4"
   rds_instance_class    = "db.t3.medium"
   rds_allocated_storage = 100
   rds_max_allocated_storage = 1000
   rds_backup_retention  = 30
   rds_backup_window     = "03:00-04:00"
   rds_maintenance_window = "Sun:04:00-Sun:05:00"
   
   database_name     = "legacybridge"
   database_username = "legacybridge"
   
   # Redis Configuration
   redis_version   = "7.0"
   redis_node_type = "cache.t3.medium"
   redis_num_nodes = 2
   
   # Security Configuration
   enable_macie = true
   ```

4. **Terraform CI/CD Integration**
   ```yaml
   # .gitlab-ci/templates/terraform.yml
   .terraform-base:
     image:
       name: hashicorp/terraform:1.6
       entrypoint: [""]
     variables:
       TF_ROOT: terraform
       TF_IN_AUTOMATION: "true"
       TF_INPUT: "false"
     cache:
       key: terraform-$CI_COMMIT_REF_SLUG
       paths:
         - $TF_ROOT/.terraform/
   
   .terraform-validate:
     extends: .terraform-base
     script:
       - cd $TF_ROOT
       - terraform fmt -check -diff -recursive
       - terraform init -backend=false
       - terraform validate
       - tflint --init
       - tflint --recursive
   
   .terraform-plan:
     extends: .terraform-base
     script:
       - cd $TF_ROOT
       - terraform init
       - terraform workspace select $ENVIRONMENT || terraform workspace new $ENVIRONMENT
       - terraform plan -var-file="environments/$ENVIRONMENT/terraform.tfvars" -out=tfplan
       - terraform show -json tfplan > plan.json
     artifacts:
       paths:
         - $TF_ROOT/tfplan
         - $TF_ROOT/plan.json
       expire_in: 1 week
   
   .terraform-apply:
     extends: .terraform-base
     script:
       - cd $TF_ROOT
       - terraform init
       - terraform workspace select $ENVIRONMENT
       - terraform apply -auto-approve tfplan
     dependencies:
       - terraform:plan:$ENVIRONMENT
   
   terraform:plan:staging:
     extends: .terraform-plan
     variables:
       ENVIRONMENT: staging
     rules:
       - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
       - if: $CI_PIPELINE_SOURCE == "merge_request_event"
   
   terraform:apply:staging:
     extends: .terraform-apply
     variables:
       ENVIRONMENT: staging
     environment:
       name: staging-infrastructure
     rules:
       - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
   
   terraform:plan:production:
     extends: .terraform-plan
     variables:
       ENVIRONMENT: production
     rules:
       - if: $CI_COMMIT_TAG
   
   terraform:apply:production:
     extends: .terraform-apply
     variables:
       ENVIRONMENT: production
     environment:
       name: production-infrastructure
     when: manual
     rules:
       - if: $CI_COMMIT_TAG
   ```

**Success Criteria:**
- ✅ Infrastructure fully defined as code
- ✅ Multi-environment support (staging, production)
- ✅ State management with remote backend
- ✅ Automated infrastructure validation

---

### **Subtask 5.1.3: Rollback Strategy**

#### **Blue-Green Deployment with Automatic Rollback**

1. **Rollback Automation Script**
   ```bash
   #!/bin/bash
   # scripts/automatic-rollback.sh
   
   set -euo pipefail
   
   NAMESPACE="${NAMESPACE:-legacybridge-production}"
   SERVICE_NAME="legacybridge-production"
   HEALTH_CHECK_URL="${HEALTH_CHECK_URL:-https://api.legacybridge.com/health}"
   MAX_ROLLBACK_TIME=300  # 5 minutes
   HEALTH_CHECK_INTERVAL=10
   FAILED_CHECKS_THRESHOLD=3
   
   log() {
       echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
   }
   
   get_current_color() {
       kubectl get service "$SERVICE_NAME" -n "$NAMESPACE" -o jsonpath='{.spec.selector.color}' 2>/dev/null || echo "blue"
   }
   
   get_other_color() {
       local current_color=$1
       if [ "$current_color" = "blue" ]; then
           echo "green"
       else
           echo "blue"
       fi
   }
   
   health_check() {
       local url=$1
       local timeout=${2:-10}
       
       response=$(curl -s -o /dev/null -w "%{http_code}" --max-time "$timeout" "$url" || echo "000")
       
       if [ "$response" = "200" ]; then
           return 0
       else
           log "Health check failed: HTTP $response"
           return 1
       fi
   }
   
   check_deployment_health() {
       local deployment_name=$1
       local max_checks=${2:-30}
       local check_interval=${3:-10}
       local failed_checks=0
       
       log "Monitoring health of deployment: $deployment_name"
       
       for ((i=1; i<=max_checks; i++)); do
           log "Health check $i/$max_checks"
           
           # Check pod readiness
           ready_replicas=$(kubectl get deployment "$deployment_name" -n "$NAMESPACE" -o jsonpath='{.status.readyReplicas}' || echo "0")
           desired_replicas=$(kubectl get deployment "$deployment_name" -n "$NAMESPACE" -o jsonpath='{.spec.replicas}' || echo "1")
           
           if [ "$ready_replicas" = "$desired_replicas" ] && [ "$ready_replicas" != "0" ]; then
               # Pod readiness check passed, now check application health
               if health_check "$HEALTH_CHECK_URL"; then
                   log "Health check passed ($i/$max_checks)"
                   failed_checks=0
               else
                   ((failed_checks++))
                   log "Health check failed ($failed_checks/$FAILED_CHECKS_THRESHOLD)"
                   
                   if [ $failed_checks -ge $FAILED_CHECKS_THRESHOLD ]; then
                       log "Health check failed $FAILED_CHECKS_THRESHOLD times consecutively"
                       return 1
                   fi
               fi
           else
               log "Deployment not ready: $ready_replicas/$desired_replicas replicas"
           fi
           
           if [ $i -lt $max_checks ]; then
               sleep "$check_interval"
           fi
       done
       
       log "Health monitoring completed successfully"
       return 0
   }
   
   perform_rollback() {
       local current_color=$1
       local rollback_color=$2
       
       log "🔄 Starting automatic rollback from $current_color to $rollback_color"
       
       # Check if rollback target exists
       if ! kubectl get deployment "legacybridge-$rollback_color" -n "$NAMESPACE" >/dev/null 2>&1; then
           log "❌ Rollback target deployment legacybridge-$rollback_color not found"
           return 1
       fi
       
       # Ensure rollback target is healthy
       if ! check_deployment_health "legacybridge-$rollback_color" 10 5; then
           log "❌ Rollback target deployment is not healthy"
           return 1
       fi
       
       # Switch traffic
       log "Switching traffic to $rollback_color deployment"
       kubectl patch service "$SERVICE_NAME" -n "$NAMESPACE" -p "{\"spec\":{\"selector\":{\"color\":\"$rollback_color\"}}}"
       
       # Wait for traffic switch to take effect
       sleep 15
       
       # Verify rollback success
       if check_deployment_health "legacybridge-$rollback_color" 10 5; then
           log "✅ Rollback completed successfully"
           
           # Send notification
           send_rollback_notification "success" "$current_color" "$rollback_color"
           
           # Scale down failed deployment
           log "Scaling down failed deployment: legacybridge-$current_color"
           kubectl scale deployment "legacybridge-$current_color" -n "$NAMESPACE" --replicas=0
           
           return 0
       else
           log "❌ Rollback verification failed"
           send_rollback_notification "failed" "$current_color" "$rollback_color"
           return 1
       fi
   }
   
   send_rollback_notification() {
       local status=$1
       local from_color=$2
       local to_color=$3
       
       local message
       if [ "$status" = "success" ]; then
           message="🔄 Automatic rollback completed successfully from $from_color to $to_color"
       else
           message="❌ Automatic rollback failed from $from_color to $to_color"
       fi
       
       # Send to Slack
       if [ -n "${SLACK_WEBHOOK_URL:-}" ]; then
           curl -X POST -H 'Content-type: application/json' \
               --data "{\"text\":\"$message\"}" \
               "$SLACK_WEBHOOK_URL" || true
       fi
       
       # Send to PagerDuty
       if [ -n "${PAGERDUTY_INTEGRATION_KEY:-}" ]; then
           curl -X POST \
               -H "Content-Type: application/json" \
               -d "{
                   \"routing_key\": \"$PAGERDUTY_INTEGRATION_KEY\",
                   \"event_action\": \"trigger\",
                   \"payload\": {
                       \"summary\": \"$message\",
                       \"severity\": \"error\",
                       \"source\": \"LegacyBridge Auto-Rollback\"
                   }
               }" \
               "https://events.pagerduty.com/v2/enqueue" || true
       fi
   }
   
   main() {
       log "🚀 Starting automatic rollback monitoring"
       
       local current_color
       current_color=$(get_current_color)
       local other_color
       other_color=$(get_other_color "$current_color")
       
       log "Current deployment color: $current_color"
       log "Monitoring deployment: legacybridge-$current_color"
       
       # Monitor current deployment health
       if ! check_deployment_health "legacybridge-$current_color" 30 10; then
           log "⚠️ Current deployment health check failed, initiating rollback"
           
           if perform_rollback "$current_color" "$other_color"; then
               log "✅ Automatic rollback completed successfully"
               exit 0
           else
               log "❌ Automatic rollback failed"
               exit 1
           fi
       else
           log "✅ Deployment is healthy, no rollback needed"
           exit 0
       fi
   }
   
   # Trap signals for cleanup
   trap 'log "Script interrupted"; exit 1' INT TERM
   
   main "$@"
   ```

2. **Kubernetes Rollback Job**
   ```yaml
   # kubernetes/rollback-job.yaml
   apiVersion: batch/v1
   kind: Job
   metadata:
     name: automatic-rollback-monitor
     namespace: legacybridge-production
   spec:
     activeDeadlineSeconds: 600  # 10 minutes max
     ttlSecondsAfterFinished: 86400  # Keep for 24 hours
     template:
       spec:
         serviceAccountName: rollback-monitor
         restartPolicy: Never
         containers:
         - name: rollback-monitor
           image: legacybridge/rollback-monitor:latest
           env:
           - name: NAMESPACE
             value: "legacybridge-production"
           - name: HEALTH_CHECK_URL
             value: "https://api.legacybridge.com/health"
           - name: SLACK_WEBHOOK_URL
             valueFrom:
               secretKeyRef:
                 name: notification-secrets
                 key: slack-webhook-url
           - name: PAGERDUTY_INTEGRATION_KEY
             valueFrom:
               secretKeyRef:
                 name: notification-secrets
                 key: pagerduty-integration-key
           resources:
             requests:
               memory: "64Mi"
               cpu: "50m"
             limits:
               memory: "128Mi"
               cpu: "100m"
           command: ["/scripts/automatic-rollback.sh"]
   
   ---
   apiVersion: v1
   kind: ServiceAccount
   metadata:
     name: rollback-monitor
     namespace: legacybridge-production
   
   ---
   apiVersion: rbac.authorization.k8s.io/v1
   kind: Role
   metadata:
     namespace: legacybridge-production
     name: rollback-monitor
   rules:
   - apiGroups: [""]
     resources: ["services"]
     verbs: ["get", "patch"]
   - apiGroups: ["apps"]
     resources: ["deployments"]
     verbs: ["get", "patch", "update"]
   - apiGroups: ["apps"]
     resources: ["deployments/scale"]
     verbs: ["patch", "update"]
   
   ---
   apiVersion: rbac.authorization.k8s.io/v1
   kind: RoleBinding
   metadata:
     name: rollback-monitor
     namespace: legacybridge-production
   subjects:
   - kind: ServiceAccount
     name: rollback-monitor
     namespace: legacybridge-production
   roleRef:
     kind: Role
     name: rollback-monitor
     apiGroup: rbac.authorization.k8s.io
   ```

3. **Manual Rollback Procedure**
   ```bash
   #!/bin/bash
   # scripts/manual-rollback.sh
   
   set -euo pipefail
   
   usage() {
       echo "Usage: $0 [options]"
       echo "Options:"
       echo "  -n, --namespace NAMESPACE    Kubernetes namespace (default: legacybridge-production)"
       echo "  -s, --service SERVICE        Service name (default: legacybridge-production)"
       echo "  -t, --target-version VERSION Target version/color to rollback to"
       echo "  -c, --confirm                Skip confirmation prompt"
       echo "  -h, --help                   Show this help message"
       exit 1
   }
   
   NAMESPACE="legacybridge-production"
   SERVICE_NAME="legacybridge-production"
   TARGET_VERSION=""
   CONFIRM=false
   
   while [[ $# -gt 0 ]]; do
       case $1 in
           -n|--namespace)
               NAMESPACE="$2"
               shift 2
               ;;
           -s|--service)
               SERVICE_NAME="$2"
               shift 2
               ;;
           -t|--target-version)
               TARGET_VERSION="$2"
               shift 2
               ;;
           -c|--confirm)
               CONFIRM=true
               shift
               ;;
           -h|--help)
               usage
               ;;
           *)
               echo "Unknown option: $1"
               usage
               ;;
       esac
   done
   
   log() {
       echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
   }
   
   get_current_version() {
       kubectl get service "$SERVICE_NAME" -n "$NAMESPACE" -o jsonpath='{.spec.selector.color}' 2>/dev/null || echo "unknown"
   }
   
   get_available_versions() {
       kubectl get deployments -n "$NAMESPACE" -l app=legacybridge -o jsonpath='{.items[*].metadata.labels.color}' 2>/dev/null || echo ""
   }
   
   confirm_rollback() {
       local current_version=$1
       local target_version=$2
       
       if [ "$CONFIRM" = true ]; then
           return 0
       fi
       
       echo "⚠️  ROLLBACK CONFIRMATION ⚠️"
       echo "Namespace: $NAMESPACE"
       echo "Service: $SERVICE_NAME"
       echo "Current Version: $current_version"
       echo "Target Version: $target_version"
       echo ""
       echo "This will immediately switch traffic from $current_version to $target_version"
       echo ""
       read -p "Are you sure you want to proceed? (yes/no): " response
       
       if [ "$response" != "yes" ]; then
           log "Rollback cancelled by user"
           exit 0
       fi
   }
   
   perform_manual_rollback() {
       local target_version=$1
       
       # Validate target deployment exists
       if ! kubectl get deployment "legacybridge-$target_version" -n "$NAMESPACE" >/dev/null 2>&1; then
           log "❌ Target deployment legacybridge-$target_version not found"
           exit 1
       fi
       
       # Check target deployment health
       local ready_replicas
       ready_replicas=$(kubectl get deployment "legacybridge-$target_version" -n "$NAMESPACE" -o jsonpath='{.status.readyReplicas}' || echo "0")
       local desired_replicas
       desired_replicas=$(kubectl get deployment "legacybridge-$target_version" -n "$NAMESPACE" -o jsonpath='{.spec.replicas}' || echo "1")
       
       if [ "$ready_replicas" != "$desired_replicas" ] || [ "$ready_replicas" = "0" ]; then
           log "⚠️ Target deployment is not fully ready ($ready_replicas/$desired_replicas replicas)"
           read -p "Continue anyway? (yes/no): " response
           if [ "$response" != "yes" ]; then
               exit 1
           fi
       fi
       
       # Switch traffic
       log "🔄 Switching traffic to $target_version"
       kubectl patch service "$SERVICE_NAME" -n "$NAMESPACE" -p "{\"spec\":{\"selector\":{\"color\":\"$target_version\"}}}"
       
       # Wait for change to propagate
       sleep 10
       
       # Verify rollback
       local new_version
       new_version=$(get_current_version)
       if [ "$new_version" = "$target_version" ]; then
           log "✅ Rollback completed successfully"
           log "Current version is now: $new_version"
           
           # Record rollback event
           kubectl annotate service "$SERVICE_NAME" -n "$NAMESPACE" \
               "rollback/timestamp=$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
               "rollback/to-version=$target_version" \
               "rollback/performed-by=${USER:-unknown}" \
               --overwrite
           
           return 0
       else
           log "❌ Rollback verification failed"
           return 1
       fi
   }
   
   main() {
       log "🚀 Starting manual rollback process"
       
       # Get current version
       local current_version
       current_version=$(get_current_version)
       log "Current version: $current_version"
       
       # Get available versions
       local available_versions
       available_versions=$(get_available_versions)
       log "Available versions: $available_versions"
       
       # Determine target version
       if [ -z "$TARGET_VERSION" ]; then
           # Auto-determine target version (the other color)
           if [ "$current_version" = "blue" ]; then
               TARGET_VERSION="green"
           elif [ "$current_version" = "green" ]; then
               TARGET_VERSION="blue"
           else
               log "❌ Cannot auto-determine target version. Current version: $current_version"
               log "Available versions: $available_versions"
               echo "Please specify target version with -t option"
               exit 1
           fi
       fi
       
       log "Target version: $TARGET_VERSION"
       
       # Validate target version
       if [[ ! " $available_versions " =~ " $TARGET_VERSION " ]]; then
           log "❌ Target version '$TARGET_VERSION' not found in available versions: $available_versions"
           exit 1
       fi
       
       # Confirm rollback
       confirm_rollback "$current_version" "$TARGET_VERSION"
       
       # Perform rollback
       if perform_manual_rollback "$TARGET_VERSION"; then
           log "✅ Manual rollback completed successfully"
           exit 0
       else
           log "❌ Manual rollback failed"
           exit 1
       fi
   }
   
   main "$@"
   ```

**Success Criteria:**
- ✅ Blue-green deployment strategy implemented
- ✅ Automatic rollback triggers on health check failures
- ✅ Manual rollback capability in <5 minutes
- ✅ Rollback notifications to operations team

---

## 📊 PHASE 5 SUCCESS CRITERIA

### **CI/CD Pipeline Achievement**
- ✅ **Unified Pipeline**: Single GitLab CI replacing multiple systems
- ✅ **Security Integration**: SAST/DAST/container scanning in pipeline
- ✅ **Automated Testing**: Full test suite execution at every stage
- ✅ **Zero-Touch Deployment**: Fully automated deployment process

### **Infrastructure as Code**
- ✅ **Complete IaC**: All infrastructure defined in Terraform
- ✅ **Multi-Environment**: Staging and production environments
- ✅ **State Management**: Remote state with locking
- ✅ **Automated Provisioning**: Infrastructure changes via CI/CD

### **Deployment & Operations**
- ✅ **Blue-Green Deployment**: Zero-downtime deployments
- ✅ **Automatic Rollback**: Health-check driven rollback <5 minutes
- ✅ **Container Security**: Hardened images with vulnerability scanning
- ✅ **Monitoring Integration**: Comprehensive observability stack

**Next Phase Dependency:** Phase 6 (Documentation & Compliance) requires stable DevOps practices from Phase 5.