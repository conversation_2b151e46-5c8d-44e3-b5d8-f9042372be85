# Production Infrastructure Services
# PostgreSQL Database
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-config
  namespace: legacybridge
  labels:
    app: postgresql
    component: database
data:
  postgresql.conf: |
    # PostgreSQL configuration for production
    max_connections = 200
    shared_buffers = 256MB
    effective_cache_size = 1GB
    maintenance_work_mem = 64MB
    checkpoint_completion_target = 0.9
    wal_buffers = 16MB
    default_statistics_target = 100
    random_page_cost = 1.1
    effective_io_concurrency = 200
    work_mem = 4MB
    min_wal_size = 1GB
    max_wal_size = 4GB
    max_worker_processes = 8
    max_parallel_workers_per_gather = 2
    max_parallel_workers = 8
    max_parallel_maintenance_workers = 2
    
    # Logging
    log_destination = 'stderr'
    logging_collector = on
    log_directory = 'log'
    log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
    log_statement = 'all'
    log_min_duration_statement = 1000
    
    # Connection settings
    listen_addresses = '*'
    port = 5432

---
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-secrets
  namespace: legacybridge
  labels:
    app: postgresql
    component: database
type: Opaque
data:
  POSTGRES_PASSWORD: "cGFzc3dvcmQxMjM="  # password123
  POSTGRES_USER: "bGVnYWN5YnJpZGdl"  # legacybridge
  POSTGRES_DB: "bGVnYWN5YnJpZGdl"  # legacybridge

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgresql
  namespace: legacybridge
  labels:
    app: postgresql
    component: database
spec:
  serviceName: postgresql
  replicas: 1
  selector:
    matchLabels:
      app: postgresql
  template:
    metadata:
      labels:
        app: postgresql
        component: database
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9187"
        prometheus.io/path: "/metrics"
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: postgresql
        image: postgres:15-alpine
        imagePullPolicy: IfNotPresent
        ports:
        - name: postgresql
          containerPort: 5432
          protocol: TCP
        env:
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-secrets
              key: POSTGRES_PASSWORD
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-secrets
              key: POSTGRES_USER
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgresql-secrets
              key: POSTGRES_DB
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" -h 127.0.0.1 -p 5432
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" -h 127.0.0.1 -p 5432
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        volumeMounts:
        - name: postgresql-data
          mountPath: /var/lib/postgresql/data
        - name: postgresql-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
          readOnly: true
      - name: postgres-exporter
        image: prometheuscommunity/postgres-exporter:v0.12.0
        imagePullPolicy: IfNotPresent
        ports:
        - name: metrics
          containerPort: 9187
          protocol: TCP
        env:
        - name: DATA_SOURCE_NAME
          value: "postgresql://$(POSTGRES_USER):$(POSTGRES_PASSWORD)@localhost:5432/$(POSTGRES_DB)?sslmode=disable"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-secrets
              key: POSTGRES_PASSWORD
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-secrets
              key: POSTGRES_USER
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgresql-secrets
              key: POSTGRES_DB
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
      volumes:
      - name: postgresql-config
        configMap:
          name: postgresql-config
  volumeClaimTemplates:
  - metadata:
      name: postgresql-data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: "fast-ssd"
      resources:
        requests:
          storage: 100Gi

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql
  namespace: legacybridge
  labels:
    app: postgresql
    component: database
spec:
  type: ClusterIP
  ports:
  - name: postgresql
    port: 5432
    targetPort: postgresql
    protocol: TCP
  - name: metrics
    port: 9187
    targetPort: metrics
    protocol: TCP
  selector:
    app: postgresql

---
# Redis Cache
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: legacybridge
  labels:
    app: redis
    component: cache
data:
  redis.conf: |
    # Redis configuration for production
    bind 0.0.0.0
    port 6379
    protected-mode no
    
    # Memory management
    maxmemory 1gb
    maxmemory-policy allkeys-lru
    
    # Persistence
    save 900 1
    save 300 10
    save 60 10000
    
    # Logging
    loglevel notice
    logfile ""
    
    # Performance
    tcp-keepalive 300
    timeout 0
    tcp-backlog 511
    
    # Security
    # requirepass will be set via environment variable

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: legacybridge
  labels:
    app: redis
    component: cache
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
        component: cache
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9121"
        prometheus.io/path: "/metrics"
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      containers:
      - name: redis
        image: redis:7-alpine
        imagePullPolicy: IfNotPresent
        ports:
        - name: redis
          containerPort: 6379
          protocol: TCP
        command:
        - redis-server
        - /etc/redis/redis.conf
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 1Gi
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: redis-config
          mountPath: /etc/redis
          readOnly: true
        - name: redis-data
          mountPath: /data
      - name: redis-exporter
        image: oliver006/redis_exporter:v1.50.0
        imagePullPolicy: IfNotPresent
        ports:
        - name: metrics
          containerPort: 9121
          protocol: TCP
        env:
        - name: REDIS_ADDR
          value: "redis://localhost:6379"
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 200m
            memory: 256Mi
      volumes:
      - name: redis-config
        configMap:
          name: redis-config
      - name: redis-data
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: legacybridge
  labels:
    app: redis
    component: cache
spec:
  type: ClusterIP
  ports:
  - name: redis
    port: 6379
    targetPort: redis
    protocol: TCP
  - name: metrics
    port: 9121
    targetPort: metrics
    protocol: TCP
  selector:
    app: redis

---
# Kong Gateway
apiVersion: v1
kind: ConfigMap
metadata:
  name: kong-config
  namespace: legacybridge
  labels:
    app: kong-gateway
    component: api-gateway
data:
  kong.conf: |
    # Kong configuration for production
    database = postgres
    pg_host = postgresql
    pg_port = 5432
    pg_database = kong
    pg_user = kong
    pg_password = kong123

    # Proxy settings
    proxy_listen = 0.0.0.0:8000
    admin_listen = 0.0.0.0:8001

    # Logging
    log_level = info
    proxy_access_log = /dev/stdout
    proxy_error_log = /dev/stderr
    admin_access_log = /dev/stdout
    admin_error_log = /dev/stderr

    # Performance
    nginx_worker_processes = auto
    nginx_daemon = off

    # Plugins
    plugins = bundled,rate-limiting,cors,jwt,request-transformer,response-transformer

    # Security
    admin_gui_url = http://localhost:8002
    admin_gui_path = /manager

---
apiVersion: v1
kind: Secret
metadata:
  name: kong-secrets
  namespace: legacybridge
  labels:
    app: kong-gateway
    component: api-gateway
type: Opaque
data:
  KONG_PG_PASSWORD: "a29uZzEyMw=="  # kong123

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kong-gateway
  namespace: legacybridge
  labels:
    app: kong-gateway
    component: api-gateway
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: kong-gateway
  template:
    metadata:
      labels:
        app: kong-gateway
        component: api-gateway
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8001"
        prometheus.io/path: "/metrics"
        sidecar.istio.io/inject: "false"  # Kong handles its own traffic management
    spec:
      serviceAccountName: kong-gateway
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      initContainers:
      - name: kong-migrations
        image: kong:3.4
        imagePullPolicy: IfNotPresent
        command:
        - /bin/bash
        - -c
        - |
          kong migrations bootstrap --conf /etc/kong/kong.conf
          kong migrations up --conf /etc/kong/kong.conf
        env:
        - name: KONG_PG_PASSWORD
          valueFrom:
            secretKeyRef:
              name: kong-secrets
              key: KONG_PG_PASSWORD
        volumeMounts:
        - name: kong-config
          mountPath: /etc/kong
          readOnly: true
      containers:
      - name: kong-gateway
        image: kong:3.4
        imagePullPolicy: IfNotPresent
        ports:
        - name: proxy
          containerPort: 8000
          protocol: TCP
        - name: admin
          containerPort: 8001
          protocol: TCP
        - name: manager
          containerPort: 8002
          protocol: TCP
        env:
        - name: KONG_PG_PASSWORD
          valueFrom:
            secretKeyRef:
              name: kong-secrets
              key: KONG_PG_PASSWORD
        - name: KONG_ADMIN_ACCESS_LOG
          value: "/dev/stdout"
        - name: KONG_ADMIN_ERROR_LOG
          value: "/dev/stderr"
        - name: KONG_PROXY_ACCESS_LOG
          value: "/dev/stdout"
        - name: KONG_PROXY_ERROR_LOG
          value: "/dev/stderr"
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /status
            port: admin
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /status
            port: admin
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: kong-config
          mountPath: /etc/kong
          readOnly: true
        - name: tmp-volume
          mountPath: /tmp
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: kong-config
        configMap:
          name: kong-config
      - name: tmp-volume
        emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - kong-gateway
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: kong-gateway
  namespace: legacybridge
  labels:
    app: kong-gateway
    component: api-gateway
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
spec:
  type: LoadBalancer
  ports:
  - name: proxy
    port: 80
    targetPort: proxy
    protocol: TCP
  - name: proxy-ssl
    port: 443
    targetPort: proxy
    protocol: TCP
  - name: admin
    port: 8001
    targetPort: admin
    protocol: TCP
  - name: manager
    port: 8002
    targetPort: manager
    protocol: TCP
  selector:
    app: kong-gateway

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kong-gateway
  namespace: legacybridge
  labels:
    app: kong-gateway
    component: rbac
