{"dashboard": {"id": null, "uid": "legacybridge-overview", "title": "LegacyBridge Overview", "tags": ["legacybridge", "production", "overview"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "editable": true, "graphTooltip": 1, "panels": [{"id": 1, "title": "Request Rate", "type": "stat", "targets": [{"expr": "rate(http_requests_total{job=\"legacybridge-backend\"}[5m])", "legendFormat": "{{method}} {{status}}"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Response Time", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"legacybridge-backend\"}[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"legacybridge-backend\"}[5m]))", "legendFormat": "50th percentile"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 6, "y": 0}}, {"id": 3, "title": "Error Rate", "type": "stat", "targets": [{"expr": "rate(http_requests_total{job=\"legacybridge-backend\",status=~\"5..\"}[5m]) / rate(http_requests_total{job=\"legacybridge-backend\"}[5m]) * 100", "legendFormat": "Error Rate"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 4, "title": "Active Conversions", "type": "timeseries", "targets": [{"expr": "conversion_jobs_active{job=\"legacybridge-backend\"}", "legendFormat": "Active Jobs"}, {"expr": "conversion_queue_size{job=\"legacybridge-backend\"}", "legendFormat": "<PERSON><PERSON> Size"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 5, "title": "Conversion Success Rate", "type": "stat", "targets": [{"expr": "rate(conversion_total{job=\"legacybridge-backend\",status=\"success\"}[5m]) / rate(conversion_total{job=\"legacybridge-backend\"}[5m]) * 100", "legendFormat": "Success Rate"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 90}, {"color": "green", "value": 95}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 8}}, {"id": 6, "title": "Memory Usage", "type": "timeseries", "targets": [{"expr": "process_resident_memory_bytes{job=\"legacybridge-backend\"}", "legendFormat": "RSS Memory"}, {"expr": "process_virtual_memory_bytes{job=\"legacybridge-backend\"}", "legendFormat": "Virtual Memory"}], "fieldConfig": {"defaults": {"unit": "bytes", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 7, "title": "CPU Usage", "type": "timeseries", "targets": [{"expr": "rate(process_cpu_seconds_total{job=\"legacybridge-backend\"}[5m]) * 100", "legendFormat": "CPU Usage"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}]}}