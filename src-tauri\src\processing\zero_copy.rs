// Zero-Copy String Processing Implementation
// Based on CURSOR-08-PERFORMANCE-OPTIMIZATION.MD specifications

use std::borrow::Cow;
use typed_arena::Arena;

pub struct ZeroCopyProcessor;

impl ZeroCopyProcessor {
    pub fn new() -> Self {
        Self
    }

    pub fn process_text<'a>(&self, text: &'a str) -> Cow<'a, str> {
        if self.needs_processing(text) {
            // Only allocate when necessary
            Cow::Owned(self.perform_processing(text))
        } else {
            // Zero-copy when possible
            Cow::Borrowed(text)
        }
    }

    pub fn escape_rtf_text<'a>(&self, text: &'a str) -> Cow<'a, str> {
        let mut needs_escaping = false;
        
        // First pass: check if escaping is needed
        for ch in text.chars() {
            if matches!(ch, '\\' | '{' | '}' | '\n' | '\r') {
                needs_escaping = true;
                break;
            }
        }

        if !needs_escaping {
            return Cow::Borrowed(text);
        }

        // Second pass: perform escaping only when needed
        let mut escaped = String::with_capacity(text.len() + text.len() / 10);
        for ch in text.chars() {
            match ch {
                '\\' => escaped.push_str("\\\\"),
                '{' => escaped.push_str("\\{"),
                '}' => escaped.push_str("\\}"),
                '\n' => escaped.push_str("\\par "),
                '\r' => {}, // Skip carriage returns
                _ => escaped.push(ch),
            }
        }
        Cow::Owned(escaped)
    }

    pub fn normalize_whitespace<'a>(&self, text: &'a str) -> Cow<'a, str> {
        // Check if normalization is needed
        let needs_normalization = text.chars().any(|c| {
            matches!(c, '\t' | '\r') || 
            (c == ' ' && text.contains("  ")) // Multiple spaces
        });

        if !needs_normalization {
            return Cow::Borrowed(text);
        }

        // Only allocate when normalization is needed
        let mut normalized = String::with_capacity(text.len());
        let mut prev_was_space = false;

        for ch in text.chars() {
            match ch {
                '\t' | '\r' => {
                    if !prev_was_space {
                        normalized.push(' ');
                        prev_was_space = true;
                    }
                }
                ' ' => {
                    if !prev_was_space {
                        normalized.push(' ');
                        prev_was_space = true;
                    }
                }
                _ => {
                    normalized.push(ch);
                    prev_was_space = false;
                }
            }
        }

        Cow::Owned(normalized)
    }

    pub fn extract_rtf_text<'a>(&self, rtf_content: &'a str) -> Cow<'a, str> {
        // Check if text is plain text (no RTF control words)
        if !rtf_content.contains('\\') && !rtf_content.contains('{') {
            return Cow::Borrowed(rtf_content);
        }

        // Only process RTF if control words are present
        let mut text = String::new();
        let mut in_control_word = false;
        let mut in_group = 0;

        for ch in rtf_content.chars() {
            match ch {
                '\\' => in_control_word = true,
                '{' => in_group += 1,
                '}' => in_group -= 1,
                ' ' if in_control_word => in_control_word = false,
                _ if !in_control_word && in_group == 0 => text.push(ch),
                _ => {}
            }
        }

        Cow::Owned(text)
    }

    pub fn batch_process_with_sharing<'a>(
        &self,
        texts: &[&'a str]
    ) -> Vec<Cow<'a, str>> {
        texts.iter()
            .map(|&text| self.normalize_whitespace(text))
            .collect()
    }

    pub fn batch_process_with_arena<'a>(
        &self,
        texts: &[&'a str],
        arena: &'a Arena<String>
    ) -> Vec<&'a str> {
        texts.iter()
            .map(|text| {
                let processed = self.process_text(text);
                match processed {
                    Cow::Borrowed(s) => s,
                    Cow::Owned(s) => arena.alloc(s),
                }
            })
            .collect()
    }

    fn needs_processing(&self, text: &str) -> bool {
        // Check if text needs any processing
        text.chars().any(|c| matches!(c, '\\' | '{' | '}' | '\t' | '\r')) ||
        text.contains("  ") // Multiple spaces
    }

    fn perform_processing(&self, text: &str) -> String {
        // Combine normalization and escaping in one pass
        let mut result = String::with_capacity(text.len() + text.len() / 10);
        let mut prev_was_space = false;

        for ch in text.chars() {
            match ch {
                '\\' => {
                    result.push_str("\\\\");
                    prev_was_space = false;
                }
                '{' => {
                    result.push_str("\\{");
                    prev_was_space = false;
                }
                '}' => {
                    result.push_str("\\}");
                    prev_was_space = false;
                }
                '\n' => {
                    result.push_str("\\par ");
                    prev_was_space = true;
                }
                '\t' | '\r' => {
                    if !prev_was_space {
                        result.push(' ');
                        prev_was_space = true;
                    }
                }
                ' ' => {
                    if !prev_was_space {
                        result.push(' ');
                        prev_was_space = true;
                    }
                }
                _ => {
                    result.push(ch);
                    prev_was_space = false;
                }
            }
        }

        result
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_zero_copy_efficiency() {
        let processor = ZeroCopyProcessor::new();
        
        // Test cases where no allocation should occur
        let clean_text = "This is clean text with no special characters";
        let result = processor.normalize_whitespace(clean_text);
        
        // Should be borrowed, not owned
        assert!(matches!(result, Cow::Borrowed(_)));
        
        // Test case where allocation is necessary
        let messy_text = "This  has\t\tmultiple\r\nwhitespace\t issues";
        let result = processor.normalize_whitespace(messy_text);
        
        // Should be owned due to normalization
        assert!(matches!(result, Cow::Owned(_)));
    }

    #[test]
    fn test_rtf_escaping_efficiency() {
        let processor = ZeroCopyProcessor::new();
        
        // Clean text should not be copied
        let clean_text = "Simple text without special characters";
        let result = processor.escape_rtf_text(clean_text);
        assert!(matches!(result, Cow::Borrowed(_)));
        
        // Text with RTF characters should be escaped
        let rtf_text = "Text with {braces} and \\backslashes";
        let result = processor.escape_rtf_text(rtf_text);
        assert!(matches!(result, Cow::Owned(_)));
        
        if let Cow::Owned(escaped) = result {
            assert!(escaped.contains("\\{braces\\}"));
            assert!(escaped.contains("\\\\backslashes"));
        }
    }

    #[test]
    fn test_arena_allocation() {
        let processor = ZeroCopyProcessor::new();
        let arena = Arena::new();
        
        let texts = vec!["clean text", "text\twith\ttabs", "more clean text"];
        let results = processor.batch_process_with_arena(&texts, &arena);
        
        assert_eq!(results.len(), 3);
        // First and third should be original references
        assert_eq!(results[0], "clean text");
        assert_eq!(results[2], "more clean text");
        // Second should be processed and allocated in arena
        assert!(results[1].contains("text with tabs"));
    }
}
