{#
Copyright 2024 LegacyBridge
Cloud SQL Template for Google Cloud Deployment Manager
#}

resources:
# Cloud SQL Instance
- name: {{ env["name"] }}-instance
  type: sqladmin.v1beta4.instance
  properties:
    region: {{ properties["region"] }}
    databaseVersion: {{ properties["databaseVersion"] }}
    settings:
      tier: {{ properties["tier"] }}
      dataDiskSizeGb: {{ properties["diskSize"] }}
      dataDiskType: {{ properties["diskType"] }}
      
      # High Availability
      availabilityType: {% if properties["highAvailability"] %}REGIONAL{% else %}ZONAL{% endif %}
      
      # Backup Configuration
      backupConfiguration:
        enabled: {{ properties["backupEnabled"] }}
        startTime: "03:00"
        pointInTimeRecoveryEnabled: true
        transactionLogRetentionDays: 7
        backupRetentionSettings:
          retentionUnit: COUNT
          retainedBackups: 7
      
      # Maintenance Window
      maintenanceWindow:
        day: {{ properties["maintenanceWindow"]["day"] }}
        hour: {{ properties["maintenanceWindow"]["hour"] }}
        updateTrack: stable
      
      # IP Configuration
      ipConfiguration:
        ipv4Enabled: true
        requireSsl: true
        authorizedNetworks: {{ properties["authorizedNetworks"] }}
      
      # Database Flags
      databaseFlags:
      - name: max_connections
        value: "200"
      - name: shared_buffers
        value: "256MB"
      - name: log_statement
        value: "all"
      
      # Insights
      insightsConfig:
        queryInsightsEnabled: true
        queryStringLength: 1024
        recordApplicationTags: true
        recordClientAddress: true
      
      # Storage
      storageAutoResize: true
      storageAutoResizeLimit: 500
      
      # User Labels
      userLabels:
        environment: production
        application: legacybridge

# Database
- name: {{ env["name"] }}-database
  type: sqladmin.v1beta4.database
  properties:
    name: legacybridge
    instance: $(ref.{{ env["name"] }}-instance.name)
    charset: UTF8
    collation: en_US.UTF8

# Database User
- name: {{ env["name"] }}-user
  type: sqladmin.v1beta4.user
  properties:
    name: legacybridge
    instance: $(ref.{{ env["name"] }}-instance.name)
    password: {{ env["project"] }}-{{ env["deployment"] }}-{{ env["name"] }}

outputs:
- name: instanceName
  value: $(ref.{{ env["name"] }}-instance.name)
- name: connectionName
  value: $(ref.{{ env["name"] }}-instance.connectionName)
- name: ipAddress
  value: $(ref.{{ env["name"] }}-instance.ipAddresses[0].ipAddress)
- name: serviceAccountEmail
  value: $(ref.{{ env["name"] }}-instance.serviceAccountEmailAddress)