// S3-compatible storage implementation
use aws_config::{BehaviorVersion, Region};
use aws_sdk_s3::{Client, Error as S3Error};
use aws_smithy_types::byte_stream::ByteStream;
use legacybridge_shared::{ServiceError, ServiceResult};
use std::collections::HashMap;
use tokio_util::io::ReaderStream;
use tracing::{info, warn, error};

pub struct S3Storage {
    client: Client,
    bucket: String,
}

impl S3Storage {
    pub async fn new() -> ServiceResult<Self> {
        // Load AWS configuration from environment
        let config = aws_config::defaults(BehaviorVersion::latest())
            .region(Region::new(
                std::env::var("AWS_REGION").unwrap_or_else(|_| "us-east-1".to_string())
            ))
            .load()
            .await;

        let client = Client::new(&config);
        let bucket = std::env::var("S3_BUCKET_NAME")
            .unwrap_or_else(|_| "legacybridge-files".to_string());

        // Test connection by listing bucket
        match client.head_bucket().bucket(&bucket).send().await {
            Ok(_) => {
                info!(bucket = %bucket, "S3 bucket connection verified");
            }
            Err(e) => {
                warn!(bucket = %bucket, error = %e, "S3 bucket not accessible, will attempt to create");
                
                // Try to create bucket if it doesn't exist
                if let Err(create_err) = client.create_bucket()
                    .bucket(&bucket)
                    .send()
                    .await 
                {
                    error!(bucket = %bucket, error = %create_err, "Failed to create S3 bucket");
                    return Err(ServiceError::Internal(format!("S3 bucket setup failed: {}", create_err)));
                }
                
                info!(bucket = %bucket, "S3 bucket created successfully");
            }
        }

        Ok(Self { client, bucket })
    }

    /// Upload a file to S3
    pub async fn upload_file(
        &self,
        key: &str,
        content: Vec<u8>,
        content_type: &str,
        metadata: Option<HashMap<String, String>>,
    ) -> ServiceResult<UploadResult> {
        let byte_stream = ByteStream::from(content.clone());
        
        let mut request = self.client
            .put_object()
            .bucket(&self.bucket)
            .key(key)
            .body(byte_stream)
            .content_type(content_type);

        // Add metadata if provided
        if let Some(meta) = metadata {
            for (k, v) in meta {
                request = request.metadata(k, v);
            }
        }

        match request.send().await {
            Ok(output) => {
                let etag = output.e_tag().unwrap_or("").trim_matches('"').to_string();
                
                info!(
                    bucket = %self.bucket,
                    key = key,
                    size = content.len(),
                    etag = %etag,
                    "File uploaded to S3"
                );

                Ok(UploadResult {
                    key: key.to_string(),
                    etag,
                    size: content.len() as u64,
                    url: format!("s3://{}/{}", self.bucket, key),
                })
            }
            Err(e) => {
                error!(bucket = %self.bucket, key = key, error = %e, "Failed to upload file to S3");
                Err(ServiceError::Internal(format!("S3 upload failed: {}", e)))
            }
        }
    }

    /// Download a file from S3
    pub async fn download_file(&self, key: &str) -> ServiceResult<DownloadResult> {
        match self.client
            .get_object()
            .bucket(&self.bucket)
            .key(key)
            .send()
            .await
        {
            Ok(output) => {
                let content_type = output.content_type().unwrap_or("application/octet-stream").to_string();
                let content_length = output.content_length().unwrap_or(0) as u64;
                let etag = output.e_tag().unwrap_or("").trim_matches('"').to_string();
                let last_modified = output.last_modified().map(|dt| dt.to_chrono_utc().unwrap());
                
                // Convert body to bytes
                let body_bytes = output.body.collect().await
                    .map_err(|e| ServiceError::Internal(format!("Failed to read S3 object body: {}", e)))?
                    .into_bytes();

                info!(
                    bucket = %self.bucket,
                    key = key,
                    size = content_length,
                    "File downloaded from S3"
                );

                Ok(DownloadResult {
                    content: body_bytes.to_vec(),
                    content_type,
                    content_length,
                    etag,
                    last_modified,
                    metadata: output.metadata().cloned().unwrap_or_default(),
                })
            }
            Err(e) => {
                error!(bucket = %self.bucket, key = key, error = %e, "Failed to download file from S3");
                Err(ServiceError::NotFound(format!("File not found in S3: {}", key)))
            }
        }
    }

    /// Delete a file from S3
    pub async fn delete_file(&self, key: &str) -> ServiceResult<()> {
        match self.client
            .delete_object()
            .bucket(&self.bucket)
            .key(key)
            .send()
            .await
        {
            Ok(_) => {
                info!(bucket = %self.bucket, key = key, "File deleted from S3");
                Ok(())
            }
            Err(e) => {
                error!(bucket = %self.bucket, key = key, error = %e, "Failed to delete file from S3");
                Err(ServiceError::Internal(format!("S3 delete failed: {}", e)))
            }
        }
    }

    /// Check if a file exists in S3
    pub async fn file_exists(&self, key: &str) -> ServiceResult<bool> {
        match self.client
            .head_object()
            .bucket(&self.bucket)
            .key(key)
            .send()
            .await
        {
            Ok(_) => Ok(true),
            Err(e) => {
                if let Some(service_err) = e.as_service_error() {
                    if service_err.is_not_found() {
                        return Ok(false);
                    }
                }
                Err(ServiceError::Internal(format!("S3 head object failed: {}", e)))
            }
        }
    }

    /// Get file metadata from S3
    pub async fn get_file_metadata(&self, key: &str) -> ServiceResult<FileMetadata> {
        match self.client
            .head_object()
            .bucket(&self.bucket)
            .key(key)
            .send()
            .await
        {
            Ok(output) => {
                Ok(FileMetadata {
                    content_type: output.content_type().unwrap_or("application/octet-stream").to_string(),
                    content_length: output.content_length().unwrap_or(0) as u64,
                    etag: output.e_tag().unwrap_or("").trim_matches('"').to_string(),
                    last_modified: output.last_modified().map(|dt| dt.to_chrono_utc().unwrap()),
                    metadata: output.metadata().cloned().unwrap_or_default(),
                })
            }
            Err(e) => {
                error!(bucket = %self.bucket, key = key, error = %e, "Failed to get file metadata from S3");
                Err(ServiceError::NotFound(format!("File not found in S3: {}", key)))
            }
        }
    }

    /// List files in S3 with prefix
    pub async fn list_files(&self, prefix: Option<&str>, max_keys: Option<i32>) -> ServiceResult<Vec<S3Object>> {
        let mut request = self.client
            .list_objects_v2()
            .bucket(&self.bucket);

        if let Some(p) = prefix {
            request = request.prefix(p);
        }

        if let Some(max) = max_keys {
            request = request.max_keys(max);
        }

        match request.send().await {
            Ok(output) => {
                let objects = output.contents()
                    .iter()
                    .map(|obj| S3Object {
                        key: obj.key().unwrap_or("").to_string(),
                        size: obj.size().unwrap_or(0) as u64,
                        last_modified: obj.last_modified().map(|dt| dt.to_chrono_utc().unwrap()),
                        etag: obj.e_tag().unwrap_or("").trim_matches('"').to_string(),
                    })
                    .collect();

                Ok(objects)
            }
            Err(e) => {
                error!(bucket = %self.bucket, error = %e, "Failed to list files in S3");
                Err(ServiceError::Internal(format!("S3 list failed: {}", e)))
            }
        }
    }

    /// Generate presigned URL for file access
    pub async fn generate_presigned_url(
        &self,
        key: &str,
        expires_in_seconds: u64,
        operation: PresignedOperation,
    ) -> ServiceResult<String> {
        let expires_in = std::time::Duration::from_secs(expires_in_seconds);

        let presigned_request = match operation {
            PresignedOperation::Get => {
                self.client
                    .get_object()
                    .bucket(&self.bucket)
                    .key(key)
                    .presigned(aws_sdk_s3::presigning::PresigningConfig::expires_in(expires_in)?)
                    .await
            }
            PresignedOperation::Put => {
                self.client
                    .put_object()
                    .bucket(&self.bucket)
                    .key(key)
                    .presigned(aws_sdk_s3::presigning::PresigningConfig::expires_in(expires_in)?)
                    .await
            }
        };

        match presigned_request {
            Ok(req) => Ok(req.uri().to_string()),
            Err(e) => {
                error!(bucket = %self.bucket, key = key, error = %e, "Failed to generate presigned URL");
                Err(ServiceError::Internal(format!("Presigned URL generation failed: {}", e)))
            }
        }
    }

    /// Get bucket name
    pub fn bucket_name(&self) -> &str {
        &self.bucket
    }
}

#[derive(Debug)]
pub struct UploadResult {
    pub key: String,
    pub etag: String,
    pub size: u64,
    pub url: String,
}

#[derive(Debug)]
pub struct DownloadResult {
    pub content: Vec<u8>,
    pub content_type: String,
    pub content_length: u64,
    pub etag: String,
    pub last_modified: Option<chrono::DateTime<chrono::Utc>>,
    pub metadata: HashMap<String, String>,
}

#[derive(Debug)]
pub struct FileMetadata {
    pub content_type: String,
    pub content_length: u64,
    pub etag: String,
    pub last_modified: Option<chrono::DateTime<chrono::Utc>>,
    pub metadata: HashMap<String, String>,
}

#[derive(Debug)]
pub struct S3Object {
    pub key: String,
    pub size: u64,
    pub last_modified: Option<chrono::DateTime<chrono::Utc>>,
    pub etag: String,
}

#[derive(Debug)]
pub enum PresignedOperation {
    Get,
    Put,
}
