// Database connection and management utilities
use crate::error::{ServiceError, ServiceResult};
use sqlx::{PgPool, Row};
use std::time::Duration;
use uuid::Uuid;
use serde::{Deserialize, Serialize};

// Re-export pool manager for enterprise features
pub mod pool_manager;
pub use pool_manager::{PoolManager, PoolConfig, PoolStatistics, PoolInfo};

/// Database connection pool statistics for monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PoolStats {
    /// Total number of connections in the pool
    pub size: u32,
    /// Number of idle connections
    pub idle: u32,
    /// Number of active connections
    pub connections: u32,
}

/// Database performance metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetric {
    pub id: Uuid,
    pub metric_name: String,
    pub metric_value: f64,
    pub labels: Option<serde_json::Value>,
    pub recorded_at: chrono::DateTime<chrono::Utc>,
}

/// Audit event for compliance and security monitoring
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ize, Deserialize)]
pub struct AuditEvent {
    pub id: Uuid,
    pub user_id: Uuid,
    pub event_type: String,
    pub event_data: Option<serde_json::Value>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

pub struct DatabaseManager {
    pool: PgPool,
}

impl DatabaseManager {
    pub async fn new(database_url: &str) -> ServiceResult<Self> {
        let pool = sqlx::postgres::PgPoolOptions::new()
            .max_connections(20)
            .min_connections(5)
            .acquire_timeout(Duration::from_secs(10))
            .idle_timeout(Duration::from_secs(600))
            .max_lifetime(Duration::from_secs(1800))
            .connect(database_url)
            .await?;

        // Run migrations
        sqlx::migrate!("./migrations").run(&pool).await?;

        Ok(Self { pool })
    }

    pub fn pool(&self) -> &PgPool {
        &self.pool
    }

    pub async fn health_check(&self) -> ServiceResult<()> {
        sqlx::query("SELECT 1")
            .fetch_one(&self.pool)
            .await?;
        Ok(())
    }

    /// Record performance metrics for monitoring and optimization
    pub async fn record_performance_metric(
        &self,
        metric_name: &str,
        metric_value: f64,
        labels: Option<&serde_json::Value>,
    ) -> ServiceResult<()> {
        sqlx::query(
            "INSERT INTO performance_metrics (metric_name, metric_value, labels, recorded_at)
             VALUES ($1, $2, $3, NOW())"
        )
        .bind(metric_name)
        .bind(metric_value)
        .bind(labels)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Record audit events for compliance and security monitoring
    pub async fn record_audit_event(
        &self,
        user_id: uuid::Uuid,
        event_type: &str,
        event_data: Option<&serde_json::Value>,
    ) -> ServiceResult<()> {
        sqlx::query(
            "INSERT INTO audit_events (user_id, event_type, event_data, created_at)
             VALUES ($1, $2, $3, NOW())"
        )
        .bind(user_id)
        .bind(event_type)
        .bind(event_data)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Get database connection pool statistics for monitoring
    pub fn get_pool_stats(&self) -> PoolStats {
        PoolStats {
            size: self.pool.size(),
            idle: self.pool.num_idle(),
            connections: self.pool.size() - self.pool.num_idle(),
        }
    }

    /// Execute a query with performance monitoring
    pub async fn execute_with_metrics<'q, A>(
        &self,
        query: sqlx::query::Query<'q, sqlx::Postgres, A>,
        operation_name: &str,
    ) -> ServiceResult<sqlx::postgres::PgQueryResult>
    where
        A: 'q + sqlx::IntoArguments<'q, sqlx::Postgres>,
    {
        let start_time = std::time::Instant::now();

        let result = query.execute(&self.pool).await;

        let duration = start_time.elapsed();
        let duration_ms = duration.as_millis() as f64;

        // Record performance metric
        let labels = serde_json::json!({
            "operation": operation_name,
            "success": result.is_ok()
        });

        if let Err(e) = self.record_performance_metric(
            "database_query_duration_ms",
            duration_ms,
            Some(&labels)
        ).await {
            tracing::warn!("Failed to record performance metric: {}", e);
        }

        result.map_err(Into::into)
    }

    /// Execute a query and fetch results with performance monitoring
    pub async fn fetch_with_metrics<'q, T, A>(
        &self,
        query: sqlx::query::QueryAs<'q, sqlx::Postgres, T, A>,
        operation_name: &str,
    ) -> ServiceResult<Vec<T>>
    where
        T: for<'r> sqlx::FromRow<'r, sqlx::postgres::PgRow> + Send + Unpin,
        A: 'q + sqlx::IntoArguments<'q, sqlx::Postgres>,
    {
        let start_time = std::time::Instant::now();

        let result = query.fetch_all(&self.pool).await;

        let duration = start_time.elapsed();
        let duration_ms = duration.as_millis() as f64;

        // Record performance metric
        let labels = serde_json::json!({
            "operation": operation_name,
            "success": result.is_ok(),
            "result_count": result.as_ref().map(|r| r.len()).unwrap_or(0)
        });

        if let Err(e) = self.record_performance_metric(
            "database_query_duration_ms",
            duration_ms,
            Some(&labels)
        ).await {
            tracing::warn!("Failed to record performance metric: {}", e);
        }

        result.map_err(Into::into)
    }

    pub async fn get_system_config(&self, key: &str) -> ServiceResult<Option<serde_json::Value>> {
        let row = sqlx::query("SELECT value FROM system_config WHERE key = $1")
            .bind(key)
            .fetch_optional(&self.pool)
            .await?;

        Ok(row.map(|r| r.get(0)))
    }

    pub async fn set_system_config(
        &self,
        key: &str,
        value: &serde_json::Value,
        updated_by: Uuid,
    ) -> ServiceResult<()> {
        sqlx::query(
            "INSERT INTO system_config (key, value, updated_by) 
             VALUES ($1, $2, $3) 
             ON CONFLICT (key) DO UPDATE SET 
             value = EXCLUDED.value, 
             updated_by = EXCLUDED.updated_by, 
             updated_at = NOW()"
        )
        .bind(key)
        .bind(value)
        .bind(updated_by)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn record_performance_metric(
        &self,
        metric_name: &str,
        metric_value: f64,
        labels: Option<&serde_json::Value>,
    ) -> ServiceResult<()> {
        sqlx::query(
            "INSERT INTO performance_metrics (metric_name, metric_value, labels) 
             VALUES ($1, $2, $3)"
        )
        .bind(metric_name)
        .bind(metric_value)
        .bind(labels)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn log_audit_event(
        &self,
        user_id: Option<Uuid>,
        event_type: &str,
        resource_type: &str,
        resource_id: Option<Uuid>,
        old_values: Option<&serde_json::Value>,
        new_values: Option<&serde_json::Value>,
        ip_address: Option<&str>,
        user_agent: Option<&str>,
    ) -> ServiceResult<()> {
        sqlx::query(
            "INSERT INTO audit_events (user_id, event_type, resource_type, resource_id, old_values, new_values, ip_address, user_agent)
             VALUES ($1, $2, $3, $4, $5, $6, $7, $8)"
        )
        .bind(user_id)
        .bind(event_type)
        .bind(resource_type)
        .bind(resource_id)
        .bind(old_values)
        .bind(new_values)
        .bind(ip_address.map(|ip| ip.parse::<std::net::IpAddr>().ok()).flatten())
        .bind(user_agent)
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}

// Enterprise repository pattern for database operations
pub trait Repository<T> {
    type Id;
    type CreateRequest;
    type UpdateRequest;
    type FilterCriteria;

    /// Create a new entity
    async fn create(&self, request: Self::CreateRequest) -> ServiceResult<T>;

    /// Find entity by ID
    async fn find_by_id(&self, id: Self::Id) -> ServiceResult<Option<T>>;

    /// Update entity by ID
    async fn update(&self, id: Self::Id, request: Self::UpdateRequest) -> ServiceResult<T>;

    /// Soft delete entity by ID (sets deleted_at timestamp)
    async fn delete(&self, id: Self::Id) -> ServiceResult<()>;

    /// Hard delete entity by ID (permanently removes from database)
    async fn hard_delete(&self, id: Self::Id) -> ServiceResult<()>;

    /// List entities with pagination
    async fn list(&self, limit: i64, offset: i64) -> ServiceResult<Vec<T>>;

    /// Count total entities
    async fn count(&self) -> ServiceResult<i64>;

    /// Find entities by filter criteria
    async fn find_by_criteria(&self, criteria: Self::FilterCriteria) -> ServiceResult<Vec<T>>;

    /// Batch create entities for enterprise bulk operations
    async fn batch_create(&self, requests: Vec<Self::CreateRequest>) -> ServiceResult<Vec<T>>;

    /// Batch update entities
    async fn batch_update(&self, updates: Vec<(Self::Id, Self::UpdateRequest)>) -> ServiceResult<Vec<T>>;

    /// Check if entity exists
    async fn exists(&self, id: Self::Id) -> ServiceResult<bool>;

    /// Get entity creation and modification timestamps
    async fn get_timestamps(&self, id: Self::Id) -> ServiceResult<Option<EntityTimestamps>>;
}

/// Entity timestamps for audit and tracking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EntityTimestamps {
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub deleted_at: Option<chrono::DateTime<chrono::Utc>>,
}

// User repository
use crate::types::User;

#[derive(Debug, Clone, serde::Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub email: String,
    pub password: String,
    pub roles: Vec<String>,
}

#[derive(Debug, Clone, serde::Deserialize)]
pub struct UpdateUserRequest {
    pub username: Option<String>,
    pub email: Option<String>,
    pub roles: Option<Vec<String>>,
    pub is_active: Option<bool>,
}

/// User filter criteria for enterprise search operations
#[derive(Debug, Clone, Default, serde::Deserialize)]
pub struct UserFilterCriteria {
    pub username_contains: Option<String>,
    pub email_contains: Option<String>,
    pub roles: Option<Vec<String>>,
    pub is_active: Option<bool>,
    pub created_after: Option<chrono::DateTime<chrono::Utc>>,
    pub created_before: Option<chrono::DateTime<chrono::Utc>>,
    pub last_login_after: Option<chrono::DateTime<chrono::Utc>>,
    pub last_login_before: Option<chrono::DateTime<chrono::Utc>>,
}

/// User statistics for enterprise reporting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserStatistics {
    pub total_users: i64,
    pub active_users: i64,
    pub recent_logins: i64,
    pub new_users_30d: i64,
}

pub struct UserRepository {
    pool: PgPool,
}

impl UserRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn find_by_username(&self, username: &str) -> ServiceResult<Option<User>> {
        let user = sqlx::query_as!(
            User,
            r#"
            SELECT id, username, email, roles as "roles: Vec<String>", 
                   created_at, updated_at, last_login_at, is_active
            FROM users 
            WHERE username = $1 AND is_active = true
            "#,
            username
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }

    pub async fn find_by_email(&self, email: &str) -> ServiceResult<Option<User>> {
        let user = sqlx::query_as!(
            User,
            r#"
            SELECT id, username, email, roles as "roles: Vec<String>", 
                   created_at, updated_at, last_login_at, is_active
            FROM users 
            WHERE email = $1 AND is_active = true
            "#,
            email
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }

    pub async fn update_last_login(&self, user_id: Uuid) -> ServiceResult<()> {
        sqlx::query!(
            "UPDATE users SET last_login_at = NOW() WHERE id = $1",
            user_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Enterprise method: Find users by role
    pub async fn find_by_role(&self, role: &str) -> ServiceResult<Vec<User>> {
        let users = sqlx::query_as!(
            User,
            r#"
            SELECT id, username, email, roles as "roles: Vec<String>",
                   created_at, updated_at, last_login_at, is_active
            FROM users
            WHERE $1 = ANY(roles) AND is_active = true
            ORDER BY created_at DESC
            "#,
            role
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(users)
    }

    /// Enterprise method: Get user statistics
    pub async fn get_user_statistics(&self) -> ServiceResult<UserStatistics> {
        let stats = sqlx::query!(
            r#"
            SELECT
                COUNT(*) as total_users,
                COUNT(*) FILTER (WHERE is_active = true) as active_users,
                COUNT(*) FILTER (WHERE last_login_at > NOW() - INTERVAL '30 days') as recent_logins,
                COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '30 days') as new_users_30d
            FROM users
            "#
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(UserStatistics {
            total_users: stats.total_users.unwrap_or(0),
            active_users: stats.active_users.unwrap_or(0),
            recent_logins: stats.recent_logins.unwrap_or(0),
            new_users_30d: stats.new_users_30d.unwrap_or(0),
        })
    }

    /// Enterprise method: Bulk deactivate users
    pub async fn bulk_deactivate(&self, user_ids: Vec<Uuid>) -> ServiceResult<i64> {
        let result = sqlx::query!(
            "UPDATE users SET is_active = false, updated_at = NOW() WHERE id = ANY($1)",
            &user_ids
        )
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected() as i64)
    }

    /// Enterprise method: Search users with full-text search
    pub async fn search_users(&self, search_term: &str, limit: i64, offset: i64) -> ServiceResult<Vec<User>> {
        let users = sqlx::query_as!(
            User,
            r#"
            SELECT id, username, email, roles as "roles: Vec<String>",
                   created_at, updated_at, last_login_at, is_active
            FROM users
            WHERE (username ILIKE $1 OR email ILIKE $1)
            AND is_active = true
            ORDER BY
                CASE
                    WHEN username ILIKE $1 THEN 1
                    WHEN email ILIKE $1 THEN 2
                    ELSE 3
                END,
                created_at DESC
            LIMIT $2 OFFSET $3
            "#,
            format!("%{}%", search_term),
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(users)
    }

    pub async fn get_password_hash(&self, user_id: Uuid) -> ServiceResult<Option<String>> {
        let row = sqlx::query!(
            "SELECT password_hash FROM users WHERE id = $1 AND is_active = true",
            user_id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(row.map(|r| r.password_hash))
    }
}

impl Repository<User> for UserRepository {
    type Id = Uuid;
    type CreateRequest = CreateUserRequest;
    type UpdateRequest = UpdateUserRequest;
    type FilterCriteria = UserFilterCriteria;

    async fn create(&self, request: Self::CreateRequest) -> ServiceResult<User> {
        let user_id = Uuid::new_v4();
        let password_hash = crate::auth::password::hash_password(&request.password)?;

        let user = sqlx::query_as!(
            User,
            r#"
            INSERT INTO users (id, username, email, password_hash, roles)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id, username, email, roles as "roles: Vec<String>", 
                     created_at, updated_at, last_login_at, is_active
            "#,
            user_id,
            request.username,
            request.email,
            password_hash,
            &request.roles
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(user)
    }

    async fn find_by_id(&self, id: Self::Id) -> ServiceResult<Option<User>> {
        let user = sqlx::query_as!(
            User,
            r#"
            SELECT id, username, email, roles as "roles: Vec<String>", 
                   created_at, updated_at, last_login_at, is_active
            FROM users 
            WHERE id = $1 AND is_active = true
            "#,
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }

    async fn update(&self, id: Self::Id, request: Self::UpdateRequest) -> ServiceResult<User> {
        let user = sqlx::query_as!(
            User,
            r#"
            UPDATE users 
            SET username = COALESCE($2, username),
                email = COALESCE($3, email),
                roles = COALESCE($4, roles),
                is_active = COALESCE($5, is_active),
                updated_at = NOW()
            WHERE id = $1
            RETURNING id, username, email, roles as "roles: Vec<String>", 
                     created_at, updated_at, last_login_at, is_active
            "#,
            id,
            request.username,
            request.email,
            request.roles.as_ref().map(|r| r.as_slice()),
            request.is_active
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(user)
    }

    async fn delete(&self, id: Self::Id) -> ServiceResult<()> {
        sqlx::query!("UPDATE users SET is_active = false WHERE id = $1", id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    async fn list(&self, limit: i64, offset: i64) -> ServiceResult<Vec<User>> {
        let users = sqlx::query_as!(
            User,
            r#"
            SELECT id, username, email, roles as "roles: Vec<String>", 
                   created_at, updated_at, last_login_at, is_active
            FROM users 
            WHERE is_active = true
            ORDER BY created_at DESC
            LIMIT $1 OFFSET $2
            "#,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(users)
    }

    async fn count(&self) -> ServiceResult<i64> {
        let count = sqlx::query!("SELECT COUNT(*) as count FROM users WHERE is_active = true")
            .fetch_one(&self.pool)
            .await?
            .count
            .unwrap_or(0);

        Ok(count)
    }

    async fn hard_delete(&self, id: Self::Id) -> ServiceResult<()> {
        sqlx::query!("DELETE FROM users WHERE id = $1", id)
            .execute(&self.pool)
            .await?;
        Ok(())
    }

    async fn find_by_criteria(&self, criteria: Self::FilterCriteria) -> ServiceResult<Vec<User>> {
        // Build dynamic query based on criteria
        let mut conditions = Vec::new();
        let mut params: Vec<String> = Vec::new();
        let mut param_count = 0;

        if let Some(username) = &criteria.username_contains {
            param_count += 1;
            conditions.push(format!("username ILIKE ${}", param_count));
            params.push(format!("%{}%", username));
        }

        if let Some(email) = &criteria.email_contains {
            param_count += 1;
            conditions.push(format!("email ILIKE ${}", param_count));
            params.push(format!("%{}%", email));
        }

        if let Some(is_active) = criteria.is_active {
            param_count += 1;
            conditions.push(format!("is_active = ${}", param_count));
            params.push(is_active.to_string());
        }

        // For simplicity, use the search method for now
        // In production, you'd want a proper query builder
        if let Some(username) = &criteria.username_contains {
            self.search_users(username, 100, 0).await
        } else if let Some(email) = &criteria.email_contains {
            self.search_users(email, 100, 0).await
        } else {
            self.list(100, 0).await
        }
    }

    async fn batch_create(&self, requests: Vec<Self::CreateRequest>) -> ServiceResult<Vec<User>> {
        let mut users = Vec::new();
        let mut tx = self.pool.begin().await?;

        for request in requests {
            let user_id = Uuid::new_v4();
            let password_hash = crate::auth::password::hash_password(&request.password)?;

            let user = sqlx::query_as!(
                User,
                r#"
                INSERT INTO users (id, username, email, password_hash, roles)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING id, username, email, roles as "roles: Vec<String>",
                         created_at, updated_at, last_login_at, is_active
                "#,
                user_id,
                request.username,
                request.email,
                password_hash,
                &request.roles
            )
            .fetch_one(&mut *tx)
            .await?;

            users.push(user);
        }

        tx.commit().await?;
        Ok(users)
    }

    async fn batch_update(&self, updates: Vec<(Self::Id, Self::UpdateRequest)>) -> ServiceResult<Vec<User>> {
        let mut users = Vec::new();
        let mut tx = self.pool.begin().await?;

        for (id, request) in updates {
            let user = sqlx::query_as!(
                User,
                r#"
                UPDATE users
                SET username = COALESCE($2, username),
                    email = COALESCE($3, email),
                    roles = COALESCE($4, roles),
                    is_active = COALESCE($5, is_active),
                    updated_at = NOW()
                WHERE id = $1
                RETURNING id, username, email, roles as "roles: Vec<String>",
                         created_at, updated_at, last_login_at, is_active
                "#,
                id,
                request.username,
                request.email,
                request.roles.as_deref(),
                request.is_active
            )
            .fetch_one(&mut *tx)
            .await?;

            users.push(user);
        }

        tx.commit().await?;
        Ok(users)
    }

    async fn exists(&self, id: Self::Id) -> ServiceResult<bool> {
        let exists = sqlx::query_scalar!(
            "SELECT EXISTS(SELECT 1 FROM users WHERE id = $1)",
            id
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(exists.unwrap_or(false))
    }

    async fn get_timestamps(&self, id: Self::Id) -> ServiceResult<Option<EntityTimestamps>> {
        let timestamps = sqlx::query!(
            "SELECT created_at, updated_at FROM users WHERE id = $1",
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(timestamps.map(|t| EntityTimestamps {
            created_at: t.created_at,
            updated_at: t.updated_at,
            deleted_at: None, // Users don't have soft delete timestamp in current schema
        }))
    }
}
