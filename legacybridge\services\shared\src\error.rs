// Error handling for microservices
use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde::{Deserialize, Serialize};
use std::fmt;
use thiserror::Error;

pub type ServiceResult<T> = Result<T, ServiceError>;

#[derive(Error, Debug)]
pub enum ServiceError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),

    #[error("Redis error: {0}")]
    Redis(#[from] redis::RedisError),

    #[error("HTTP client error: {0}")]
    HttpClient(#[from] reqwest::Error),

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("JWT error: {0}")]
    Jwt(#[from] jsonwebtoken::errors::Error),

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("Authentication failed")]
    Unauthorized,

    #[error("Access denied")]
    Forbidden,

    #[error("Resource not found: {0}")]
    NotFound(String),

    #[error("Conflict: {0}")]
    Conflict(String),

    #[error("Bad request: {0}")]
    BadRequest(String),

    #[error("Internal server error: {0}")]
    Internal(String),

    #[error("Service unavailable: {0}")]
    ServiceUnavailable(String),

    #[error("Rate limit exceeded")]
    RateLimitExceeded,

    #[error("Circuit breaker open")]
    CircuitBreakerOpen,

    #[error("Timeout")]
    Timeout,

    #[error("File operation error: {0}")]
    FileOperation(String),

    #[error("Conversion error: {0}")]
    Conversion(String),

    #[error("Configuration error: {0}")]
    Configuration(String),
}

#[derive(Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error: String,
    pub message: String,
    pub code: u16,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub request_id: Option<String>,
}

impl ServiceError {
    pub fn status_code(&self) -> StatusCode {
        match self {
            ServiceError::Validation(_) | ServiceError::BadRequest(_) => StatusCode::BAD_REQUEST,
            ServiceError::Unauthorized => StatusCode::UNAUTHORIZED,
            ServiceError::Forbidden => StatusCode::FORBIDDEN,
            ServiceError::NotFound(_) => StatusCode::NOT_FOUND,
            ServiceError::Conflict(_) => StatusCode::CONFLICT,
            ServiceError::RateLimitExceeded => StatusCode::TOO_MANY_REQUESTS,
            ServiceError::ServiceUnavailable(_) | ServiceError::CircuitBreakerOpen => {
                StatusCode::SERVICE_UNAVAILABLE
            }
            ServiceError::Timeout => StatusCode::REQUEST_TIMEOUT,
            _ => StatusCode::INTERNAL_SERVER_ERROR,
        }
    }

    pub fn error_type(&self) -> &'static str {
        match self {
            ServiceError::Database(_) => "DATABASE_ERROR",
            ServiceError::Redis(_) => "REDIS_ERROR",
            ServiceError::HttpClient(_) => "HTTP_CLIENT_ERROR",
            ServiceError::Serialization(_) => "SERIALIZATION_ERROR",
            ServiceError::Jwt(_) => "JWT_ERROR",
            ServiceError::Validation(_) => "VALIDATION_ERROR",
            ServiceError::Unauthorized => "UNAUTHORIZED",
            ServiceError::Forbidden => "FORBIDDEN",
            ServiceError::NotFound(_) => "NOT_FOUND",
            ServiceError::Conflict(_) => "CONFLICT",
            ServiceError::BadRequest(_) => "BAD_REQUEST",
            ServiceError::Internal(_) => "INTERNAL_ERROR",
            ServiceError::ServiceUnavailable(_) => "SERVICE_UNAVAILABLE",
            ServiceError::RateLimitExceeded => "RATE_LIMIT_EXCEEDED",
            ServiceError::CircuitBreakerOpen => "CIRCUIT_BREAKER_OPEN",
            ServiceError::Timeout => "TIMEOUT",
            ServiceError::FileOperation(_) => "FILE_OPERATION_ERROR",
            ServiceError::Conversion(_) => "CONVERSION_ERROR",
            ServiceError::Configuration(_) => "CONFIGURATION_ERROR",
        }
    }
}

impl IntoResponse for ServiceError {
    fn into_response(self) -> Response {
        let status = self.status_code();
        let error_response = ErrorResponse {
            error: self.error_type().to_string(),
            message: self.to_string(),
            code: status.as_u16(),
            timestamp: chrono::Utc::now(),
            request_id: None, // TODO: Extract from request context
        };

        tracing::error!(
            error = %self,
            status = %status,
            error_type = self.error_type(),
            "Service error occurred"
        );

        (status, Json(error_response)).into_response()
    }
}

// Helper functions for common error scenarios
impl ServiceError {
    pub fn validation<T: fmt::Display>(message: T) -> Self {
        Self::Validation(message.to_string())
    }

    pub fn not_found<T: fmt::Display>(resource: T) -> Self {
        Self::NotFound(resource.to_string())
    }

    pub fn bad_request<T: fmt::Display>(message: T) -> Self {
        Self::BadRequest(message.to_string())
    }

    pub fn internal<T: fmt::Display>(message: T) -> Self {
        Self::Internal(message.to_string())
    }

    pub fn conflict<T: fmt::Display>(message: T) -> Self {
        Self::Conflict(message.to_string())
    }

    pub fn service_unavailable<T: fmt::Display>(message: T) -> Self {
        Self::ServiceUnavailable(message.to_string())
    }

    pub fn file_operation<T: fmt::Display>(message: T) -> Self {
        Self::FileOperation(message.to_string())
    }

    pub fn conversion<T: fmt::Display>(message: T) -> Self {
        Self::Conversion(message.to_string())
    }

    pub fn configuration<T: fmt::Display>(message: T) -> Self {
        Self::Configuration(message.to_string())
    }
}

// Conversion from validation errors
impl From<validator::ValidationErrors> for ServiceError {
    fn from(errors: validator::ValidationErrors) -> Self {
        let error_messages: Vec<String> = errors
            .field_errors()
            .iter()
            .flat_map(|(field, errors)| {
                errors.iter().map(move |error| {
                    format!("{}: {}", field, error.message.as_ref().unwrap_or(&"Invalid value".into()))
                })
            })
            .collect();

        Self::Validation(error_messages.join(", "))
    }
}

// Conversion from anyhow errors
impl From<anyhow::Error> for ServiceError {
    fn from(error: anyhow::Error) -> Self {
        Self::Internal(error.to_string())
    }
}
