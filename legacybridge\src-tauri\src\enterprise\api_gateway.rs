// API Gateway with Rate Limiting & Throttling for LegacyBridge
use serde::{Serialize, Deserialize};
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant, SystemTime};
use std::collections::{HashMap, VecDeque};
use tokio::sync::{mpsc, Semaphore};
use std::net::IpAddr;
use uuid::Uuid;

/// API Gateway for managing rate limiting, throttling, and request routing
pub struct ApiGateway {
    /// Rate limiters per client
    rate_limiters: Arc<RwLock<HashMap<String, RateLimiter>>>,
    
    /// Global rate limiter
    global_limiter: Arc<RateLimiter>,
    
    /// Request throttler
    throttler: Arc<RequestThrottler>,
    
    /// Circuit breakers per endpoint
    circuit_breakers: Arc<RwLock<HashMap<String, CircuitBreaker>>>,
    
    /// API usage tracker
    usage_tracker: Arc<RwLock<UsageTracker>>,
    
    /// Gateway configuration
    config: GatewayConfig,
    
    /// Request queue
    request_queue: Arc<RequestQueue>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GatewayConfig {
    /// Rate limit per client (requests per minute)
    pub client_rate_limit: u32,
    
    /// Global rate limit (requests per minute)
    pub global_rate_limit: u32,
    
    /// Burst size for rate limiting
    pub burst_size: u32,
    
    /// Request timeout
    pub request_timeout: Duration,
    
    /// Maximum concurrent requests
    pub max_concurrent_requests: usize,
    
    /// Maximum queue size
    pub max_queue_size: usize,
    
    /// Enable request logging
    pub enable_request_logging: bool,
    
    /// Enable metrics collection
    pub enable_metrics: bool,
    
    /// IP-based rate limiting
    pub ip_rate_limiting: bool,
    
    /// API key validation
    pub require_api_key: bool,
    
    /// Circuit breaker configuration
    pub circuit_breaker_config: CircuitBreakerConfig,
}

impl Default for GatewayConfig {
    fn default() -> Self {
        Self {
            client_rate_limit: 1000, // 1000 requests per minute
            global_rate_limit: 10000, // 10k requests per minute
            burst_size: 100,
            request_timeout: Duration::from_secs(30),
            max_concurrent_requests: 100,
            max_queue_size: 1000,
            enable_request_logging: true,
            enable_metrics: true,
            ip_rate_limiting: true,
            require_api_key: false,
            circuit_breaker_config: CircuitBreakerConfig::default(),
        }
    }
}

/// Rate limiter using token bucket algorithm
pub struct RateLimiter {
    /// Maximum tokens (requests)
    capacity: u32,
    
    /// Current tokens available
    tokens: Arc<RwLock<f64>>,
    
    /// Refill rate (tokens per second)
    refill_rate: f64,
    
    /// Last refill time
    last_refill: Arc<RwLock<Instant>>,
    
    /// Burst size
    burst_size: u32,
}

impl RateLimiter {
    pub fn new(requests_per_minute: u32, burst_size: u32) -> Self {
        let capacity = requests_per_minute;
        let refill_rate = capacity as f64 / 60.0;
        
        Self {
            capacity,
            tokens: Arc::new(RwLock::new(capacity as f64)),
            refill_rate,
            last_refill: Arc::new(RwLock::new(Instant::now())),
            burst_size,
        }
    }
    
    /// Try to consume tokens for a request
    pub fn try_consume(&self, tokens: u32) -> Result<(), RateLimitError> {
        let mut available_tokens = self.tokens.write().unwrap();
        let mut last_refill = self.last_refill.write().unwrap();
        
        // Refill tokens based on elapsed time
        let now = Instant::now();
        let elapsed = now.duration_since(*last_refill).as_secs_f64();
        let tokens_to_add = elapsed * self.refill_rate;
        
        *available_tokens = (*available_tokens + tokens_to_add).min(self.capacity as f64);
        *last_refill = now;
        
        // Check if enough tokens available
        if *available_tokens >= tokens as f64 {
            *available_tokens -= tokens as f64;
            Ok(())
        } else {
            Err(RateLimitError::RateLimitExceeded {
                retry_after: self.calculate_retry_after(*available_tokens, tokens),
            })
        }
    }
    
    /// Calculate retry-after duration
    fn calculate_retry_after(&self, available: f64, required: u32) -> Duration {
        let tokens_needed = required as f64 - available;
        let seconds_to_wait = tokens_needed / self.refill_rate;
        Duration::from_secs_f64(seconds_to_wait)
    }
    
    /// Get current token count
    pub fn get_tokens(&self) -> f64 {
        *self.tokens.read().unwrap()
    }
}

/// Request throttler for controlling request flow
pub struct RequestThrottler {
    /// Semaphore for concurrent request limiting
    semaphore: Arc<Semaphore>,
    
    /// Request queue
    queue: Arc<RwLock<VecDeque<QueuedRequest>>>,
    
    /// Maximum queue size
    max_queue_size: usize,
}

#[derive(Debug, Clone)]
struct QueuedRequest {
    id: Uuid,
    client_id: String,
    endpoint: String,
    queued_at: Instant,
    priority: RequestPriority,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum RequestPriority {
    High = 0,
    Normal = 1,
    Low = 2,
}

impl RequestThrottler {
    pub fn new(max_concurrent: usize, max_queue_size: usize) -> Self {
        Self {
            semaphore: Arc::new(Semaphore::new(max_concurrent)),
            queue: Arc::new(RwLock::new(VecDeque::with_capacity(max_queue_size))),
            max_queue_size,
        }
    }
    
    /// Acquire permit for request processing
    pub async fn acquire(&self) -> Result<ThrottlePermit, ThrottleError> {
        match self.semaphore.clone().try_acquire_owned() {
            Ok(permit) => Ok(ThrottlePermit { _permit: permit }),
            Err(_) => {
                // Check queue size
                let queue_size = self.queue.read().unwrap().len();
                if queue_size >= self.max_queue_size {
                    return Err(ThrottleError::QueueFull);
                }
                
                // Wait for permit
                match tokio::time::timeout(
                    Duration::from_secs(30),
                    self.semaphore.clone().acquire_owned()
                ).await {
                    Ok(Ok(permit)) => Ok(ThrottlePermit { _permit: permit }),
                    Ok(Err(_)) => Err(ThrottleError::Unavailable),
                    Err(_) => Err(ThrottleError::Timeout),
                }
            }
        }
    }
}

/// Permit for request processing
pub struct ThrottlePermit {
    _permit: tokio::sync::OwnedSemaphorePermit,
}

/// Circuit breaker for fault tolerance
#[derive(Debug, Clone)]
pub struct CircuitBreaker {
    /// Circuit breaker state
    state: Arc<RwLock<CircuitState>>,
    
    /// Configuration
    config: CircuitBreakerConfig,
    
    /// Failure tracking
    failures: Arc<RwLock<VecDeque<Instant>>>,
    
    /// Success tracking
    successes: Arc<RwLock<VecDeque<Instant>>>,
}

#[derive(Debug, Clone, Copy, PartialEq)]
enum CircuitState {
    Closed,
    Open(Instant), // Time when opened
    HalfOpen,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerConfig {
    /// Failure threshold to open circuit
    pub failure_threshold: u32,
    
    /// Success threshold to close circuit
    pub success_threshold: u32,
    
    /// Time window for counting failures
    pub failure_window: Duration,
    
    /// Cool-down period when circuit is open
    pub cooldown_period: Duration,
    
    /// Timeout for requests
    pub timeout: Duration,
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self {
            failure_threshold: 5,
            success_threshold: 3,
            failure_window: Duration::from_secs(60),
            cooldown_period: Duration::from_secs(30),
            timeout: Duration::from_secs(10),
        }
    }
}

impl CircuitBreaker {
    pub fn new(config: CircuitBreakerConfig) -> Self {
        Self {
            state: Arc::new(RwLock::new(CircuitState::Closed)),
            config,
            failures: Arc::new(RwLock::new(VecDeque::new())),
            successes: Arc::new(RwLock::new(VecDeque::new())),
        }
    }
    
    /// Check if request is allowed
    pub fn is_allowed(&self) -> bool {
        let mut state = self.state.write().unwrap();
        
        match *state {
            CircuitState::Closed => true,
            CircuitState::Open(opened_at) => {
                // Check if cooldown period has passed
                if opened_at.elapsed() >= self.config.cooldown_period {
                    *state = CircuitState::HalfOpen;
                    true
                } else {
                    false
                }
            }
            CircuitState::HalfOpen => true,
        }
    }
    
    /// Record success
    pub fn record_success(&self) {
        let mut state = self.state.write().unwrap();
        let mut successes = self.successes.write().unwrap();
        
        successes.push_back(Instant::now());
        
        // Maintain window size
        let cutoff = Instant::now() - self.config.failure_window;
        successes.retain(|&instant| instant > cutoff);
        
        // Check state transitions
        match *state {
            CircuitState::HalfOpen => {
                if successes.len() >= self.config.success_threshold as usize {
                    *state = CircuitState::Closed;
                    // Clear failure history on recovery
                    self.failures.write().unwrap().clear();
                }
            }
            _ => {}
        }
    }
    
    /// Record failure
    pub fn record_failure(&self) {
        let mut state = self.state.write().unwrap();
        let mut failures = self.failures.write().unwrap();
        
        failures.push_back(Instant::now());
        
        // Maintain window size
        let cutoff = Instant::now() - self.config.failure_window;
        failures.retain(|&instant| instant > cutoff);
        
        // Check state transitions
        match *state {
            CircuitState::Closed => {
                if failures.len() >= self.config.failure_threshold as usize {
                    *state = CircuitState::Open(Instant::now());
                }
            }
            CircuitState::HalfOpen => {
                // Single failure in half-open state reopens circuit
                *state = CircuitState::Open(Instant::now());
            }
            _ => {}
        }
    }
}

/// Request queue for managing pending requests
pub struct RequestQueue {
    /// Priority queues
    high_priority: Arc<RwLock<VecDeque<ApiRequest>>>,
    normal_priority: Arc<RwLock<VecDeque<ApiRequest>>>,
    low_priority: Arc<RwLock<VecDeque<ApiRequest>>>,
    
    /// Maximum queue size
    max_size: usize,
}

impl RequestQueue {
    pub fn new(max_size: usize) -> Self {
        Self {
            high_priority: Arc::new(RwLock::new(VecDeque::new())),
            normal_priority: Arc::new(RwLock::new(VecDeque::new())),
            low_priority: Arc::new(RwLock::new(VecDeque::new())),
            max_size,
        }
    }
    
    /// Enqueue request
    pub fn enqueue(&self, request: ApiRequest) -> Result<(), QueueError> {
        let total_size = self.get_total_size();
        if total_size >= self.max_size {
            return Err(QueueError::QueueFull);
        }
        
        match request.priority {
            RequestPriority::High => {
                self.high_priority.write().unwrap().push_back(request);
            }
            RequestPriority::Normal => {
                self.normal_priority.write().unwrap().push_back(request);
            }
            RequestPriority::Low => {
                self.low_priority.write().unwrap().push_back(request);
            }
        }
        
        Ok(())
    }
    
    /// Dequeue next request
    pub fn dequeue(&self) -> Option<ApiRequest> {
        // Priority order: high -> normal -> low
        if let Some(request) = self.high_priority.write().unwrap().pop_front() {
            return Some(request);
        }
        if let Some(request) = self.normal_priority.write().unwrap().pop_front() {
            return Some(request);
        }
        if let Some(request) = self.low_priority.write().unwrap().pop_front() {
            return Some(request);
        }
        None
    }
    
    fn get_total_size(&self) -> usize {
        self.high_priority.read().unwrap().len() +
        self.normal_priority.read().unwrap().len() +
        self.low_priority.read().unwrap().len()
    }
}

/// API usage tracking
#[derive(Debug, Clone)]
pub struct UsageTracker {
    /// Usage by client
    client_usage: HashMap<String, ClientUsage>,
    
    /// Global usage statistics
    global_stats: GlobalUsageStats,
    
    /// Usage history
    history: VecDeque<UsageSnapshot>,
}

#[derive(Debug, Clone)]
pub struct ClientUsage {
    pub client_id: String,
    pub request_count: u64,
    pub error_count: u64,
    pub total_bytes: u64,
    pub last_request: Instant,
    pub endpoints: HashMap<String, EndpointUsage>,
}

#[derive(Debug, Clone)]
pub struct EndpointUsage {
    pub request_count: u64,
    pub error_count: u64,
    pub avg_response_time: Duration,
}

#[derive(Debug, Clone)]
pub struct GlobalUsageStats {
    pub total_requests: u64,
    pub total_errors: u64,
    pub total_bytes: u64,
    pub unique_clients: usize,
    pub start_time: Instant,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UsageSnapshot {
    pub timestamp: SystemTime,
    pub requests_per_minute: f64,
    pub errors_per_minute: f64,
    pub active_clients: usize,
    pub avg_response_time: Duration,
}

/// API request structure
#[derive(Debug, Clone)]
pub struct ApiRequest {
    pub id: Uuid,
    pub client_id: String,
    pub ip_address: Option<IpAddr>,
    pub api_key: Option<String>,
    pub endpoint: String,
    pub method: HttpMethod,
    pub priority: RequestPriority,
    pub created_at: Instant,
    pub metadata: HashMap<String, String>,
}

#[derive(Debug, Clone)]
pub enum HttpMethod {
    Get,
    Post,
    Put,
    Delete,
    Patch,
}

impl ApiGateway {
    pub fn new(config: GatewayConfig) -> Self {
        let global_limiter = Arc::new(RateLimiter::new(
            config.global_rate_limit,
            config.burst_size,
        ));
        
        let throttler = Arc::new(RequestThrottler::new(
            config.max_concurrent_requests,
            config.max_queue_size,
        ));
        
        let request_queue = Arc::new(RequestQueue::new(config.max_queue_size));
        
        Self {
            rate_limiters: Arc::new(RwLock::new(HashMap::new())),
            global_limiter,
            throttler,
            circuit_breakers: Arc::new(RwLock::new(HashMap::new())),
            usage_tracker: Arc::new(RwLock::new(UsageTracker {
                client_usage: HashMap::new(),
                global_stats: GlobalUsageStats {
                    total_requests: 0,
                    total_errors: 0,
                    total_bytes: 0,
                    unique_clients: 0,
                    start_time: Instant::now(),
                },
                history: VecDeque::with_capacity(1440), // 24 hours of minute snapshots
            })),
            config,
            request_queue,
        }
    }
    
    /// Process incoming API request
    pub async fn process_request(&self, request: ApiRequest) -> Result<RequestPermit, GatewayError> {
        // 1. Validate API key if required
        if self.config.require_api_key {
            if request.api_key.is_none() {
                return Err(GatewayError::Unauthorized("API key required".to_string()));
            }
            // Validate API key here
        }
        
        // 2. Check circuit breaker
        let circuit_breaker = self.get_or_create_circuit_breaker(&request.endpoint);
        if !circuit_breaker.is_allowed() {
            return Err(GatewayError::ServiceUnavailable("Circuit breaker open".to_string()));
        }
        
        // 3. Apply rate limiting
        let client_id = if self.config.ip_rate_limiting && request.ip_address.is_some() {
            format!("ip:{}", request.ip_address.unwrap())
        } else {
            request.client_id.clone()
        };
        
        // Check client rate limit
        let client_limiter = self.get_or_create_rate_limiter(&client_id);
        if let Err(e) = client_limiter.try_consume(1) {
            self.record_rate_limit_hit(&client_id);
            return Err(GatewayError::RateLimitExceeded(e));
        }
        
        // Check global rate limit
        if let Err(e) = self.global_limiter.try_consume(1) {
            return Err(GatewayError::RateLimitExceeded(e));
        }
        
        // 4. Acquire throttle permit
        let permit = self.throttler.acquire().await
            .map_err(|e| GatewayError::ThrottleError(e))?;
        
        // 5. Record usage
        self.record_request(&request);
        
        Ok(RequestPermit {
            request_id: request.id,
            circuit_breaker,
            _permit: permit,
        })
    }
    
    /// Get or create rate limiter for client
    fn get_or_create_rate_limiter(&self, client_id: &str) -> Arc<RateLimiter> {
        let mut limiters = self.rate_limiters.write().unwrap();
        
        if let Some(limiter) = limiters.get(client_id) {
            return limiter.clone();
        }
        
        let limiter = Arc::new(RateLimiter::new(
            self.config.client_rate_limit,
            self.config.burst_size,
        ));
        
        limiters.insert(client_id.to_string(), limiter.clone());
        limiter
    }
    
    /// Get or create circuit breaker for endpoint
    fn get_or_create_circuit_breaker(&self, endpoint: &str) -> Arc<CircuitBreaker> {
        let mut breakers = self.circuit_breakers.write().unwrap();
        
        if let Some(breaker) = breakers.get(endpoint) {
            return breaker.clone();
        }
        
        let breaker = Arc::new(CircuitBreaker::new(
            self.config.circuit_breaker_config.clone()
        ));
        
        breakers.insert(endpoint.to_string(), breaker.clone());
        breaker
    }
    
    /// Record request for usage tracking
    fn record_request(&self, request: &ApiRequest) {
        let mut tracker = self.usage_tracker.write().unwrap();
        
        // Update global stats
        tracker.global_stats.total_requests += 1;
        
        // Update client usage
        let client_usage = tracker.client_usage
            .entry(request.client_id.clone())
            .or_insert_with(|| ClientUsage {
                client_id: request.client_id.clone(),
                request_count: 0,
                error_count: 0,
                total_bytes: 0,
                last_request: request.created_at,
                endpoints: HashMap::new(),
            });
        
        client_usage.request_count += 1;
        client_usage.last_request = request.created_at;
        
        // Update endpoint usage
        let endpoint_usage = client_usage.endpoints
            .entry(request.endpoint.clone())
            .or_insert_with(|| EndpointUsage {
                request_count: 0,
                error_count: 0,
                avg_response_time: Duration::from_secs(0),
            });
        
        endpoint_usage.request_count += 1;
    }
    
    /// Record rate limit hit
    fn record_rate_limit_hit(&self, client_id: &str) {
        // Track rate limit violations for monitoring
        eprintln!("Rate limit hit for client: {}", client_id);
    }
    
    /// Get usage statistics
    pub fn get_usage_stats(&self) -> UsageSnapshot {
        let tracker = self.usage_tracker.read().unwrap();
        let elapsed = tracker.global_stats.start_time.elapsed();
        let minutes = elapsed.as_secs() / 60;
        
        UsageSnapshot {
            timestamp: SystemTime::now(),
            requests_per_minute: if minutes > 0 {
                tracker.global_stats.total_requests as f64 / minutes as f64
            } else {
                0.0
            },
            errors_per_minute: if minutes > 0 {
                tracker.global_stats.total_errors as f64 / minutes as f64
            } else {
                0.0
            },
            active_clients: tracker.client_usage.len(),
            avg_response_time: Duration::from_millis(100), // Placeholder
        }
    }
}

/// Request processing permit
pub struct RequestPermit {
    pub request_id: Uuid,
    circuit_breaker: Arc<CircuitBreaker>,
    _permit: ThrottlePermit,
}

impl RequestPermit {
    /// Mark request as successful
    pub fn success(self) {
        self.circuit_breaker.record_success();
    }
    
    /// Mark request as failed
    pub fn failure(self) {
        self.circuit_breaker.record_failure();
    }
}

/// Gateway errors
#[derive(Debug)]
pub enum GatewayError {
    Unauthorized(String),
    RateLimitExceeded(RateLimitError),
    ThrottleError(ThrottleError),
    ServiceUnavailable(String),
    InvalidRequest(String),
}

#[derive(Debug)]
pub enum RateLimitError {
    RateLimitExceeded { retry_after: Duration },
}

#[derive(Debug)]
pub enum ThrottleError {
    QueueFull,
    Timeout,
    Unavailable,
}

#[derive(Debug)]
pub enum QueueError {
    QueueFull,
}

impl std::fmt::Display for GatewayError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            GatewayError::Unauthorized(msg) => write!(f, "Unauthorized: {}", msg),
            GatewayError::RateLimitExceeded(e) => write!(f, "Rate limit exceeded: {:?}", e),
            GatewayError::ThrottleError(e) => write!(f, "Throttle error: {:?}", e),
            GatewayError::ServiceUnavailable(msg) => write!(f, "Service unavailable: {}", msg),
            GatewayError::InvalidRequest(msg) => write!(f, "Invalid request: {}", msg),
        }
    }
}

impl std::error::Error for GatewayError {}