// Security Limits and Rate Limiting System
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use std::collections::HashMap;
use tokio::sync::Semaphore;

use super::validator::SecurityLimits;

/// Global security limits enforcer
pub struct SecurityLimitsEnforcer {
    limits: SecurityLimits,
    rate_limiter: Arc<RateLimiter>,
    memory_tracker: Arc<Mutex<MemoryTracker>>,
    concurrent_operations: Arc<Semaphore>,
}

impl SecurityLimitsEnforcer {
    pub fn new(limits: SecurityLimits) -> Self {
        let concurrent_operations = Arc::new(Semaphore::new(
            limits.max_concurrent_operations as usize
        ));
        
        Self {
            rate_limiter: Arc::new(RateLimiter::new(limits.rate_limit_per_minute)),
            memory_tracker: Arc::new(Mutex::new(MemoryTracker::new(limits.max_memory_allocation))),
            concurrent_operations,
            limits,
        }
    }
    
    /// Check all limits before processing
    pub async fn check_limits(&self, client_id: &str, operation_size: usize) -> Result<OperationPermit, SecurityError> {
        // 1. Rate limiting check
        if !self.rate_limiter.check_rate_limit(client_id).await {
            return Err(SecurityError::RateLimitExceeded);
        }
        
        // 2. Memory allocation check
        {
            let mut tracker = self.memory_tracker.lock().unwrap();
            if !tracker.can_allocate(operation_size) {
                return Err(SecurityError::MemoryLimitExceeded);
            }
            tracker.allocate(operation_size);
        }
        
        // 3. Concurrent operations check
        let permit = self.concurrent_operations.try_acquire()
            .map_err(|_| SecurityError::TooManyConcurrentOperations)?;
        
        Ok(OperationPermit {
            _permit: permit,
            allocated_memory: operation_size,
            memory_tracker: self.memory_tracker.clone(),
        })
    }
}

/// Rate limiter using token bucket algorithm
pub struct RateLimiter {
    buckets: Arc<Mutex<HashMap<String, TokenBucket>>>,
    global_limit: u32,
}

impl RateLimiter {
    pub fn new(requests_per_minute: u32) -> Self {
        Self {
            buckets: Arc::new(Mutex::new(HashMap::new())),
            global_limit: requests_per_minute,
        }
    }
    
    pub async fn check_rate_limit(&self, client_id: &str) -> bool {
        let mut buckets = self.buckets.lock().unwrap();
        let bucket = buckets.entry(client_id.to_string())
            .or_insert_with(|| TokenBucket::new(self.global_limit));
        
        bucket.consume()
    }
}

/// Token bucket for rate limiting
struct TokenBucket {
    tokens: f64,
    max_tokens: f64,
    refill_rate: f64, // tokens per second
    last_refill: Instant,
}

impl TokenBucket {
    fn new(max_requests_per_minute: u32) -> Self {
        let max_tokens = max_requests_per_minute as f64;
        Self {
            tokens: max_tokens,
            max_tokens,
            refill_rate: max_tokens / 60.0, // per second
            last_refill: Instant::now(),
        }
    }
    
    fn consume(&mut self) -> bool {
        self.refill();
        
        if self.tokens >= 1.0 {
            self.tokens -= 1.0;
            true
        } else {
            false
        }
    }
    
    fn refill(&mut self) {
        let now = Instant::now();
        let elapsed = now.duration_since(self.last_refill).as_secs_f64();
        
        self.tokens = (self.tokens + elapsed * self.refill_rate).min(self.max_tokens);
        self.last_refill = now;
    }
}

/// Memory usage tracker
struct MemoryTracker {
    allocated: usize,
    max_allocation: usize,
}

impl MemoryTracker {
    fn new(max_allocation: usize) -> Self {
        Self {
            allocated: 0,
            max_allocation,
        }
    }
    
    fn can_allocate(&self, size: usize) -> bool {
        self.allocated + size <= self.max_allocation
    }
    
    fn allocate(&mut self, size: usize) {
        self.allocated += size;
    }
    
    fn deallocate(&mut self, size: usize) {
        self.allocated = self.allocated.saturating_sub(size);
    }
}

/// Operation permit that automatically releases resources when dropped
pub struct OperationPermit {
    _permit: tokio::sync::SemaphorePermit<'static>,
    allocated_memory: usize,
    memory_tracker: Arc<Mutex<MemoryTracker>>,
}

impl Drop for OperationPermit {
    fn drop(&mut self) {
        let mut tracker = self.memory_tracker.lock().unwrap();
        tracker.deallocate(self.allocated_memory);
    }
}

#[derive(Debug)]
pub enum SecurityError {
    RateLimitExceeded,
    MemoryLimitExceeded,
    TooManyConcurrentOperations,
    InvalidInput(String),
    SecurityThreatDetected(String),
}

impl std::fmt::Display for SecurityError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SecurityError::RateLimitExceeded => write!(f, "Rate limit exceeded"),
            SecurityError::MemoryLimitExceeded => write!(f, "Memory limit exceeded"),
            SecurityError::TooManyConcurrentOperations => write!(f, "Too many concurrent operations"),
            SecurityError::InvalidInput(msg) => write!(f, "Invalid input: {}", msg),
            SecurityError::SecurityThreatDetected(msg) => write!(f, "Security threat detected: {}", msg),
        }
    }
}

impl std::error::Error for SecurityError {}