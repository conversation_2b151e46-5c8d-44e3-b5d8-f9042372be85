apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: legacybridge

resources:
  - namespace.yaml
  - rbac.yaml
  - configmap.yaml
  - secrets.yaml
  - pvc.yaml
  - deployment.yaml
  - service.yaml
  - ingress.yaml
  - hpa.yaml
  - pdb.yaml
  - network-policy.yaml
  - cert-issuer.yaml

images:
  - name: legacybridge/backend
    newTag: v2.0.0
  - name: legacybridge/frontend
    newTag: v2.0.0

commonLabels:
  app: legacybridge
  managed-by: kustomize

configMapGenerator:
  - name: legacybridge-config
    behavior: merge
    literals:
      - version=v2.0.0

secretGenerator:
  - name: legacybridge-secrets
    behavior: merge
    envs:
      - secrets.env