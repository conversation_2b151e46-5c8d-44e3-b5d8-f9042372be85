# LegacyBridge Enterprise Deployment Guide

## Overview

LegacyBridge MCP Server supports enterprise deployments with advanced features including high availability, compliance, and customization. This guide covers the deployment process for enterprise customers.

## Enterprise vs Development Configuration

### Development Configuration
- The `.mcp.json` file in the repository is for development and testing only
- Contains hardcoded values and minimal security
- Should NOT be used in production environments

### Enterprise Configuration
- Generated dynamically based on organization requirements
- Uses environment variables and secure configuration files
- Includes enterprise features like audit logging, compliance, and HA

## Enterprise Deployment Process

### 1. Initial Setup

```bash
# Generate enterprise configuration
legacybridge-mcp enterprise init \
  --organization "ACME Corporation" \
  --license-key "XXXX-XXXX-XXXX-XXXX" \
  --admin-email "<EMAIL>"

# This creates:
# - /etc/legacybridge/deployments/{deployment-id}/enterprise.json
# - /etc/legacybridge/deployments/{deployment-id}/.mcp.json
# - /etc/legacybridge/deployments/{deployment-id}/config.toml
```

### 2. Configuration Structure

```
/etc/legacybridge/
├── deployments/
│   └── {deployment-id}/
│       ├── enterprise.json    # Enterprise configuration
│       ├── .mcp.json          # Generated MCP config
│       ├── config.toml        # Server configuration
│       └── certs/            # TLS certificates
│           ├── server.crt
│           └── server.key
├── licenses/
│   └── license.key           # License file
└── logs/
    ├── server.log           # Server logs
    └── audit.log           # Audit logs
```

### 3. Environment Variables

Enterprise deployments use environment variables for sensitive configuration:

```bash
# Required
export LEGACYBRIDGE_LICENSE_KEY="XXXX-XXXX-XXXX-XXXX"
export MCP_JWT_SECRET="your-secret-key"
export MCP_TLS_CERT_PATH="/etc/legacybridge/certs/server.crt"
export MCP_TLS_KEY_PATH="/etc/legacybridge/certs/server.key"

# Optional
export MCP_ENVIRONMENT="production"
export MCP_LOG_LEVEL="warn"
export MCP_TELEMETRY_ENDPOINT="https://telemetry.yourcompany.com"
export MCP_API_KEYS_FILE="/etc/legacybridge/api_keys.json"
```

### 4. Enterprise Features

#### High Availability
```toml
[deployment]
high_availability = true
replica_count = 3
load_balancer = "nginx"
session_affinity = true
```

#### Compliance
```toml
[compliance]
data_residency = ["US", "EU"]
encryption_at_rest = true
encryption_in_transit = true
audit_logging = true
gdpr_compliant = true
hipaa_compliant = true
```

#### Integration
```toml
[integrations]
active_directory = true
ldap_server = "ldap://ad.company.com"
saml_provider = "https://sso.company.com"
webhook_url = "https://api.company.com/webhooks"
```

### 5. Security Configuration

#### API Key Management
```json
{
  "api_keys": [
    {
      "id": "prod-api-001",
      "key": "${API_KEY_001}",
      "name": "Production API Key",
      "permissions": ["legacy:read", "legacy:convert"],
      "rate_limit": 1000,
      "expires": "2025-12-31"
    }
  ]
}
```

#### TLS Configuration
```toml
[security.tls]
enabled = true
cert_path = "/etc/legacybridge/certs/server.crt"
key_path = "/etc/legacybridge/certs/server.key"
ca_path = "/etc/legacybridge/certs/ca.crt"
verify_client = true
min_version = "1.3"
cipher_suites = [
  "TLS_AES_256_GCM_SHA384",
  "TLS_CHACHA20_POLY1305_SHA256"
]
```

### 6. Deployment Commands

```bash
# Validate configuration
legacybridge-mcp validate --config /etc/legacybridge/config.toml

# Start server
legacybridge-mcp server \
  --config /etc/legacybridge/config.toml \
  --deployment-id {deployment-id}

# Health check
curl https://localhost:443/health

# View metrics
legacybridge-mcp metrics --deployment-id {deployment-id}
```

### 7. Monitoring and Logging

#### Log Formats
- **Audit Logs**: JSON format with timestamp, user, action, resource
- **Server Logs**: Structured logs with trace IDs
- **Performance Logs**: Metrics and latency data

#### Telemetry
```toml
[monitoring.telemetry]
enabled = true
endpoint = "https://telemetry.company.com"
interval = 60
metrics = ["requests", "errors", "latency", "conversions"]
```

### 8. Backup and Recovery

```bash
# Backup configuration
legacybridge-mcp backup \
  --deployment-id {deployment-id} \
  --output /backup/legacybridge-backup.tar.gz

# Restore configuration
legacybridge-mcp restore \
  --deployment-id {deployment-id} \
  --input /backup/legacybridge-backup.tar.gz
```

### 9. Scaling Considerations

#### Horizontal Scaling
- Use load balancer with health checks
- Share cache via Redis
- Use distributed job queue

#### Vertical Scaling
```toml
[performance]
thread_pool_size = 32
max_concurrent_requests = 1000
cache_size = 2048  # MB
max_heap_size = 8192  # MB
```

### 10. Troubleshooting

#### Common Issues
1. **License Validation Failed**
   - Check license key format
   - Verify expiration date
   - Ensure deployment ID matches

2. **TLS Connection Errors**
   - Verify certificate paths
   - Check certificate validity
   - Ensure proper permissions

3. **Performance Issues**
   - Review thread pool settings
   - Check cache hit rates
   - Monitor resource usage

#### Debug Mode
```bash
# Enable debug logging
export RUST_LOG=debug
export MCP_DEBUG=true

# Run with verbose output
legacybridge-mcp server --verbose --debug
```

## Support

For enterprise support:
- Email: <EMAIL>
- Phone: 1-800-LEGACY-1
- Portal: https://support.legacybridge.com

## License

Enterprise deployments require a valid license key. Contact <EMAIL> for licensing information.