// MCP Server Deployment Configuration
use serde::{Serialize, Deserialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct McpDeploymentConfig {
    /// Server binding configuration
    pub server: ServerConfig,
    
    /// Security and authentication
    pub security: SecurityConfig,
    
    /// Performance and scaling
    pub performance: PerformanceConfig,
    
    /// Integration settings
    pub integrations: IntegrationConfig,
    
    /// Monitoring and logging
    pub monitoring: MonitoringConfig,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
    pub protocol: String, // "stdio", "http", "websocket"
    pub max_connections: usize,
    pub connection_timeout: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub enable_authentication: bool,
    pub api_keys: Vec<String>,
    pub rate_limiting: RateLimitConfig,
    pub cors_origins: Vec<String>,
    pub max_request_size: usize,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RateLimitConfig {
    pub enabled: bool,
    pub requests_per_minute: u32,
    pub burst_size: u32,
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            requests_per_minute: 60,
            burst_size: 10,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PerformanceConfig {
    pub worker_threads: usize,
    pub conversion_timeout: u64,
    pub cache_size: usize,
    pub enable_compression: bool,
    pub parallel_conversions: usize,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct IntegrationConfig {
    pub taskmaster_enabled: bool,
    pub quick_data_enabled: bool,
    pub github_enabled: bool,
    pub playwright_enabled: bool,
    pub memory_bank_enabled: bool,
}

impl Default for IntegrationConfig {
    fn default() -> Self {
        Self {
            taskmaster_enabled: true,
            quick_data_enabled: true,
            github_enabled: true,
            playwright_enabled: false,
            memory_bank_enabled: true,
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MonitoringConfig {
    pub enable_telemetry: bool,
    pub log_level: String,
    pub log_format: String,
    pub metrics_export_interval: u64,
    pub health_check_interval: u64,
}

impl Default for MonitoringConfig {
    fn default() -> Self {
        Self {
            enable_telemetry: false,
            log_level: "info".to_string(),
            log_format: "json".to_string(),
            metrics_export_interval: 60,
            health_check_interval: 30,
        }
    }
}

impl Default for McpDeploymentConfig {
    fn default() -> Self {
        Self {
            server: ServerConfig {
                host: "127.0.0.1".to_string(),
                port: 8765,
                protocol: "stdio".to_string(),
                max_connections: 100,
                connection_timeout: 30,
            },
            security: SecurityConfig {
                enable_authentication: false,
                api_keys: vec![],
                rate_limiting: RateLimitConfig::default(),
                cors_origins: vec!["*".to_string()],
                max_request_size: 50 * 1024 * 1024, // 50MB
            },
            performance: PerformanceConfig {
                worker_threads: num_cpus::get(),
                conversion_timeout: 300, // 5 minutes
                cache_size: 1000,
                enable_compression: true,
                parallel_conversions: 4,
            },
            integrations: IntegrationConfig::default(),
            monitoring: MonitoringConfig::default(),
        }
    }
}

/// Create deployment configuration for different environments
impl McpDeploymentConfig {
    /// Development environment configuration
    pub fn development() -> Self {
        Self::default()
    }
    
    /// Production environment configuration
    pub fn production() -> Self {
        Self {
            server: ServerConfig {
                host: "0.0.0.0".to_string(),
                port: 8765,
                protocol: "websocket".to_string(),
                max_connections: 1000,
                connection_timeout: 60,
            },
            security: SecurityConfig {
                enable_authentication: true,
                api_keys: vec![], // Should be loaded from environment
                rate_limiting: RateLimitConfig {
                    enabled: true,
                    requests_per_minute: 100,
                    burst_size: 20,
                },
                cors_origins: vec![], // Should be configured per deployment
                max_request_size: 100 * 1024 * 1024, // 100MB
            },
            performance: PerformanceConfig {
                worker_threads: num_cpus::get() * 2,
                conversion_timeout: 600, // 10 minutes
                cache_size: 10000,
                enable_compression: true,
                parallel_conversions: num_cpus::get(),
            },
            integrations: IntegrationConfig::default(),
            monitoring: MonitoringConfig {
                enable_telemetry: true,
                log_level: "warn".to_string(),
                log_format: "json".to_string(),
                metrics_export_interval: 300,
                health_check_interval: 60,
            },
        }
    }
    
    /// Docker container configuration
    pub fn docker() -> Self {
        let mut config = Self::production();
        config.server.host = "0.0.0.0".to_string();
        config.monitoring.log_format = "json".to_string();
        config
    }
    
    /// Load configuration from environment variables
    pub fn from_env() -> Self {
        let mut config = Self::default();
        
        // Server configuration
        if let Ok(host) = std::env::var("MCP_SERVER_HOST") {
            config.server.host = host;
        }
        if let Ok(port) = std::env::var("MCP_SERVER_PORT") {
            if let Ok(port_num) = port.parse() {
                config.server.port = port_num;
            }
        }
        if let Ok(protocol) = std::env::var("MCP_SERVER_PROTOCOL") {
            config.server.protocol = protocol;
        }
        
        // Security configuration
        if let Ok(auth) = std::env::var("MCP_ENABLE_AUTH") {
            config.security.enable_authentication = auth.parse().unwrap_or(false);
        }
        if let Ok(api_keys) = std::env::var("MCP_API_KEYS") {
            config.security.api_keys = api_keys.split(',').map(String::from).collect();
        }
        
        // Performance configuration
        if let Ok(threads) = std::env::var("MCP_WORKER_THREADS") {
            if let Ok(thread_count) = threads.parse() {
                config.performance.worker_threads = thread_count;
            }
        }
        
        // Monitoring configuration
        if let Ok(log_level) = std::env::var("MCP_LOG_LEVEL") {
            config.monitoring.log_level = log_level;
        }
        
        config
    }
    
    /// Load configuration from a TOML file
    pub fn from_file(path: &str) -> Result<Self, ConfigError> {
        let content = std::fs::read_to_string(path)
            .map_err(|e| ConfigError::IoError(e))?;
        toml::from_str(&content)
            .map_err(|e| ConfigError::ParseError(e))
    }
    
    /// Validate configuration
    pub fn validate(&self) -> Result<(), ConfigError> {
        // Validate server configuration
        match self.server.protocol.as_str() {
            "stdio" | "http" | "websocket" => {},
            _ => return Err(ConfigError::ValidationError(
                format!("Invalid protocol: {}", self.server.protocol)
            )),
        }
        
        if self.server.port == 0 {
            return Err(ConfigError::ValidationError(
                "Server port cannot be 0".to_string()
            ));
        }
        
        // Validate security configuration
        if self.security.enable_authentication && self.security.api_keys.is_empty() {
            return Err(ConfigError::ValidationError(
                "Authentication enabled but no API keys provided".to_string()
            ));
        }
        
        // Validate performance configuration
        if self.performance.worker_threads == 0 {
            return Err(ConfigError::ValidationError(
                "Worker threads must be at least 1".to_string()
            ));
        }
        
        Ok(())
    }
}

#[derive(Debug, thiserror::Error)]
pub enum ConfigError {
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("Parse error: {0}")]
    ParseError(#[from] toml::de::Error),
    
    #[error("Validation error: {0}")]
    ValidationError(String),
}