// Format Detection Module
// Advanced file format detection for all legacy and modern formats

use crate::conversion::ConversionError;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormatDetectionResult {
    pub format_id: String,
    pub confidence: f32,
    pub magic_bytes: Option<String>,
    pub supported_conversions: Vec<String>,
    pub metadata: HashMap<String, serde_json::Value>,
}

#[derive(Debug)]
pub struct FormatDetector {
    magic_signatures: Vec<MagicSignature>,
}

#[derive(Debug)]
struct MagicSignature {
    format_id: String,
    offset: usize,
    bytes: Vec<u8>,
    description: String,
}

impl FormatDetector {
    pub fn new() -> Self {
        Self {
            magic_signatures: Self::initialize_signatures(),
        }
    }
    
    /// Detect file format from content and optional filename
    pub fn detect_format(&self, content: &[u8], filename: Option<&str>) -> Result<FormatDetectionResult, ConversionError> {
        if content.is_empty() {
            return Err(ConversionError::InvalidInput("Empty content".to_string()));
        }
        
        // Try magic byte detection first
        if let Some(result) = self.detect_by_magic_bytes(content) {
            return Ok(result);
        }
        
        // Fall back to filename extension
        if let Some(filename) = filename {
            if let Some(result) = self.detect_by_extension(filename, content) {
                return Ok(result);
            }
        }
        
        // Content-based heuristics
        self.detect_by_content_analysis(content)
    }
    
    /// Validate file format and integrity
    pub fn validate_file(&self, content: &[u8], expected_format: Option<&str>, strict: bool) -> Result<crate::legacy_formats::ValidationResult, ConversionError> {
        let detection = self.detect_format(content, None)?;
        
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut is_valid = true;
        
        if let Some(expected) = expected_format {
            if detection.format_id != expected {
                if strict {
                    errors.push(format!("Expected {} but detected {}", expected, detection.format_id));
                    is_valid = false;
                } else {
                    warnings.push(format!("Expected {} but detected {}", expected, detection.format_id));
                }
            }
        }
        
        let integrity_check = self.check_integrity(content, &detection.format_id);
        if !integrity_check && strict {
            errors.push("File integrity check failed".to_string());
            is_valid = false;
        }
        
        Ok(crate::legacy_formats::ValidationResult {
            is_valid,
            detected_format: detection.format_id,
            errors,
            warnings,
            integrity_check,
        })
    }
    
    /// Analyze format details
    pub fn analyze_format_details(&self, content: &[u8], detection: &FormatDetectionResult) -> Option<serde_json::Value> {
        match detection.format_id.as_str() {
            "rtf" => self.analyze_rtf_details(content),
            "doc" => self.analyze_doc_details(content),
            "wpd" => self.analyze_wordperfect_details(content),
            "dbf" => self.analyze_dbase_details(content),
            "wk1" | "wks" | "123" => self.analyze_lotus_details(content),
            "ws" | "wsd" => self.analyze_wordstar_details(content),
            _ => None,
        }
    }
    
    fn initialize_signatures() -> Vec<MagicSignature> {
        vec![
            // RTF
            MagicSignature {
                format_id: "rtf".to_string(),
                offset: 0,
                bytes: b"{\\rtf".to_vec(),
                description: "Rich Text Format".to_string(),
            },
            
            // Microsoft Word DOC
            MagicSignature {
                format_id: "doc".to_string(),
                offset: 0,
                bytes: vec![0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1],
                description: "OLE2 Compound Document (DOC)".to_string(),
            },
            
            // WordPerfect
            MagicSignature {
                format_id: "wpd".to_string(),
                offset: 0,
                bytes: vec![0xFF, 0x57, 0x50, 0x43],
                description: "WordPerfect 5.x".to_string(),
            },
            MagicSignature {
                format_id: "wpd".to_string(),
                offset: 0,
                bytes: vec![0xFF, 0x57, 0x44, 0x58],
                description: "WordPerfect 6.x".to_string(),
            },
            
            // dBase
            MagicSignature {
                format_id: "dbf".to_string(),
                offset: 0,
                bytes: vec![0x03],
                description: "dBase III".to_string(),
            },
            MagicSignature {
                format_id: "dbf".to_string(),
                offset: 0,
                bytes: vec![0x04],
                description: "dBase IV".to_string(),
            },
            MagicSignature {
                format_id: "dbf".to_string(),
                offset: 0,
                bytes: vec![0x05],
                description: "dBase V".to_string(),
            },
            
            // Lotus 1-2-3
            MagicSignature {
                format_id: "wk1".to_string(),
                offset: 0,
                bytes: vec![0x00, 0x00, 0x02, 0x00, 0x06, 0x04, 0x04, 0x00],
                description: "Lotus WK1".to_string(),
            },
            MagicSignature {
                format_id: "wk1".to_string(),
                offset: 0,
                bytes: vec![0x00, 0x00, 0x1A, 0x00, 0x00, 0x10, 0x04, 0x00],
                description: "Lotus WK3".to_string(),
            },
            
            // WordStar
            MagicSignature {
                format_id: "ws".to_string(),
                offset: 0,
                bytes: vec![0x1D, 0x7D],
                description: "WordStar Document".to_string(),
            },
            
            // PDF
            MagicSignature {
                format_id: "pdf".to_string(),
                offset: 0,
                bytes: b"%PDF".to_vec(),
                description: "PDF Document".to_string(),
            },
            
            // Modern formats
            MagicSignature {
                format_id: "docx".to_string(),
                offset: 0,
                bytes: vec![0x50, 0x4B, 0x03, 0x04],
                description: "ZIP Archive (DOCX container)".to_string(),
            },
        ]
    }
    
    fn detect_by_magic_bytes(&self, content: &[u8]) -> Option<FormatDetectionResult> {
        for signature in &self.magic_signatures {
            if content.len() >= signature.offset + signature.bytes.len() {
                let slice = &content[signature.offset..signature.offset + signature.bytes.len()];
                if slice == signature.bytes {
                    return Some(FormatDetectionResult {
                        format_id: signature.format_id.clone(),
                        confidence: 0.95,
                        magic_bytes: Some(hex::encode(&signature.bytes)),
                        supported_conversions: self.get_supported_conversions(&signature.format_id),
                        metadata: HashMap::new(),
                    });
                }
            }
        }
        None
    }
    
    fn detect_by_extension(&self, filename: &str, content: &[u8]) -> Option<FormatDetectionResult> {
        let extension = std::path::Path::new(filename)
            .extension()
            .and_then(|ext| ext.to_str())
            .map(|ext| ext.to_lowercase());
        
        if let Some(ext) = extension {
            let format_id = match ext.as_str() {
                "rtf" => "rtf",
                "doc" => "doc",
                "wpd" | "wp" | "wp5" | "wp6" => "wpd",
                "dbf" | "db3" | "db4" => "dbf",
                "wk1" | "wks" | "123" | "wk3" | "wk4" => "wk1",
                "ws" | "wsd" => "ws",
                "md" | "markdown" => "md",
                "txt" => "txt",
                "html" | "htm" => "html",
                "xml" => "xml",
                "json" => "json",
                "csv" => "csv",
                "pdf" => "pdf",
                "docx" => "docx",
                "xlsx" => "xlsx",
                "pptx" => "pptx",
                _ => return None,
            };
            
            Some(FormatDetectionResult {
                format_id: format_id.to_string(),
                confidence: 0.7, // Lower confidence for extension-based detection
                magic_bytes: None,
                supported_conversions: self.get_supported_conversions(format_id),
                metadata: HashMap::new(),
            })
        } else {
            None
        }
    }
    
    fn detect_by_content_analysis(&self, content: &[u8]) -> Result<FormatDetectionResult, ConversionError> {
        // Text-based format detection
        if let Ok(text) = std::str::from_utf8(content) {
            // Check for Markdown patterns
            if self.looks_like_markdown(text) {
                return Ok(FormatDetectionResult {
                    format_id: "md".to_string(),
                    confidence: 0.6,
                    magic_bytes: None,
                    supported_conversions: self.get_supported_conversions("md"),
                    metadata: HashMap::new(),
                });
            }
            
            // Check for HTML patterns
            if self.looks_like_html(text) {
                return Ok(FormatDetectionResult {
                    format_id: "html".to_string(),
                    confidence: 0.6,
                    magic_bytes: None,
                    supported_conversions: self.get_supported_conversions("html"),
                    metadata: HashMap::new(),
                });
            }
            
            // Check for XML patterns
            if self.looks_like_xml(text) {
                return Ok(FormatDetectionResult {
                    format_id: "xml".to_string(),
                    confidence: 0.6,
                    magic_bytes: None,
                    supported_conversions: self.get_supported_conversions("xml"),
                    metadata: HashMap::new(),
                });
            }
            
            // Check for JSON patterns
            if self.looks_like_json(text) {
                return Ok(FormatDetectionResult {
                    format_id: "json".to_string(),
                    confidence: 0.6,
                    magic_bytes: None,
                    supported_conversions: self.get_supported_conversions("json"),
                    metadata: HashMap::new(),
                });
            }
            
            // Check for CSV patterns
            if self.looks_like_csv(text) {
                return Ok(FormatDetectionResult {
                    format_id: "csv".to_string(),
                    confidence: 0.5,
                    magic_bytes: None,
                    supported_conversions: self.get_supported_conversions("csv"),
                    metadata: HashMap::new(),
                });
            }
            
            // Default to plain text
            return Ok(FormatDetectionResult {
                format_id: "txt".to_string(),
                confidence: 0.4,
                magic_bytes: None,
                supported_conversions: self.get_supported_conversions("txt"),
                metadata: HashMap::new(),
            });
        }
        
        // Binary format - unknown
        Err(ConversionError::UnsupportedFormat("Unknown binary format".to_string()))
    }
    
    fn get_supported_conversions(&self, format_id: &str) -> Vec<String> {
        match format_id {
            "rtf" => vec!["md", "html", "txt", "docx"],
            "doc" => vec!["rtf", "md", "html", "txt"],
            "wpd" => vec!["rtf", "md", "html", "txt"],
            "dbf" => vec!["csv", "json", "md", "html"],
            "wk1" | "wks" | "123" => vec!["csv", "json", "md", "html", "xlsx"],
            "ws" | "wsd" => vec!["txt", "md", "rtf", "html"],
            "md" => vec!["rtf", "html", "pdf", "docx"],
            "html" => vec!["md", "txt", "pdf", "rtf"],
            "txt" => vec!["md", "html", "rtf"],
            "json" => vec!["xml", "csv", "md", "txt"],
            "xml" => vec!["json", "md", "html", "txt"],
            "csv" => vec!["json", "xlsx", "md", "html"],
            _ => vec![],
        }.iter().map(|s| s.to_string()).collect()
    }
    
    fn check_integrity(&self, content: &[u8], format_id: &str) -> bool {
        match format_id {
            "rtf" => {
                if let Ok(text) = std::str::from_utf8(content) {
                    text.starts_with("{\\rtf") && text.ends_with('}')
                } else {
                    false
                }
            },
            "doc" => {
                content.len() > 512 && content.starts_with(&[0xD0, 0xCF, 0x11, 0xE0])
            },
            "pdf" => content.starts_with(b"%PDF") && content.contains(b"%%EOF"),
            "json" => {
                if let Ok(text) = std::str::from_utf8(content) {
                    serde_json::from_str::<serde_json::Value>(text).is_ok()
                } else {
                    false
                }
            },
            _ => content.len() > 0,
        }
    }
    
    // Content analysis heuristics
    fn looks_like_markdown(&self, text: &str) -> bool {
        let lines: Vec<&str> = text.lines().collect();
        if lines.is_empty() {
            return false;
        }
        
        let markdown_indicators = [
            "# ",      // Headers
            "## ",
            "### ",
            "* ",      // Lists
            "- ",
            "+ ",
            "1. ",     // Numbered lists
            "```",     // Code blocks
            "[",       // Links
            "![",      // Images
            "**",      // Bold
            "*",       // Italic (less specific)
        ];
        
        let indicator_count = lines.iter()
            .take(20) // Check first 20 lines
            .filter(|line| {
                markdown_indicators.iter().any(|indicator| line.starts_with(indicator))
            })
            .count();
        
        indicator_count >= 2 // At least 2 markdown indicators
    }
    
    fn looks_like_html(&self, text: &str) -> bool {
        let html_indicators = ["<html", "<HTML", "<!DOCTYPE", "<head", "<body", "<div", "<p>", "<span"];
        html_indicators.iter().any(|indicator| text.contains(indicator))
    }
    
    fn looks_like_xml(&self, text: &str) -> bool {
        text.trim_start().starts_with("<?xml") || 
        (text.contains('<') && text.contains('>') && !self.looks_like_html(text))
    }
    
    fn looks_like_json(&self, text: &str) -> bool {
        let trimmed = text.trim();
        (trimmed.starts_with('{') && trimmed.ends_with('}')) ||
        (trimmed.starts_with('[') && trimmed.ends_with(']'))
    }
    
    fn looks_like_csv(&self, text: &str) -> bool {
        let lines: Vec<&str> = text.lines().take(5).collect();
        if lines.len() < 2 {
            return false;
        }
        
        // Check if lines have consistent comma counts
        let first_comma_count = lines[0].matches(',').count();
        if first_comma_count == 0 {
            return false;
        }
        
        lines.iter().skip(1).take(3).all(|line| {
            let comma_count = line.matches(',').count();
            comma_count == first_comma_count || comma_count == first_comma_count - 1 // Allow for empty last field
        })
    }
    
    // Format-specific analysis methods
    fn analyze_rtf_details(&self, content: &[u8]) -> Option<serde_json::Value> {
        if let Ok(text) = std::str::from_utf8(content) {
            Some(serde_json::json!({
                "rtf_version": self.extract_rtf_version(text),
                "has_tables": text.contains("\\trowd"),
                "has_images": text.contains("\\pict"),
                "font_count": text.matches("\\f").count(),
                "page_count": text.matches("\\page").count().max(1),
                "character_count": text.len()
            }))
        } else {
            None
        }
    }
    
    fn analyze_doc_details(&self, content: &[u8]) -> Option<serde_json::Value> {
        Some(serde_json::json!({
            "file_size": content.len(),
            "is_compound_document": content.starts_with(&[0xD0, 0xCF, 0x11, 0xE0]),
            "has_ole_objects": content.windows(4).any(|w| w == b"\x01\x00\x00\x00"),
            "estimated_complexity": if content.len() > 100_000 { "high" } else if content.len() > 10_000 { "medium" } else { "low" }
        }))
    }
    
    fn analyze_wordperfect_details(&self, content: &[u8]) -> Option<serde_json::Value> {
        let version = if content.starts_with(&[0xFF, 0x57, 0x50, 0x43]) {
            "5.x"
        } else if content.starts_with(&[0xFF, 0x57, 0x44, 0x58]) {
            "6.x"
        } else {
            "unknown"
        };
        
        Some(serde_json::json!({
            "wordperfect_version": version,
            "file_size": content.len(),
            "has_graphics": content.windows(4).any(|w| w == b"\x14\x01\x14\x01"),
        }))
    }
    
    fn analyze_dbase_details(&self, content: &[u8]) -> Option<serde_json::Value> {
        if content.is_empty() {
            return None;
        }
        
        let version = match content[0] {
            0x02 => "dBase II",
            0x03 => "dBase III",
            0x04 => "dBase IV",
            0x05 => "dBase V",
            0x83 => "dBase III with memo",
            0x8B => "dBase IV with memo",
            _ => "unknown",
        };
        
        // Extract record count (bytes 4-7, little endian)
        let record_count = if content.len() >= 8 {
            u32::from_le_bytes([content[4], content[5], content[6], content[7]])
        } else {
            0
        };
        
        // Extract header length (bytes 8-9, little endian)
        let header_length = if content.len() >= 10 {
            u16::from_le_bytes([content[8], content[9]])
        } else {
            0
        };
        
        Some(serde_json::json!({
            "dbase_version": version,
            "record_count": record_count,
            "header_length": header_length,
            "file_size": content.len(),
            "has_memo_fields": content[0] & 0x80 != 0,
        }))
    }
    
    fn analyze_lotus_details(&self, content: &[u8]) -> Option<serde_json::Value> {
        Some(serde_json::json!({
            "file_size": content.len(),
            "lotus_format": if content.starts_with(&[0x00, 0x00, 0x02, 0x00]) { "WK1" } else if content.starts_with(&[0x00, 0x00, 0x1A, 0x00]) { "WK3" } else { "unknown" },
            "estimated_cell_count": content.len() / 16, // Rough estimate
        }))
    }
    
    fn analyze_wordstar_details(&self, content: &[u8]) -> Option<serde_json::Value> {
        Some(serde_json::json!({
            "file_size": content.len(),
            "is_wordstar": content.starts_with(&[0x1D, 0x7D]),
            "estimated_complexity": "low", // WordStar is relatively simple
        }))
    }
    
    fn extract_rtf_version(&self, text: &str) -> String {
        if let Some(start) = text.find("{\\rtf") {
            if let Some(end) = text[start..].find(' ') {
                let version_part = &text[start + 5..start + end];
                return version_part.to_string();
            }
        }
        "1".to_string() // Default
    }
}

impl Default for FormatDetector {
    fn default() -> Self {
        Self::new()
    }
}

// Convenience functions
pub async fn detect_format(content: &[u8], filename: Option<&str>) -> Result<FormatDetectionResult, ConversionError> {
    let detector = FormatDetector::new();
    detector.detect_format(content, filename)
}