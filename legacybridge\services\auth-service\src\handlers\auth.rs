// Authentication handlers
use axum::{
    extract::{Extension, Json},
    http::StatusCode,
    response::<PERSON><PERSON> as ResponseJ<PERSON>,
};
use legacybridge_shared::{
    auth::{LoginRequest, LoginResponse, RefreshTokenRequest, TokenValidationResponse, UserInfo, password},
    database::UserRepository,
    types::{ApiResponse, DomainEvent},
    ServiceError, ServiceResult,
};
use serde::{Deserialize, Serialize};
use tracing::{info, warn, error};
use uuid::Uuid;
use validator::Validate;

use crate::AppState;

#[derive(Debug, Deserialize, Validate)]
pub struct ValidateTokenRequest {
    #[validate(length(min = 1))]
    pub token: String,
}

#[derive(Debug, Serialize)]
pub struct LogoutResponse {
    pub message: String,
}

/// User login endpoint
pub async fn login(
    Extension(state): Extension<AppState>,
    <PERSON><PERSON>(request): <PERSON><PERSON><LoginRequest>,
) -> ServiceResult<ResponseJson<ApiResponse<LoginResponse>>> {
    info!(username = %request.username, "Login attempt");

    // Validate input
    if request.username.is_empty() || request.password.is_empty() {
        return Err(ServiceError::BadRequest("Username and password are required".to_string()));
    }

    let user_repo = UserRepository::new(state.db.pool().clone());

    // Find user by username
    let user = user_repo
        .find_by_username(&request.username)
        .await?
        .ok_or_else(|| ServiceError::Unauthorized)?;

    // Get password hash
    let password_hash = user_repo
        .get_password_hash(user.id)
        .await?
        .ok_or_else(|| ServiceError::Unauthorized)?;

    // Verify password
    if !password::verify_password(&request.password, &password_hash)? {
        warn!(username = %request.username, "Invalid password");
        state.metrics.auth_failures_total.inc();
        return Err(ServiceError::Unauthorized);
    }

    // Update last login
    user_repo.update_last_login(user.id).await?;

    // Create user info
    let user_info = UserInfo {
        id: user.id,
        username: user.username.clone(),
        roles: user.roles.clone(),
    };

    // Generate tokens
    let token = state.jwt_manager.generate_token(&user_info)?;
    let refresh_token = state.jwt_manager.generate_refresh_token(&user_info)?;

    // Cache user session
    let session_key = format!("session:{}", user.id);
    state.cache.set_with_ttl(&session_key, &user_info, std::time::Duration::from_secs(3600)).await?;

    // Publish authentication event
    let auth_event = DomainEvent::UserAuthenticated {
        user_id: user.id,
        timestamp: chrono::Utc::now(),
    };
    if let Err(e) = state.event_publisher.publish(auth_event).await {
        error!(error = %e, "Failed to publish authentication event");
    }

    // Update metrics
    state.metrics.auth_events_total.inc();

    let response = LoginResponse {
        token,
        refresh_token,
        expires_in: 3600, // 1 hour
        user: user_info,
    };

    info!(user_id = %user.id, username = %user.username, "User logged in successfully");

    Ok(ResponseJson(ApiResponse::success(response)))
}

/// Token refresh endpoint
pub async fn refresh_token(
    Extension(state): Extension<AppState>,
    Json(request): Json<RefreshTokenRequest>,
) -> ServiceResult<ResponseJson<ApiResponse<LoginResponse>>> {
    info!("Token refresh attempt");

    // Validate refresh token
    let claims = state.jwt_manager.validate_refresh_token(&request.refresh_token)?;

    let user_repo = UserRepository::new(state.db.pool().clone());

    // Get current user info
    let user = user_repo
        .find_by_id(claims.sub)
        .await?
        .ok_or_else(|| ServiceError::NotFound("User not found".to_string()))?;

    let user_info = UserInfo {
        id: user.id,
        username: user.username.clone(),
        roles: user.roles.clone(),
    };

    // Generate new tokens
    let token = state.jwt_manager.generate_token(&user_info)?;
    let refresh_token = state.jwt_manager.generate_refresh_token(&user_info)?;

    // Update session cache
    let session_key = format!("session:{}", user.id);
    state.cache.set_with_ttl(&session_key, &user_info, std::time::Duration::from_secs(3600)).await?;

    let response = LoginResponse {
        token,
        refresh_token,
        expires_in: 3600,
        user: user_info,
    };

    info!(user_id = %user.id, "Token refreshed successfully");

    Ok(ResponseJson(ApiResponse::success(response)))
}

/// User logout endpoint
pub async fn logout(
    Extension(state): Extension<AppState>,
    Json(request): Json<ValidateTokenRequest>,
) -> ServiceResult<ResponseJson<ApiResponse<LogoutResponse>>> {
    info!("Logout attempt");

    // Validate token to get user ID
    match state.jwt_manager.validate_token(&request.token) {
        Ok(claims) => {
            // Remove session from cache
            let session_key = format!("session:{}", claims.sub);
            if let Err(e) = state.cache.delete(&session_key).await {
                warn!(error = %e, "Failed to remove session from cache");
            }

            info!(user_id = %claims.sub, "User logged out successfully");
        }
        Err(e) => {
            warn!(error = %e, "Invalid token in logout request");
        }
    }

    let response = LogoutResponse {
        message: "Logged out successfully".to_string(),
    };

    Ok(ResponseJson(ApiResponse::success(response)))
}

/// Token validation endpoint (for other services)
pub async fn validate_token(
    Extension(state): Extension<AppState>,
    Json(request): Json<ValidateTokenRequest>,
) -> ServiceResult<ResponseJson<ApiResponse<TokenValidationResponse>>> {
    // Validate the token
    match state.jwt_manager.validate_token(&request.token) {
        Ok(claims) => {
            // Check if session exists in cache
            let session_key = format!("session:{}", claims.sub);
            let session_exists = state.cache.exists(&session_key).await.unwrap_or(false);

            if !session_exists {
                // Session expired or doesn't exist
                let response = TokenValidationResponse {
                    valid: false,
                    user_id: None,
                    roles: vec![],
                    expires_at: 0,
                };
                return Ok(ResponseJson(ApiResponse::success(response)));
            }

            let response = TokenValidationResponse {
                valid: true,
                user_id: Some(claims.sub),
                roles: claims.roles,
                expires_at: claims.exp,
            };

            Ok(ResponseJson(ApiResponse::success(response)))
        }
        Err(_) => {
            let response = TokenValidationResponse {
                valid: false,
                user_id: None,
                roles: vec![],
                expires_at: 0,
            };
            Ok(ResponseJson(ApiResponse::success(response)))
        }
    }
}
