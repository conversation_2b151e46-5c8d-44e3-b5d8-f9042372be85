// Memory Management Module
// Phase 2.1: Memory Leak Fixes

pub mod bounded_string_cache;

pub use bounded_string_cache::{
    BoundedStringCache,
    CacheStats,
    get_cached_string,
    get_cache_stats,
    clear_cache,
};

// Re-export existing memory modules if they exist
#[cfg(feature = "memory-pools")]
pub use crate::memory_pool_optimization::*;

#[cfg(feature = "performance")]
pub use crate::performance::memory::*;
