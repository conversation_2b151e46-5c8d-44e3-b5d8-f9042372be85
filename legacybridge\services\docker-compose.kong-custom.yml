version: '3.8'

# Override for Kong with custom plugins
services:
  kong:
    build:
      context: ./kong-plugins
      dockerfile: Dockerfile
    container_name: legacybridge-kong-custom
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_DATABASE: kong
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kong
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_ADMIN_GUI_URL: http://localhost:8002
      # Enable custom plugins
      KONG_PLUGINS: bundled,legacybridge-auth,legacybridge-rate-limit,legacybridge-transformer
      KONG_LUA_PACKAGE_PATH: /usr/local/share/lua/5.1/?.lua;;
      # Performance tuning
      KONG_NGINX_WORKER_PROCESSES: auto
      KONG_NGINX_WORKER_CONNECTIONS: 1024
      # Logging
      KONG_LOG_LEVEL: info
      KONG_ACCESS_LOG: /dev/stdout
      KONG_ERROR_LOG: /dev/stderr
      # Security
      KONG_TRUSTED_IPS: 0.0.0.0/0,::/0
      KONG_REAL_IP_HEADER: X-Forwarded-For
      KONG_REAL_IP_RECURSIVE: "on"
    ports:
      - "8000:8000"  # Proxy port
      - "8001:8001"  # Admin API port
      - "8002:8002"  # Admin GUI port
      - "8443:8443"  # Proxy SSL port
      - "8444:8444"  # Admin SSL port
    depends_on:
      kong-database:
        condition: service_healthy
      kong-migration:
        condition: service_completed_successfully
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 10s
      timeout: 10s
      retries: 10
    networks:
      - legacybridge-network
    volumes:
      - kong_logs:/var/log/kong

volumes:
  kong_logs:

networks:
  legacybridge-network:
    external: true
