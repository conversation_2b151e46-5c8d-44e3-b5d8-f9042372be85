# Example Terraform Variables File
# Copy this to terraform.tfvars and update with your values

# Cloud Provider Selection (aws, azure, gcp)
cloud_provider = "aws"

# Common Configuration
environment = "production"
region      = "us-west-2"  # AWS region, Azure location, or GCP region

# GCP-specific (only needed if cloud_provider = "gcp")
# gcp_project_id = "your-gcp-project-id"

# Kubernetes Configuration
kubernetes_version = "1.28"
node_count        = 3
min_node_count    = 2
max_node_count    = 20

# Instance Types (defaults are for AWS)
# For Azure/GCP, these will be mapped automatically
node_instance_type  = "t3.large"
db_instance_type    = "db.t3.medium"
redis_instance_type = "cache.t3.micro"

# Database Configuration
db_storage_size = 100
db_version      = "15"

# Tags/Labels
tags = {
  Environment = "production"
  Project     = "LegacyBridge"
  ManagedBy   = "Terraform"
  CostCenter  = "Engineering"
}