# Job Service Auto-Scaling Configuration
# Horizontal and Vertical Pod Autoscalers for the Job Processing Service

apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: job-service-hpa
  namespace: legacybridge
  labels:
    app: job-service
    component: autoscaling
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: job-service
  minReplicas: 2
  maxReplicas: 12
  metrics:
  # CPU-based scaling
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  # Memory-based scaling
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 75
  # Custom metrics for job service
  - type: Pods
    pods:
      metric:
        name: job_queue_length
      target:
        type: AverageValue
        averageValue: "10"
  - type: Pods
    pods:
      metric:
        name: active_jobs
      target:
        type: AverageValue
        averageValue: "5"
  - type: Pods
    pods:
      metric:
        name: job_processing_rate
      target:
        type: AverageValue
        averageValue: "2"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 20
        periodSeconds: 60
      - type: Pods
        value: 1
        periodSeconds: 60
      selectPolicy: Min

---
# Vertical Pod Autoscaler for Job Service
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: job-service-vpa
  namespace: legacybridge
  labels:
    app: job-service
    component: autoscaling
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: job-service
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: job-service
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 2000m
        memory: 4Gi
      controlledResources: ["cpu", "memory"]
      controlledValues: RequestsAndLimits

---
# Pod Disruption Budget for Job Service
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: job-service-pdb
  namespace: legacybridge
  labels:
    app: job-service
    component: availability
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: job-service

---
# ServiceMonitor for Job Service Metrics
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: job-service-metrics
  namespace: legacybridge
  labels:
    app: job-service
    component: monitoring
spec:
  selector:
    matchLabels:
      app: job-service
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    honorLabels: true

---
# Custom Metrics Configuration for Job Service
apiVersion: v1
kind: ConfigMap
metadata:
  name: job-service-metrics-config
  namespace: legacybridge
  labels:
    app: job-service
    component: metrics
data:
  metrics.yaml: |
    metrics:
      job_queue_length:
        description: "Number of jobs in processing queue"
        type: "gauge"
        labels: ["job_type", "priority"]
      active_jobs:
        description: "Number of currently processing jobs"
        type: "gauge"
        labels: ["job_type"]
      job_processing_rate:
        description: "Number of jobs processed per second"
        type: "gauge"
        labels: ["job_type", "status"]
      job_duration_seconds:
        description: "Time taken to process jobs"
        type: "histogram"
        labels: ["job_type", "status"]
        buckets: [1.0, 5.0, 10.0, 30.0, 60.0, 300.0, 600.0, 1800.0]
      job_success_rate:
        description: "Job processing success rate"
        type: "gauge"
        labels: ["job_type"]
      job_retry_count:
        description: "Number of job retries"
        type: "counter"
        labels: ["job_type", "retry_reason"]
      workflow_step_duration:
        description: "Time taken for workflow steps"
        type: "histogram"
        labels: ["workflow_type", "step_name", "status"]
        buckets: [0.1, 0.5, 1.0, 5.0, 10.0, 30.0, 60.0]

---
# Prometheus Rules for Job Service
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: job-service-alerts
  namespace: legacybridge
  labels:
    app: job-service
    component: alerting
spec:
  groups:
  - name: job-service.rules
    rules:
    # High queue length alert
    - alert: JobServiceHighQueueLength
      expr: job_queue_length > 100
      for: 2m
      labels:
        severity: warning
        service: job-service
      annotations:
        summary: "Job service queue is backing up"
        description: "Job queue has {{ $value }} pending jobs"
    
    # Low job processing rate
    - alert: JobServiceLowProcessingRate
      expr: rate(job_processing_rate[5m]) < 0.1
      for: 5m
      labels:
        severity: warning
        service: job-service
      annotations:
        summary: "Job service low processing rate"
        description: "Job processing rate is {{ $value }} jobs per second"
    
    # High job failure rate
    - alert: JobServiceHighFailureRate
      expr: rate(job_processing_rate{status="failed"}[5m]) / rate(job_processing_rate[5m]) > 0.2
      for: 5m
      labels:
        severity: critical
        service: job-service
      annotations:
        summary: "Job service high failure rate"
        description: "Job failure rate is {{ $value | humanizePercentage }}"
    
    # Slow job processing
    - alert: JobServiceSlowProcessing
      expr: histogram_quantile(0.95, rate(job_duration_seconds_bucket[5m])) > 300
      for: 5m
      labels:
        severity: warning
        service: job-service
      annotations:
        summary: "Job service slow processing"
        description: "95th percentile job processing time is {{ $value }}s"
    
    # High retry rate
    - alert: JobServiceHighRetryRate
      expr: rate(job_retry_count[5m]) > 1
      for: 3m
      labels:
        severity: warning
        service: job-service
      annotations:
        summary: "Job service high retry rate"
        description: "Job retry rate is {{ $value }} retries per second"
    
    # Service down alert
    - alert: JobServiceDown
      expr: up{job="job-service"} == 0
      for: 1m
      labels:
        severity: critical
        service: job-service
      annotations:
        summary: "Job service is down"
        description: "Job service has been down for more than 1 minute"
    
    # Workflow step failures
    - alert: JobServiceWorkflowStepFailures
      expr: rate(workflow_step_duration_seconds_count{status="failed"}[5m]) > 0.1
      for: 2m
      labels:
        severity: warning
        service: job-service
      annotations:
        summary: "Job service workflow step failures"
        description: "Workflow steps are failing at {{ $value }} failures per second"

---
# Network Policy for Job Service
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: job-service-network-policy
  namespace: legacybridge
  labels:
    app: job-service
    component: security
spec:
  podSelector:
    matchLabels:
      app: job-service
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from Kong Gateway
  - from:
    - podSelector:
        matchLabels:
          app: kong-gateway
    ports:
    - protocol: TCP
      port: 3004
  # Allow traffic from other microservices
  - from:
    - podSelector:
        matchLabels:
          app: conversion-service
    - podSelector:
        matchLabels:
          app: file-service
    ports:
    - protocol: TCP
      port: 3004
  # Allow monitoring traffic
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
  egress:
  # Allow traffic to PostgreSQL
  - to:
    - podSelector:
        matchLabels:
          app: postgresql
    ports:
    - protocol: TCP
      port: 5432
  # Allow traffic to Redis
  - to:
    - podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  # Allow traffic to other services for job orchestration
  - to:
    - podSelector:
        matchLabels:
          app: conversion-service
    - podSelector:
        matchLabels:
          app: file-service
    - podSelector:
        matchLabels:
          app: auth-service
    ports:
    - protocol: TCP
      port: 3001
    - protocol: TCP
      port: 3002
    - protocol: TCP
      port: 3003
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
