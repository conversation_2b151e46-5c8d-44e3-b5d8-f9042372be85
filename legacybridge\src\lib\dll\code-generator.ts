// DLL Integration Code Generator
// Generates platform-specific wrapper code for various languages

import { IntegrationCode, Architecture } from './dll-config';

export interface CodeGeneratorOptions {
  dllPath: string;
  architecture: Architecture;
  includeExamples: boolean;
  includeErrorHandling: boolean;
  namespace?: string;
}

export interface CodeGenerator {
  generateVB6Code(options: CodeGeneratorOptions): IntegrationCode;
  generateVFP9Code(options: CodeGeneratorOptions): IntegrationCode;
  generateCSharpCode(options: CodeGeneratorOptions): IntegrationCode;
  generatePythonCode(options: CodeGeneratorOptions): IntegrationCode;
  generateAllCode(options: CodeGeneratorOptions): IntegrationCode[];
}

export const codeGenerator: CodeGenerator = {
  generateVB6Code(options: CodeGeneratorOptions): IntegrationCode {
    const dllName = options.dllPath.split('/').pop() || 'legacybridge.dll';
    
    const code = `' LegacyBridge VB6 Integration Module
' Generated on ${new Date().toISOString()}
' DLL: ${dllName}
' Architecture: ${options.architecture}

Option Explicit

' Document conversion functions
Private Declare Function ConvertDocument Lib "${dllName}" _
    (ByVal inputPath As String, ByVal outputPath As String, _
     ByVal inputFormat As String, ByVal outputFormat As String) As Long

Private Declare Function DetectFormat Lib "${dllName}" _
    (ByVal filePath As String, ByVal formatBuffer As String, _
     ByVal bufferSize As Long) As Long

Private Declare Function GetLastError Lib "${dllName}" _
    (ByVal errorBuffer As String, ByVal bufferSize As Long) As Long

Private Declare Function GetVersion Lib "${dllName}" _
    (ByVal versionBuffer As String, ByVal bufferSize As Long) As Long

' Batch processing functions
Private Declare Function BatchConvert Lib "${dllName}" _
    (ByVal inputDir As String, ByVal outputDir As String, _
     ByVal inputFormat As String, ByVal outputFormat As String, _
     ByVal recursive As Long) As Long

' Memory management
Private Declare Function InitializeLibrary Lib "${dllName}" () As Long
Private Declare Function CleanupLibrary Lib "${dllName}" () As Long

' Constants
Public Const LEGACYBRIDGE_SUCCESS = 0
Public Const LEGACYBRIDGE_ERROR_INVALID_INPUT = -1
Public Const LEGACYBRIDGE_ERROR_INVALID_OUTPUT = -2
Public Const LEGACYBRIDGE_ERROR_UNKNOWN_FORMAT = -3
Public Const LEGACYBRIDGE_ERROR_CONVERSION_FAILED = -4
Public Const LEGACYBRIDGE_ERROR_IO_ERROR = -5

${options.includeErrorHandling ? generateVB6ErrorHandling() : ''}

' Main conversion function
Public Function ConvertToMarkdown(ByVal inputFile As String, ByVal outputFile As String) As Boolean
    Dim result As Long
    Dim errorMsg As String * 1024
    
    ' Initialize library if needed
    Call InitializeLibrary
    
    ' Detect input format
    Dim detectedFormat As String * 256
    result = DetectFormat(inputFile, detectedFormat, 256)
    
    If result <> LEGACYBRIDGE_SUCCESS Then
        ${options.includeErrorHandling ? 'ConvertToMarkdown = HandleError(result)' : 'ConvertToMarkdown = False'}
        Exit Function
    End If
    
    ' Perform conversion
    result = ConvertDocument(inputFile, outputFile, Trim$(detectedFormat), "markdown")
    
    If result = LEGACYBRIDGE_SUCCESS Then
        ConvertToMarkdown = True
    Else
        ${options.includeErrorHandling ? 'ConvertToMarkdown = HandleError(result)' : 'ConvertToMarkdown = False'}
    End If
End Function

' Batch conversion function
Public Function BatchConvertToMarkdown(ByVal inputDir As String, _
                                      ByVal outputDir As String, _
                                      Optional ByVal recursive As Boolean = False) As Long
    Dim result As Long
    
    ' Initialize library if needed
    Call InitializeLibrary
    
    ' Perform batch conversion
    result = BatchConvert(inputDir, outputDir, "auto", "markdown", IIf(recursive, 1, 0))
    
    BatchConvertToMarkdown = result
End Function

' Get library version
Public Function GetLibraryVersion() As String
    Dim versionStr As String * 256
    Dim result As Long
    
    result = GetVersion(versionStr, 256)
    
    If result = LEGACYBRIDGE_SUCCESS Then
        GetLibraryVersion = Trim$(versionStr)
    Else
        GetLibraryVersion = "Unknown"
    End If
End Function

${options.includeExamples ? generateVB6Examples() : ''}

' Cleanup on module unload
Private Sub Class_Terminate()
    Call CleanupLibrary
End Sub`;

    const documentation = `VB6 Integration Module for LegacyBridge DLL

This module provides VB6 wrapper functions for the LegacyBridge document conversion library.

Key Functions:
- ConvertToMarkdown: Converts a single document to Markdown format
- BatchConvertToMarkdown: Converts all documents in a directory
- GetLibraryVersion: Returns the DLL version string

Usage:
1. Add this .bas file to your VB6 project
2. Ensure ${dllName} is in your application directory or system PATH
3. Call the conversion functions as needed

Architecture: ${options.architecture} (${options.architecture === 'x86' ? '32-bit' : '64-bit'})`;

    const examples = [
      `' Convert single file
Dim success As Boolean
success = ConvertToMarkdown("C:\\Documents\\report.rtf", "C:\\Output\\report.md")
If success Then
    MsgBox "Conversion successful!"
End If`,
      
      `' Batch convert directory
Dim filesConverted As Long
filesConverted = BatchConvertToMarkdown("C:\\LegacyDocs", "C:\\MarkdownDocs", True)
MsgBox "Converted " & filesConverted & " files"`,
      
      `' Get version
Dim version As String
version = GetLibraryVersion()
MsgBox "LegacyBridge version: " & version`
    ];

    return {
      language: 'vb6',
      code,
      fileName: 'LegacyBridge.bas',
      documentation,
      examples
    };
  },

  generateVFP9Code(options: CodeGeneratorOptions): IntegrationCode {
    const dllName = options.dllPath.split('/').pop() || 'legacybridge.dll';
    
    const code = `* LegacyBridge VFP9 Integration Class
* Generated on ${DATE()}
* DLL: ${dllName}
* Architecture: ${options.architecture}

DEFINE CLASS LegacyBridge AS Custom
    * Properties
    cDLLPath = "${dllName}"
    nLastError = 0
    cLastErrorMessage = ""
    
    * DLL Function declarations
    PROCEDURE Init
        * Document conversion functions
        DECLARE INTEGER ConvertDocument IN (This.cDLLPath) ;
            STRING inputPath, STRING outputPath, ;
            STRING inputFormat, STRING outputFormat
            
        DECLARE INTEGER DetectFormat IN (This.cDLLPath) ;
            STRING filePath, STRING @formatBuffer, ;
            INTEGER bufferSize
            
        DECLARE INTEGER GetLastError IN (This.cDLLPath) ;
            STRING @errorBuffer, INTEGER bufferSize
            
        DECLARE INTEGER GetVersion IN (This.cDLLPath) ;
            STRING @versionBuffer, INTEGER bufferSize
            
        * Batch processing
        DECLARE INTEGER BatchConvert IN (This.cDLLPath) ;
            STRING inputDir, STRING outputDir, ;
            STRING inputFormat, STRING outputFormat, ;
            INTEGER recursive
            
        * Memory management
        DECLARE INTEGER InitializeLibrary IN (This.cDLLPath)
        DECLARE INTEGER CleanupLibrary IN (This.cDLLPath)
        
        * Initialize the library
        InitializeLibrary()
    ENDPROC
    
    * Destructor
    PROCEDURE Destroy
        CleanupLibrary()
    ENDPROC
    
    ${options.includeErrorHandling ? generateVFP9ErrorHandling() : ''}
    
    * Convert single document to Markdown
    FUNCTION ConvertToMarkdown(tcInputFile, tcOutputFile)
        LOCAL lnResult, lcFormat, lcErrorMsg
        
        * Validate parameters
        IF EMPTY(tcInputFile) OR EMPTY(tcOutputFile)
            This.SetError(-1, "Invalid file paths")
            RETURN .F.
        ENDIF
        
        * Detect format
        lcFormat = SPACE(256)
        lnResult = DetectFormat(tcInputFile, @lcFormat, 256)
        
        IF lnResult != 0
            This.SetError(lnResult, "Failed to detect format")
            RETURN .F.
        ENDIF
        
        * Perform conversion
        lnResult = ConvertDocument(tcInputFile, tcOutputFile, ;
                                  ALLTRIM(lcFormat), "markdown")
        
        IF lnResult = 0
            RETURN .T.
        ELSE
            ${options.includeErrorHandling ? 'This.HandleError(lnResult)' : 'This.SetError(lnResult, "Conversion failed")'}
            RETURN .F.
        ENDIF
    ENDFUNC
    
    * Batch convert directory
    FUNCTION BatchConvertToMarkdown(tcInputDir, tcOutputDir, tlRecursive)
        LOCAL lnResult
        
        * Validate parameters
        IF EMPTY(tcInputDir) OR EMPTY(tcOutputDir)
            This.SetError(-1, "Invalid directory paths")
            RETURN 0
        ENDIF
        
        * Set default for recursive
        IF TYPE("tlRecursive") != "L"
            tlRecursive = .F.
        ENDIF
        
        * Perform batch conversion
        lnResult = BatchConvert(tcInputDir, tcOutputDir, ;
                               "auto", "markdown", IIF(tlRecursive, 1, 0))
        
        IF lnResult < 0
            ${options.includeErrorHandling ? 'This.HandleError(lnResult)' : 'This.SetError(lnResult, "Batch conversion failed")'}
        ENDIF
        
        RETURN lnResult
    ENDFUNC
    
    * Get library version
    FUNCTION GetLibraryVersion()
        LOCAL lcVersion, lnResult
        
        lcVersion = SPACE(256)
        lnResult = GetVersion(@lcVersion, 256)
        
        IF lnResult = 0
            RETURN ALLTRIM(lcVersion)
        ELSE
            RETURN "Unknown"
        ENDIF
    ENDFUNC
    
    * Set error information
    PROCEDURE SetError(tnErrorCode, tcErrorMessage)
        This.nLastError = tnErrorCode
        This.cLastErrorMessage = tcErrorMessage
    ENDPROC
    
    ${options.includeExamples ? generateVFP9Examples() : ''}
    
ENDDEFINE`;

    const documentation = `VFP9 Integration Class for LegacyBridge DLL

This class provides Visual FoxPro 9 wrapper methods for the LegacyBridge document conversion library.

Class: LegacyBridge
Methods:
- ConvertToMarkdown: Converts a single document to Markdown format
- BatchConvertToMarkdown: Converts all documents in a directory
- GetLibraryVersion: Returns the DLL version string

Usage:
1. Add this .prg file to your VFP9 project
2. Ensure ${dllName} is in your application directory or system PATH
3. Instantiate the class and call methods as needed

Architecture: ${options.architecture} (${options.architecture === 'x86' ? '32-bit' : '64-bit'})`;

    const examples = [
      `* Create instance and convert file
LOCAL loConverter
loConverter = CREATEOBJECT("LegacyBridge")

IF loConverter.ConvertToMarkdown("C:\\Documents\\report.rtf", "C:\\Output\\report.md")
    MESSAGEBOX("Conversion successful!")
ELSE
    MESSAGEBOX("Conversion failed: " + loConverter.cLastErrorMessage)
ENDIF`,
      
      `* Batch convert with recursion
LOCAL loConverter, lnCount
loConverter = CREATEOBJECT("LegacyBridge")

lnCount = loConverter.BatchConvertToMarkdown("C:\\LegacyDocs", "C:\\MarkdownDocs", .T.)
MESSAGEBOX("Converted " + TRANSFORM(lnCount) + " files")`,
      
      `* Get version information
LOCAL loConverter, lcVersion
loConverter = CREATEOBJECT("LegacyBridge")

lcVersion = loConverter.GetLibraryVersion()
MESSAGEBOX("LegacyBridge version: " + lcVersion)`
    ];

    return {
      language: 'vfp9',
      code,
      fileName: 'legacybridge.prg',
      documentation,
      examples
    };
  },

  generateCSharpCode(options: CodeGeneratorOptions): IntegrationCode {
    const dllName = options.dllPath.split('/').pop() || 'legacybridge.dll';
    const namespace = options.namespace || 'LegacyBridge.Interop';
    
    const code = `using System;
using System.Runtime.InteropServices;
using System.Text;

namespace ${namespace}
{
    /// <summary>
    /// C# wrapper for LegacyBridge document conversion DLL
    /// Generated on ${new Date().toISOString()}
    /// Architecture: ${options.architecture}
    /// </summary>
    public class LegacyBridgeWrapper : IDisposable
    {
        private const string DllName = "${dllName}";
        private bool _initialized = false;

        // DLL imports
        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int ConvertDocument(
            [MarshalAs(UnmanagedType.LPStr)] string inputPath,
            [MarshalAs(UnmanagedType.LPStr)] string outputPath,
            [MarshalAs(UnmanagedType.LPStr)] string inputFormat,
            [MarshalAs(UnmanagedType.LPStr)] string outputFormat);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int DetectFormat(
            [MarshalAs(UnmanagedType.LPStr)] string filePath,
            StringBuilder formatBuffer,
            int bufferSize);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int GetLastError(
            StringBuilder errorBuffer,
            int bufferSize);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int GetVersion(
            StringBuilder versionBuffer,
            int bufferSize);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int BatchConvert(
            [MarshalAs(UnmanagedType.LPStr)] string inputDir,
            [MarshalAs(UnmanagedType.LPStr)] string outputDir,
            [MarshalAs(UnmanagedType.LPStr)] string inputFormat,
            [MarshalAs(UnmanagedType.LPStr)] string outputFormat,
            int recursive);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int InitializeLibrary();

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int CleanupLibrary();

        // Error codes
        public const int Success = 0;
        public const int ErrorInvalidInput = -1;
        public const int ErrorInvalidOutput = -2;
        public const int ErrorUnknownFormat = -3;
        public const int ErrorConversionFailed = -4;
        public const int ErrorIOError = -5;

        public LegacyBridgeWrapper()
        {
            Initialize();
        }

        private void Initialize()
        {
            if (!_initialized)
            {
                int result = InitializeLibrary();
                if (result != Success)
                {
                    throw new Exception($"Failed to initialize LegacyBridge library: {result}");
                }
                _initialized = true;
            }
        }

        ${options.includeErrorHandling ? generateCSharpErrorHandling() : ''}

        /// <summary>
        /// Converts a document to Markdown format
        /// </summary>
        public bool ConvertToMarkdown(string inputFile, string outputFile)
        {
            if (string.IsNullOrEmpty(inputFile) || string.IsNullOrEmpty(outputFile))
            {
                throw new ArgumentException("Input and output files must be specified");
            }

            // Detect format
            var formatBuffer = new StringBuilder(256);
            int result = DetectFormat(inputFile, formatBuffer, 256);
            
            if (result != Success)
            {
                ${options.includeErrorHandling ? 'throw CreateException(result);' : 'return false;'}
            }

            // Convert document
            result = ConvertDocument(inputFile, outputFile, formatBuffer.ToString().Trim(), "markdown");
            
            if (result == Success)
            {
                return true;
            }
            else
            {
                ${options.includeErrorHandling ? 'throw CreateException(result);' : 'return false;'}
            }
        }

        /// <summary>
        /// Batch converts documents in a directory to Markdown
        /// </summary>
        public int BatchConvertToMarkdown(string inputDir, string outputDir, bool recursive = false)
        {
            if (string.IsNullOrEmpty(inputDir) || string.IsNullOrEmpty(outputDir))
            {
                throw new ArgumentException("Input and output directories must be specified");
            }

            int result = BatchConvert(inputDir, outputDir, "auto", "markdown", recursive ? 1 : 0);
            
            if (result < 0)
            {
                ${options.includeErrorHandling ? 'throw CreateException(result);' : ''}
            }
            
            return result;
        }

        /// <summary>
        /// Gets the library version
        /// </summary>
        public string GetLibraryVersion()
        {
            var versionBuffer = new StringBuilder(256);
            int result = GetVersion(versionBuffer, 256);
            
            if (result == Success)
            {
                return versionBuffer.ToString().Trim();
            }
            
            return "Unknown";
        }

        public void Dispose()
        {
            if (_initialized)
            {
                CleanupLibrary();
                _initialized = false;
            }
        }

        ${options.includeExamples ? generateCSharpExamples() : ''}
    }
}`;

    const documentation = `C# Wrapper for LegacyBridge DLL

This class provides a managed wrapper for the LegacyBridge document conversion library.

Class: LegacyBridgeWrapper
Key Methods:
- ConvertToMarkdown: Converts a single document to Markdown format
- BatchConvertToMarkdown: Converts all documents in a directory
- GetLibraryVersion: Returns the DLL version string

Usage:
1. Add this .cs file to your C# project
2. Ensure ${dllName} is accessible (same directory or PATH)
3. Use the wrapper class with proper disposal

Architecture: ${options.architecture} (${options.architecture === 'x86' ? '32-bit - ensure x86 target' : '64-bit - ensure x64 target'})`;

    const examples = [
      `// Convert single file
using (var converter = new LegacyBridgeWrapper())
{
    try
    {
        bool success = converter.ConvertToMarkdown(@"C:\\Documents\\report.rtf", @"C:\\Output\\report.md");
        Console.WriteLine($"Conversion {(success ? "succeeded" : "failed")}");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error: {ex.Message}");
    }
}`,
      
      `// Batch convert with recursion
using (var converter = new LegacyBridgeWrapper())
{
    int filesConverted = converter.BatchConvertToMarkdown(@"C:\\LegacyDocs", @"C:\\MarkdownDocs", true);
    Console.WriteLine($"Converted {filesConverted} files");
}`,
      
      `// Get version and convert with error handling
using (var converter = new LegacyBridgeWrapper())
{
    string version = converter.GetLibraryVersion();
    Console.WriteLine($"LegacyBridge version: {version}");
    
    try
    {
        converter.ConvertToMarkdown("input.rtf", "output.md");
    }
    catch (LegacyBridgeException ex)
    {
        Console.WriteLine($"Conversion error: {ex.ErrorCode} - {ex.Message}");
    }
}`
    ];

    return {
      language: 'csharp',
      code,
      fileName: 'LegacyBridgeWrapper.cs',
      documentation,
      examples
    };
  },

  generatePythonCode(options: CodeGeneratorOptions): IntegrationCode {
    const dllName = options.dllPath.split('/').pop() || 'legacybridge.dll';
    
    const code = `"""
LegacyBridge Python Bindings
Generated on ${new Date().toISOString()}
DLL: ${dllName}
Architecture: ${options.architecture}
"""

import ctypes
import os
from enum import IntEnum
from pathlib import Path
from typing import Optional, Union

class LegacyBridgeError(IntEnum):
    """Error codes from LegacyBridge DLL"""
    SUCCESS = 0
    ERROR_INVALID_INPUT = -1
    ERROR_INVALID_OUTPUT = -2
    ERROR_UNKNOWN_FORMAT = -3
    ERROR_CONVERSION_FAILED = -4
    ERROR_IO_ERROR = -5

${options.includeErrorHandling ? 'class LegacyBridgeException(Exception):\n    """Exception raised by LegacyBridge operations"""\n    def __init__(self, error_code: int, message: str):\n        self.error_code = error_code\n        self.message = message\n        super().__init__(self.message)\n' : ''}

class LegacyBridge:
    """Python wrapper for LegacyBridge document conversion DLL"""
    
    def __init__(self, dll_path: str = "${dllName}"):
        """
        Initialize the LegacyBridge wrapper
        
        Args:
            dll_path: Path to the LegacyBridge DLL
        """
        # Load the DLL
        if not os.path.isabs(dll_path):
            # Search in current directory first
            local_dll = os.path.join(os.getcwd(), dll_path)
            if os.path.exists(local_dll):
                dll_path = local_dll
        
        self._dll = ctypes.CDLL(dll_path)
        
        # Configure function signatures
        self._configure_functions()
        
        # Initialize the library
        result = self._dll.InitializeLibrary()
        if result != LegacyBridgeError.SUCCESS:
            raise RuntimeError(f"Failed to initialize LegacyBridge: {result}")
    
    def _configure_functions(self):
        """Configure ctypes function signatures"""
        # ConvertDocument
        self._dll.ConvertDocument.argtypes = [
            ctypes.c_char_p, ctypes.c_char_p,
            ctypes.c_char_p, ctypes.c_char_p
        ]
        self._dll.ConvertDocument.restype = ctypes.c_int
        
        # DetectFormat
        self._dll.DetectFormat.argtypes = [
            ctypes.c_char_p, ctypes.c_char_p, ctypes.c_int
        ]
        self._dll.DetectFormat.restype = ctypes.c_int
        
        # GetLastError
        self._dll.GetLastError.argtypes = [ctypes.c_char_p, ctypes.c_int]
        self._dll.GetLastError.restype = ctypes.c_int
        
        # GetVersion
        self._dll.GetVersion.argtypes = [ctypes.c_char_p, ctypes.c_int]
        self._dll.GetVersion.restype = ctypes.c_int
        
        # BatchConvert
        self._dll.BatchConvert.argtypes = [
            ctypes.c_char_p, ctypes.c_char_p,
            ctypes.c_char_p, ctypes.c_char_p,
            ctypes.c_int
        ]
        self._dll.BatchConvert.restype = ctypes.c_int
        
        # InitializeLibrary & CleanupLibrary
        self._dll.InitializeLibrary.argtypes = []
        self._dll.InitializeLibrary.restype = ctypes.c_int
        self._dll.CleanupLibrary.argtypes = []
        self._dll.CleanupLibrary.restype = ctypes.c_int
    
    ${options.includeErrorHandling ? generatePythonErrorHandling() : ''}
    
    def convert_to_markdown(self, input_file: Union[str, Path], 
                           output_file: Union[str, Path]) -> bool:
        """
        Convert a document to Markdown format
        
        Args:
            input_file: Path to input document
            output_file: Path for output Markdown file
            
        Returns:
            bool: True if conversion successful
            
        ${options.includeErrorHandling ? 'Raises:\n            LegacyBridgeException: If conversion fails' : ''}
        """
        input_path = str(input_file).encode('utf-8')
        output_path = str(output_file).encode('utf-8')
        
        # Detect format
        format_buffer = ctypes.create_string_buffer(256)
        result = self._dll.DetectFormat(input_path, format_buffer, 256)
        
        if result != LegacyBridgeError.SUCCESS:
            ${options.includeErrorHandling ? 'self._raise_error(result)' : 'return False'}
        
        detected_format = format_buffer.value.decode('utf-8').strip()
        
        # Convert document
        result = self._dll.ConvertDocument(
            input_path, output_path,
            detected_format.encode('utf-8'), b"markdown"
        )
        
        if result == LegacyBridgeError.SUCCESS:
            return True
        else:
            ${options.includeErrorHandling ? 'self._raise_error(result)' : 'return False'}
    
    def batch_convert_to_markdown(self, input_dir: Union[str, Path],
                                 output_dir: Union[str, Path],
                                 recursive: bool = False) -> int:
        """
        Batch convert documents in a directory to Markdown
        
        Args:
            input_dir: Input directory path
            output_dir: Output directory path
            recursive: Process subdirectories recursively
            
        Returns:
            int: Number of files converted
            
        ${options.includeErrorHandling ? 'Raises:\n            LegacyBridgeException: If batch conversion fails' : ''}
        """
        input_path = str(input_dir).encode('utf-8')
        output_path = str(output_dir).encode('utf-8')
        
        result = self._dll.BatchConvert(
            input_path, output_path,
            b"auto", b"markdown",
            1 if recursive else 0
        )
        
        if result < 0:
            ${options.includeErrorHandling ? 'self._raise_error(result)' : 'pass'}
        
        return result
    
    def get_version(self) -> str:
        """Get the LegacyBridge library version"""
        version_buffer = ctypes.create_string_buffer(256)
        result = self._dll.GetVersion(version_buffer, 256)
        
        if result == LegacyBridgeError.SUCCESS:
            return version_buffer.value.decode('utf-8').strip()
        return "Unknown"
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - cleanup library"""
        self._dll.CleanupLibrary()
    
    def __del__(self):
        """Destructor - ensure cleanup"""
        if hasattr(self, '_dll'):
            self._dll.CleanupLibrary()

${options.includeExamples ? generatePythonExamples() : ''}`;

    const documentation = `Python Bindings for LegacyBridge DLL

This module provides Python bindings for the LegacyBridge document conversion library.

Class: LegacyBridge
Key Methods:
- convert_to_markdown: Converts a single document to Markdown format
- batch_convert_to_markdown: Converts all documents in a directory
- get_version: Returns the DLL version string

Usage:
1. Ensure ${dllName} is in your Python script directory or system PATH
2. Import and use the LegacyBridge class
3. Use context manager (with statement) for automatic cleanup

Architecture: ${options.architecture} (${options.architecture === 'x86' ? '32-bit - use 32-bit Python' : '64-bit - use 64-bit Python'})`;

    const examples = [
      `# Convert single file
from legacybridge import LegacyBridge

with LegacyBridge() as converter:
    success = converter.convert_to_markdown("document.rtf", "output.md")
    if success:
        print("Conversion successful!")`,
      
      `# Batch convert with recursion
from pathlib import Path

with LegacyBridge() as converter:
    files_converted = converter.batch_convert_to_markdown(
        Path("legacy_docs"),
        Path("markdown_docs"),
        recursive=True
    )
    print(f"Converted {files_converted} files")`,
      
      `# Error handling example
from legacybridge import LegacyBridge, LegacyBridgeException

try:
    with LegacyBridge() as converter:
        version = converter.get_version()
        print(f"LegacyBridge version: {version}")
        
        converter.convert_to_markdown("input.rtf", "output.md")
except LegacyBridgeException as e:
    print(f"Conversion error {e.error_code}: {e.message}")
except Exception as e:
    print(f"Unexpected error: {e}")`
    ];

    return {
      language: 'python',
      code,
      fileName: 'legacybridge.py',
      documentation,
      examples
    };
  },

  generateAllCode(options: CodeGeneratorOptions): IntegrationCode[] {
    return [
      this.generateVB6Code(options),
      this.generateVFP9Code(options),
      this.generateCSharpCode(options),
      this.generatePythonCode(options)
    ];
  }
};

// Helper functions for error handling code generation
function generateVB6ErrorHandling(): string {
  return `
' Error handling function
Private Function HandleError(ByVal errorCode As Long) As Boolean
    Dim errorMsg As String * 1024
    Dim result As Long
    
    result = GetLastError(errorMsg, 1024)
    
    Select Case errorCode
        Case LEGACYBRIDGE_ERROR_INVALID_INPUT
            Err.Raise vbObjectError + 1001, "LegacyBridge", "Invalid input file: " & Trim$(errorMsg)
        Case LEGACYBRIDGE_ERROR_INVALID_OUTPUT
            Err.Raise vbObjectError + 1002, "LegacyBridge", "Invalid output file: " & Trim$(errorMsg)
        Case LEGACYBRIDGE_ERROR_UNKNOWN_FORMAT
            Err.Raise vbObjectError + 1003, "LegacyBridge", "Unknown file format: " & Trim$(errorMsg)
        Case LEGACYBRIDGE_ERROR_CONVERSION_FAILED
            Err.Raise vbObjectError + 1004, "LegacyBridge", "Conversion failed: " & Trim$(errorMsg)
        Case LEGACYBRIDGE_ERROR_IO_ERROR
            Err.Raise vbObjectError + 1005, "LegacyBridge", "I/O error: " & Trim$(errorMsg)
        Case Else
            Err.Raise vbObjectError + 1000, "LegacyBridge", "Unknown error: " & errorCode
    End Select
    
    HandleError = False
End Function
`;
}

function generateVFP9ErrorHandling(): string {
  return `
    * Error handling method
    PROCEDURE HandleError(tnErrorCode)
        LOCAL lcErrorMsg
        
        lcErrorMsg = SPACE(1024)
        GetLastError(@lcErrorMsg, 1024)
        
        DO CASE
            CASE tnErrorCode = -1
                This.SetError(tnErrorCode, "Invalid input file: " + ALLTRIM(lcErrorMsg))
            CASE tnErrorCode = -2
                This.SetError(tnErrorCode, "Invalid output file: " + ALLTRIM(lcErrorMsg))
            CASE tnErrorCode = -3
                This.SetError(tnErrorCode, "Unknown file format: " + ALLTRIM(lcErrorMsg))
            CASE tnErrorCode = -4
                This.SetError(tnErrorCode, "Conversion failed: " + ALLTRIM(lcErrorMsg))
            CASE tnErrorCode = -5
                This.SetError(tnErrorCode, "I/O error: " + ALLTRIM(lcErrorMsg))
            OTHERWISE
                This.SetError(tnErrorCode, "Unknown error: " + TRANSFORM(tnErrorCode))
        ENDCASE
    ENDPROC
`;
}

function generateCSharpErrorHandling(): string {
  return `
        public class LegacyBridgeException : Exception
        {
            public int ErrorCode { get; }
            
            public LegacyBridgeException(int errorCode, string message) 
                : base(message)
            {
                ErrorCode = errorCode;
            }
        }

        private LegacyBridgeException CreateException(int errorCode)
        {
            var errorBuffer = new StringBuilder(1024);
            GetLastError(errorBuffer, 1024);
            string errorMessage = errorBuffer.ToString().Trim();
            
            string message = errorCode switch
            {
                ErrorInvalidInput => $"Invalid input file: {errorMessage}",
                ErrorInvalidOutput => $"Invalid output file: {errorMessage}",
                ErrorUnknownFormat => $"Unknown file format: {errorMessage}",
                ErrorConversionFailed => $"Conversion failed: {errorMessage}",
                ErrorIOError => $"I/O error: {errorMessage}",
                _ => $"Unknown error ({errorCode}): {errorMessage}"
            };
            
            return new LegacyBridgeException(errorCode, message);
        }
`;
}

function generatePythonErrorHandling(): string {
  return `
    def _get_error_message(self, error_code: int) -> str:
        """Get error message from DLL"""
        error_buffer = ctypes.create_string_buffer(1024)
        self._dll.GetLastError(error_buffer, 1024)
        return error_buffer.value.decode('utf-8').strip()
    
    def _raise_error(self, error_code: int):
        """Raise LegacyBridgeException with appropriate message"""
        error_msg = self._get_error_message(error_code)
        
        if error_code == LegacyBridgeError.ERROR_INVALID_INPUT:
            message = f"Invalid input file: {error_msg}"
        elif error_code == LegacyBridgeError.ERROR_INVALID_OUTPUT:
            message = f"Invalid output file: {error_msg}"
        elif error_code == LegacyBridgeError.ERROR_UNKNOWN_FORMAT:
            message = f"Unknown file format: {error_msg}"
        elif error_code == LegacyBridgeError.ERROR_CONVERSION_FAILED:
            message = f"Conversion failed: {error_msg}"
        elif error_code == LegacyBridgeError.ERROR_IO_ERROR:
            message = f"I/O error: {error_msg}"
        else:
            message = f"Unknown error ({error_code}): {error_msg}"
        
        raise LegacyBridgeException(error_code, message)
`;
}

function generateVB6Examples(): string {
  return `
' ================== EXAMPLE USAGE ==================
' 
' Example 1: Simple conversion with error handling
' Private Sub ConvertDocument()
'     On Error GoTo ErrorHandler
'     
'     If ConvertToMarkdown("C:\\input.rtf", "C:\\output.md") Then
'         MsgBox "Conversion successful!"
'     End If
'     Exit Sub
'     
' ErrorHandler:
'     MsgBox "Error: " & Err.Description, vbCritical
' End Sub
'
' Example 2: Batch conversion with progress
' Private Sub BatchConvert()
'     Dim count As Long
'     count = BatchConvertToMarkdown("C:\\Legacy", "C:\\Modern", True)
'     MsgBox "Converted " & count & " documents"
' End Sub`;
}

function generateVFP9Examples(): string {
  return `
    * ================== EXAMPLE USAGE ==================
    * 
    * Example usage in a form or program:
    * LOCAL loConverter
    * loConverter = CREATEOBJECT("LegacyBridge")
    * 
    * * Convert with error checking
    * IF loConverter.ConvertToMarkdown("legacy.rtf", "modern.md")
    *     MESSAGEBOX("Success!", 64, "LegacyBridge")
    * ELSE
    *     MESSAGEBOX(loConverter.cLastErrorMessage, 16, "Error")
    * ENDIF
    * 
    * * Batch convert entire directory tree
    * LOCAL lnFiles
    * lnFiles = loConverter.BatchConvertToMarkdown("C:\\OldDocs", "C:\\NewDocs", .T.)
    * MESSAGEBOX("Processed " + TRANSFORM(lnFiles) + " files", 64, "Complete")`;
}

function generateCSharpExamples(): string {
  return `
        #region Example Usage
        /*
        // Example 1: Simple conversion
        static void ConvertSingleFile()
        {
            using (var converter = new LegacyBridgeWrapper())
            {
                if (converter.ConvertToMarkdown(@"C:\\legacy.rtf", @"C:\\modern.md"))
                {
                    Console.WriteLine("Conversion successful!");
                }
            }
        }
        
        // Example 2: Batch conversion with error handling
        static async Task BatchConvertAsync()
        {
            using (var converter = new LegacyBridgeWrapper())
            {
                try
                {
                    int count = converter.BatchConvertToMarkdown(
                        @"C:\\LegacyDocuments",
                        @"C:\\ModernDocuments",
                        recursive: true
                    );
                    Console.WriteLine($"Successfully converted {count} files");
                }
                catch (LegacyBridgeException ex)
                {
                    Console.WriteLine($"Conversion error {ex.ErrorCode}: {ex.Message}");
                }
            }
        }
        */
        #endregion`;
}

function generatePythonExamples(): string {
  return `
# ================== EXAMPLE USAGE ==================

if __name__ == "__main__":
    # Example 1: Convert single file
    with LegacyBridge() as bridge:
        if bridge.convert_to_markdown("legacy.rtf", "modern.md"):
            print("✓ Conversion successful!")
    
    # Example 2: Batch convert with progress tracking
    import time
    from pathlib import Path
    
    input_dir = Path("legacy_documents")
    output_dir = Path("markdown_output")
    output_dir.mkdir(exist_ok=True)
    
    with LegacyBridge() as bridge:
        print(f"LegacyBridge {bridge.get_version()}")
        print(f"Converting documents in {input_dir}...")
        
        start_time = time.time()
        count = bridge.batch_convert_to_markdown(
            input_dir, 
            output_dir,
            recursive=True
        )
        elapsed = time.time() - start_time
        
        print(f"✓ Converted {count} files in {elapsed:.2f} seconds")
        print(f"  Average: {elapsed/count:.3f} seconds per file")`;
}