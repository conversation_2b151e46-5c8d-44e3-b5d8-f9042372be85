// Library exports for legacybridge

pub mod conversion;
pub mod pipeline;
pub mod ffi;
pub mod ffi_error_bridge;
#[cfg(target_os = "windows")]
pub mod ffi_legacy;
pub mod ffi_unified;  // New unified FFI interface
pub mod formats;
pub mod memory_pool_optimization;
pub mod panic_handler;

// Legacy formats module with comprehensive MCP support
pub mod legacy_formats;

// Format detection module
pub mod format_detection;

// Configuration module
pub mod config;

// CLI interface module
#[cfg(feature = "cli")]
pub mod cli;

// API server module
#[cfg(feature = "api")]
pub mod api;

// DLL management module
pub mod dll;

// MCP Server module
pub mod mcp;

// Security module
pub mod security;

// Performance module
pub mod performance;

// Enterprise features module
pub mod enterprise;

// Performance optimization modules (Phase 2)
pub mod memory;
pub mod processing;
pub mod simd;
pub mod concurrency;

#[cfg(test)]
pub mod tests;

#[cfg(test)]
pub mod test_legacy_formats;

// Re-export main conversion functions
pub use conversion::{
    rtf_to_markdown,
    markdown_to_rtf,
    secure_rtf_to_markdown,
    secure_markdown_to_rtf,
};

// Re-export SIMD-optimized functions when available
#[cfg(any(target_arch = "x86", target_arch = "x86_64"))]
pub use conversion::simd_conversion::{
    rtf_to_markdown_simd,
    markdown_to_rtf_simd,
};

// Re-export error types
pub use conversion::{ConversionError, ConversionResult};

// Re-export unified error handling
pub use conversion::{UnifiedError, UnifiedResult, generate_error_id, ToUnifiedError, ToUnifiedResult};

// Re-export legacy format support
pub use formats::{FormatManager, FormatType, FormatDetection, ConversionResult as LegacyConversionResult};

// Re-export FFI functions for VB6/VFP9 (Windows only)
#[cfg(target_os = "windows")]
pub use ffi_legacy::{
    LegacyBridge_Initialize,
    LegacyBridge_Cleanup,
    LegacyBridge_DetectFormat,
    LegacyBridge_ConvertDocToMarkdown,
    LegacyBridge_ConvertDocToRtf,
    LegacyBridge_ConvertWordPerfectToMarkdown,
    LegacyBridge_ConvertDBaseToMarkdown,
    LegacyBridge_ConvertWordStarToMarkdown,
    LegacyBridge_ConvertLotusToMarkdown,
    LegacyBridge_GetVersion,
    LegacyBridge_GetSupportedFormats,
};

// Version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const NAME: &str = env!("CARGO_PKG_NAME");