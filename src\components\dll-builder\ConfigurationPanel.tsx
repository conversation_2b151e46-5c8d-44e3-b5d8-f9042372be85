'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Settings, FileCode, Shield, Package, Plus, X, AlertCircle } from 'lucide-react';
import { DLLConfiguration, ExportFunction, SecurityOptions } from '@/lib/dll/dll-config';

interface ConfigurationPanelProps {
  configuration: DLLConfiguration | null;
  onChange: (config: DLLConfiguration) => void;
}

export function ConfigurationPanel({ configuration, onChange }: ConfigurationPanelProps) {
  const [config, setConfig] = useState<DLLConfiguration>(
    configuration || {
      name: '',
      version: '1.0.0',
      description: '',
      author: '',
      targetArchitectures: ['x86'],
      compilerFlags: [],
      linkerFlags: [],
      dependencies: [],
      exports: [],
      security: {
        signDLL: false,
        certificatePath: '',
        timestampServer: '',
        enableDEP: true,
        enableASLR: true,
        enableSafeSEH: true,
      },
      outputDirectory: './build',
      includeDebugInfo: false,
      optimizationLevel: 'O2',
      customBuildScript: '',
    }
  );

  const [newExportFunction, setNewExportFunction] = useState<ExportFunction>({
    name: '',
    ordinal: 1,
    signature: '',
    callingConvention: 'stdcall',
    description: '',
  });

  const [newDependency, setNewDependency] = useState('');
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  useEffect(() => {
    if (configuration) {
      setConfig(configuration);
    }
  }, [configuration]);

  const validateConfiguration = (): boolean => {
    const errors: string[] = [];

    if (!config.name.trim()) {
      errors.push('DLL name is required');
    }
    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(config.name)) {
      errors.push('DLL name must start with a letter and contain only letters, numbers, and underscores');
    }
    if (!config.version.match(/^\d+\.\d+\.\d+$/)) {
      errors.push('Version must be in format X.Y.Z');
    }
    if (config.exports.length === 0) {
      errors.push('At least one export function is required');
    }
    if (config.targetArchitectures.length === 0) {
      errors.push('At least one target architecture must be selected');
    }
    if (config.security.signDLL && !config.security.certificatePath) {
      errors.push('Certificate path is required when signing is enabled');
    }

    setValidationErrors(errors);
    return errors.length === 0;
  };

  const handleSave = () => {
    if (validateConfiguration()) {
      onChange(config);
    }
  };

  const addExportFunction = () => {
    if (newExportFunction.name && newExportFunction.signature) {
      const nextOrdinal = Math.max(...config.exports.map(e => e.ordinal || 0), 0) + 1;
      setConfig({
        ...config,
        exports: [...config.exports, { ...newExportFunction, ordinal: nextOrdinal }],
      });
      setNewExportFunction({
        name: '',
        ordinal: nextOrdinal + 1,
        signature: '',
        callingConvention: 'stdcall',
        description: '',
      });
    }
  };

  const removeExportFunction = (index: number) => {
    setConfig({
      ...config,
      exports: config.exports.filter((_, i) => i !== index),
    });
  };

  const addDependency = () => {
    if (newDependency.trim() && !config.dependencies.includes(newDependency.trim())) {
      setConfig({
        ...config,
        dependencies: [...config.dependencies, newDependency.trim()],
      });
      setNewDependency('');
    }
  };

  const removeDependency = (index: number) => {
    setConfig({
      ...config,
      dependencies: config.dependencies.filter((_, i) => i !== index),
    });
  };

  return (
    <div className="h-full overflow-auto p-4">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="exports">Export Functions</TabsTrigger>
          <TabsTrigger value="build">Build Options</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Basic Configuration
              </CardTitle>
              <CardDescription>
                Configure the basic properties of your DLL
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">DLL Name</Label>
                  <Input
                    id="name"
                    value={config.name}
                    onChange={(e) => setConfig({ ...config, name: e.target.value })}
                    placeholder="MyLibrary"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="version">Version</Label>
                  <Input
                    id="version"
                    value={config.version}
                    onChange={(e) => setConfig({ ...config, version: e.target.value })}
                    placeholder="1.0.0"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={config.description}
                  onChange={(e) => setConfig({ ...config, description: e.target.value })}
                  placeholder="A brief description of your DLL"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="author">Author</Label>
                <Input
                  id="author"
                  value={config.author}
                  onChange={(e) => setConfig({ ...config, author: e.target.value })}
                  placeholder="Your Name"
                />
              </div>

              <div className="space-y-2">
                <Label>Target Architectures</Label>
                <div className="flex gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="x86"
                      checked={config.targetArchitectures.includes('x86')}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setConfig({
                            ...config,
                            targetArchitectures: [...config.targetArchitectures, 'x86'],
                          });
                        } else {
                          setConfig({
                            ...config,
                            targetArchitectures: config.targetArchitectures.filter(a => a !== 'x86'),
                          });
                        }
                      }}
                    />
                    <Label htmlFor="x86">x86 (32-bit)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="x64"
                      checked={config.targetArchitectures.includes('x64')}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setConfig({
                            ...config,
                            targetArchitectures: [...config.targetArchitectures, 'x64'],
                          });
                        } else {
                          setConfig({
                            ...config,
                            targetArchitectures: config.targetArchitectures.filter(a => a !== 'x64'),
                          });
                        }
                      }}
                    />
                    <Label htmlFor="x64">x64 (64-bit)</Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Dependencies</Label>
                <div className="flex gap-2">
                  <Input
                    value={newDependency}
                    onChange={(e) => setNewDependency(e.target.value)}
                    placeholder="kernel32.dll"
                    onKeyDown={(e) => e.key === 'Enter' && addDependency()}
                  />
                  <Button onClick={addDependency} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {config.dependencies.map((dep, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {dep}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeDependency(index)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="exports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileCode className="h-5 w-5" />
                Export Functions
              </CardTitle>
              <CardDescription>
                Define the functions that will be exported by your DLL
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4 border rounded-lg p-4">
                <h4 className="font-medium">Add New Export</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Function Name</Label>
                    <Input
                      value={newExportFunction.name}
                      onChange={(e) => setNewExportFunction({ ...newExportFunction, name: e.target.value })}
                      placeholder="MyFunction"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Calling Convention</Label>
                    <Select
                      value={newExportFunction.callingConvention}
                      onValueChange={(value: any) => setNewExportFunction({ ...newExportFunction, callingConvention: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="stdcall">__stdcall</SelectItem>
                        <SelectItem value="cdecl">__cdecl</SelectItem>
                        <SelectItem value="fastcall">__fastcall</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Function Signature</Label>
                  <Input
                    value={newExportFunction.signature}
                    onChange={(e) => setNewExportFunction({ ...newExportFunction, signature: e.target.value })}
                    placeholder="int MyFunction(int param1, char* param2)"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Description</Label>
                  <Textarea
                    value={newExportFunction.description}
                    onChange={(e) => setNewExportFunction({ ...newExportFunction, description: e.target.value })}
                    placeholder="Describe what this function does"
                    rows={2}
                  />
                </div>
                <Button onClick={addExportFunction} className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Export Function
                </Button>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Exported Functions</h4>
                {config.exports.length === 0 ? (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      No export functions defined. Add at least one function to export.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <div className="space-y-2">
                    {config.exports.map((func, index) => (
                      <div key={index} className="border rounded-lg p-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className="font-mono font-medium">{func.name}</span>
                              <Badge variant="outline">@{func.ordinal}</Badge>
                              <Badge variant="secondary">{func.callingConvention}</Badge>
                            </div>
                            <p className="text-sm text-muted-foreground mt-1 font-mono">
                              {func.signature}
                            </p>
                            {func.description && (
                              <p className="text-sm text-muted-foreground mt-1">
                                {func.description}
                              </p>
                            )}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeExportFunction(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="build" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Build Options
              </CardTitle>
              <CardDescription>
                Configure compiler and linker options
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="outputDir">Output Directory</Label>
                  <Input
                    id="outputDir"
                    value={config.outputDirectory}
                    onChange={(e) => setConfig({ ...config, outputDirectory: e.target.value })}
                    placeholder="./build"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="optimization">Optimization Level</Label>
                  <Select
                    value={config.optimizationLevel}
                    onValueChange={(value) => setConfig({ ...config, optimizationLevel: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="O0">No Optimization (O0)</SelectItem>
                      <SelectItem value="O1">Basic Optimization (O1)</SelectItem>
                      <SelectItem value="O2">Standard Optimization (O2)</SelectItem>
                      <SelectItem value="O3">Full Optimization (O3)</SelectItem>
                      <SelectItem value="Os">Size Optimization (Os)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="debug"
                  checked={config.includeDebugInfo}
                  onCheckedChange={(checked) => setConfig({ ...config, includeDebugInfo: !!checked })}
                />
                <Label htmlFor="debug">Include Debug Information</Label>
              </div>

              <div className="space-y-2">
                <Label>Compiler Flags</Label>
                <Textarea
                  value={config.compilerFlags.join(' ')}
                  onChange={(e) => setConfig({ ...config, compilerFlags: e.target.value.split(' ').filter(Boolean) })}
                  placeholder="/W4 /WX /GS"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label>Linker Flags</Label>
                <Textarea
                  value={config.linkerFlags.join(' ')}
                  onChange={(e) => setConfig({ ...config, linkerFlags: e.target.value.split(' ').filter(Boolean) })}
                  placeholder="/DYNAMICBASE /NXCOMPAT"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label>Custom Build Script</Label>
                <Textarea
                  value={config.customBuildScript}
                  onChange={(e) => setConfig({ ...config, customBuildScript: e.target.value })}
                  placeholder="Additional build commands (optional)"
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Options
              </CardTitle>
              <CardDescription>
                Configure security features for your DLL
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="sign"
                  checked={config.security.signDLL}
                  onCheckedChange={(checked) => setConfig({
                    ...config,
                    security: { ...config.security, signDLL: !!checked }
                  })}
                />
                <Label htmlFor="sign">Sign DLL with Certificate</Label>
              </div>

              {config.security.signDLL && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="cert">Certificate Path</Label>
                    <Input
                      id="cert"
                      value={config.security.certificatePath}
                      onChange={(e) => setConfig({
                        ...config,
                        security: { ...config.security, certificatePath: e.target.value }
                      })}
                      placeholder="path/to/certificate.pfx"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="timestamp">Timestamp Server</Label>
                    <Input
                      id="timestamp"
                      value={config.security.timestampServer}
                      onChange={(e) => setConfig({
                        ...config,
                        security: { ...config.security, timestampServer: e.target.value }
                      })}
                      placeholder="http://timestamp.digicert.com"
                    />
                  </div>
                </>
              )}

              <div className="space-y-4">
                <h4 className="font-medium">Security Features</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="dep"
                      checked={config.security.enableDEP}
                      onCheckedChange={(checked) => setConfig({
                        ...config,
                        security: { ...config.security, enableDEP: !!checked }
                      })}
                    />
                    <Label htmlFor="dep">Enable Data Execution Prevention (DEP)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="aslr"
                      checked={config.security.enableASLR}
                      onCheckedChange={(checked) => setConfig({
                        ...config,
                        security: { ...config.security, enableASLR: !!checked }
                      })}
                    />
                    <Label htmlFor="aslr">Enable Address Space Layout Randomization (ASLR)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="seh"
                      checked={config.security.enableSafeSEH}
                      onCheckedChange={(checked) => setConfig({
                        ...config,
                        security: { ...config.security, enableSafeSEH: !!checked }
                      })}
                    />
                    <Label htmlFor="seh">Enable Safe Structured Exception Handling (SafeSEH)</Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {validationErrors.length > 0 && (
        <Alert variant="destructive" className="mt-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <ul className="list-disc list-inside">
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      <div className="flex justify-end mt-4">
        <Button onClick={handleSave} size="lg">
          Save Configuration
        </Button>
      </div>
    </div>
  );
}