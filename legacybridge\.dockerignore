# Dependencies
node_modules/
target/
.next/
dist/
build/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Testing
coverage/
.nyc_output
test-results/
playwright-report/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
docs/
*.md

# Development
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
tmp/
temp/
*.tmp

# Cache
.cache/
.npm/
.eslintcache

# Build artifacts
*.tsbuildinfo