-- Schema definition for LegacyBridge authentication plugin

local typedefs = require "kong.db.schema.typedefs"

return {
  name = "legacybridge-auth",
  fields = {
    { consumer = typedefs.no_consumer },
    { protocols = typedefs.protocols_http },
    { config = {
        type = "record",
        fields = {
          {
            auth_service_url = {
              type = "string",
              default = "http://auth-service:3001",
              description = "URL of the authentication service"
            }
          },
          {
            auth_service_timeout = {
              type = "number",
              default = 5000,
              description = "Timeout for auth service requests in milliseconds"
            }
          },
          {
            public_paths = {
              type = "array",
              elements = { type = "string" },
              default = {
                "/api/v1/auth/login",
                "/api/v1/auth/refresh",
                "/health",
                "/ready",
                "/status",
                "/metrics"
              },
              description = "List of paths that don't require authentication"
            }
          },
          {
            required_permissions = {
              type = "array",
              elements = { type = "string" },
              description = "List of required permissions/roles for this route"
            }
          },
          {
            cache_ttl = {
              type = "number",
              default = 300,
              description = "Cache TTL for token validation results in seconds"
            }
          },
          {
            enable_caching = {
              type = "boolean",
              default = true,
              description = "Enable caching of token validation results"
            }
          },
          {
            rate_limit_per_user = {
              type = "number",
              default = 1000,
              description = "Rate limit per user per minute"
            }
          },
          {
            enable_audit_logging = {
              type = "boolean",
              default = true,
              description = "Enable audit logging for authentication events"
            }
          }
        }
      }
    }
  }
}
