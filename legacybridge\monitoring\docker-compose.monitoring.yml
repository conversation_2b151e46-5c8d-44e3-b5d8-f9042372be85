version: '3.8'

services:
  # Prometheus
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: legacybridge-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./prometheus/alert_rules.yml:/etc/prometheus/alert_rules.yml:ro
      - ./prometheus/slo_rules.yml:/etc/prometheus/slo_rules.yml:ro
      - ./prometheus/recording_rules.yml:/etc/prometheus/recording_rules.yml:ro
      - prometheus_data:/prometheus
    networks:
      - monitoring
    restart: unless-stopped

  # Grafana
  grafana:
    image: grafana/grafana:10.2.0
    container_name: legacybridge-grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_SERVER_ROOT_URL=http://localhost:3000
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    ports:
      - "3000:3000"
    volumes:
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
      - ./grafana/dashboards:/var/lib/grafana/dashboards:ro
      - grafana_data:/var/lib/grafana
    networks:
      - monitoring
    depends_on:
      - prometheus
    restart: unless-stopped

  # Jaeger
  jaeger:
    image: jaegertracing/all-in-one:1.50
    container_name: legacybridge-jaeger
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - SPAN_STORAGE_TYPE=badger
      - BADGER_EPHEMERAL=false
      - BADGER_DIRECTORY_VALUE=/badger/data
      - BADGER_DIRECTORY_KEY=/badger/key
    ports:
      - "5775:5775/udp"
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      - "16686:16686"
      - "14250:14250"
      - "14268:14268"
      - "14269:14269"
      - "4317:4317"
      - "4318:4318"
    volumes:
      - jaeger_data:/badger
    networks:
      - monitoring
    restart: unless-stopped

  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: legacybridge-elasticsearch
    environment:
      - discovery.type=single-node
      - cluster.name=legacybridge-logs
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - monitoring
    restart: unless-stopped

  # Logstash
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: legacybridge-logstash
    volumes:
      - ./elasticsearch/logstash-config.yaml:/usr/share/logstash/config/logstash.yml:ro
      - ./elasticsearch/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
    ports:
      - "5000:5000/tcp"
      - "5044:5044/tcp"
      - "5514:5514/udp"
      - "9600:9600"
    environment:
      - "LS_JAVA_OPTS=-Xms512m -Xmx512m"
    networks:
      - monitoring
    depends_on:
      - elasticsearch
    restart: unless-stopped

  # Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: legacybridge-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - SERVER_BASEPATH=/kibana
      - SERVER_REWRITEBASEPATH=true
      - ELASTICSEARCH_SERVICEACCOUNTTOKEN=""
      - XPACK_SECURITY_ENABLED=false
    ports:
      - "5601:5601"
    networks:
      - monitoring
    depends_on:
      - elasticsearch
    restart: unless-stopped

  # AlertManager
  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: legacybridge-alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    ports:
      - "9093:9093"
    volumes:
      - ./alerting/alertmanager-config.yaml:/etc/alertmanager/alertmanager.yml:ro
      - alertmanager_data:/alertmanager
    networks:
      - monitoring
    restart: unless-stopped

  # Node Exporter (for host metrics)
  node-exporter:
    image: prom/node-exporter:v1.7.0
    container_name: legacybridge-node-exporter
    command:
      - '--path.rootfs=/host'
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    networks:
      - monitoring
    restart: unless-stopped

  # Blackbox Exporter (for endpoint monitoring)
  blackbox-exporter:
    image: prom/blackbox-exporter:v0.24.0
    container_name: legacybridge-blackbox
    command:
      - '--config.file=/config/blackbox.yml'
    ports:
      - "9115:9115"
    volumes:
      - ./prometheus/blackbox.yml:/config/blackbox.yml:ro
    networks:
      - monitoring
    restart: unless-stopped

networks:
  monitoring:
    driver: bridge
    name: legacybridge-monitoring

volumes:
  prometheus_data:
  grafana_data:
  elasticsearch_data:
  jaeger_data:
  alertmanager_data: