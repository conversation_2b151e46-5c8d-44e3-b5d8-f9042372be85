// Configuration management for microservices
use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ServiceConfig {
    pub service: ServiceInfo,
    pub database: DatabaseConfig,
    pub redis: RedisConfig,
    pub auth: AuthConfig,
    pub logging: LoggingConfig,
    pub metrics: MetricsConfig,
    pub cors: CorsConfig,
    pub rate_limiting: RateLimitConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ServiceInfo {
    pub name: String,
    pub version: String,
    pub port: u16,
    pub host: String,
    pub environment: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub acquire_timeout_seconds: u64,
    pub idle_timeout_seconds: u64,
    pub max_lifetime_seconds: u64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RedisConfig {
    pub url: String,
    pub max_connections: u32,
    pub connection_timeout_seconds: u64,
    pub command_timeout_seconds: u64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AuthConfig {
    pub jwt_secret: String,
    pub jwt_expiry_hours: i64,
    pub refresh_token_expiry_days: i64,
    pub issuer: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub format: String,
    pub file_path: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsConfig {
    pub enabled: bool,
    pub port: u16,
    pub path: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CorsConfig {
    pub allowed_origins: Vec<String>,
    pub allowed_methods: Vec<String>,
    pub allowed_headers: Vec<String>,
    pub max_age_seconds: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    pub enabled: bool,
    pub requests_per_minute: u32,
    pub burst_size: u32,
}

impl Default for ServiceConfig {
    fn default() -> Self {
        Self {
            service: ServiceInfo {
                name: "legacybridge-service".to_string(),
                version: "0.1.0".to_string(),
                port: 3000,
                host: "0.0.0.0".to_string(),
                environment: "development".to_string(),
            },
            database: DatabaseConfig {
                url: "postgresql://postgres:password@localhost:5432/legacybridge".to_string(),
                max_connections: 20,
                min_connections: 5,
                acquire_timeout_seconds: 10,
                idle_timeout_seconds: 600,
                max_lifetime_seconds: 1800,
            },
            redis: RedisConfig {
                url: "redis://localhost:6379".to_string(),
                max_connections: 10,
                connection_timeout_seconds: 5,
                command_timeout_seconds: 5,
            },
            auth: AuthConfig {
                jwt_secret: "your-secret-key-change-in-production".to_string(),
                jwt_expiry_hours: 1,
                refresh_token_expiry_days: 7,
                issuer: "legacybridge".to_string(),
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                format: "json".to_string(),
                file_path: None,
            },
            metrics: MetricsConfig {
                enabled: true,
                port: 9090,
                path: "/metrics".to_string(),
            },
            cors: CorsConfig {
                allowed_origins: vec!["*".to_string()],
                allowed_methods: vec!["GET".to_string(), "POST".to_string(), "PUT".to_string(), "DELETE".to_string(), "OPTIONS".to_string()],
                allowed_headers: vec!["Content-Type".to_string(), "Authorization".to_string()],
                max_age_seconds: 3600,
            },
            rate_limiting: RateLimitConfig {
                enabled: true,
                requests_per_minute: 100,
                burst_size: 10,
            },
        }
    }
}

impl ServiceConfig {
    pub fn from_env() -> Result<Self, config::ConfigError> {
        let mut cfg = config::Config::builder()
            .add_source(config::Config::try_from(&ServiceConfig::default())?)
            .add_source(config::Environment::with_prefix("LEGACYBRIDGE").separator("_"));

        // Load from file if specified
        if let Ok(config_file) = env::var("LEGACYBRIDGE_CONFIG_FILE") {
            cfg = cfg.add_source(config::File::with_name(&config_file));
        }

        cfg.build()?.try_deserialize()
    }

    pub fn auth_service_config() -> Self {
        let mut config = Self::default();
        config.service.name = "auth-service".to_string();
        config.service.port = 3001;
        config
    }

    pub fn conversion_service_config() -> Self {
        let mut config = Self::default();
        config.service.name = "conversion-service".to_string();
        config.service.port = 3002;
        config
    }

    pub fn file_service_config() -> Self {
        let mut config = Self::default();
        config.service.name = "file-service".to_string();
        config.service.port = 3003;
        config
    }

    pub fn job_service_config() -> Self {
        let mut config = Self::default();
        config.service.name = "job-service".to_string();
        config.service.port = 3004;
        config
    }

    pub fn validate(&self) -> Result<(), String> {
        if self.service.name.is_empty() {
            return Err("Service name cannot be empty".to_string());
        }

        if self.service.port == 0 {
            return Err("Service port must be greater than 0".to_string());
        }

        if self.database.url.is_empty() {
            return Err("Database URL cannot be empty".to_string());
        }

        if self.redis.url.is_empty() {
            return Err("Redis URL cannot be empty".to_string());
        }

        if self.auth.jwt_secret.is_empty() {
            return Err("JWT secret cannot be empty".to_string());
        }

        if self.auth.jwt_secret == "your-secret-key-change-in-production" && self.service.environment == "production" {
            return Err("JWT secret must be changed in production".to_string());
        }

        Ok(())
    }
}
