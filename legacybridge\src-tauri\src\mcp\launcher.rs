// MCP Server Launcher - Handles server startup, initialization, and lifecycle management
use crate::mcp::{
    deployment::{McpDeploymentConfig, ConfigError},
    official_server::LegacyBridgeMcpServerOfficial,
    security::SecurityMiddleware,
    websocket::WebSocketServer,
    job_tracker::JobTracker,
    cache::CacheManager,
};
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::signal;
use tracing::{info, error, warn};

#[cfg(feature = "mcp")]
use rust_mcp_sdk::{
    server::Server,
    transport::{StdioTransport, HttpTransport, WebSocketTransport, Transport},
};

pub struct McpServerLauncher {
    config: McpDeploymentConfig,
    server: Option<Arc<LegacyBridgeMcpServerOfficial>>,
    job_tracker: Arc<JobTracker>,
    cache_manager: Arc<CacheManager>,
    security_middleware: Arc<SecurityMiddleware>,
    shutdown_signal: Option<tokio::sync::broadcast::Sender<()>>,
}

impl McpServerLauncher {
    /// Create a new MCP server launcher
    pub fn new() -> Result<Self, LauncherError> {
        // Load configuration
        let config = Self::load_configuration()?;
        config.validate()?;
        
        // Initialize components
        let job_tracker = Arc::new(JobTracker::new());
        let cache_manager = Arc::new(CacheManager::new(config.performance.cache_size));
        let security_middleware = Arc::new(SecurityMiddleware::new(
            config.security.enable_authentication,
            config.security.api_keys.clone(),
            config.security.rate_limiting.clone(),
        ));
        
        Ok(Self {
            config,
            server: None,
            job_tracker,
            cache_manager,
            security_middleware,
            shutdown_signal: None,
        })
    }
    
    /// Start the MCP server with the configured protocol
    pub async fn start(&mut self) -> Result<(), LauncherError> {
        info!("Starting LegacyBridge MCP Server");
        info!("Protocol: {}", self.config.server.protocol);
        info!("Environment: {}", self.get_environment());
        
        // Initialize logging
        self.init_logging()?;
        
        // Create server instance
        let server = Arc::new(LegacyBridgeMcpServerOfficial::new(
            self.config.clone(),
            self.job_tracker.clone(),
            self.cache_manager.clone(),
        )?);
        self.server = Some(server.clone());
        
        // Start monitoring if enabled
        if self.config.monitoring.enable_telemetry {
            self.start_monitoring().await?;
        }
        
        // Create shutdown channel
        let (shutdown_tx, _) = tokio::sync::broadcast::channel(1);
        self.shutdown_signal = Some(shutdown_tx.clone());
        
        // Start server based on protocol
        match self.config.server.protocol.as_str() {
            "stdio" => self.start_stdio_server(server).await?,
            "http" => self.start_http_server(server).await?,
            "websocket" => self.start_websocket_server(server).await?,
            _ => return Err(LauncherError::InvalidProtocol(self.config.server.protocol.clone())),
        }
        
        Ok(())
    }
    
    /// Start server with stdio transport (for AI assistants)
    async fn start_stdio_server(&self, server: Arc<LegacyBridgeMcpServerOfficial>) -> Result<(), LauncherError> {
        info!("Starting stdio transport server");
        
        #[cfg(feature = "mcp")]
        {
            let transport = StdioTransport::new();
            let mcp_server = server.create_mcp_server()?;
            
            // Run server with graceful shutdown
            let shutdown_rx = self.shutdown_signal.as_ref().unwrap().subscribe();
            tokio::select! {
                result = mcp_server.run(transport) => {
                    match result {
                        Ok(_) => info!("Stdio server stopped"),
                        Err(e) => error!("Stdio server error: {}", e),
                    }
                }
                _ = shutdown_rx => {
                    info!("Received shutdown signal");
                }
                _ = signal::ctrl_c() => {
                    info!("Received Ctrl+C signal");
                }
            }
        }
        
        #[cfg(not(feature = "mcp"))]
        {
            // Fallback implementation without MCP SDK
            self.run_stdio_fallback(server).await?;
        }
        
        Ok(())
    }
    
    /// Start server with HTTP transport
    async fn start_http_server(&self, server: Arc<LegacyBridgeMcpServerOfficial>) -> Result<(), LauncherError> {
        info!("Starting HTTP server on {}:{}", self.config.server.host, self.config.server.port);
        
        #[cfg(feature = "mcp")]
        {
            let addr = format!("{}:{}", self.config.server.host, self.config.server.port);
            let transport = HttpTransport::new(&addr)?;
            let mcp_server = server.create_mcp_server()?;
            
            // Apply security middleware
            let secured_transport = self.security_middleware.wrap_transport(transport);
            
            // Run server
            let shutdown_rx = self.shutdown_signal.as_ref().unwrap().subscribe();
            tokio::select! {
                result = mcp_server.run(secured_transport) => {
                    match result {
                        Ok(_) => info!("HTTP server stopped"),
                        Err(e) => error!("HTTP server error: {}", e),
                    }
                }
                _ = shutdown_rx => {
                    info!("Received shutdown signal");
                }
            }
        }
        
        #[cfg(not(feature = "mcp"))]
        {
            self.run_http_fallback(server).await?;
        }
        
        Ok(())
    }
    
    /// Start server with WebSocket transport
    async fn start_websocket_server(&self, server: Arc<LegacyBridgeMcpServerOfficial>) -> Result<(), LauncherError> {
        info!("Starting WebSocket server on {}:{}", self.config.server.host, self.config.server.port);
        
        // Create WebSocket server
        let ws_server = WebSocketServer::new(
            self.config.server.host.clone(),
            self.config.server.port,
            self.config.server.max_connections,
        );
        
        // Start WebSocket server with MCP integration
        let shutdown_rx = self.shutdown_signal.as_ref().unwrap().subscribe();
        ws_server.start_with_mcp(server, shutdown_rx).await?;
        
        Ok(())
    }
    
    /// Gracefully shutdown the server
    pub async fn shutdown(&mut self) -> Result<(), LauncherError> {
        info!("Initiating graceful shutdown");
        
        // Send shutdown signal
        if let Some(shutdown_tx) = &self.shutdown_signal {
            let _ = shutdown_tx.send(());
        }
        
        // Wait for components to shutdown
        tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
        
        // Cleanup resources
        if let Some(server) = &self.server {
            server.cleanup().await?;
        }
        
        info!("Server shutdown complete");
        Ok(())
    }
    
    /// Load configuration from environment or file
    fn load_configuration() -> Result<McpDeploymentConfig, ConfigError> {
        // Check for config file
        if let Ok(config_path) = std::env::var("MCP_CONFIG_FILE") {
            info!("Loading configuration from file: {}", config_path);
            return McpDeploymentConfig::from_file(&config_path);
        }
        
        // Check for environment-based config
        if std::env::var("MCP_SERVER_PROTOCOL").is_ok() {
            info!("Loading configuration from environment variables");
            return Ok(McpDeploymentConfig::from_env());
        }
        
        // Determine environment and use appropriate defaults
        let env = std::env::var("MCP_ENV").unwrap_or_else(|_| "development".to_string());
        info!("Using {} configuration", env);
        
        Ok(match env.as_str() {
            "production" => McpDeploymentConfig::production(),
            "docker" => McpDeploymentConfig::docker(),
            _ => McpDeploymentConfig::development(),
        })
    }
    
    /// Initialize logging based on configuration
    fn init_logging(&self) -> Result<(), LauncherError> {
        use tracing_subscriber::{EnvFilter, fmt};
        
        let filter = EnvFilter::try_from_default_env()
            .unwrap_or_else(|_| EnvFilter::new(&self.config.monitoring.log_level));
        
        match self.config.monitoring.log_format.as_str() {
            "json" => {
                fmt()
                    .json()
                    .with_env_filter(filter)
                    .init();
            }
            _ => {
                fmt()
                    .with_env_filter(filter)
                    .init();
            }
        }
        
        Ok(())
    }
    
    /// Start monitoring and metrics collection
    async fn start_monitoring(&self) -> Result<(), LauncherError> {
        info!("Starting monitoring with {} second interval", 
              self.config.monitoring.metrics_export_interval);
        
        let config = self.config.clone();
        let server = self.server.clone();
        let job_tracker = self.job_tracker.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                tokio::time::Duration::from_secs(config.monitoring.metrics_export_interval)
            );
            
            loop {
                interval.tick().await;
                
                if let Some(server) = &server {
                    // Export metrics
                    let metrics = server.collect_metrics().await;
                    let job_metrics = job_tracker.get_metrics().await;
                    
                    // Log metrics (in production, export to monitoring system)
                    info!("Server metrics: {:?}", metrics);
                    info!("Job metrics: {:?}", job_metrics);
                }
            }
        });
        
        // Start health check endpoint
        self.start_health_check().await?;
        
        Ok(())
    }
    
    /// Start health check endpoint
    async fn start_health_check(&self) -> Result<(), LauncherError> {
        let health_port = self.config.server.port + 1000; // Health check on separate port
        let server = self.server.clone();
        
        tokio::spawn(async move {
            use warp::Filter;
            
            let health = warp::path("health")
                .map(move || {
                    if server.is_some() {
                        warp::reply::json(&serde_json::json!({
                            "status": "healthy",
                            "service": "legacybridge-mcp",
                            "timestamp": chrono::Utc::now().to_rfc3339(),
                        }))
                    } else {
                        warp::reply::json(&serde_json::json!({
                            "status": "unhealthy",
                            "service": "legacybridge-mcp",
                            "timestamp": chrono::Utc::now().to_rfc3339(),
                        }))
                    }
                });
            
            info!("Health check endpoint available at http://0.0.0.0:{}/health", health_port);
            warp::serve(health).run(([0, 0, 0, 0], health_port)).await;
        });
        
        Ok(())
    }
    
    /// Get current environment name
    fn get_environment(&self) -> &str {
        match (self.config.server.protocol.as_str(), self.config.security.enable_authentication) {
            ("stdio", false) => "development",
            ("websocket", true) => "production",
            _ => "custom",
        }
    }
    
    /// Fallback stdio implementation without MCP SDK
    #[cfg(not(feature = "mcp"))]
    async fn run_stdio_fallback(&self, server: Arc<LegacyBridgeMcpServerOfficial>) -> Result<(), LauncherError> {
        use tokio::io::{AsyncBufReadExt, AsyncWriteExt, BufReader};
        
        warn!("Running stdio server in fallback mode (MCP SDK not available)");
        
        let stdin = tokio::io::stdin();
        let mut stdout = tokio::io::stdout();
        let mut reader = BufReader::new(stdin);
        let mut line = String::new();
        
        // Send initialization response
        let init_response = serde_json::json!({
            "jsonrpc": "2.0",
            "result": {
                "name": "LegacyBridge MCP Server",
                "version": "1.0.0",
                "capabilities": {
                    "tools": true,
                    "resources": true,
                    "prompts": true,
                }
            }
        });
        
        stdout.write_all(format!("{}\n", init_response).as_bytes()).await?;
        stdout.flush().await?;
        
        // Process messages
        loop {
            line.clear();
            match reader.read_line(&mut line).await {
                Ok(0) => break, // EOF
                Ok(_) => {
                    if let Ok(request) = serde_json::from_str::<serde_json::Value>(&line) {
                        let response = server.handle_fallback_request(request).await;
                        stdout.write_all(format!("{}\n", response).as_bytes()).await?;
                        stdout.flush().await?;
                    }
                }
                Err(e) => {
                    error!("Error reading stdin: {}", e);
                    break;
                }
            }
        }
        
        Ok(())
    }
    
    /// Fallback HTTP implementation without MCP SDK
    #[cfg(not(feature = "mcp"))]
    async fn run_http_fallback(&self, server: Arc<LegacyBridgeMcpServerOfficial>) -> Result<(), LauncherError> {
        use warp::Filter;
        
        warn!("Running HTTP server in fallback mode (MCP SDK not available)");
        
        let server_clone = server.clone();
        let api = warp::path("api")
            .and(warp::post())
            .and(warp::body::json())
            .and_then(move |request: serde_json::Value| {
                let server = server_clone.clone();
                async move {
                    let response = server.handle_fallback_request(request).await;
                    Ok::<_, warp::Rejection>(warp::reply::json(&response))
                }
            });
        
        let addr = format!("{}:{}", self.config.server.host, self.config.server.port)
            .parse::<std::net::SocketAddr>()
            .map_err(|e| LauncherError::InvalidAddress(e.to_string()))?;
        
        warp::serve(api).run(addr).await;
        Ok(())
    }
}

#[derive(Debug, thiserror::Error)]
pub enum LauncherError {
    #[error("Configuration error: {0}")]
    ConfigError(#[from] ConfigError),
    
    #[error("Invalid protocol: {0}")]
    InvalidProtocol(String),
    
    #[error("Server initialization error: {0}")]
    InitError(String),
    
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("Invalid address: {0}")]
    InvalidAddress(String),
    
    #[error("Shutdown error: {0}")]
    ShutdownError(String),
}

/// Main entry point for the MCP server
pub async fn run_mcp_server() -> Result<(), LauncherError> {
    let mut launcher = McpServerLauncher::new()?;
    
    // Handle shutdown signals
    let shutdown_handle = launcher.shutdown_signal.clone();
    tokio::spawn(async move {
        signal::ctrl_c().await.expect("Failed to listen for Ctrl+C");
        if let Some(tx) = shutdown_handle {
            let _ = tx.send(());
        }
    });
    
    // Start server
    launcher.start().await?;
    
    Ok(())
}