// RTF parsing (simplified implementation)
use super::{ParsedDocument, DocumentElement};
use legacybridge_shared::{ServiceError, ServiceResult};
use serde_json::Value;
use std::collections::HashMap;

/// Parse RTF content into structured document
pub fn parse_rtf(content: &str) -> ServiceResult<ParsedDocument> {
    // This is a simplified RTF parser
    // In a production environment, you'd use a proper RTF parsing library
    
    let mut elements = Vec::new();
    let mut plain_text = String::new();
    
    // Simple RTF to text conversion
    let text_content = extract_text_from_rtf(content)?;
    
    // Parse the extracted text as paragraphs
    let paragraphs: Vec<&str> = text_content
        .split("\n\n")
        .filter(|p| !p.trim().is_empty())
        .collect();
    
    for paragraph in paragraphs {
        let trimmed = paragraph.trim();
        if !trimmed.is_empty() {
            elements.push(DocumentElement::Paragraph {
                text: trimmed.to_string(),
            });
            plain_text.push_str(trimmed);
            plain_text.push('\n');
        }
    }
    
    // Try to extract title from first paragraph
    let title = elements.first().and_then(|element| {
        if let DocumentElement::Paragraph { text } = element {
            if text.len() < 100 && !text.contains('.') {
                Some(text.clone())
            } else {
                None
            }
        } else {
            None
        }
    });
    
    let mut metadata = HashMap::new();
    metadata.insert("format".to_string(), Value::String("rtf".to_string()));
    metadata.insert("original_size".to_string(), Value::Number(content.len().into()));
    metadata.insert("extracted_size".to_string(), Value::Number(text_content.len().into()));
    
    Ok(ParsedDocument {
        title,
        content: plain_text,
        metadata,
        structure: elements,
    })
}

/// Extract plain text from RTF content
fn extract_text_from_rtf(rtf_content: &str) -> ServiceResult<String> {
    let mut result = String::new();
    let mut chars = rtf_content.chars().peekable();
    let mut in_control_word = false;
    let mut in_group = 0;
    let mut skip_next = false;
    
    while let Some(ch) = chars.next() {
        if skip_next {
            skip_next = false;
            continue;
        }
        
        match ch {
            '{' => {
                in_group += 1;
            }
            '}' => {
                in_group -= 1;
                in_control_word = false;
            }
            '\\' => {
                in_control_word = true;
                
                // Check for common RTF control words
                let mut control_word = String::new();
                while let Some(&next_ch) = chars.peek() {
                    if next_ch.is_alphabetic() {
                        control_word.push(chars.next().unwrap());
                    } else {
                        break;
                    }
                }
                
                // Handle specific control words
                match control_word.as_str() {
                    "par" | "line" => {
                        result.push('\n');
                        // Skip any following space or number
                        if let Some(&next_ch) = chars.peek() {
                            if next_ch == ' ' || next_ch.is_numeric() {
                                chars.next();
                            }
                        }
                    }
                    "tab" => {
                        result.push('\t');
                    }
                    "u" => {
                        // Unicode character - skip the number and following character
                        while let Some(&next_ch) = chars.peek() {
                            if next_ch.is_numeric() || next_ch == '-' {
                                chars.next();
                            } else {
                                break;
                            }
                        }
                        // Skip the replacement character
                        if let Some(&next_ch) = chars.peek() {
                            if next_ch == ' ' {
                                chars.next();
                            }
                        }
                        skip_next = true;
                    }
                    _ => {
                        // Skip unknown control words and their parameters
                        while let Some(&next_ch) = chars.peek() {
                            if next_ch.is_numeric() || next_ch == '-' {
                                chars.next();
                            } else {
                                break;
                            }
                        }
                        // Skip following space
                        if let Some(&next_ch) = chars.peek() {
                            if next_ch == ' ' {
                                chars.next();
                            }
                        }
                    }
                }
                in_control_word = false;
            }
            _ => {
                if !in_control_word && in_group <= 1 {
                    // Only add text when we're in the main document group
                    if ch.is_control() {
                        // Skip control characters except newlines
                        if ch == '\n' || ch == '\r' {
                            result.push(' ');
                        }
                    } else {
                        result.push(ch);
                    }
                }
            }
        }
    }
    
    // Clean up the result
    let cleaned = result
        .lines()
        .map(|line| line.trim())
        .filter(|line| !line.is_empty())
        .collect::<Vec<_>>()
        .join("\n");
    
    // Replace multiple spaces with single spaces
    let final_result = regex::Regex::new(r"\s+")
        .unwrap()
        .replace_all(&cleaned, " ")
        .to_string();
    
    Ok(final_result)
}
