// Memory-Efficient Cache Implementation
// Based on CURSOR-08-PERFORMANCE-OPTIMIZATION.MD specifications

interface CacheEntry<T> {
  value: T;
  expiryTime: number;
  accessCount: number;
  lastAccessed: number;
}

export class MemoryEfficientCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private cleanupInterval: number | null = null;
  private maxSize = 1000;
  private ttl = 3600000; // 1 hour

  constructor(maxSize: number = 1000, ttl: number = 3600000) {
    this.maxSize = maxSize;
    this.ttl = ttl;
    
    // Periodic cleanup instead of per-item timeouts
    this.cleanupInterval = window.setInterval(() => {
      this.performCleanup();
    }, 60000); // Cleanup every minute
  }

  set(key: string, value: T): void {
    // Check size limit
    if (this.cache.size >= this.maxSize) {
      this.evictLeastRecentlyUsed();
    }

    this.cache.set(key, {
      value,
      expiryTime: Date.now() + this.ttl,
      accessCount: 0,
      lastAccessed: Date.now()
    });
  }

  get(key: string): T | undefined {
    const entry = this.cache.get(key);
    if (!entry) return undefined;

    // Check expiry
    if (Date.now() > entry.expiryTime) {
      this.cache.delete(key);
      return undefined;
    }

    // Update access info
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    return entry.value;
  }

  private performCleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache) {
      if (now > entry.expiryTime) {
        keysToDelete.push(key);
      }
    }

    for (const key of keysToDelete) {
      this.cache.delete(key);
    }
  }

  private evictLeastRecentlyUsed(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  // Get cache statistics
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.calculateHitRate(),
      memoryUsage: this.estimateMemoryUsage()
    };
  }

  private calculateHitRate(): number {
    let totalAccess = 0;
    for (const entry of this.cache.values()) {
      totalAccess += entry.accessCount;
    }
    return totalAccess > 0 ? totalAccess / this.cache.size : 0;
  }

  private estimateMemoryUsage(): number {
    let totalSize = 0;
    for (const [key, entry] of this.cache) {
      totalSize += key.length * 2; // UTF-16 characters
      totalSize += JSON.stringify(entry.value).length * 2;
      totalSize += 32; // Estimated overhead per entry
    }
    return totalSize;
  }

  clear(): void {
    this.cache.clear();
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.cache.clear();
  }
}

// Global cache instances for different data types
export const conversionCache = new MemoryEfficientCache<string>(500, 1800000); // 30 minutes
export const fileContentCache = new MemoryEfficientCache<string>(200, 600000); // 10 minutes
export const metadataCache = new MemoryEfficientCache<any>(1000, 3600000); // 1 hour

// Cleanup function for application shutdown
export function cleanupCaches(): void {
  conversionCache.destroy();
  fileContentCache.destroy();
  metadataCache.destroy();
}
