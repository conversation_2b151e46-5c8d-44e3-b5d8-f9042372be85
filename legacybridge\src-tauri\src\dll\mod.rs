pub mod builder;
pub mod generator;
pub mod inspector;
pub mod tester;
pub mod performance_tests;
pub mod packaging;

#[cfg(test)]
mod tests;

pub use builder::{DllBuilder, BuildConfig, BuildResult};
pub use generator::{CodeGenerator, GeneratorConfig, GeneratorResult};
pub use inspector::{DllIns<PERSON>or, InspectionResult, ExportInfo};
pub use tester::{DllTester, TestConfig, TestResult};
pub use performance_tests::{PerformanceTester, SecurityTester, PerformanceBenchmark, SecurityCheck};
pub use packaging::{PackageBuilder, PackageConfig, PackageFormat, DeploymentPackage, PackageValidation};

use std::path::PathBuf;
use thiserror::Error;

#[derive(Debug, Error)]
pub enum DllError {
    #[error("Build failed: {0}")]
    BuildError(String),
    
    #[error("Test failed: {0}")]
    TestError(String),
    
    #[error("Code generation failed: {0}")]
    GenerationError(String),
    
    #[error("Inspection failed: {0}")]
    InspectionError(String),
    
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("File not found: {0}")]
    FileNotFound(PathBuf),
    
    #[error("Invalid configuration: {0}")]
    InvalidConfig(String),
    
    #[error("Platform not supported: {0}")]
    PlatformNotSupported(String),
    
    #[error("Compilation error: {0}")]
    CompilationError(String),
    
    #[error("Library loading error: {0}")]
    LibraryError(#[from] libloading::Error),
    
    #[error("Packaging error: {0}")]
    PackagingError(String),
}

pub type DllResult<T> = Result<T, DllError>;

/// Common utilities for DLL operations
pub mod utils {
    use std::path::Path;
    
    /// Check if a file is a valid DLL
    pub fn is_valid_dll(path: &Path) -> bool {
        if !path.exists() || !path.is_file() {
            return false;
        }
        
        // Check file extension
        match path.extension() {
            Some(ext) => ext == "dll" || ext == "DLL",
            None => false,
        }
    }
    
    /// Get the architecture of a DLL
    pub fn get_dll_architecture(path: &Path) -> Result<String, super::DllError> {
        use std::fs::File;
        use std::io::Read;
        
        let mut file = File::open(path)?;
        let mut buffer = vec![0u8; 1024];
        file.read_exact(&mut buffer)?;
        
        // Check PE header for architecture
        // This is a simplified check - real implementation would parse PE headers properly
        if buffer.starts_with(b"MZ") {
            // Look for PE signature
            let pe_offset = u32::from_le_bytes([buffer[60], buffer[61], buffer[62], buffer[63]]) as usize;
            if pe_offset < buffer.len() - 6 {
                let machine = u16::from_le_bytes([buffer[pe_offset + 4], buffer[pe_offset + 5]]);
                match machine {
                    0x014c => Ok("x86".to_string()),
                    0x8664 => Ok("x64".to_string()),
                    _ => Ok("unknown".to_string()),
                }
            } else {
                Ok("unknown".to_string())
            }
        } else {
            Err(super::DllError::InspectionError("Invalid PE file".to_string()))
        }
    }
}