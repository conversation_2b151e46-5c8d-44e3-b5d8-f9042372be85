# Production Auth Service Deployment
apiVersion: v1
kind: ConfigMap
metadata:
  name: auth-service-config
  namespace: legacybridge
  labels:
    app: auth-service
    component: config
data:
  config.yaml: |
    server:
      host: "0.0.0.0"
      port: 3001
      workers: 4
    
    database:
      host: postgresql
      port: 5432
      database: legacybridge
      pool_size: 20
      max_connections: 100
      connection_timeout: 30
      idle_timeout: 600
    
    redis:
      host: redis
      port: 6379
      pool_size: 10
      connection_timeout: 5
      command_timeout: 10
    
    auth:
      jwt_secret_key: "${JWT_SECRET_KEY}"
      jwt_expiration: 3600
      password_hash_cost: 12
      session_timeout: 1800
      max_login_attempts: 5
      lockout_duration: 900
    
    metrics:
      enabled: true
      port: 9090
      path: "/metrics"
    
    logging:
      level: "info"
      format: "json"
      output: "stdout"
    
    circuit_breaker:
      failure_threshold: 5
      recovery_timeout: 30
      request_timeout: 10
      half_open_max_calls: 3
    
    service_discovery:
      enabled: true
      health_check_interval: 30
      registration_ttl: 60

---
apiVersion: v1
kind: Secret
metadata:
  name: auth-service-secrets
  namespace: legacybridge
  labels:
    app: auth-service
    component: secrets
type: Opaque
data:
  # Base64 encoded secrets
  JWT_SECRET_KEY: "c3VwZXItc2VjcmV0LWp3dC1rZXktZm9yLXByb2R1Y3Rpb24="  # super-secret-jwt-key-for-production
  DATABASE_PASSWORD: "cGFzc3dvcmQxMjM="  # password123
  REDIS_PASSWORD: ""  # empty for no password

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  namespace: legacybridge
  labels:
    app: auth-service
    version: v1.0.0
    component: microservice
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
        version: v1.0.0
        component: microservice
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
        sidecar.istio.io/inject: "true"
    spec:
      serviceAccountName: auth-service
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: auth-service
        image: legacybridge/auth-service:v1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 3001
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        env:
        - name: RUST_LOG
          value: "info"
        - name: CONFIG_PATH
          value: "/etc/auth-service/config.yaml"
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: auth-service-secrets
              key: JWT_SECRET_KEY
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: auth-service-secrets
              key: DATABASE_PASSWORD
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: auth-service-secrets
              key: REDIS_PASSWORD
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 30
        volumeMounts:
        - name: config-volume
          mountPath: /etc/auth-service
          readOnly: true
        - name: tmp-volume
          mountPath: /tmp
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: config-volume
        configMap:
          name: auth-service-config
      - name: tmp-volume
        emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - auth-service
              topologyKey: kubernetes.io/hostname
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300

---
apiVersion: v1
kind: Service
metadata:
  name: auth-service
  namespace: legacybridge
  labels:
    app: auth-service
    component: microservice
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 3001
    targetPort: http
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: metrics
    protocol: TCP
  selector:
    app: auth-service

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: auth-service
  namespace: legacybridge
  labels:
    app: auth-service
    component: rbac

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: auth-service
  namespace: legacybridge
  labels:
    app: auth-service
    component: rbac
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: auth-service
  namespace: legacybridge
  labels:
    app: auth-service
    component: rbac
subjects:
- kind: ServiceAccount
  name: auth-service
  namespace: legacybridge
roleRef:
  kind: Role
  name: auth-service
  apiGroup: rbac.authorization.k8s.io
