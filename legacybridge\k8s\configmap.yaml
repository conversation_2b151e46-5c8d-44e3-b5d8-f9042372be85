apiVersion: v1
kind: ConfigMap
metadata:
  name: legacybridge-config
  namespace: legacybridge
  labels:
    app: legacybridge
    component: config
data:
  # API URLs for frontend
  api-url: "https://api.legacybridge.com"
  ws-url: "wss://api.legacybridge.com"
  
  # Backend configuration
  rust-log-level: "info"
  environment: "production"
  
  # Application configuration
  max-upload-size: "100Mi"
  conversion-timeout: "300"
  worker-threads: "4"
  
  # Feature flags
  enable-ai-features: "true"
  enable-legacy-formats: "true"
  enable-batch-processing: "true"
  
  # Monitoring configuration
  metrics-enabled: "true"
  metrics-port: "9090"
  
  # Rate limiting
  rate-limit-requests: "1000"
  rate-limit-window: "60"