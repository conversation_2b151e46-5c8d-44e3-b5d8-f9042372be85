// Multi-level Caching System for LegacyBridge
use std::sync::{Arc, RwLock};
use std::collections::HashMap;
use std::time::{Duration, Instant};
use serde::{Serialize, Deserialize};
use blake3::Hasher;

/// Multi-level cache with LRU eviction and TTL support
pub struct MultiLevelCache {
    /// Level 1: In-memory cache for hot data
    l1_cache: Arc<RwLock<LruCache<String, CacheEntry>>>,
    
    /// Level 2: Compressed cache for larger items
    l2_cache: Arc<RwLock<CompressedCache>>,
    
    /// Level 3: Persistent disk cache
    l3_cache: Option<DiskCache>,
    
    /// Cache configuration
    config: CacheConfig,
    
    /// Cache statistics
    stats: Arc<RwLock<CacheStats>>,
}

#[derive(Debug, Clone)]
pub struct CacheConfig {
    /// L1 cache size (number of entries)
    pub l1_max_entries: usize,
    
    /// L2 cache size (bytes)
    pub l2_max_size: usize,
    
    /// Default TTL for cache entries
    pub default_ttl: Duration,
    
    /// Enable disk caching
    pub enable_disk_cache: bool,
    
    /// Disk cache directory
    pub disk_cache_dir: String,
    
    /// Compression level (0-9)
    pub compression_level: u32,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            l1_max_entries: 1000,
            l2_max_size: 100 * 1024 * 1024, // 100MB
            default_ttl: Duration::from_secs(3600), // 1 hour
            enable_disk_cache: true,
            disk_cache_dir: "./cache".to_string(),
            compression_level: 6,
        }
    }
}

#[derive(Debug, Clone)]
struct CacheEntry {
    data: Vec<u8>,
    created_at: Instant,
    accessed_at: Instant,
    ttl: Duration,
    access_count: u64,
    size: usize,
}

impl CacheEntry {
    fn new(data: Vec<u8>, ttl: Duration) -> Self {
        let now = Instant::now();
        let size = data.len();
        
        Self {
            data,
            created_at: now,
            accessed_at: now,
            ttl,
            access_count: 1,
            size,
        }
    }
    
    fn is_expired(&self) -> bool {
        self.created_at.elapsed() > self.ttl
    }
    
    fn access(&mut self) -> &[u8] {
        self.accessed_at = Instant::now();
        self.access_count += 1;
        &self.data
    }
}

impl MultiLevelCache {
    pub fn new(config: CacheConfig) -> Result<Self, CacheError> {
        let l1_cache = Arc::new(RwLock::new(LruCache::new(config.l1_max_entries)));
        let l2_cache = Arc::new(RwLock::new(CompressedCache::new(config.l2_max_size, config.compression_level)));
        
        let l3_cache = if config.enable_disk_cache {
            Some(DiskCache::new(&config.disk_cache_dir)?)
        } else {
            None
        };
        
        Ok(Self {
            l1_cache,
            l2_cache,
            l3_cache,
            config,
            stats: Arc::new(RwLock::new(CacheStats::new())),
        })
    }
    
    /// Get cached conversion result
    pub async fn get_conversion(&self, key: &ConversionKey) -> Option<Vec<u8>> {
        let cache_key = self.generate_cache_key(key);
        
        // Try L1 cache first
        if let Some(data) = self.get_from_l1(&cache_key).await {
            self.record_hit(CacheLevel::L1);
            return Some(data);
        }
        
        // Try L2 cache
        if let Some(data) = self.get_from_l2(&cache_key).await {
            // Promote to L1
            self.put_to_l1(&cache_key, &data, self.config.default_ttl).await;
            self.record_hit(CacheLevel::L2);
            return Some(data);
        }
        
        // Try L3 cache
        if let Some(ref disk_cache) = self.l3_cache {
            if let Ok(Some(data)) = disk_cache.get(&cache_key).await {
                // Promote to L2 and L1
                self.put_to_l2(&cache_key, &data).await;
                self.put_to_l1(&cache_key, &data, self.config.default_ttl).await;
                self.record_hit(CacheLevel::L3);
                return Some(data);
            }
        }
        
        self.record_miss();
        None
    }
    
    /// Store conversion result in cache
    pub async fn put_conversion(&self, key: &ConversionKey, data: &[u8]) {
        let cache_key = self.generate_cache_key(key);
        
        // Always store in L1
        self.put_to_l1(&cache_key, data, self.config.default_ttl).await;
        
        // Store in L2 if data is large enough
        if data.len() > 1024 { // > 1KB
            self.put_to_l2(&cache_key, data).await;
        }
        
        // Store in L3 for persistence
        if let Some(ref disk_cache) = self.l3_cache {
            if let Err(e) = disk_cache.put(&cache_key, data).await {
                eprintln!("Disk cache write error: {}", e);
            }
        }
    }
    
    async fn get_from_l1(&self, key: &str) -> Option<Vec<u8>> {
        let mut cache = self.l1_cache.write().unwrap();
        if let Some(entry) = cache.get_mut(key) {
            if !entry.is_expired() {
                return Some(entry.access().to_vec());
            } else {
                cache.remove(key);
            }
        }
        None
    }
    
    async fn put_to_l1(&self, key: &str, data: &[u8], ttl: Duration) {
        let mut cache = self.l1_cache.write().unwrap();
        let entry = CacheEntry::new(data.to_vec(), ttl);
        cache.put(key.to_string(), entry);
    }
    
    async fn get_from_l2(&self, key: &str) -> Option<Vec<u8>> {
        let cache = self.l2_cache.read().unwrap();
        cache.get(key)
    }
    
    async fn put_to_l2(&self, key: &str, data: &[u8]) {
        let mut cache = self.l2_cache.write().unwrap();
        cache.put(key.to_string(), data.to_vec());
    }
    
    fn generate_cache_key(&self, key: &ConversionKey) -> String {
        let mut hasher = Hasher::new();
        hasher.update(key.input_format.as_bytes());
        hasher.update(key.output_format.as_bytes());
        hasher.update(&key.content_hash);
        hasher.update(&key.options_hash);
        
        hex::encode(hasher.finalize().as_bytes())
    }
    
    fn record_hit(&self, level: CacheLevel) {
        let mut stats = self.stats.write().unwrap();
        stats.hits += 1;
        match level {
            CacheLevel::L1 => stats.l1_hits += 1,
            CacheLevel::L2 => stats.l2_hits += 1,
            CacheLevel::L3 => stats.l3_hits += 1,
        }
    }
    
    fn record_miss(&self) {
        let mut stats = self.stats.write().unwrap();
        stats.misses += 1;
    }
    
    /// Get cache statistics
    pub fn get_stats(&self) -> CacheStats {
        self.stats.read().unwrap().clone()
    }
    
    /// Clear all cache levels
    pub async fn clear(&self) {
        self.l1_cache.write().unwrap().clear();
        self.l2_cache.write().unwrap().clear();
        
        if let Some(ref disk_cache) = self.l3_cache {
            if let Err(e) = disk_cache.clear().await {
                eprintln!("Disk cache clear error: {}", e);
            }
        }
    }
    
    /// Cleanup old entries
    pub async fn cleanup(&self) -> Result<(), CacheError> {
        // Cleanup expired entries from L1
        {
            let mut cache = self.l1_cache.write().unwrap();
            let keys_to_remove: Vec<String> = cache.map.keys().cloned().collect();
            for key in keys_to_remove {
                if let Some(entry) = cache.map.get(&key) {
                    if entry.is_expired() {
                        cache.remove(&key);
                    }
                }
            }
        }
        
        Ok(())
    }
}

/// Conversion cache key
#[derive(Debug, Clone)]
pub struct ConversionKey {
    pub input_format: String,
    pub output_format: String,
    pub content_hash: [u8; 32], // Blake3 hash of content
    pub options_hash: [u8; 32], // Blake3 hash of conversion options
}

impl ConversionKey {
    pub fn new(
        input_format: &str,
        output_format: &str,
        content: &[u8],
        options: &ConversionOptions,
    ) -> Self {
        let mut content_hasher = Hasher::new();
        content_hasher.update(content);
        let content_hash = *content_hasher.finalize().as_bytes();
        
        let mut options_hasher = Hasher::new();
        options_hasher.update(&serde_json::to_vec(options).unwrap_or_default());
        let options_hash = *options_hasher.finalize().as_bytes();
        
        Self {
            input_format: input_format.to_string(),
            output_format: output_format.to_string(),
            content_hash,
            options_hash,
        }
    }
}

/// LRU Cache implementation
struct LruCache<K, V> {
    map: HashMap<K, V>,
    max_size: usize,
    access_order: Vec<K>,
}

impl<K: Clone + Eq + std::hash::Hash, V> LruCache<K, V> {
    fn new(max_size: usize) -> Self {
        Self {
            map: HashMap::new(),
            max_size,
            access_order: Vec::new(),
        }
    }
    
    fn get_mut(&mut self, key: &K) -> Option<&mut V> {
        if self.map.contains_key(key) {
            self.move_to_front(key);
            self.map.get_mut(key)
        } else {
            None
        }
    }
    
    fn put(&mut self, key: K, value: V) {
        if self.map.contains_key(&key) {
            self.map.insert(key.clone(), value);
            self.move_to_front(&key);
        } else {
            if self.map.len() >= self.max_size {
                if let Some(lru_key) = self.access_order.last().cloned() {
                    self.map.remove(&lru_key);
                    self.access_order.retain(|k| k != &lru_key);
                }
            }
            
            self.map.insert(key.clone(), value);
            self.access_order.insert(0, key);
        }
    }
    
    fn remove(&mut self, key: &K) -> Option<V> {
        self.access_order.retain(|k| k != key);
        self.map.remove(key)
    }
    
    fn clear(&mut self) {
        self.map.clear();
        self.access_order.clear();
    }
    
    fn move_to_front(&mut self, key: &K) {
        if let Some(pos) = self.access_order.iter().position(|k| k == key) {
            let key = self.access_order.remove(pos);
            self.access_order.insert(0, key);
        }
    }
}

/// Compressed cache for larger items
struct CompressedCache {
    storage: HashMap<String, CompressedEntry>,
    max_size: usize,
    current_size: usize,
    compression_level: u32,
}

struct CompressedEntry {
    compressed_data: Vec<u8>,
    original_size: usize,
    created_at: Instant,
}

impl CompressedCache {
    fn new(max_size: usize, compression_level: u32) -> Self {
        Self {
            storage: HashMap::new(),
            max_size,
            current_size: 0,
            compression_level,
        }
    }
    
    fn get(&self, key: &str) -> Option<Vec<u8>> {
        if let Some(entry) = self.storage.get(key) {
            // Decompress data
            if let Ok(decompressed) = self.decompress(&entry.compressed_data) {
                return Some(decompressed);
            }
        }
        None
    }
    
    fn put(&mut self, key: String, data: Vec<u8>) {
        if let Ok(compressed) = self.compress(&data) {
            let entry = CompressedEntry {
                compressed_data: compressed.clone(),
                original_size: data.len(),
                created_at: Instant::now(),
            };
            
            // Evict old entries if necessary
            while self.current_size + compressed.len() > self.max_size && !self.storage.is_empty() {
                self.evict_oldest();
            }
            
            if compressed.len() <= self.max_size {
                self.current_size += compressed.len();
                self.storage.insert(key, entry);
            }
        }
    }
    
    fn clear(&mut self) {
        self.storage.clear();
        self.current_size = 0;
    }
    
    fn compress(&self, data: &[u8]) -> Result<Vec<u8>, std::io::Error> {
        use flate2::write::GzEncoder;
        use flate2::Compression;
        use std::io::Write;
        
        let mut encoder = GzEncoder::new(Vec::new(), Compression::new(self.compression_level));
        encoder.write_all(data)?;
        encoder.finish()
    }
    
    fn decompress(&self, data: &[u8]) -> Result<Vec<u8>, std::io::Error> {
        use flate2::read::GzDecoder;
        use std::io::Read;
        
        let mut decoder = GzDecoder::new(data);
        let mut decompressed = Vec::new();
        decoder.read_to_end(&mut decompressed)?;
        Ok(decompressed)
    }
    
    fn evict_oldest(&mut self) {
        if let Some((oldest_key, _)) = self.storage.iter()
            .min_by_key(|(_, entry)| entry.created_at)
            .map(|(k, v)| (k.clone(), v.compressed_data.len())) {
            
            if let Some(entry) = self.storage.remove(&oldest_key) {
                self.current_size -= entry.compressed_data.len();
            }
        }
    }
}

/// Disk cache for persistence
struct DiskCache {
    base_dir: std::path::PathBuf,
}

impl DiskCache {
    fn new(base_dir: &str) -> Result<Self, CacheError> {
        let base_dir = std::path::PathBuf::from(base_dir);
        std::fs::create_dir_all(&base_dir)
            .map_err(|e| CacheError::DiskError(format!("Failed to create cache directory: {}", e)))?;
        
        Ok(Self { base_dir })
    }
    
    async fn get(&self, key: &str) -> Result<Option<Vec<u8>>, CacheError> {
        let file_path = self.get_file_path(key);
        
        match tokio::fs::read(&file_path).await {
            Ok(data) => Ok(Some(data)),
            Err(e) if e.kind() == std::io::ErrorKind::NotFound => Ok(None),
            Err(e) => Err(CacheError::DiskError(format!("Failed to read cache file: {}", e))),
        }
    }
    
    async fn put(&self, key: &str, data: &[u8]) -> Result<(), CacheError> {
        let file_path = self.get_file_path(key);
        
        if let Some(parent) = file_path.parent() {
            tokio::fs::create_dir_all(parent).await
                .map_err(|e| CacheError::DiskError(format!("Failed to create cache subdirectory: {}", e)))?;
        }
        
        tokio::fs::write(&file_path, data).await
            .map_err(|e| CacheError::DiskError(format!("Failed to write cache file: {}", e)))?;
        
        Ok(())
    }
    
    async fn clear(&self) -> Result<(), CacheError> {
        tokio::fs::remove_dir_all(&self.base_dir).await
            .map_err(|e| CacheError::DiskError(format!("Failed to clear disk cache: {}", e)))?;
        
        tokio::fs::create_dir_all(&self.base_dir).await
            .map_err(|e| CacheError::DiskError(format!("Failed to recreate cache directory: {}", e)))?;
        
        Ok(())
    }
    
    fn get_file_path(&self, key: &str) -> std::path::PathBuf {
        // Create subdirectories based on key prefix to avoid too many files in one directory
        let subdir = &key[..2.min(key.len())];
        self.base_dir.join(subdir).join(format!("{}.cache", key))
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    pub hits: u64,
    pub misses: u64,
    pub l1_hits: u64,
    pub l2_hits: u64,
    pub l3_hits: u64,
    pub hit_rate: f64,
}

impl CacheStats {
    fn new() -> Self {
        Self {
            hits: 0,
            misses: 0,
            l1_hits: 0,
            l2_hits: 0,
            l3_hits: 0,
            hit_rate: 0.0,
        }
    }
    
    pub fn calculate_hit_rate(&mut self) {
        let total = self.hits + self.misses;
        self.hit_rate = if total > 0 {
            self.hits as f64 / total as f64
        } else {
            0.0
        };
    }
}

#[derive(Debug, Clone)]
pub enum CacheLevel {
    L1,
    L2,
    L3,
}

#[derive(Debug)]
pub enum CacheError {
    DiskError(String),
    CompressionError(String),
    SerializationError(String),
}

impl std::fmt::Display for CacheError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CacheError::DiskError(msg) => write!(f, "Disk cache error: {}", msg),
            CacheError::CompressionError(msg) => write!(f, "Compression error: {}", msg),
            CacheError::SerializationError(msg) => write!(f, "Serialization error: {}", msg),
        }
    }
}

impl std::error::Error for CacheError {}

// Placeholder for ConversionOptions
#[derive(Serialize, Deserialize)]
pub struct ConversionOptions {
    pub preserve_formatting: bool,
    pub quality: Option<u8>,
    // ... other options
}