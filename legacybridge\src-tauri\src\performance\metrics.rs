// Performance Monitoring and Metrics for LegacyBridge
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use std::collections::{HashMap, VecDeque};
use serde::{Serialize, Deserialize};
use tokio::time::interval;

/// Performance monitoring system
pub struct PerformanceMonitor {
    /// Real-time metrics
    metrics: Arc<RwLock<MetricsStore>>,
    
    /// Historical data
    history: Arc<RwLock<MetricsHistory>>,
    
    /// Active operation trackers
    operations: Arc<RwLock<HashMap<String, OperationTracker>>>,
    
    /// Configuration
    config: MonitorConfig,
}

#[derive(Debug, Clone)]
pub struct MonitorConfig {
    /// Metrics collection interval
    pub collection_interval: Duration,
    
    /// History retention period
    pub retention_period: Duration,
    
    /// Enable detailed operation tracking
    pub enable_operation_tracking: bool,
    
    /// Performance thresholds
    pub thresholds: PerformanceThresholds,
}

impl Default for MonitorConfig {
    fn default() -> Self {
        Self {
            collection_interval: Duration::from_secs(10),
            retention_period: Duration::from_secs(3600), // 1 hour
            enable_operation_tracking: true,
            thresholds: PerformanceThresholds::default(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceThresholds {
    /// Response time warning threshold (ms)
    pub response_time_warning: u64,
    
    /// Response time critical threshold (ms)
    pub response_time_critical: u64,
    
    /// Memory usage warning threshold (MB)
    pub memory_usage_warning: u64,
    
    /// Memory usage critical threshold (MB)
    pub memory_usage_critical: u64,
    
    /// CPU usage warning threshold (%)
    pub cpu_usage_warning: f64,
    
    /// CPU usage critical threshold (%)
    pub cpu_usage_critical: f64,
}

impl Default for PerformanceThresholds {
    fn default() -> Self {
        Self {
            response_time_warning: 1000,
            response_time_critical: 5000,
            memory_usage_warning: 500,
            memory_usage_critical: 1000,
            cpu_usage_warning: 70.0,
            cpu_usage_critical: 90.0,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct MetricsStore {
    /// Current metrics snapshot
    current: MetricsSnapshot,
    
    /// Aggregated metrics
    aggregated: AggregatedMetrics,
    
    /// Last update time
    last_update: Instant,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsSnapshot {
    /// Timestamp
    pub timestamp: u64,
    
    /// Response time metrics
    pub response_times: ResponseTimeMetrics,
    
    /// Throughput metrics
    pub throughput: ThroughputMetrics,
    
    /// Resource usage
    pub resources: ResourceMetrics,
    
    /// Operation counts
    pub operations: OperationMetrics,
    
    /// Cache performance
    pub cache: CacheMetrics,
    
    /// Memory pool performance
    pub memory_pool: MemoryPoolMetrics,
    
    /// SIMD acceleration metrics
    pub simd: SimdMetrics,
    
    /// Additional metrics used by PerformanceOptimizer
    pub cpu_usage: f64,
    pub memory_usage_mb: u64,
    pub operations_per_second: f64,
    pub average_latency_ms: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseTimeMetrics {
    pub min: f64,
    pub max: f64,
    pub avg: f64,
    pub p50: f64,
    pub p90: f64,
    pub p95: f64,
    pub p99: f64,
    pub count: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThroughputMetrics {
    pub requests_per_second: f64,
    pub bytes_per_second: f64,
    pub conversions_per_minute: f64,
    pub successful_conversions: u64,
    pub failed_conversions: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceMetrics {
    pub cpu_usage_percent: f64,
    pub memory_usage_mb: u64,
    pub thread_count: u32,
    pub file_descriptors: u32,
    pub disk_io_bytes_per_second: f64,
    pub network_io_bytes_per_second: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OperationMetrics {
    pub total_operations: u64,
    pub active_operations: u32,
    pub operations_by_type: HashMap<String, u64>,
    pub average_duration_by_type: HashMap<String, f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheMetrics {
    pub hit_rate: f64,
    pub l1_hit_rate: f64,
    pub l2_hit_rate: f64,
    pub l3_hit_rate: f64,
    pub eviction_rate: f64,
    pub total_cached_bytes: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryPoolMetrics {
    pub allocations_from_pool: u64,
    pub new_allocations: u64,
    pub pool_hit_rate: f64,
    pub total_pool_memory_mb: u64,
    pub fragmentation_percent: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SimdMetrics {
    pub operations_accelerated: u64,
    pub bytes_processed: u64,
    pub speedup_factor: f64,
    pub fallback_operations: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct AggregatedMetrics {
    /// Total operations processed
    total_operations: u64,
    
    /// Total bytes processed
    total_bytes_processed: u64,
    
    /// Total errors
    total_errors: u64,
    
    /// Average response time (exponential moving average)
    avg_response_time_ema: f64,
    
    /// Peak response time
    peak_response_time: f64,
    
    /// Peak memory usage
    peak_memory_usage_mb: u64,
}

struct MetricsHistory {
    /// Historical snapshots
    snapshots: VecDeque<MetricsSnapshot>,
    
    /// Maximum snapshots to keep
    max_snapshots: usize,
}

struct OperationTracker {
    operation_id: String,
    operation_type: String,
    start_time: Instant,
    memory_at_start: u64,
}

impl PerformanceMonitor {
    pub fn new() -> Self {
        Self::with_config(MonitorConfig::default())
    }
    
    pub fn with_config(config: MonitorConfig) -> Self {
        let max_snapshots = (config.retention_period.as_secs() / config.collection_interval.as_secs()) as usize;
        
        Self {
            metrics: Arc::new(RwLock::new(MetricsStore {
                current: MetricsSnapshot::default(),
                aggregated: AggregatedMetrics::default(),
                last_update: Instant::now(),
            })),
            history: Arc::new(RwLock::new(MetricsHistory {
                snapshots: VecDeque::with_capacity(max_snapshots),
                max_snapshots,
            })),
            operations: Arc::new(RwLock::new(HashMap::new())),
            config,
        }
    }
    
    /// Start monitoring background task
    pub fn start_monitoring(&self) -> tokio::task::JoinHandle<()> {
        let metrics = Arc::clone(&self.metrics);
        let history = Arc::clone(&self.history);
        let interval_duration = self.config.collection_interval;
        
        tokio::spawn(async move {
            let mut interval = interval(interval_duration);
            
            loop {
                interval.tick().await;
                Self::collect_metrics(&metrics, &history).await;
            }
        })
    }
    
    /// Start an operation tracking
    pub fn start_operation(&self, operation_id: String, operation_type: String) -> OperationHandle {
        if self.config.enable_operation_tracking {
            let tracker = OperationTracker {
                operation_id: operation_id.clone(),
                operation_type: operation_type.clone(),
                start_time: Instant::now(),
                memory_at_start: Self::get_current_memory_usage(),
            };
            
            self.operations.write().unwrap().insert(operation_id.clone(), tracker);
        }
        
        OperationHandle {
            operation_id,
            monitor: self,
            completed: false,
        }
    }
    
    /// Record response time
    pub fn record_response_time(&self, duration: Duration) {
        let mut metrics = self.metrics.write().unwrap();
        let ms = duration.as_millis() as f64;
        
        // Update response time metrics
        let rt = &mut metrics.current.response_times;
        rt.count += 1;
        rt.min = if rt.count == 1 { ms } else { rt.min.min(ms) };
        rt.max = rt.max.max(ms);
        rt.avg = (rt.avg * (rt.count - 1) as f64 + ms) / rt.count as f64;
        
        // Update aggregated metrics
        let alpha = 0.1; // EMA smoothing factor
        metrics.aggregated.avg_response_time_ema = 
            alpha * ms + (1.0 - alpha) * metrics.aggregated.avg_response_time_ema;
        metrics.aggregated.peak_response_time = metrics.aggregated.peak_response_time.max(ms);
    }
    
    /// Record throughput
    pub fn record_throughput(&self, bytes: usize, success: bool) {
        let mut metrics = self.metrics.write().unwrap();
        
        if success {
            metrics.current.throughput.successful_conversions += 1;
        } else {
            metrics.current.throughput.failed_conversions += 1;
        }
        
        metrics.aggregated.total_operations += 1;
        metrics.aggregated.total_bytes_processed += bytes as u64;
        
        if !success {
            metrics.aggregated.total_errors += 1;
        }
    }
    
    /// Update cache metrics
    pub fn update_cache_metrics(&self, cache_stats: super::cache::CacheStats) {
        let mut metrics = self.metrics.write().unwrap();
        let cache = &mut metrics.current.cache;
        
        let total_requests = cache_stats.hits + cache_stats.misses;
        cache.hit_rate = if total_requests > 0 {
            cache_stats.hits as f64 / total_requests as f64
        } else {
            0.0
        };
        
        if cache_stats.l1_hits + cache_stats.misses > 0 {
            cache.l1_hit_rate = cache_stats.l1_hits as f64 / 
                (cache_stats.l1_hits + cache_stats.misses) as f64;
        }
        
        if cache_stats.l2_hits > 0 {
            cache.l2_hit_rate = cache_stats.l2_hits as f64 / 
                (cache_stats.l2_hits + cache_stats.l3_hits + cache_stats.misses) as f64;
        }
        
        if cache_stats.l3_hits > 0 {
            cache.l3_hit_rate = cache_stats.l3_hits as f64 / 
                (cache_stats.l3_hits + cache_stats.misses) as f64;
        }
    }
    
    /// Update memory pool metrics
    pub fn update_memory_pool_metrics(&self, pool_stats: super::memory::MemoryStats) {
        let mut metrics = self.metrics.write().unwrap();
        let pool = &mut metrics.current.memory_pool;
        
        let total_allocations = pool_stats.allocation_count;
        pool.pool_hit_rate = if total_allocations > 0 {
            pool_stats.pool_hit_rate
        } else {
            0.0
        };
        
        pool.total_pool_memory_mb = pool_stats.current_usage / (1024 * 1024);
        
        // Update peak memory usage
        metrics.aggregated.peak_memory_usage_mb = 
            metrics.aggregated.peak_memory_usage_mb.max(pool.total_pool_memory_mb);
    }
    
    /// Update SIMD metrics
    pub fn update_simd_metrics(&self, simd_stats: super::simd::SimdStats) {
        let mut metrics = self.metrics.write().unwrap();
        let simd = &mut metrics.current.simd;
        
        simd.operations_accelerated = simd_stats.operations_count;
        simd.bytes_processed = simd_stats.bytes_processed;
        simd.speedup_factor = simd_stats.speedup_factor;
        simd.fallback_operations = simd_stats.fallback_count;
    }
    
    /// Get current metrics snapshot
    pub fn get_current_metrics(&self) -> MetricsSnapshot {
        self.metrics.read().unwrap().current.clone()
    }
    
    /// Get historical metrics
    pub fn get_history(&self, duration: Duration) -> Vec<MetricsSnapshot> {
        let history = self.history.read().unwrap();
        let cutoff = Instant::now() - duration;
        
        history.snapshots.iter()
            .filter(|s| {
                // Approximate time filter based on snapshot position
                true // In production, add proper timestamp filtering
            })
            .cloned()
            .collect()
    }
    
    /// Get performance report
    pub fn get_performance_report(&self) -> PerformanceReport {
        let metrics = self.metrics.read().unwrap();
        let current = &metrics.current;
        let aggregated = &metrics.aggregated;
        
        let health_status = self.calculate_health_status(current);
        let recommendations = self.generate_recommendations(current, aggregated);
        
        PerformanceReport {
            timestamp: current.timestamp,
            health_status,
            current_metrics: current.clone(),
            aggregated_metrics: AggregatedMetricsReport {
                total_operations: aggregated.total_operations,
                total_bytes_processed: aggregated.total_bytes_processed,
                total_errors: aggregated.total_errors,
                error_rate: if aggregated.total_operations > 0 {
                    aggregated.total_errors as f64 / aggregated.total_operations as f64
                } else {
                    0.0
                },
                avg_response_time: aggregated.avg_response_time_ema,
                peak_response_time: aggregated.peak_response_time,
                peak_memory_usage_mb: aggregated.peak_memory_usage_mb,
            },
            recommendations,
        }
    }
    
    async fn collect_metrics(
        metrics: &Arc<RwLock<MetricsStore>>,
        history: &Arc<RwLock<MetricsHistory>>,
    ) {
        // Update current metrics
        let mut store = metrics.write().unwrap();
        store.current.timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // Calculate throughput
        let elapsed = store.last_update.elapsed().as_secs_f64();
        if elapsed > 0.0 {
            let throughput = &mut store.current.throughput;
            throughput.requests_per_second = store.current.operations.total_operations as f64 / elapsed;
            throughput.conversions_per_minute = throughput.requests_per_second * 60.0;
        }
        
        // Update resource metrics
        let cpu_usage = Self::get_cpu_usage();
        let memory_usage_mb = Self::get_current_memory_usage() / (1024 * 1024);
        
        store.current.resources = ResourceMetrics {
            cpu_usage_percent: cpu_usage,
            memory_usage_mb,
            thread_count: Self::get_thread_count(),
            file_descriptors: Self::get_file_descriptors(),
            disk_io_bytes_per_second: 0.0, // Placeholder
            network_io_bytes_per_second: 0.0, // Placeholder
        };
        
        // Update additional metrics
        store.current.cpu_usage = cpu_usage;
        store.current.memory_usage_mb = memory_usage_mb;
        store.current.operations_per_second = store.current.throughput.requests_per_second;
        store.current.average_latency_ms = store.current.response_times.avg;
        
        store.last_update = Instant::now();
        
        // Add to history
        let snapshot = store.current.clone();
        drop(store);
        
        let mut hist = history.write().unwrap();
        hist.snapshots.push_back(snapshot);
        
        // Maintain history size
        while hist.snapshots.len() > hist.max_snapshots {
            hist.snapshots.pop_front();
        }
    }
    
    fn calculate_health_status(&self, metrics: &MetricsSnapshot) -> HealthStatus {
        let mut status = HealthStatus::Healthy;
        let thresholds = &self.config.thresholds;
        
        // Check response time
        if metrics.response_times.p95 > thresholds.response_time_critical as f64 {
            status = HealthStatus::Critical;
        } else if metrics.response_times.p90 > thresholds.response_time_warning as f64 {
            status = status.max(HealthStatus::Warning);
        }
        
        // Check resource usage
        if metrics.resources.cpu_usage_percent > thresholds.cpu_usage_critical {
            status = HealthStatus::Critical;
        } else if metrics.resources.cpu_usage_percent > thresholds.cpu_usage_warning {
            status = status.max(HealthStatus::Warning);
        }
        
        if metrics.resources.memory_usage_mb > thresholds.memory_usage_critical {
            status = HealthStatus::Critical;
        } else if metrics.resources.memory_usage_mb > thresholds.memory_usage_warning {
            status = status.max(HealthStatus::Warning);
        }
        
        status
    }
    
    fn generate_recommendations(
        &self,
        current: &MetricsSnapshot,
        aggregated: &AggregatedMetrics,
    ) -> Vec<String> {
        let mut recommendations = Vec::new();
        
        // Response time recommendations
        if current.response_times.p95 > 2000.0 {
            recommendations.push(
                "High response times detected. Consider enabling caching or increasing thread pool size.".to_string()
            );
        }
        
        // Cache recommendations
        if current.cache.hit_rate < 0.5 {
            recommendations.push(
                "Low cache hit rate. Consider pre-warming cache with common conversions.".to_string()
            );
        }
        
        // Memory pool recommendations
        if current.memory_pool.pool_hit_rate < 0.7 {
            recommendations.push(
                "Low memory pool hit rate. Consider adjusting pool sizes.".to_string()
            );
        }
        
        // SIMD recommendations
        if current.simd.fallback_operations > current.simd.operations_accelerated * 0.1 {
            recommendations.push(
                "High SIMD fallback rate. Check CPU capabilities.".to_string()
            );
        }
        
        recommendations
    }
    
    // System metric collection helpers
    fn get_current_memory_usage() -> u64 {
        // In production, use sysinfo crate
        512 * 1024 * 1024 // 512MB placeholder
    }
    
    fn get_cpu_usage() -> f64 {
        // In production, use sysinfo crate
        45.0 // 45% placeholder
    }
    
    fn get_thread_count() -> u32 {
        // In production, use sysinfo crate
        16 // placeholder
    }
    
    fn get_file_descriptors() -> u32 {
        // In production, use platform-specific APIs
        256 // placeholder
    }
    
    /// Track an operation
    pub fn track_operation(&self, operation_name: &str) {
        let mut metrics = self.metrics.write().unwrap();
        let ops = &mut metrics.current.operations;
        ops.total_operations += 1;
        *ops.operations_by_type.entry(operation_name.to_string()).or_insert(0) += 1;
    }
    
    /// Record operation duration
    pub fn record_operation_duration(&self, operation_name: &str, duration: Duration) {
        self.record_response_time(duration);
        
        let mut metrics = self.metrics.write().unwrap();
        let ops = &mut metrics.current.operations;
        let avg = ops.average_duration_by_type.entry(operation_name.to_string()).or_insert(0.0);
        *avg = (*avg + duration.as_millis() as f64) / 2.0; // Simple moving average
    }
}

/// RAII handle for operation tracking
pub struct OperationHandle<'a> {
    operation_id: String,
    monitor: &'a PerformanceMonitor,
    completed: bool,
}

impl<'a> OperationHandle<'a> {
    /// Complete the operation with success
    pub fn complete(mut self) {
        self.completed = true;
        self.complete_operation(true);
    }
    
    /// Complete the operation with failure
    pub fn fail(mut self) {
        self.completed = true;
        self.complete_operation(false);
    }
    
    fn complete_operation(&self, success: bool) {
        if let Some(tracker) = self.monitor.operations.write().unwrap().remove(&self.operation_id) {
            let duration = tracker.start_time.elapsed();
            let memory_delta = PerformanceMonitor::get_current_memory_usage() - tracker.memory_at_start;
            
            // Record metrics
            self.monitor.record_response_time(duration);
            
            // Update operation metrics
            let mut metrics = self.monitor.metrics.write().unwrap();
            let ops = &mut metrics.current.operations;
            *ops.operations_by_type.entry(tracker.operation_type.clone()).or_insert(0) += 1;
            
            let avg = ops.average_duration_by_type.entry(tracker.operation_type).or_insert(0.0);
            *avg = (*avg + duration.as_millis() as f64) / 2.0; // Simple moving average
        }
    }
}

impl<'a> Drop for OperationHandle<'a> {
    fn drop(&mut self) {
        if !self.completed {
            self.complete_operation(false);
        }
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum HealthStatus {
    Healthy,
    Warning,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceReport {
    pub timestamp: u64,
    pub health_status: HealthStatus,
    pub current_metrics: MetricsSnapshot,
    pub aggregated_metrics: AggregatedMetricsReport,
    pub recommendations: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregatedMetricsReport {
    pub total_operations: u64,
    pub total_bytes_processed: u64,
    pub total_errors: u64,
    pub error_rate: f64,
    pub avg_response_time: f64,
    pub peak_response_time: f64,
    pub peak_memory_usage_mb: u64,
}

// Default implementations
impl Default for MetricsSnapshot {
    fn default() -> Self {
        Self {
            timestamp: 0,
            response_times: ResponseTimeMetrics {
                min: f64::MAX,
                max: 0.0,
                avg: 0.0,
                p50: 0.0,
                p90: 0.0,
                p95: 0.0,
                p99: 0.0,
                count: 0,
            },
            throughput: ThroughputMetrics {
                requests_per_second: 0.0,
                bytes_per_second: 0.0,
                conversions_per_minute: 0.0,
                successful_conversions: 0,
                failed_conversions: 0,
            },
            resources: ResourceMetrics {
                cpu_usage_percent: 0.0,
                memory_usage_mb: 0,
                thread_count: 0,
                file_descriptors: 0,
                disk_io_bytes_per_second: 0.0,
                network_io_bytes_per_second: 0.0,
            },
            operations: OperationMetrics {
                total_operations: 0,
                active_operations: 0,
                operations_by_type: HashMap::new(),
                average_duration_by_type: HashMap::new(),
            },
            cache: CacheMetrics {
                hit_rate: 0.0,
                l1_hit_rate: 0.0,
                l2_hit_rate: 0.0,
                l3_hit_rate: 0.0,
                eviction_rate: 0.0,
                total_cached_bytes: 0,
            },
            memory_pool: MemoryPoolMetrics {
                allocations_from_pool: 0,
                new_allocations: 0,
                pool_hit_rate: 0.0,
                total_pool_memory_mb: 0,
                fragmentation_percent: 0.0,
            },
            simd: SimdMetrics {
                operations_accelerated: 0,
                bytes_processed: 0,
                speedup_factor: 0.0,
                fallback_operations: 0,
            },
            cpu_usage: 0.0,
            memory_usage_mb: 0,
            operations_per_second: 0.0,
            average_latency_ms: 0.0,
        }
    }
}

impl Default for AggregatedMetrics {
    fn default() -> Self {
        Self {
            total_operations: 0,
            total_bytes_processed: 0,
            total_errors: 0,
            avg_response_time_ema: 0.0,
            peak_response_time: 0.0,
            peak_memory_usage_mb: 0,
        }
    }
}