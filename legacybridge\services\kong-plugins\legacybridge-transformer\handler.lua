-- Request/Response transformation plugin for LegacyBridge services

local cjson = require "cjson"

local LegacyBridgeTransformerHandler = {}

LegacyBridgeTransformerHandler.PRIORITY = 800
LegacyBridgeTransformerHandler.VERSION = "1.0.0"

local function add_request_headers(config)
    local service_name = kong.router.get_service().name or "unknown"
    
    -- Add standard headers
    kong.service.request.set_header("X-Service-Name", service_name)
    kong.service.request.set_header("X-Request-Time", tostring(ngx.time()))
    kong.service.request.set_header("X-Request-ID", kong.request.get_header("X-Request-ID") or kong.uuid())
    
    -- Add correlation ID for tracing
    local correlation_id = kong.request.get_header("X-Correlation-ID") or kong.uuid()
    kong.service.request.set_header("X-Correlation-ID", correlation_id)
    
    -- Add client information
    local user_agent = kong.request.get_header("User-Agent") or "unknown"
    kong.service.request.set_header("X-Client-User-Agent", user_agent)
    
    local client_ip = kong.client.get_ip()
    kong.service.request.set_header("X-Client-IP", client_ip)
    
    -- Add forwarded headers for proper IP tracking
    local forwarded_for = kong.request.get_header("X-Forwarded-For")
    if not forwarded_for then
        kong.service.request.set_header("X-Forwarded-For", client_ip)
    end
    
    -- Add custom headers from config
    if config.add_request_headers then
        for header_name, header_value in pairs(config.add_request_headers) do
            kong.service.request.set_header(header_name, header_value)
        end
    end
end

local function remove_request_headers(config)
    -- Remove sensitive headers that shouldn't reach backend services
    local headers_to_remove = config.remove_request_headers or {
        "X-Kong-Request-ID",
        "X-Kong-Proxy-Latency",
        "X-Kong-Upstream-Latency"
    }
    
    for _, header_name in ipairs(headers_to_remove) do
        kong.service.request.clear_header(header_name)
    end
end

local function transform_request_body(config)
    if not config.transform_request_body then
        return
    end
    
    local content_type = kong.request.get_header("Content-Type")
    if not content_type or not string.find(content_type, "application/json") then
        return
    end
    
    local body = kong.request.get_raw_body()
    if not body then
        return
    end
    
    local success, json_body = pcall(cjson.decode, body)
    if not success then
        kong.log.warn("Failed to decode JSON request body")
        return
    end
    
    -- Add metadata to request
    if config.add_request_metadata then
        json_body._metadata = {
            service = kong.router.get_service().name,
            timestamp = ngx.time(),
            request_id = kong.request.get_header("X-Request-ID"),
            user_id = kong.request.get_header("X-User-ID")
        }
    end
    
    -- Apply custom transformations
    if config.request_transformations then
        for field, transformation in pairs(config.request_transformations) do
            if transformation.action == "add" and not json_body[field] then
                json_body[field] = transformation.value
            elseif transformation.action == "remove" then
                json_body[field] = nil
            elseif transformation.action == "rename" and json_body[field] then
                json_body[transformation.new_name] = json_body[field]
                json_body[field] = nil
            end
        end
    end
    
    local new_body = cjson.encode(json_body)
    kong.service.request.set_raw_body(new_body)
    kong.service.request.set_header("Content-Length", tostring(#new_body))
end

local function add_response_headers(config)
    local service_name = kong.router.get_service().name or "unknown"
    
    -- Add standard response headers
    kong.response.set_header("X-Service-Name", service_name)
    kong.response.set_header("X-Response-Time", tostring(ngx.time()))
    kong.response.set_header("X-Request-ID", kong.request.get_header("X-Request-ID"))
    kong.response.set_header("X-Correlation-ID", kong.request.get_header("X-Correlation-ID"))
    
    -- Add performance headers
    local proxy_latency = kong.ctx.shared.proxy_latency
    local upstream_latency = kong.ctx.shared.upstream_latency
    
    if proxy_latency then
        kong.response.set_header("X-Kong-Proxy-Latency", tostring(proxy_latency))
    end
    
    if upstream_latency then
        kong.response.set_header("X-Kong-Upstream-Latency", tostring(upstream_latency))
    end
    
    -- Add security headers
    kong.response.set_header("X-Content-Type-Options", "nosniff")
    kong.response.set_header("X-Frame-Options", "DENY")
    kong.response.set_header("X-XSS-Protection", "1; mode=block")
    kong.response.set_header("Referrer-Policy", "strict-origin-when-cross-origin")
    
    -- Add CORS headers if enabled
    if config.enable_cors then
        kong.response.set_header("Access-Control-Allow-Origin", config.cors_origin or "*")
        kong.response.set_header("Access-Control-Allow-Methods", config.cors_methods or "GET, POST, PUT, DELETE, OPTIONS")
        kong.response.set_header("Access-Control-Allow-Headers", config.cors_headers or "Content-Type, Authorization")
        kong.response.set_header("Access-Control-Max-Age", tostring(config.cors_max_age or 3600))
    end
    
    -- Add custom headers from config
    if config.add_response_headers then
        for header_name, header_value in pairs(config.add_response_headers) do
            kong.response.set_header(header_name, header_value)
        end
    end
end

local function remove_response_headers(config)
    -- Remove sensitive headers from response
    local headers_to_remove = config.remove_response_headers or {
        "Server",
        "X-Powered-By"
    }
    
    for _, header_name in ipairs(headers_to_remove) do
        kong.response.clear_header(header_name)
    end
    
    -- Always remove internal user context headers
    kong.response.clear_header("X-User-ID")
    kong.response.clear_header("X-User-Roles")
    kong.response.clear_header("X-User-Email")
end

local function transform_response_body(config)
    if not config.transform_response_body then
        return
    end
    
    local content_type = kong.response.get_header("Content-Type")
    if not content_type or not string.find(content_type, "application/json") then
        return
    end
    
    local body = kong.response.get_raw_body()
    if not body then
        return
    end
    
    local success, json_body = pcall(cjson.decode, body)
    if not success then
        kong.log.warn("Failed to decode JSON response body")
        return
    end
    
    -- Add metadata to response
    if config.add_response_metadata then
        json_body._metadata = {
            service = kong.router.get_service().name,
            timestamp = ngx.time(),
            request_id = kong.request.get_header("X-Request-ID"),
            processing_time_ms = kong.ctx.shared.upstream_latency
        }
    end
    
    -- Apply custom transformations
    if config.response_transformations then
        for field, transformation in pairs(config.response_transformations) do
            if transformation.action == "add" and not json_body[field] then
                json_body[field] = transformation.value
            elseif transformation.action == "remove" then
                json_body[field] = nil
            elseif transformation.action == "rename" and json_body[field] then
                json_body[transformation.new_name] = json_body[field]
                json_body[field] = nil
            end
        end
    end
    
    local new_body = cjson.encode(json_body)
    kong.response.set_raw_body(new_body)
    kong.response.set_header("Content-Length", tostring(#new_body))
end

function LegacyBridgeTransformerHandler:access(config)
    add_request_headers(config)
    remove_request_headers(config)
    transform_request_body(config)
end

function LegacyBridgeTransformerHandler:header_filter(config)
    add_response_headers(config)
    remove_response_headers(config)
end

function LegacyBridgeTransformerHandler:body_filter(config)
    transform_response_body(config)
end

return LegacyBridgeTransformerHandler
