# LegacyBridge Production Configuration
# This file contains production settings for the LegacyBridge application

[server]
host = "0.0.0.0"
port = 8080
workers = 8
max_connections = 10000
keep_alive = 75
request_timeout = 30
body_limit = "50MB"

[database]
# Database URL should be provided via environment variable LEGACY_BRIDGE_DB_URL
# Example: ********************************/dbname
pool_size = 50
max_overflow = 100
pool_timeout = 30
pool_recycle = 3600
enable_ssl = true

[redis]
# Redis URL should be provided via environment variable LEGACY_BRIDGE_REDIS_URL
# Example: redis://host:6379/0
pool_size = 20
max_connections = 100
connection_timeout = 5
socket_timeout = 5
socket_keepalive = true
socket_keepalive_options = { interval = 5, retries = 3 }

[security]
enable_cors = true
cors_origins = ["https://app.legacybridge.com", "https://api.legacybridge.com"]
cors_max_age = 86400
enable_csrf = true
csrf_token_expiry = 3600
enable_rate_limiting = true
rate_limit_requests = 1000
rate_limit_window = 60
enable_security_headers = true
enable_content_security_policy = true
enable_audit_logging = true
jwt_expiry = 86400
jwt_refresh_expiry = 604800
bcrypt_rounds = 12
max_login_attempts = 5
lockout_duration = 900

[performance]
enable_caching = true
cache_ttl = 3600
enable_compression = true
compression_level = 6
enable_etag = true
enable_http2 = true
enable_connection_pooling = true
enable_query_optimization = true
enable_lazy_loading = true
batch_size = 100
prefetch_size = 10

[monitoring]
enable_metrics = true
metrics_port = 9090
metrics_path = "/metrics"
enable_tracing = true
tracing_sample_rate = 0.1
enable_logging = true
log_level = "INFO"
log_format = "json"
log_rotation = "daily"
log_retention = 30
enable_health_check = true
health_check_path = "/health"
enable_readiness_check = true
readiness_check_path = "/ready"

[storage]
upload_dir = "/app/data/uploads"
temp_dir = "/app/data/temp"
cache_dir = "/app/cache"
max_file_size = "100MB"
allowed_extensions = [
    "doc", "docx", "xls", "xlsx", "ppt", "pptx",
    "odt", "ods", "odp", "rtf", "txt", "csv",
    "pdf", "html", "xml", "json", "yaml",
    "md", "tex", "wpd", "wps", "dbf", "123"
]
enable_virus_scanning = true
enable_file_compression = true
storage_quota_per_tenant = "10GB"

[conversion]
enable_parallel_processing = true
worker_threads = 4
conversion_timeout = 300
enable_preview_generation = true
preview_max_pages = 10
enable_ocr = true
ocr_languages = ["en", "es", "fr", "de", "it", "pt", "zh", "ja", "ko"]
enable_format_detection = true
enable_metadata_extraction = true
preserve_formatting = true
optimize_output = true

[api]
enable_api_versioning = true
default_api_version = "v2"
enable_api_documentation = true
api_docs_path = "/api/docs"
enable_graphql = false
graphql_path = "/graphql"
enable_webhooks = true
webhook_timeout = 30
webhook_retry_count = 3
webhook_retry_delay = 60

[tenant]
enable_multi_tenancy = true
tenant_isolation = true
default_tenant_quota = "10GB"
max_users_per_tenant = 1000
enable_custom_domains = true
enable_sso = true
sso_providers = ["saml", "oauth2", "ldap"]

[backup]
enable_automatic_backup = true
backup_schedule = "0 2 * * *"  # 2 AM daily
backup_retention = 30
backup_location = "s3://legacybridge-backups"
enable_point_in_time_recovery = true
recovery_window = 7

[compliance]
enable_gdpr_compliance = true
data_retention_days = 2555  # 7 years
enable_audit_trail = true
audit_retention_days = 2555
enable_data_encryption = true
encryption_algorithm = "AES-256-GCM"
enable_pii_detection = true
pii_masking = true
compliance_reports_enabled = true
compliance_report_schedule = "0 0 1 * *"  # Monthly

[features]
enable_ai_enhancement = true
enable_realtime_collaboration = true
enable_version_control = true
enable_workflow_automation = true
enable_custom_plugins = true
enable_mobile_api = true
enable_offline_mode = false
enable_beta_features = false

[maintenance]
enable_maintenance_mode = false
maintenance_message = "LegacyBridge is currently undergoing scheduled maintenance. We'll be back shortly."
enable_rolling_updates = true
enable_zero_downtime_deployment = true
health_check_interval = 30
unhealthy_threshold = 3
healthy_threshold = 2