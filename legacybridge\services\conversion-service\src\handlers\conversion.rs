// Conversion handlers
use axum::{
    extract::{Extension, Path, Json},
    response::<PERSON><PERSON> as ResponseJson,
};
use legacybridge_shared::{
    types::{ApiResponse, ConversionRequest, ConversionResponse, ConversionResult, JobStatusResponse, JobStatus},
    ServiceError, ServiceResult,
};
use serde::{Deserialize, Serialize};
use tracing::{info, warn, error};
use uuid::Uuid;
use validator::Validate;

use crate::AppState;

#[derive(Debug, Deserialize, Validate)]
pub struct ConvertDocumentRequest {
    #[validate(length(min = 1))]
    pub input_format: String,
    #[validate(length(min = 1))]
    pub output_format: String,
    #[validate(length(min = 1))]
    pub content: String, // Base64 encoded content
    pub options: Option<serde_json::Value>,
    pub priority: Option<i32>,
}

#[derive(Debug, Serialize)]
pub struct CancelResponse {
    pub message: String,
    pub cancelled: bool,
}

/// Convert document endpoint
pub async fn convert_document(
    Extension(state): Extension<AppState>,
    Json(request): Json<ConvertDocumentRequest>,
) -> ServiceResult<ResponseJson<ApiResponse<ConversionResponse>>> {
    info!(
        input_format = %request.input_format,
        output_format = %request.output_format,
        "Document conversion request"
    );

    // Validate request
    request.validate().map_err(ServiceError::from)?;

    // Validate supported formats
    if !is_supported_input_format(&request.input_format) {
        return Err(ServiceError::BadRequest(format!("Unsupported input format: {}", request.input_format)));
    }

    if !is_supported_output_format(&request.output_format) {
        return Err(ServiceError::BadRequest(format!("Unsupported output format: {}", request.output_format)));
    }

    // Decode and validate content
    let content_bytes = base64::decode(&request.content)
        .map_err(|e| ServiceError::BadRequest(format!("Invalid base64 content: {}", e)))?;

    if content_bytes.is_empty() {
        return Err(ServiceError::BadRequest("Content cannot be empty".to_string()));
    }

    // Check content size limit (100MB default)
    const MAX_CONTENT_SIZE: usize = 100 * 1024 * 1024;
    if content_bytes.len() > MAX_CONTENT_SIZE {
        return Err(ServiceError::BadRequest("Content size exceeds maximum limit".to_string()));
    }

    // Create conversion job
    let job_id = Uuid::new_v4().to_string();
    let conversion_request = ConversionRequest {
        input_format: request.input_format.clone(),
        output_format: request.output_format.clone(),
        content: request.content,
        options: request.options,
    };

    // Queue the job
    let priority = request.priority.unwrap_or(0);
    state.conversion_service
        .queue_conversion_job(&job_id, conversion_request, priority)
        .await?;

    // Update metrics
    state.metrics.conversion_jobs_started_total.inc();

    let response = ConversionResponse {
        job_id: job_id.clone(),
        status: "queued".to_string(),
        estimated_completion: Some(chrono::Utc::now() + chrono::Duration::minutes(5)), // Rough estimate
    };

    info!(job_id = %job_id, "Conversion job queued successfully");

    Ok(ResponseJson(ApiResponse::success(response)))
}

/// Get conversion status endpoint
pub async fn get_conversion_status(
    Extension(state): Extension<AppState>,
    Path(job_id): Path<String>,
) -> ServiceResult<ResponseJson<ApiResponse<JobStatusResponse>>> {
    info!(job_id = %job_id, "Getting conversion status");

    let status = state.conversion_service
        .get_job_status(&job_id)
        .await?
        .ok_or_else(|| ServiceError::NotFound("Job not found".to_string()))?;

    Ok(ResponseJson(ApiResponse::success(status)))
}

/// Get conversion result endpoint
pub async fn get_conversion_result(
    Extension(state): Extension<AppState>,
    Path(job_id): Path<String>,
) -> ServiceResult<ResponseJson<ApiResponse<ConversionResult>>> {
    info!(job_id = %job_id, "Getting conversion result");

    let result = state.conversion_service
        .get_conversion_result(&job_id)
        .await?
        .ok_or_else(|| ServiceError::NotFound("Result not found or job not completed".to_string()))?;

    Ok(ResponseJson(ApiResponse::success(result)))
}

/// Cancel conversion endpoint
pub async fn cancel_conversion(
    Extension(state): Extension<AppState>,
    Path(job_id): Path<String>,
) -> ServiceResult<ResponseJson<ApiResponse<CancelResponse>>> {
    info!(job_id = %job_id, "Cancelling conversion");

    let cancelled = state.conversion_service
        .cancel_job(&job_id)
        .await?;

    let response = CancelResponse {
        message: if cancelled {
            "Job cancelled successfully".to_string()
        } else {
            "Job could not be cancelled (may already be completed or processing)".to_string()
        },
        cancelled,
    };

    Ok(ResponseJson(ApiResponse::success(response)))
}

// Helper functions for format validation
fn is_supported_input_format(format: &str) -> bool {
    matches!(format.to_lowercase().as_str(), 
        "rtf" | "doc" | "docx" | "md" | "markdown" | "txt" | "text" | "html" | "htm"
    )
}

fn is_supported_output_format(format: &str) -> bool {
    matches!(format.to_lowercase().as_str(), 
        "md" | "markdown" | "txt" | "text" | "html" | "htm" | "json"
    )
}
