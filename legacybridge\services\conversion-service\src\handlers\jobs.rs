// Job management handlers
use axum::{
    extract::{Extension, Path, Query},
    response::<PERSON><PERSON> as ResponseJ<PERSON>,
    J<PERSON>,
};
use legacybridge_shared::{
    types::{ApiResponse, PaginationParams, PaginatedResponse},
    ServiceError, ServiceResult,
};
use serde::{Deserialize, Serialize};
use tracing::{info, warn};

use crate::AppState;
use crate::service::QueueStats;

#[derive(Debug, Serialize)]
pub struct JobInfo {
    pub job_id: String,
    pub input_format: String,
    pub output_format: String,
    pub status: String,
    pub queued_at: chrono::DateTime<chrono::Utc>,
    pub attempts: u32,
    pub max_attempts: u32,
}

#[derive(Debug, Serialize)]
pub struct RetryResponse {
    pub message: String,
    pub retried: bool,
}

/// List jobs with pagination
pub async fn list_jobs(
    Extension(state): Extension<AppState>,
    Query(params): Query<PaginationParams>,
) -> ServiceResult<ResponseJson<ApiResponse<PaginatedResponse<JobInfo>>>> {
    info!("Listing conversion jobs");

    // For now, return empty list since we don't have a job listing implementation
    // In a full implementation, you'd query the database or Redis for job history
    let jobs = vec![];
    
    let page = params.page.unwrap_or(1);
    let limit = params.limit.unwrap_or(20);
    
    let response = PaginatedResponse {
        items: jobs,
        total: 0,
        page,
        limit,
        total_pages: 0,
    };

    Ok(ResponseJson(ApiResponse::success(response)))
}

/// Get job details
pub async fn get_job(
    Extension(state): Extension<AppState>,
    Path(job_id): Path<String>,
) -> ServiceResult<ResponseJson<ApiResponse<JobInfo>>> {
    info!(job_id = %job_id, "Getting job details");

    let job = state.job_queue
        .get_job(&job_id)
        .await?
        .ok_or_else(|| ServiceError::NotFound("Job not found".to_string()))?;

    let status = state.job_queue
        .get_job_status(&job_id)
        .await?
        .unwrap_or_else(|| "unknown".to_string());

    let job_info = JobInfo {
        job_id: job.job_id,
        input_format: job.request.input_format,
        output_format: job.request.output_format,
        status,
        queued_at: job.queued_at,
        attempts: job.attempts,
        max_attempts: job.max_attempts,
    };

    Ok(ResponseJson(ApiResponse::success(job_info)))
}

/// Retry a failed job
pub async fn retry_job(
    Extension(state): Extension<AppState>,
    Path(job_id): Path<String>,
) -> ServiceResult<ResponseJson<ApiResponse<RetryResponse>>> {
    info!(job_id = %job_id, "Retrying job");

    // Get the job
    let job = state.job_queue
        .get_job(&job_id)
        .await?
        .ok_or_else(|| ServiceError::NotFound("Job not found".to_string()))?;

    // Check if job can be retried
    let status = state.job_queue
        .get_job_status(&job_id)
        .await?
        .unwrap_or_else(|| "unknown".to_string());

    if status != "failed" {
        return Ok(ResponseJson(ApiResponse::success(RetryResponse {
            message: "Job can only be retried if it has failed".to_string(),
            retried: false,
        })));
    }

    // Requeue the job
    match state.job_queue.requeue_job(job).await {
        Ok(_) => {
            info!(job_id = %job_id, "Job requeued successfully");
            Ok(ResponseJson(ApiResponse::success(RetryResponse {
                message: "Job requeued successfully".to_string(),
                retried: true,
            })))
        }
        Err(e) => {
            warn!(job_id = %job_id, error = %e, "Failed to requeue job");
            Ok(ResponseJson(ApiResponse::success(RetryResponse {
                message: format!("Failed to requeue job: {}", e),
                retried: false,
            })))
        }
    }
}

/// Get queue statistics
pub async fn get_queue_stats(
    Extension(state): Extension<AppState>,
) -> ServiceResult<ResponseJson<ApiResponse<QueueStats>>> {
    info!("Getting queue statistics");

    let stats = state.conversion_service.get_queue_stats().await?;

    Ok(ResponseJson(ApiResponse::success(stats)))
}
