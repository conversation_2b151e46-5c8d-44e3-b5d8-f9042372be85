apiVersion: v1
kind: ServiceAccount
metadata:
  name: legacybridge-service-account
  namespace: legacybridge
  labels:
    app: legacybridge
  annotations:
    description: "Service account for LegacyBridge application pods"

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: legacybridge-role
  namespace: legacybridge
  labels:
    app: legacybridge
rules:
  # Allow reading configmaps
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "list", "watch"]
  
  # Allow reading secrets
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "list"]
  
  # Allow reading services for service discovery
  - apiGroups: [""]
    resources: ["services", "endpoints"]
    verbs: ["get", "list", "watch"]
  
  # Allow reading pods for self-awareness
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list"]
  
  # Allow creating events for auditing
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "patch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: legacybridge-rolebinding
  namespace: legacybridge
  labels:
    app: legacybridge
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: legacybridge-role
subjects:
  - kind: ServiceAccount
    name: legacybridge-service-account
    namespace: legacybridge