{"name": "legacybridge-mcp", "version": "1.0.0", "description": "LegacyBridge MCP Server - Enterprise Configuration Template", "server": {"command": "legacybridge-mcp", "args": ["server", "--config", "/etc/legacybridge/config.toml"], "env": {"RUST_LOG": "info", "MCP_ENVIRONMENT": "production"}}, "capabilities": {"resources": true, "tools": true, "prompts": true, "notifications": true}, "deployment": {"enterprise": {"server": {"protocol": "${MCP_PROTOCOL:-websocket}", "host": "${MCP_HOST:-0.0.0.0}", "port": "${MCP_PORT:-443}"}, "security": {"enable_authentication": true, "enable_tls": true, "tls_cert": "${MCP_TLS_CERT_PATH}", "tls_key": "${MCP_TLS_KEY_PATH}", "api_keys": "${MCP_API_KEYS_FILE}", "jwt_secret": "${MCP_JWT_SECRET}", "rate_limiting": {"enabled": true, "requests_per_minute": "${MCP_RATE_LIMIT:-1000}", "burst_size": "${MCP_BURST_SIZE:-100}"}}, "performance": {"thread_pool_size": "${MCP_THREAD_POOL_SIZE:-0}", "max_concurrent_requests": "${MCP_MAX_CONCURRENT:-500}", "cache_size": "${MCP_CACHE_SIZE_MB:-512}"}, "monitoring": {"enable_telemetry": true, "telemetry_endpoint": "${MCP_TELEMETRY_ENDPOINT}", "enable_health_check": true, "health_check_endpoint": "/health", "log_level": "${MCP_LOG_LEVEL:-warn}", "log_output": "${MCP_LOG_OUTPUT:-syslog}"}}}, "enterprise": {"license_key": "${LEGACYBRIDGE_LICENSE_KEY}", "organization": "${ORGANIZATION_NAME}", "support_email": "${SUPPORT_EMAIL}", "features": {"max_file_size": "${MAX_FILE_SIZE:-500MB}", "concurrent_conversions": "${MAX_CONCURRENT_CONVERSIONS:-50}", "batch_processing": true, "priority_queue": true, "audit_logging": true, "data_retention_days": "${DATA_RETENTION_DAYS:-90}"}}}