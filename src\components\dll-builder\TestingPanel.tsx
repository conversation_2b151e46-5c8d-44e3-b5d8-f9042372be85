'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  TestTube2, 
  CheckCircle2, 
  XCircle, 
  Clock, 
  Play, 
  FileText,
  Shield,
  Zap,
  Link,
  AlertCircle
} from 'lucide-react';
import { DLLConfiguration, TestResult, TestType } from '@/lib/dll/dll-config';

interface TestingPanelProps {
  configuration: DLLConfiguration;
  testResults: TestResult[];
  onRunTests: () => void;
  isRunning: boolean;
}

export function TestingPanel({ configuration, testResults, onRunTests, isRunning }: TestingPanelProps) {
  const [selectedTest, setSelectedTest] = useState<TestResult | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Set<TestType>>(new Set(['compatibility']));

  const groupedTests = testResults.reduce((acc, test) => {
    if (!acc[test.type]) {
      acc[test.type] = [];
    }
    acc[test.type].push(test);
    return acc;
  }, {} as Record<TestType, TestResult[]>);

  const getTestTypeIcon = (type: TestType) => {
    switch (type) {
      case 'compatibility':
        return <Link className="h-4 w-4" />;
      case 'performance':
        return <Zap className="h-4 w-4" />;
      case 'security':
        return <Shield className="h-4 w-4" />;
      case 'integration':
        return <FileText className="h-4 w-4" />;
      default:
        return <TestTube2 className="h-4 w-4" />;
    }
  };

  const getTestTypeLabel = (type: TestType) => {
    switch (type) {
      case 'compatibility':
        return 'Compatibility Tests';
      case 'performance':
        return 'Performance Tests';
      case 'security':
        return 'Security Tests';
      case 'integration':
        return 'Integration Tests';
      default:
        return 'Tests';
    }
  };

  const getTestStatusIcon = (passed: boolean) => {
    return passed ? (
      <CheckCircle2 className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getTestStatusBadge = (passed: boolean) => {
    return passed ? (
      <Badge variant="default">Passed</Badge>
    ) : (
      <Badge variant="destructive">Failed</Badge>
    );
  };

  const toggleCategory = (category: TestType) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  const totalTests = testResults.length;
  const passedTests = testResults.filter(t => t.passed).length;
  const failedTests = totalTests - passedTests;
  const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

  const averageExecutionTime = testResults.length > 0
    ? testResults.reduce((sum, test) => sum + test.executionTime, 0) / testResults.length
    : 0;

  return (
    <div className="h-full flex">
      <div className="flex-1 flex flex-col">
        {testResults.length === 0 ? (
          <Card className="flex-1 flex items-center justify-center">
            <CardContent className="text-center">
              <TestTube2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No Test Results</h3>
              <p className="text-muted-foreground mb-4">
                Run tests to verify your DLL works correctly with legacy systems
              </p>
              <Button onClick={onRunTests} disabled={isRunning}>
                {isRunning ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    Running Tests...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Run All Tests
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        ) : (
          <>
            <Card className="mb-4">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <TestTube2 className="h-5 w-5" />
                    Test Summary
                  </span>
                  <Button onClick={onRunTests} disabled={isRunning} size="sm">
                    {isRunning ? 'Running...' : 'Run Again'}
                  </Button>
                </CardTitle>
                <CardDescription>
                  Overall test results for {configuration.name}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold">{totalTests}</p>
                    <p className="text-sm text-muted-foreground">Total Tests</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-500">{passedTests}</p>
                    <p className="text-sm text-muted-foreground">Passed</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-red-500">{failedTests}</p>
                    <p className="text-sm text-muted-foreground">Failed</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">{averageExecutionTime.toFixed(0)}ms</p>
                    <p className="text-sm text-muted-foreground">Avg Time</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Success Rate</span>
                    <span className="font-medium">{successRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={successRate} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <div className="flex-1 flex gap-4">
              <Card className="flex-1">
                <CardHeader>
                  <CardTitle>Test Results</CardTitle>
                  <CardDescription>
                    Click on a test to view details
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[400px]">
                    <div className="space-y-4">
                      {Object.entries(groupedTests).map(([type, tests]) => (
                        <div key={type} className="space-y-2">
                          <button
                            className="flex items-center gap-2 font-medium hover:text-primary transition-colors w-full text-left"
                            onClick={() => toggleCategory(type as TestType)}
                          >
                            {getTestTypeIcon(type as TestType)}
                            <span>{getTestTypeLabel(type as TestType)}</span>
                            <Badge variant="outline" className="ml-auto">
                              {tests.filter(t => t.passed).length}/{tests.length}
                            </Badge>
                          </button>
                          {expandedCategories.has(type as TestType) && (
                            <div className="ml-6 space-y-1">
                              {tests.map((test, index) => (
                                <div
                                  key={index}
                                  className={`flex items-center justify-between p-2 rounded cursor-pointer hover:bg-muted/50 ${
                                    selectedTest === test ? 'bg-muted' : ''
                                  }`}
                                  onClick={() => setSelectedTest(test)}
                                >
                                  <div className="flex items-center gap-2">
                                    {getTestStatusIcon(test.passed)}
                                    <span className="text-sm">{test.name}</span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <span className="text-xs text-muted-foreground">
                                      {test.executionTime}ms
                                    </span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>

              {selectedTest && (
                <Card className="flex-1">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>{selectedTest.name}</span>
                      {getTestStatusBadge(selectedTest.passed)}
                    </CardTitle>
                    <CardDescription>
                      {selectedTest.type} test • {selectedTest.executionTime}ms
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Tabs defaultValue="details" className="w-full">
                      <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="details">Details</TabsTrigger>
                        <TabsTrigger value="output">Output</TabsTrigger>
                        <TabsTrigger value="logs">Logs</TabsTrigger>
                      </TabsList>

                      <TabsContent value="details" className="space-y-4">
                        <div className="space-y-2">
                          <p className="text-sm font-medium">Description</p>
                          <p className="text-sm text-muted-foreground">
                            {selectedTest.description}
                          </p>
                        </div>

                        {!selectedTest.passed && selectedTest.error && (
                          <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>
                              {selectedTest.error}
                            </AlertDescription>
                          </Alert>
                        )}

                        {selectedTest.details && (
                          <div className="space-y-2">
                            <p className="text-sm font-medium">Test Details</p>
                            <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                              {JSON.stringify(selectedTest.details, null, 2)}
                            </pre>
                          </div>
                        )}
                      </TabsContent>

                      <TabsContent value="output">
                        <ScrollArea className="h-[300px]">
                          <pre className="text-xs font-mono">
                            {selectedTest.output || 'No output generated'}
                          </pre>
                        </ScrollArea>
                      </TabsContent>

                      <TabsContent value="logs">
                        <ScrollArea className="h-[300px]">
                          {selectedTest.logs && selectedTest.logs.length > 0 ? (
                            <div className="space-y-1">
                              {selectedTest.logs.map((log, index) => (
                                <div key={index} className="text-xs font-mono">
                                  <span className="text-muted-foreground">
                                    [{new Date(log.timestamp).toLocaleTimeString()}]
                                  </span>{' '}
                                  <span className={
                                    log.level === 'error' ? 'text-red-500' :
                                    log.level === 'warning' ? 'text-yellow-500' :
                                    ''
                                  }>
                                    [{log.level.toUpperCase()}]
                                  </span>{' '}
                                  {log.message}
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-xs text-muted-foreground">No logs available</p>
                          )}
                        </ScrollArea>
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}