// MCP Server Type Definitions for LegacyBridge
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use uuid::Uuid;

/// MCP Server Configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpServerConfig {
    pub enable_websocket: bool,
    pub max_file_size: usize,
    pub max_batch_size: usize,
    pub enable_dll_building: bool,
    pub cache_conversions: bool,
    pub log_level: String,
}

impl Default for McpServerConfig {
    fn default() -> Self {
        Self {
            enable_websocket: true,
            max_file_size: 50 * 1024 * 1024, // 50MB
            max_batch_size: 100,
            enable_dll_building: true,
            cache_conversions: true,
            log_level: "info".to_string(),
        }
    }
}

/// Conversion job for batch processing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversionJob {
    pub id: String,
    pub job_type: String,
    pub status: JobStatus,
    pub total_files: usize,
    pub processed_files: usize,
    pub successful_conversions: usize,
    pub failed_conversions: usize,
    pub start_time: DateTime<Utc>,
    pub end_time: Option<DateTime<Utc>>,
    pub results: Vec<ConversionResult>,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum JobStatus {
    Queued,
    Processing,
    Completed,
    Failed,
    Cancelled,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversionResult {
    pub filename: String,
    pub input_format: String,
    pub output_format: String,
    pub success: bool,
    pub error: Option<String>,
    pub output_content: Option<String>,
    pub input_size: usize,
    pub output_size: usize,
    pub conversion_time_ms: u64,
    pub quality_score: Option<u8>,
}

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct ServerStats {
    pub total_conversions: u64,
    pub total_input_bytes: usize,
    pub total_output_bytes: usize,
    pub active_jobs: usize,
    pub uptime_seconds: u64,
    pub last_updated: DateTime<Utc>,
}

/// Format detection result with confidence
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormatDetectionResult {
    pub format: FormatDefinition,
    pub confidence: f64,
    pub version: Option<String>,
    pub metadata: HashMap<String, String>,
}

/// Format definition for MCP exposure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormatDefinition {
    pub id: String,
    pub name: String,
    pub description: String,
    pub extensions: Vec<String>,
    pub mime_types: Vec<String>,
    pub can_convert_to: Vec<String>,
    pub conversion_quality: HashMap<String, String>,
}

/// DLL Configuration for MCP
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DllConfig {
    pub architectures: Vec<DllArchitecture>,
    pub included_formats: Vec<String>,
    pub optimization_level: DllOptimization,
    pub generate_integration_code: GenerateIntegration,
}

impl Default for DllConfig {
    fn default() -> Self {
        Self {
            architectures: vec![DllArchitecture::X86],
            included_formats: vec!["rtf".to_string(), "doc".to_string()],
            optimization_level: DllOptimization::Release,
            generate_integration_code: GenerateIntegration {
                vb6: true,
                vfp9: true,
            },
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DllArchitecture {
    X86,
    X64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DllOptimization {
    Debug,
    Release,
    Size,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GenerateIntegration {
    pub vb6: bool,
    pub vfp9: bool,
}

/// DLL Build Result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DllBuildResult {
    pub success: bool,
    pub build_id: String,
    pub output_files: Vec<String>,
    pub architectures_built: Vec<String>,
    pub warnings: Vec<String>,
    pub errors: Vec<String>,
    pub build_duration: std::time::Duration,
}

/// Integration error type
#[derive(Debug, thiserror::Error)]
pub enum IntegrationError {
    #[error("MCP client error: {0}")]
    ClientError(String),
    
    #[error("Conversion error: {0}")]
    ConversionError(String),
    
    #[error("Format detection error: {0}")]
    DetectionError(String),
    
    #[error("DLL build error: {0}")]
    BuildError(String),
    
    #[error("Job management error: {0}")]
    JobError(String),
    
    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),
}