# Grafana Dashboards Configuration for LegacyBridge
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: monitoring
data:
  dashboards.yaml: |
    apiVersion: 1
    providers:
    - name: 'default'
      orgId: 1
      folder: ''
      type: file
      disableDeletion: false
      updateIntervalSeconds: 10
      allowUiUpdates: true
      options:
        path: /var/lib/grafana/dashboards

  legacybridge-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "LegacyBridge Overview",
        "tags": ["legacybridge", "overview"],
        "style": "dark",
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Service Health Overview",
            "type": "stat",
            "targets": [
              {
                "expr": "up{job=~\"auth-service|conversion-service|file-service|job-service\"}",
                "legendFormat": "{{job}}"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "green", "value": 1}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total[5m])) by (service)",
                "legendFormat": "{{service}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Error Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total{status=~\"4..|5..\"}[5m])) by (service) / sum(rate(http_requests_total[5m])) by (service)",
                "legendFormat": "{{service}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 4,
            "title": "Response Time",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le, service))",
                "legendFormat": "95th percentile - {{service}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "5s"
      }
    }

  circuit-breakers.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Circuit Breakers",
        "tags": ["legacybridge", "circuit-breakers"],
        "style": "dark",
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Circuit Breaker States",
            "type": "stat",
            "targets": [
              {
                "expr": "circuit_breaker_state",
                "legendFormat": "{{service}} - {{state}}"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": 0},
                    {"color": "yellow", "value": 1},
                    {"color": "red", "value": 2}
                  ]
                },
                "mappings": [
                  {"options": {"0": {"text": "Closed"}}, "type": "value"},
                  {"options": {"1": {"text": "Half-Open"}}, "type": "value"},
                  {"options": {"2": {"text": "Open"}}, "type": "value"}
                ]
              }
            },
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Circuit Breaker Failures",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(circuit_breaker_failures_total[5m])",
                "legendFormat": "{{service}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 3,
            "title": "Fallback Executions",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(fallback_executions_total[5m])",
                "legendFormat": "{{service}} - {{fallback_type}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "5s"
      }
    }

  auto-scaling.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Auto-Scaling Metrics",
        "tags": ["legacybridge", "auto-scaling"],
        "style": "dark",
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Pod Replicas",
            "type": "graph",
            "targets": [
              {
                "expr": "kube_deployment_status_replicas",
                "legendFormat": "{{deployment}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "HPA Current vs Desired Replicas",
            "type": "graph",
            "targets": [
              {
                "expr": "kube_hpa_status_current_replicas",
                "legendFormat": "Current - {{hpa}}"
              },
              {
                "expr": "kube_hpa_status_desired_replicas",
                "legendFormat": "Desired - {{hpa}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "CPU Utilization",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(container_cpu_usage_seconds_total{pod=~\".*-service-.*\"}[5m]) * 100",
                "legendFormat": "{{pod}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 4,
            "title": "Memory Utilization",
            "type": "graph",
            "targets": [
              {
                "expr": "container_memory_usage_bytes{pod=~\".*-service-.*\"} / container_spec_memory_limit_bytes * 100",
                "legendFormat": "{{pod}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          },
          {
            "id": 5,
            "title": "Custom Metrics for Scaling",
            "type": "graph",
            "targets": [
              {
                "expr": "auth_requests_per_second",
                "legendFormat": "Auth Requests/sec"
              },
              {
                "expr": "conversion_queue_length",
                "legendFormat": "Conversion Queue"
              },
              {
                "expr": "file_upload_requests_per_second",
                "legendFormat": "File Uploads/sec"
              },
              {
                "expr": "job_queue_length",
                "legendFormat": "Job Queue"
              }
            ],
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "5s"
      }
    }

  service-performance.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Service Performance",
        "tags": ["legacybridge", "performance"],
        "style": "dark",
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Auth Service Metrics",
            "type": "row",
            "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "JWT Validation Queue",
            "type": "graph",
            "targets": [
              {
                "expr": "jwt_validation_queue_length",
                "legendFormat": "Queue Length"
              }
            ],
            "gridPos": {"h": 6, "w": 12, "x": 0, "y": 1}
          },
          {
            "id": 3,
            "title": "Auth Success Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "auth_success_rate",
                "legendFormat": "Success Rate"
              }
            ],
            "gridPos": {"h": 6, "w": 12, "x": 12, "y": 1}
          },
          {
            "id": 4,
            "title": "Conversion Service Metrics",
            "type": "row",
            "gridPos": {"h": 1, "w": 24, "x": 0, "y": 7}
          },
          {
            "id": 5,
            "title": "Conversion Duration",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, rate(conversion_duration_seconds_bucket[5m]))",
                "legendFormat": "95th percentile"
              },
              {
                "expr": "histogram_quantile(0.50, rate(conversion_duration_seconds_bucket[5m]))",
                "legendFormat": "50th percentile"
              }
            ],
            "gridPos": {"h": 6, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 6,
            "title": "Active Conversions",
            "type": "graph",
            "targets": [
              {
                "expr": "active_conversions",
                "legendFormat": "Active Conversions"
              }
            ],
            "gridPos": {"h": 6, "w": 12, "x": 12, "y": 8}
          },
          {
            "id": 7,
            "title": "File Service Metrics",
            "type": "row",
            "gridPos": {"h": 1, "w": 24, "x": 0, "y": 14}
          },
          {
            "id": 8,
            "title": "File Operations",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(files_uploaded_total[5m])",
                "legendFormat": "Uploads/sec"
              },
              {
                "expr": "rate(files_downloaded_total[5m])",
                "legendFormat": "Downloads/sec"
              }
            ],
            "gridPos": {"h": 6, "w": 12, "x": 0, "y": 15}
          },
          {
            "id": 9,
            "title": "S3 Operation Success Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "s3_operation_success_rate",
                "legendFormat": "Success Rate"
              }
            ],
            "gridPos": {"h": 6, "w": 12, "x": 12, "y": 15}
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "5s"
      }
    }
