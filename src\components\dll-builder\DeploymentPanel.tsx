'use client';

import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Package,
  Download,
  FileText,
  Code,
  Shield,
  Zap,
  CheckCircle,
  ExternalLink,
  Copy,
  Archive,
  Folder
} from 'lucide-react';

import { 
  BuildStatus, 
  TestResult, 
  DeploymentPackage, 
  PackageFormat 
} from '@/lib/dll/dll-config';
import { usePackaging } from '@/lib/dll/packaging';
import { useToast } from '@/hooks/use-toast';

interface DeploymentPanelProps {
  buildStatus: BuildStatus | null;
  testResults: TestResult[];
  deploymentPackage: DeploymentPackage | null;
  onCreatePackage: (pkg: DeploymentPackage) => void;
}

export function DeploymentPanel({
  buildStatus,
  testResults,
  deploymentPackage,
  onCreatePackage
}: DeploymentPanelProps) {
  const [packageConfig, setPackageConfig] = useState({
    format: 'zip' as PackageFormat,
    includeDocs: true,
    includeExamples: true,
    includeSource: false,
    createInstaller: false,
    packageName: 'LegacyBridge-DLL',
    version: '2.0.0',
    description: 'Document conversion library for legacy systems',
    author: 'LegacyBridge Team',
    license: 'MIT'
  });
  
  const [isPackaging, setIsPackaging] = useState(false);
  const [packagingProgress, setPackagingProgress] = useState(0);
  
  const { createPackage, validatePackaging } = usePackaging();
  const { toast } = useToast();
  
  const handleCreatePackage = useCallback(async () => {
    if (!buildStatus?.success) {
      toast({
        title: "Build Required",
        description: "Please build the DLL successfully before creating a package",
        variant: "destructive"
      });
      return;
    }
    
    const validation = await validatePackaging(buildStatus, packageConfig);
    if (!validation.isValid) {
      toast({
        title: "Package Configuration Error",
        description: validation.errors.join(', '),
        variant: "destructive"
      });
      return;
    }
    
    try {
      setIsPackaging(true);
      setPackagingProgress(0);
      
      const pkg = await createPackage(
        buildStatus,
        testResults,
        packageConfig,
        (progress) => setPackagingProgress(progress)
      );
      
      onCreatePackage(pkg);
      
      toast({
        title: "Package Created Successfully! 📦",
        description: `${packageConfig.format.toUpperCase()} package ready for distribution`,
        variant: "success"
      });
    } catch (error) {
      toast({
        title: "Packaging Failed",
        description: error instanceof Error ? error.message : "Unknown error",
        variant: "destructive"
      });
    } finally {
      setIsPackaging(false);
    }
  }, [buildStatus, testResults, packageConfig, createPackage, validatePackaging, onCreatePackage, toast]);
  
  const copyToClipboard = useCallback((text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to Clipboard",
      description: "Content has been copied to your clipboard",
      variant: "success"
    });
  }, [toast]);
  
  if (!buildStatus?.success) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Package className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Build Required for Deployment</h3>
            <p className="text-muted-foreground mb-4">
              Complete the build process before creating deployment packages
            </p>
            <Button variant="outline" disabled>
              Deployment Unavailable
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  const allTestsPassed = testResults.length === 0 || testResults.every(t => t.passed);
  
  return (
    <div className="space-y-6">
      {/* Deployment Status */}
      <Card className={allTestsPassed ? "border-green-200 bg-green-50 dark:bg-green-950/20" : "border-yellow-200 bg-yellow-50 dark:bg-yellow-950/20"}>
        <CardContent className="pt-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {allTestsPassed ? (
                <CheckCircle className="w-6 h-6 text-green-600" />
              ) : (
                <Shield className="w-6 h-6 text-yellow-600" />
              )}
              <div>
                <h3 className="font-semibold">
                  {allTestsPassed ? "Ready for Deployment" : "Testing Recommended"}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {allTestsPassed 
                    ? "All tests passed - safe to deploy to production"
                    : "Some tests haven't been run or failed - consider testing before deployment"
                  }
                </p>
              </div>
            </div>
            <Badge variant={allTestsPassed ? "success" : "warning"}>
              {allTestsPassed ? "Production Ready" : "Needs Testing"}
            </Badge>
          </div>
        </CardContent>
      </Card>
      
      {/* Package Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5 text-blue-500" />
            Package Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Package Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="package-name">Package Name</Label>
              <Input
                id="package-name"
                value={packageConfig.packageName}
                onChange={(e) => setPackageConfig(prev => ({ ...prev, packageName: e.target.value }))}
                placeholder="LegacyBridge-DLL"
              />
            </div>
            
            <div>
              <Label htmlFor="version">Version</Label>
              <Input
                id="version"
                value={packageConfig.version}
                onChange={(e) => setPackageConfig(prev => ({ ...prev, version: e.target.value }))}
                placeholder="2.0.0"
              />
            </div>
            
            <div>
              <Label htmlFor="format">Package Format</Label>
              <Select
                value={packageConfig.format}
                onValueChange={(value: PackageFormat) => setPackageConfig(prev => ({ ...prev, format: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="zip">ZIP Archive</SelectItem>
                  <SelectItem value="tar">TAR Archive</SelectItem>
                  <SelectItem value="msi">MSI Installer (Windows)</SelectItem>
                  <SelectItem value="nsis">NSIS Installer</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="author">Author</Label>
              <Input
                id="author"
                value={packageConfig.author}
                onChange={(e) => setPackageConfig(prev => ({ ...prev, author: e.target.value }))}
                placeholder="LegacyBridge Team"
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={packageConfig.description}
              onChange={(e) => setPackageConfig(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Document conversion library for legacy systems"
              rows={3}
            />
          </div>
          
          {/* Package Contents */}
          <div className="space-y-4">
            <h4 className="font-medium">Package Contents</h4>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Include Documentation</Label>
                  <p className="text-sm text-muted-foreground">
                    API documentation and user guides
                  </p>
                </div>
                <Switch
                  checked={packageConfig.includeDocs}
                  onCheckedChange={(checked) => setPackageConfig(prev => ({ ...prev, includeDocs: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label>Include Examples</Label>
                  <p className="text-sm text-muted-foreground">
                    Sample code and integration examples
                  </p>
                </div>
                <Switch
                  checked={packageConfig.includeExamples}
                  onCheckedChange={(checked) => setPackageConfig(prev => ({ ...prev, includeExamples: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label>Include Source Code</Label>
                  <p className="text-sm text-muted-foreground">
                    Rust source code for the DLL
                  </p>
                </div>
                <Switch
                  checked={packageConfig.includeSource}
                  onCheckedChange={(checked) => setPackageConfig(prev => ({ ...prev, includeSource: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label>Create Installer</Label>
                  <p className="text-sm text-muted-foreground">
                    Generate installer package for easy deployment
                  </p>
                </div>
                <Switch
                  checked={packageConfig.createInstaller}
                  onCheckedChange={(checked) => setPackageConfig(prev => ({ ...prev, createInstaller: checked }))}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Package Creation */}
      <Card>
        <CardHeader>
          <CardTitle>Create Deployment Package</CardTitle>
        </CardHeader>
        <CardContent>
          {isPackaging ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Creating Package...</span>
                <span className="text-sm text-muted-foreground">{packagingProgress}%</span>
              </div>
              <Progress value={packagingProgress} />
              <p className="text-sm text-muted-foreground">
                Collecting files, generating documentation, and creating archive...
              </p>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Ready to Package</h4>
                <p className="text-sm text-muted-foreground">
                  Create a deployment package with all necessary files
                </p>
              </div>
              <Button onClick={handleCreatePackage} size="lg">
                <Package className="w-4 h-4 mr-2" />
                Create Package
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Generated Package */}
      {deploymentPackage && (
        <Card className="border-green-200 bg-green-50 dark:bg-green-950/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-800 dark:text-green-200">
              <CheckCircle className="w-5 h-5" />
              Package Created Successfully
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <PackageInfoCard
                title="Package File"
                value={deploymentPackage.fileName}
                action={() => copyToClipboard(deploymentPackage.filePath)}
                actionIcon={Copy}
                actionLabel="Copy Path"
              />
              
              <PackageInfoCard
                title="File Size"
                value={formatFileSize(deploymentPackage.fileSize)}
                action={() => window.open(`file://${deploymentPackage.filePath}`)}
                actionIcon={Folder}
                actionLabel="Open Folder"
              />
              
              <PackageInfoCard
                title="Files Included"
                value={`${deploymentPackage.includedFiles.length} files`}
                action={() => copyToClipboard(deploymentPackage.includedFiles.join('\n'))}
                actionIcon={FileText}
                actionLabel="Copy List"
              />
              
              <PackageInfoCard
                title="Package Type"
                value={deploymentPackage.format.toUpperCase()}
                action={() => window.open(deploymentPackage.downloadUrl)}
                actionIcon={Download}
                actionLabel="Download"
              />
            </div>
            
            {/* Installation Instructions */}
            <div className="mt-6">
              <h4 className="font-medium mb-3">Installation Instructions</h4>
              <div className="bg-muted rounded-lg p-4">
                <InstallationInstructions 
                  packageType={deploymentPackage.format}
                  fileName={deploymentPackage.fileName}
                />
              </div>
            </div>
            
            {/* Package Contents */}
            <div className="mt-6">
              <h4 className="font-medium mb-3">Package Contents</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h5 className="text-sm font-medium text-muted-foreground mb-2">DLL Files</h5>
                  <div className="space-y-1">
                    {deploymentPackage.includedFiles
                      .filter(f => f.endsWith('.dll'))
                      .map((file, index) => (
                        <div key={index} className="text-sm flex items-center gap-2">
                          <Archive className="w-3 h-3" />
                          {file}
                        </div>
                      ))}
                  </div>
                </div>
                
                <div>
                  <h5 className="text-sm font-medium text-muted-foreground mb-2">Integration Code</h5>
                  <div className="space-y-1">
                    {deploymentPackage.includedFiles
                      .filter(f => f.includes('integration') || f.endsWith('.bas') || f.endsWith('.prg'))
                      .map((file, index) => (
                        <div key={index} className="text-sm flex items-center gap-2">
                          <Code className="w-3 h-3" />
                          {file}
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Supporting Components
interface PackageInfoCardProps {
  title: string;
  value: string;
  action: () => void;
  actionIcon: React.ComponentType<{ className?: string }>;
  actionLabel: string;
}

function PackageInfoCard({ 
  title, 
  value, 
  action, 
  actionIcon: Icon, 
  actionLabel 
}: PackageInfoCardProps) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-muted-foreground">{title}</p>
          <p className="font-medium">{value}</p>
        </div>
        <Button size="sm" variant="ghost" onClick={action}>
          <Icon className="w-4 h-4 mr-1" />
          {actionLabel}
        </Button>
      </div>
    </div>
  );
}

function InstallationInstructions({ 
  packageType, 
  fileName 
}: { 
  packageType: PackageFormat; 
  fileName: string; 
}) {
  const instructions = {
    zip: `1. Extract ${fileName} to your desired location
2. Copy the DLL files to your project directory
3. Import the integration code files (VB6: .bas, VFP9: .prg)
4. Add DLL references to your project
5. Follow the examples in the /examples directory`,
    
    tar: `1. Extract the archive: tar -xzf ${fileName}
2. Copy DLL files to your system library path
3. Import integration code into your project
4. Update your application references`,
    
    msi: `1. Double-click ${fileName} to start installation
2. Follow the installation wizard
3. DLL will be automatically registered
4. Integration examples installed to Documents/LegacyBridge`,
    
    nsis: `1. Run ${fileName} as Administrator
2. Choose installation directory
3. Select components to install
4. DLL will be registered automatically
5. Desktop shortcuts created for documentation`
  };
  
  return (
    <pre className="text-sm whitespace-pre-wrap font-mono">
      {instructions[packageType] || 'Installation instructions not available'}
    </pre>
  );
}

function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`;
}