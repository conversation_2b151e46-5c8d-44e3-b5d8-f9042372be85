// DLL Build Engine
// Handles the orchestration of DLL building process

import { useState, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { 
  DLLConfiguration, 
  BuildStatus, 
  BuildOptions, 
  ValidationResult,
  BuildProgress,
  BuildArtifact,
  BuildStage,
  IntegrationCode
} from './dll-config';

export interface BuildResult {
  success: boolean;
  artifacts?: BuildArtifact[];
  error?: string;
  logs: string[];
  duration: number;
}

export interface BuildEngine {
  buildDLL: (
    config: DLLConfiguration, 
    onProgress: (status: BuildStatus) => void,
    options?: BuildOptions
  ) => Promise<BuildResult>;
  validateConfig: (config: DLLConfiguration) => Promise<ValidationResult>;
  generateCode: (language: string, dllPath: string) => Promise<IntegrationCode>;
  cancelBuild: (buildId: string) => Promise<void>;
  getBuildLogs: (buildId: string) => Promise<string[]>;
}

export function useDLLBuilder(): BuildEngine {
  const [activeBuildId, setActiveBuildId] = useState<string | null>(null);

  const validateConfig = useCallback(async (config: DLLConfiguration): Promise<ValidationResult> => {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Validate architectures
    if (config.architectures.length === 0) {
      errors.push('At least one architecture must be selected');
    }

    // Validate output directory
    if (!config.outputDirectory || config.outputDirectory.trim() === '') {
      errors.push('Output directory must be specified');
    }

    // Validate included formats
    if (config.includedFormats.length === 0) {
      warnings.push('No formats selected - DLL will have limited functionality');
    }

    // Validate build metadata
    if (!config.buildMetadata.version || !config.buildMetadata.version.match(/^\d+\.\d+\.\d+$/)) {
      errors.push('Version must be in format X.Y.Z');
    }

    // Suggestions
    if (config.architectures.includes('x86') && !config.staticLinking) {
      suggestions.push('Consider enabling static linking for better x86 compatibility');
    }

    if (config.optimization === 'debug' && !config.includeDebugSymbols) {
      suggestions.push('Enable debug symbols when using debug optimization');
    }

    if (config.includedFormats.length > 10) {
      suggestions.push('Including many formats may increase DLL size significantly');
    }

    // Call Tauri backend for additional validation
    try {
      const backendValidation = await invoke<ValidationResult>('validate_dll_config', { config });
      errors.push(...backendValidation.errors);
      warnings.push(...backendValidation.warnings);
      suggestions.push(...backendValidation.suggestions);
    } catch (error) {
      console.error('Backend validation failed:', error);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }, []);

  const buildDLL = useCallback(async (
    config: DLLConfiguration,
    onProgress: (status: BuildStatus) => void,
    options: BuildOptions = {}
  ): Promise<BuildResult> => {
    const buildId = `build-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    setActiveBuildId(buildId);

    const startTime = new Date();
    const logs: string[] = [];

    const updateStatus = (stage: BuildStage, step: string, progress: number, error?: string) => {
      const status: BuildStatus = {
        stage,
        currentStep: step,
        progress,
        error,
        outputFiles: [],
        logs: [...logs],
        startTime,
        buildId,
        success: stage === 'completed' && !error
      };

      if (stage === 'completed' || stage === 'failed') {
        status.endTime = new Date();
      }

      onProgress(status);
    };

    try {
      // Phase 1: Preparation
      updateStatus('preparing', 'Validating configuration', 10);
      const validation = await validateConfig(config);
      if (!validation.isValid) {
        throw new Error(`Configuration errors: ${validation.errors.join(', ')}`);
      }

      logs.push(`Build started at ${startTime.toISOString()}`);
      logs.push(`Target architectures: ${config.architectures.join(', ')}`);
      logs.push(`Optimization level: ${config.optimization}`);
      logs.push(`Included formats: ${config.includedFormats.join(', ')}`);

      // Phase 2: Compilation
      updateStatus('compiling', 'Compiling Rust sources', 30);
      
      const compileResult = await invoke<{
        success: boolean;
        logs: string[];
        intermediateFiles: string[];
      }>('compile_dll_sources', {
        config,
        buildId,
        options
      });

      logs.push(...compileResult.logs);
      if (!compileResult.success) {
        throw new Error('Compilation failed');
      }

      // Phase 3: Linking
      updateStatus('linking', 'Linking libraries', 60);
      
      const linkResult = await invoke<{
        success: boolean;
        logs: string[];
        outputFiles: string[];
      }>('link_dll_libraries', {
        config,
        buildId,
        intermediateFiles: compileResult.intermediateFiles,
        options
      });

      logs.push(...linkResult.logs);
      if (!linkResult.success) {
        throw new Error('Linking failed');
      }

      // Phase 4: Packaging
      updateStatus('packaging', 'Creating final package', 80);
      
      const packageResult = await invoke<{
        success: boolean;
        logs: string[];
        artifacts: BuildArtifact[];
      }>('package_dll_artifacts', {
        config,
        buildId,
        dllFiles: linkResult.outputFiles,
        options
      });

      logs.push(...packageResult.logs);
      if (!packageResult.success) {
        throw new Error('Packaging failed');
      }

      // Phase 5: Completion
      updateStatus('completed', 'Build completed successfully', 100);
      logs.push(`Build completed at ${new Date().toISOString()}`);
      logs.push(`Total artifacts created: ${packageResult.artifacts.length}`);

      const duration = Date.now() - startTime.getTime();

      return {
        success: true,
        artifacts: packageResult.artifacts,
        logs,
        duration
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown build error';
      updateStatus('failed', errorMessage, 0, errorMessage);
      logs.push(`Build failed: ${errorMessage}`);

      return {
        success: false,
        error: errorMessage,
        logs,
        duration: Date.now() - startTime.getTime()
      };
    } finally {
      setActiveBuildId(null);
    }
  }, [validateConfig]);

  const generateCode = useCallback(async (
    language: string, 
    dllPath: string
  ): Promise<IntegrationCode> => {
    try {
      const result = await invoke<IntegrationCode>('generate_integration_code', {
        language,
        dllPath
      });

      return result;
    } catch (error) {
      throw new Error(`Failed to generate ${language} code: ${error}`);
    }
  }, []);

  const cancelBuild = useCallback(async (buildId: string): Promise<void> => {
    if (buildId !== activeBuildId) {
      throw new Error('Build ID does not match active build');
    }

    try {
      await invoke('cancel_dll_build', { buildId });
      setActiveBuildId(null);
    } catch (error) {
      throw new Error(`Failed to cancel build: ${error}`);
    }
  }, [activeBuildId]);

  const getBuildLogs = useCallback(async (buildId: string): Promise<string[]> => {
    try {
      const logs = await invoke<string[]>('get_build_logs', { buildId });
      return logs;
    } catch (error) {
      throw new Error(`Failed to retrieve build logs: ${error}`);
    }
  }, []);

  return {
    buildDLL,
    validateConfig,
    generateCode,
    cancelBuild,
    getBuildLogs
  };
}

// Mock implementation for development
export function useMockDLLBuilder(): BuildEngine {
  const validateConfig = useCallback(async (config: DLLConfiguration): Promise<ValidationResult> => {
    // Simulate validation delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    if (config.architectures.length === 0) {
      errors.push('At least one architecture must be selected');
    }

    if (!config.outputDirectory) {
      errors.push('Output directory must be specified');
    }

    if (config.includedFormats.length === 0) {
      warnings.push('No formats selected');
    }

    return { isValid: errors.length === 0, errors, warnings, suggestions };
  }, []);

  const buildDLL = useCallback(async (
    config: DLLConfiguration,
    onProgress: (status: BuildStatus) => void,
    options?: BuildOptions
  ): Promise<BuildResult> => {
    const buildId = `mock-build-${Date.now()}`;
    const startTime = new Date();
    const logs: string[] = [];

    // Simulate build phases
    const phases: { stage: BuildStage; step: string; progress: number; delay: number }[] = [
      { stage: 'preparing', step: 'Validating configuration', progress: 10, delay: 500 },
      { stage: 'preparing', step: 'Setting up build environment', progress: 20, delay: 1000 },
      { stage: 'compiling', step: 'Compiling Rust sources', progress: 40, delay: 2000 },
      { stage: 'compiling', step: 'Optimizing code', progress: 50, delay: 1500 },
      { stage: 'linking', step: 'Linking libraries', progress: 70, delay: 1000 },
      { stage: 'packaging', step: 'Creating DLL package', progress: 90, delay: 1000 },
      { stage: 'completed', step: 'Build completed', progress: 100, delay: 500 }
    ];

    for (const phase of phases) {
      await new Promise(resolve => setTimeout(resolve, phase.delay));
      
      logs.push(`[${new Date().toISOString()}] ${phase.step}`);
      
      onProgress({
        stage: phase.stage,
        currentStep: phase.step,
        progress: phase.progress,
        outputFiles: phase.stage === 'completed' ? ['legacybridge_x86.dll', 'legacybridge_x64.dll'] : [],
        logs: [...logs],
        startTime,
        buildId,
        success: phase.stage === 'completed'
      });
    }

    return {
      success: true,
      artifacts: [
        {
          type: 'dll',
          path: 'dist/dll/legacybridge_x86.dll',
          size: 2048000,
          checksum: 'abc123',
          architecture: 'x86'
        },
        {
          type: 'dll',
          path: 'dist/dll/legacybridge_x64.dll',
          size: 2560000,
          checksum: 'def456',
          architecture: 'x64'
        }
      ],
      logs,
      duration: Date.now() - startTime.getTime()
    };
  }, []);

  const generateCode = useCallback(async (
    language: string,
    dllPath: string
  ): Promise<IntegrationCode> => {
    await new Promise(resolve => setTimeout(resolve, 500));

    const templates: Record<string, IntegrationCode> = {
      vb6: {
        language: 'vb6',
        code: `' LegacyBridge VB6 Integration Module
Private Declare Function ConvertDocument Lib "legacybridge.dll" _
    (ByVal inputPath As String, ByVal outputPath As String, _
     ByVal inputFormat As String, ByVal outputFormat As String) As Long

Public Function ConvertToMarkdown(inputFile As String, outputFile As String) As Boolean
    Dim result As Long
    result = ConvertDocument(inputFile, outputFile, "rtf", "markdown")
    ConvertToMarkdown = (result = 0)
End Function`,
        fileName: 'LegacyBridge.bas',
        documentation: 'VB6 integration module for LegacyBridge DLL',
        examples: ['ConvertToMarkdown("document.rtf", "output.md")']
      },
      vfp9: {
        language: 'vfp9',
        code: `* LegacyBridge VFP9 Integration
DECLARE INTEGER ConvertDocument IN legacybridge.dll ;
    STRING inputPath, STRING outputPath, ;
    STRING inputFormat, STRING outputFormat

FUNCTION ConvertToMarkdown(tcInputFile, tcOutputFile)
    LOCAL lnResult
    lnResult = ConvertDocument(tcInputFile, tcOutputFile, "rtf", "markdown")
    RETURN (lnResult = 0)
ENDFUNC`,
        fileName: 'legacybridge.prg',
        documentation: 'VFP9 integration class for LegacyBridge DLL',
        examples: ['? ConvertToMarkdown("document.rtf", "output.md")']
      }
    };

    return templates[language] || templates.vb6;
  }, []);

  const cancelBuild = useCallback(async (buildId: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 100));
    console.log(`Build ${buildId} cancelled`);
  }, []);

  const getBuildLogs = useCallback(async (buildId: string): Promise<string[]> => {
    await new Promise(resolve => setTimeout(resolve, 100));
    return [
      `[2024-01-01T00:00:00Z] Build ${buildId} started`,
      '[2024-01-01T00:00:01Z] Configuration validated',
      '[2024-01-01T00:00:02Z] Compilation completed',
      '[2024-01-01T00:00:03Z] Build finished successfully'
    ];
  }, []);

  return {
    buildDLL,
    validateConfig,
    generateCode,
    cancelBuild,
    getBuildLogs
  };
}