{#
Copyright 2024 LegacyBridge
Network Template for Google Cloud Deployment Manager
#}

resources:
# VPC Network
- name: {{ env["name"] }}-vpc
  type: compute.v1.network
  properties:
    autoCreateSubnetworks: false
    routingConfig:
      routingMode: REGIONAL

# GKE Subnet
- name: {{ env["name"] }}-gke-subnet
  type: compute.v1.subnetwork
  properties:
    network: $(ref.{{ env["name"] }}-vpc.selfLink)
    region: {{ properties["region"] }}
    ipCidrRange: {{ properties["subnetCidrs"]["gke"] }}
    privateIpGoogleAccess: true
    secondaryIpRanges:
    - rangeName: pods
      ipCidrRange: {{ properties["subnetCidrs"]["pods"] }}
    - rangeName: services
      ipCidrRange: {{ properties["subnetCidrs"]["services"] }}

# Cloud Router for NAT
- name: {{ env["name"] }}-router
  type: compute.v1.router
  properties:
    network: $(ref.{{ env["name"] }}-vpc.selfLink)
    region: {{ properties["region"] }}
    nats:
    - name: {{ env["name"] }}-nat
      natIpAllocateOption: AUTO_ONLY
      sourceSubnetworkIpRangesToNat: ALL_SUBNETWORKS_ALL_IP_RANGES

# Firewall Rules
- name: {{ env["name"] }}-allow-internal
  type: compute.v1.firewall
  properties:
    network: $(ref.{{ env["name"] }}-vpc.selfLink)
    priority: 1000
    direction: INGRESS
    sourceRanges:
    - {{ properties["vpcCidr"] }}
    allowed:
    - IPProtocol: tcp
    - IPProtocol: udp
    - IPProtocol: icmp

- name: {{ env["name"] }}-allow-health-checks
  type: compute.v1.firewall
  properties:
    network: $(ref.{{ env["name"] }}-vpc.selfLink)
    priority: 1000
    direction: INGRESS
    sourceRanges:
    - **********/16
    - ***********/22
    allowed:
    - IPProtocol: tcp

outputs:
- name: networkName
  value: $(ref.{{ env["name"] }}-vpc.name)
- name: vpcNetwork
  value: $(ref.{{ env["name"] }}-vpc.selfLink)
- name: gkeSubnet
  value: $(ref.{{ env["name"] }}-gke-subnet.name)
- name: gkeSubnetCidr
  value: {{ properties["subnetCidrs"]["gke"] }}