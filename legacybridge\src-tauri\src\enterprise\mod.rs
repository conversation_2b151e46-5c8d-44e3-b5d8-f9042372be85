// Enterprise Features Module for LegacyBridge
pub mod monitoring;
pub mod scaling;
pub mod background;
pub mod api_gateway;

use serde::{Serialize, Deserialize};
use std::sync::Arc;
use tokio::sync::mpsc;

pub use monitoring::{
    MonitoringSystem, MonitoringConfig, MonitoringError,
    SystemMetrics, AlertThresholds, Alert, AlertLevel,
};

pub use scaling::{
    AutoScaler, ScalingConfig, ScalingError,
    ScalingStatus, ScalingEvent, ScalingAction,
    WorkerPool, WorkerPoolConfig,
};

pub use background::{
    BackgroundJobProcessor, JobProcessorConfig, Job, JobType,
    JobPriority, JobPayload, JobResult, JobStatus, JobError,
    JobHistory, QueueStats,
};

pub use api_gateway::{
    ApiGateway, GatewayConfig, GatewayError,
    ApiRequest, RequestPriority, RequestPermit,
    RateLimiter, CircuitBreaker, UsageSnapshot,
};

/// Enterprise features orchestrator
pub struct EnterpriseFeatures {
    /// Monitoring system
    pub monitoring: Arc<MonitoringSystem>,
    
    /// Auto-scaling system
    pub scaler: Arc<tokio::sync::Mutex<AutoScaler>>,
    
    /// Background job processor
    pub job_processor: Arc<tokio::sync::Mutex<BackgroundJobProcessor>>,
    
    /// API gateway
    pub gateway: Arc<ApiGateway>,
    
    /// Configuration
    config: EnterpriseConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnterpriseConfig {
    pub monitoring: MonitoringConfig,
    pub scaling: ScalingConfig,
    pub jobs: JobProcessorConfig,
    pub gateway: GatewayConfig,
    pub enable_all: bool,
}

impl Default for EnterpriseConfig {
    fn default() -> Self {
        Self {
            monitoring: MonitoringConfig::default(),
            scaling: ScalingConfig::default(),
            jobs: JobProcessorConfig::default(),
            gateway: GatewayConfig::default(),
            enable_all: true,
        }
    }
}

impl EnterpriseFeatures {
    /// Create new enterprise features instance
    pub fn new(config: EnterpriseConfig) -> Self {
        let monitoring = Arc::new(MonitoringSystem::new(config.monitoring.clone()));
        let scaler = Arc::new(tokio::sync::Mutex::new(AutoScaler::new(config.scaling.clone())));
        let job_processor = Arc::new(tokio::sync::Mutex::new(
            BackgroundJobProcessor::new(config.jobs.clone())
        ));
        let gateway = Arc::new(ApiGateway::new(config.gateway.clone()));
        
        Self {
            monitoring,
            scaler,
            job_processor,
            gateway,
            config,
        }
    }
    
    /// Initialize and start all enterprise features
    pub async fn start(&mut self) -> Result<(), EnterpriseError> {
        if !self.config.enable_all {
            return Ok(());
        }
        
        // Start monitoring system
        self.monitoring.start().await
            .map_err(|e| EnterpriseError::MonitoringError(e.to_string()))?;
        
        // Create metrics channel for auto-scaling
        let (metrics_tx, metrics_rx) = mpsc::channel::<SystemMetrics>(100);
        
        // Start metrics forwarding to scaler
        let monitoring = self.monitoring.clone();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(30));
            loop {
                interval.tick().await;
                let metrics = monitoring.get_metrics();
                let _ = metrics_tx.send(metrics).await;
            }
        });
        
        // Start auto-scaler
        let mut scaler = self.scaler.lock().await;
        scaler.start(metrics_rx).await
            .map_err(|e| EnterpriseError::ScalingError(e.to_string()))?;
        drop(scaler);
        
        // Start background job processor
        let mut job_processor = self.job_processor.lock().await;
        job_processor.start().await
            .map_err(|e| EnterpriseError::JobProcessingError(e.to_string()))?;
        drop(job_processor);
        
        // Schedule regular maintenance jobs
        self.schedule_maintenance_jobs().await?;
        
        Ok(())
    }
    
    /// Schedule regular maintenance jobs
    async fn schedule_maintenance_jobs(&self) -> Result<(), EnterpriseError> {
        use uuid::Uuid;
        use std::time::SystemTime;
        
        let job_processor = self.job_processor.lock().await;
        
        // Cache maintenance job (every hour)
        let cache_job = Job {
            id: Uuid::new_v4(),
            job_type: JobType::CacheMaintenance,
            priority: JobPriority::Background,
            payload: JobPayload::Empty,
            created_at: SystemTime::now(),
            scheduled_for: Some(SystemTime::now() + std::time::Duration::from_secs(3600)),
            retry_count: 0,
            parent_job_id: None,
            metadata: std::collections::HashMap::new(),
        };
        
        job_processor.submit_job(cache_job).await
            .map_err(|e| EnterpriseError::JobProcessingError(e.to_string()))?;
        
        // Metrics aggregation job (every 5 minutes)
        let metrics_job = Job {
            id: Uuid::new_v4(),
            job_type: JobType::MetricsAggregation,
            priority: JobPriority::Low,
            payload: JobPayload::Empty,
            created_at: SystemTime::now(),
            scheduled_for: Some(SystemTime::now() + std::time::Duration::from_secs(300)),
            retry_count: 0,
            parent_job_id: None,
            metadata: std::collections::HashMap::new(),
        };
        
        job_processor.submit_job(metrics_job).await
            .map_err(|e| EnterpriseError::JobProcessingError(e.to_string()))?;
        
        // Health check job (every 2 minutes)
        let health_job = Job {
            id: Uuid::new_v4(),
            job_type: JobType::HealthCheck,
            priority: JobPriority::Normal,
            payload: JobPayload::Empty,
            created_at: SystemTime::now(),
            scheduled_for: Some(SystemTime::now() + std::time::Duration::from_secs(120)),
            retry_count: 0,
            parent_job_id: None,
            metadata: std::collections::HashMap::new(),
        };
        
        job_processor.submit_job(health_job).await
            .map_err(|e| EnterpriseError::JobProcessingError(e.to_string()))?;
        
        Ok(())
    }
    
    /// Get current system status
    pub async fn get_status(&self) -> EnterpriseStatus {
        let monitoring_metrics = self.monitoring.get_metrics();
        let scaling_status = self.scaler.lock().await.get_status();
        let scaling_instances = self.scaler.lock().await.get_instance_count();
        let job_stats = self.job_processor.lock().await.get_queue_stats().await;
        let api_stats = self.gateway.get_usage_stats();
        
        EnterpriseStatus {
            monitoring: MonitoringStatus {
                cpu_usage: monitoring_metrics.resources.cpu_usage,
                memory_usage: monitoring_metrics.resources.memory_usage_percent,
                error_rate: monitoring_metrics.errors.recent_error_rate,
                active_connections: monitoring_metrics.performance.active_connections,
            },
            scaling: ScalingStatusInfo {
                status: format!("{:?}", scaling_status),
                current_instances: scaling_instances,
            },
            jobs: JobsStatus {
                queued: job_stats.total - job_stats.active,
                active: job_stats.active,
                by_priority: JobsByPriority {
                    critical: job_stats.critical,
                    high: job_stats.high,
                    normal: job_stats.normal,
                    low: job_stats.low,
                    background: job_stats.background,
                },
            },
            api: ApiStatus {
                requests_per_minute: api_stats.requests_per_minute,
                active_clients: api_stats.active_clients,
                avg_response_time_ms: api_stats.avg_response_time.as_millis() as u64,
            },
        }
    }
    
    /// Shutdown all enterprise features
    pub async fn shutdown(&mut self) -> Result<(), EnterpriseError> {
        // Shutdown job processor
        let mut job_processor = self.job_processor.lock().await;
        job_processor.shutdown().await
            .map_err(|e| EnterpriseError::JobProcessingError(e.to_string()))?;
        
        // Other shutdowns would be implemented here
        
        Ok(())
    }
}

/// Enterprise status information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnterpriseStatus {
    pub monitoring: MonitoringStatus,
    pub scaling: ScalingStatusInfo,
    pub jobs: JobsStatus,
    pub api: ApiStatus,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringStatus {
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub error_rate: f64,
    pub active_connections: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScalingStatusInfo {
    pub status: String,
    pub current_instances: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JobsStatus {
    pub queued: usize,
    pub active: usize,
    pub by_priority: JobsByPriority,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JobsByPriority {
    pub critical: usize,
    pub high: usize,
    pub normal: usize,
    pub low: usize,
    pub background: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiStatus {
    pub requests_per_minute: f64,
    pub active_clients: usize,
    pub avg_response_time_ms: u64,
}

/// Enterprise feature errors
#[derive(Debug)]
pub enum EnterpriseError {
    MonitoringError(String),
    ScalingError(String),
    JobProcessingError(String),
    GatewayError(String),
    ConfigurationError(String),
}

impl std::fmt::Display for EnterpriseError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            EnterpriseError::MonitoringError(msg) => write!(f, "Monitoring error: {}", msg),
            EnterpriseError::ScalingError(msg) => write!(f, "Scaling error: {}", msg),
            EnterpriseError::JobProcessingError(msg) => write!(f, "Job processing error: {}", msg),
            EnterpriseError::GatewayError(msg) => write!(f, "Gateway error: {}", msg),
            EnterpriseError::ConfigurationError(msg) => write!(f, "Configuration error: {}", msg),
        }
    }
}

impl std::error::Error for EnterpriseError {}

/// Helper function to create default enterprise features
pub fn create_default_enterprise_features() -> EnterpriseFeatures {
    EnterpriseFeatures::new(EnterpriseConfig::default())
}