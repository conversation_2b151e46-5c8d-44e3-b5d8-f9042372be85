// Comprehensive unit tests for circuit breaker functionality
use shared::circuit_breaker::{CircuitBreaker, CircuitBreakerConfig, CircuitState, CircuitBreakerRegistry};
use shared::error::ServiceError;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;

#[tokio::test]
async fn test_circuit_breaker_closed_state() {
    let config = CircuitBreakerConfig {
        failure_threshold: 3,
        recovery_timeout: Duration::from_millis(100),
        request_timeout: Duration::from_millis(50),
        half_open_max_calls: 2,
    };
    
    let circuit_breaker = CircuitBreaker::new(config);
    
    // Initially should be closed
    assert_eq!(circuit_breaker.get_state(), CircuitState::Closed);
    
    // Successful calls should keep it closed
    for _ in 0..5 {
        let result = circuit_breaker.call(|| async { Ok::<i32, ServiceError>(42) }).await;
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 42);
        assert_eq!(circuit_breaker.get_state(), CircuitState::Closed);
    }
}

#[tokio::test]
async fn test_circuit_breaker_opens_on_failures() {
    let config = CircuitBreakerConfig {
        failure_threshold: 3,
        recovery_timeout: Duration::from_millis(100),
        request_timeout: Duration::from_millis(50),
        half_open_max_calls: 2,
    };
    
    let circuit_breaker = CircuitBreaker::new(config);
    
    // Make failing calls to trigger circuit breaker
    for i in 0..3 {
        let result = circuit_breaker.call(|| async { 
            Err::<i32, ServiceError>(ServiceError::InternalError("Test failure".to_string()))
        }).await;
        
        assert!(result.is_err());
        
        if i < 2 {
            assert_eq!(circuit_breaker.get_state(), CircuitState::Closed);
        } else {
            assert_eq!(circuit_breaker.get_state(), CircuitState::Open);
        }
    }
    
    // Further calls should be rejected immediately
    let result = circuit_breaker.call(|| async { Ok::<i32, ServiceError>(42) }).await;
    assert!(matches!(result, Err(ServiceError::CircuitBreakerOpen)));
}

#[tokio::test]
async fn test_circuit_breaker_half_open_recovery() {
    let config = CircuitBreakerConfig {
        failure_threshold: 2,
        recovery_timeout: Duration::from_millis(50),
        request_timeout: Duration::from_millis(100),
        half_open_max_calls: 2,
    };
    
    let circuit_breaker = CircuitBreaker::new(config);
    
    // Trigger circuit breaker to open
    for _ in 0..2 {
        let _ = circuit_breaker.call(|| async { 
            Err::<i32, ServiceError>(ServiceError::InternalError("Test failure".to_string()))
        }).await;
    }
    
    assert_eq!(circuit_breaker.get_state(), CircuitState::Open);
    
    // Wait for recovery timeout
    sleep(Duration::from_millis(60)).await;
    
    // Next call should transition to half-open
    let result = circuit_breaker.call(|| async { Ok::<i32, ServiceError>(42) }).await;
    assert!(result.is_ok());
    assert_eq!(circuit_breaker.get_state(), CircuitState::Closed);
}

#[tokio::test]
async fn test_circuit_breaker_half_open_failure() {
    let config = CircuitBreakerConfig {
        failure_threshold: 2,
        recovery_timeout: Duration::from_millis(50),
        request_timeout: Duration::from_millis(100),
        half_open_max_calls: 2,
    };
    
    let circuit_breaker = CircuitBreaker::new(config);
    
    // Trigger circuit breaker to open
    for _ in 0..2 {
        let _ = circuit_breaker.call(|| async { 
            Err::<i32, ServiceError>(ServiceError::InternalError("Test failure".to_string()))
        }).await;
    }
    
    assert_eq!(circuit_breaker.get_state(), CircuitState::Open);
    
    // Wait for recovery timeout
    sleep(Duration::from_millis(60)).await;
    
    // Failing call in half-open should return to open
    let result = circuit_breaker.call(|| async { 
        Err::<i32, ServiceError>(ServiceError::InternalError("Test failure".to_string()))
    }).await;
    
    assert!(result.is_err());
    assert_eq!(circuit_breaker.get_state(), CircuitState::Open);
}

#[tokio::test]
async fn test_circuit_breaker_timeout() {
    let config = CircuitBreakerConfig {
        failure_threshold: 3,
        recovery_timeout: Duration::from_millis(100),
        request_timeout: Duration::from_millis(50),
        half_open_max_calls: 2,
    };
    
    let circuit_breaker = CircuitBreaker::new(config);
    
    // Call that takes longer than timeout
    let result = circuit_breaker.call(|| async { 
        sleep(Duration::from_millis(100)).await;
        Ok::<i32, ServiceError>(42)
    }).await;
    
    assert!(matches!(result, Err(ServiceError::Timeout)));
}

#[tokio::test]
async fn test_circuit_breaker_registry() {
    let config = CircuitBreakerConfig::default();
    let registry = CircuitBreakerRegistry::new(config.clone());
    
    // Get circuit breakers for different services
    let auth_cb = registry.get_or_create("auth-service");
    let conversion_cb = registry.get_or_create("conversion-service");
    let auth_cb2 = registry.get_or_create("auth-service");
    
    // Should return the same instance for the same service
    assert!(Arc::ptr_eq(&auth_cb, &auth_cb2));
    
    // Should return different instances for different services
    assert!(!Arc::ptr_eq(&auth_cb, &conversion_cb));
    
    // Test with custom config
    let custom_config = CircuitBreakerConfig {
        failure_threshold: 5,
        recovery_timeout: Duration::from_millis(200),
        request_timeout: Duration::from_millis(100),
        half_open_max_calls: 3,
    };
    
    let file_cb = registry.get_or_create_with_config("file-service", custom_config);
    
    // Trigger failures on auth service
    for _ in 0..3 {
        let _ = auth_cb.call(|| async { 
            Err::<i32, ServiceError>(ServiceError::InternalError("Test failure".to_string()))
        }).await;
    }
    
    // Auth service should be open, but conversion service should still be closed
    assert_eq!(auth_cb.get_state(), CircuitState::Open);
    assert_eq!(conversion_cb.get_state(), CircuitState::Closed);
    assert_eq!(file_cb.get_state(), CircuitState::Closed);
    
    // Get stats for all circuit breakers
    let all_stats = registry.get_all_stats();
    assert_eq!(all_stats.len(), 3);
    assert!(all_stats.contains_key("auth-service"));
    assert!(all_stats.contains_key("conversion-service"));
    assert!(all_stats.contains_key("file-service"));
}

#[tokio::test]
async fn test_circuit_breaker_force_operations() {
    let config = CircuitBreakerConfig::default();
    let circuit_breaker = CircuitBreaker::new(config);
    
    // Force open
    circuit_breaker.force_open();
    assert_eq!(circuit_breaker.get_state(), CircuitState::Open);
    
    // Calls should be rejected
    let result = circuit_breaker.call(|| async { Ok::<i32, ServiceError>(42) }).await;
    assert!(matches!(result, Err(ServiceError::CircuitBreakerOpen)));
    
    // Force close
    circuit_breaker.force_close();
    assert_eq!(circuit_breaker.get_state(), CircuitState::Closed);
    
    // Calls should work again
    let result = circuit_breaker.call(|| async { Ok::<i32, ServiceError>(42) }).await;
    assert!(result.is_ok());
    assert_eq!(result.unwrap(), 42);
}

#[tokio::test]
async fn test_circuit_breaker_stats() {
    let config = CircuitBreakerConfig {
        failure_threshold: 2,
        recovery_timeout: Duration::from_millis(100),
        request_timeout: Duration::from_millis(50),
        half_open_max_calls: 2,
    };
    
    let circuit_breaker = CircuitBreaker::new(config);
    
    // Initial stats
    let stats = circuit_breaker.get_stats();
    assert_eq!(stats.state, CircuitState::Closed);
    assert_eq!(stats.failure_count, 0);
    assert!(stats.last_failure_time.is_none());
    assert!(stats.last_success_time.is_none());
    
    // Successful call
    let _ = circuit_breaker.call(|| async { Ok::<i32, ServiceError>(42) }).await;
    let stats = circuit_breaker.get_stats();
    assert!(stats.last_success_time.is_some());
    
    // Failed call
    let _ = circuit_breaker.call(|| async { 
        Err::<i32, ServiceError>(ServiceError::InternalError("Test failure".to_string()))
    }).await;
    let stats = circuit_breaker.get_stats();
    assert_eq!(stats.failure_count, 1);
    assert!(stats.last_failure_time.is_some());
    
    // Another failed call to trigger open state
    let _ = circuit_breaker.call(|| async { 
        Err::<i32, ServiceError>(ServiceError::InternalError("Test failure".to_string()))
    }).await;
    let stats = circuit_breaker.get_stats();
    assert_eq!(stats.state, CircuitState::Open);
    assert_eq!(stats.failure_count, 2);
}

#[tokio::test]
async fn test_circuit_breaker_concurrent_access() {
    let config = CircuitBreakerConfig::default();
    let circuit_breaker = Arc::new(CircuitBreaker::new(config));
    
    let mut handles = vec![];
    
    // Spawn multiple concurrent tasks
    for i in 0..10 {
        let cb = circuit_breaker.clone();
        let handle = tokio::spawn(async move {
            let result = cb.call(|| async { 
                sleep(Duration::from_millis(10)).await;
                Ok::<i32, ServiceError>(i)
            }).await;
            result
        });
        handles.push(handle);
    }
    
    // Wait for all tasks to complete
    let mut success_count = 0;
    for handle in handles {
        let result = handle.await.unwrap();
        if result.is_ok() {
            success_count += 1;
        }
    }
    
    // All calls should succeed
    assert_eq!(success_count, 10);
    assert_eq!(circuit_breaker.get_state(), CircuitState::Closed);
}

#[tokio::test]
async fn test_circuit_breaker_registry_force_operations() {
    let config = CircuitBreakerConfig::default();
    let registry = CircuitBreakerRegistry::new(config);
    
    // Create some circuit breakers
    let _auth_cb = registry.get_or_create("auth-service");
    let _conversion_cb = registry.get_or_create("conversion-service");
    
    // Force all open
    registry.force_open_all();
    
    let auth_cb = registry.get_or_create("auth-service");
    let conversion_cb = registry.get_or_create("conversion-service");
    
    assert_eq!(auth_cb.get_state(), CircuitState::Open);
    assert_eq!(conversion_cb.get_state(), CircuitState::Open);
    
    // Force all closed
    registry.force_close_all();
    
    assert_eq!(auth_cb.get_state(), CircuitState::Closed);
    assert_eq!(conversion_cb.get_state(), CircuitState::Closed);
}
