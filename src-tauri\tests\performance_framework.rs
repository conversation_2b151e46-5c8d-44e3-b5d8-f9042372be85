// Performance Testing Framework
// Based on CURSOR-08-PERFORMANCE-OPTIMIZATION.MD specifications

use std::time::{Duration, Instant};
use serde::{Serialize, Deserialize};

#[derive(Debug, <PERSON>lone)]
pub struct PerformanceTestCase {
    pub name: String,
    pub content: String,
    pub expected_ops_per_second: usize,
    pub size_category: String,
    pub timeout: Duration,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TestResult {
    pub name: String,
    pub success: bool,
    pub operations_per_second: f64,
    pub mean_duration: Duration,
    pub median_duration: Duration,
    pub p95_duration: Duration,
    pub p99_duration: Duration,
    pub standard_deviation: Duration,
    pub expected_ops_per_second: f64,
    pub performance_ratio: f64,
    pub content_size: usize,
    pub error_message: Option<String>,
}

impl TestResult {
    pub fn failed(name: String, error: String) -> Self {
        Self {
            name,
            success: false,
            operations_per_second: 0.0,
            mean_duration: Duration::ZERO,
            median_duration: Duration::ZERO,
            p95_duration: Duration::ZERO,
            p99_duration: Duration::ZERO,
            standard_deviation: Duration::ZERO,
            expected_ops_per_second: 0.0,
            performance_ratio: 0.0,
            content_size: 0,
            error_message: Some(error),
        }
    }
}

#[derive(Debug)]
pub struct Statistics {
    pub mean: Duration,
    pub median: Duration,
    pub p95: Duration,
    pub p99: Duration,
    pub std_dev: Duration,
}

pub struct DocumentCorpus {
    tiny_documents: Vec<String>,    // <100 bytes
    small_documents: Vec<String>,   // 1KB
    medium_documents: Vec<String>,  // 10KB
    large_documents: Vec<String>,   // 100KB
    enterprise_documents: Vec<String>, // 1MB+
}

impl DocumentCorpus {
    pub fn generate_realistic() -> Self {
        Self {
            tiny_documents: Self::generate_tiny_documents(),
            small_documents: Self::generate_small_documents(),
            medium_documents: Self::generate_medium_documents(),
            large_documents: Self::generate_large_documents(),
            enterprise_documents: Self::generate_enterprise_documents(),
        }
    }

    fn generate_tiny_documents() -> Vec<String> {
        vec![
            "Hello World".to_string(),
            r"\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} Hello World".to_string(),
            "# Simple Title\n\nParagraph.".to_string(),
            "".to_string(), // Empty document
            "A".repeat(99), // Max tiny size
        ]
    }

    fn generate_small_documents() -> Vec<String> {
        vec![
            Self::create_rtf_document_with_formatting(1000),
            Self::create_markdown_document_with_tables(1000),
            Self::create_mixed_content_document(1000),
            Self::create_complex_rtf_document(1000),
        ]
    }

    fn generate_medium_documents() -> Vec<String> {
        vec![
            Self::create_rtf_document_with_formatting(10000),
            Self::create_markdown_document_with_tables(10000),
            Self::create_mixed_content_document(10000),
            Self::create_complex_rtf_document(10000),
        ]
    }

    fn generate_large_documents() -> Vec<String> {
        vec![
            Self::create_rtf_document_with_formatting(100000),
            Self::create_markdown_document_with_tables(100000),
            Self::create_mixed_content_document(100000),
        ]
    }

    fn generate_enterprise_documents() -> Vec<String> {
        vec![
            Self::create_rtf_document_with_formatting(1000000),
            Self::create_markdown_document_with_tables(1000000),
        ]
    }

    fn create_rtf_document_with_formatting(target_size: usize) -> String {
        let mut doc = String::from(r"\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}}");
        doc.push_str(r"\f0\fs24 ");
        
        let mut current_size = doc.len();
        let paragraph = "This is a test paragraph with \\b bold \\b0 and \\i italic \\i0 text. ";
        
        while current_size < target_size {
            doc.push_str(paragraph);
            current_size += paragraph.len();
        }
        
        doc.push('}');
        doc
    }

    fn create_markdown_document_with_tables(target_size: usize) -> String {
        let mut doc = String::from("# Test Document\n\n");
        
        let table = r#"| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Data A   | Data B   | Data C   |
| Data D   | Data E   | Data F   |

"#;
        
        let mut current_size = doc.len();
        while current_size < target_size {
            doc.push_str(table);
            current_size += table.len();
        }
        
        doc
    }

    fn create_mixed_content_document(target_size: usize) -> String {
        let mut doc = String::from("# Mixed Content Document\n\n");
        
        let content_blocks = vec![
            "## Section with **bold** and *italic* text\n\n",
            "Here's a [link](https://example.com) and some `code`.\n\n",
            "- List item 1\n- List item 2\n- List item 3\n\n",
            "1. Numbered item\n2. Another item\n3. Final item\n\n",
            "> This is a blockquote with some text.\n\n",
            "```rust\nfn main() {\n    println!(\"Hello, world!\");\n}\n```\n\n",
        ];
        
        let mut current_size = doc.len();
        let mut block_idx = 0;
        
        while current_size < target_size {
            let block = content_blocks[block_idx % content_blocks.len()];
            doc.push_str(block);
            current_size += block.len();
            block_idx += 1;
        }
        
        doc
    }

    fn create_complex_rtf_document(target_size: usize) -> String {
        let mut doc = String::from(r"\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}{\f1 Arial;}}");
        
        let complex_content = r"\f0\fs24 This is \b bold \b0 text with \i italic \i0 and \ul underlined \ul0 formatting. \f1 Different font here. \f0 Back to original font. \par ";
        
        let mut current_size = doc.len();
        while current_size < target_size {
            doc.push_str(complex_content);
            current_size += complex_content.len();
        }
        
        doc.push('}');
        doc
    }

    pub fn get_performance_test_suite(&self) -> Vec<PerformanceTestCase> {
        let mut test_cases = Vec::new();
        
        // Add test cases for each size category with realistic targets
        test_cases.extend(self.create_test_cases("tiny", &self.tiny_documents, 20000));
        test_cases.extend(self.create_test_cases("small", &self.small_documents, 5000));
        test_cases.extend(self.create_test_cases("medium", &self.medium_documents, 1000));
        test_cases.extend(self.create_test_cases("large", &self.large_documents, 200));
        test_cases.extend(self.create_test_cases("enterprise", &self.enterprise_documents, 20));
        
        test_cases
    }

    fn create_test_cases(
        category: &str,
        documents: &[String],
        target_ops_per_second: usize
    ) -> Vec<PerformanceTestCase> {
        documents.iter().enumerate().map(|(i, doc)| {
            PerformanceTestCase {
                name: format!("{}_doc_{}", category, i),
                content: doc.clone(),
                expected_ops_per_second: target_ops_per_second,
                size_category: category.to_string(),
                timeout: Duration::from_secs(30),
            }
        }).collect()
    }
}

pub struct PerformanceTestRunner {
    warmup_iterations: usize,
    measurement_iterations: usize,
    confidence_interval: f64,
}

impl PerformanceTestRunner {
    pub fn new() -> Self {
        Self {
            warmup_iterations: 10,
            measurement_iterations: 100,
            confidence_interval: 0.95,
        }
    }

    pub fn run_performance_suite(&self) -> PerformanceReport {
        let corpus = DocumentCorpus::generate_realistic();
        let test_cases = corpus.get_performance_test_suite();
        
        let mut results = Vec::new();
        
        for test_case in test_cases {
            println!("Running test: {}", test_case.name);
            let result = self.run_single_test(&test_case);
            results.push(result);
        }
        
        PerformanceReport::new(results)
    }

    fn run_single_test(&self, test_case: &PerformanceTestCase) -> TestResult {
        // Warmup
        for _ in 0..self.warmup_iterations {
            let _ = legacybridge::conversion::rtf_to_markdown(&test_case.content);
        }

        // Actual measurements
        let mut durations = Vec::new();
        
        for _ in 0..self.measurement_iterations {
            let start = Instant::now();
            match legacybridge::conversion::rtf_to_markdown(&test_case.content) {
                Ok(_) => {
                    durations.push(start.elapsed());
                }
                Err(e) => {
                    return TestResult::failed(test_case.name.clone(), e.to_string());
                }
            }
        }

        // Calculate statistics
        let stats = self.calculate_statistics(&durations);
        let ops_per_second = 1.0 / stats.mean.as_secs_f64();
        
        TestResult {
            name: test_case.name.clone(),
            success: true,
            operations_per_second: ops_per_second,
            mean_duration: stats.mean,
            median_duration: stats.median,
            p95_duration: stats.p95,
            p99_duration: stats.p99,
            standard_deviation: stats.std_dev,
            expected_ops_per_second: test_case.expected_ops_per_second as f64,
            performance_ratio: ops_per_second / test_case.expected_ops_per_second as f64,
            content_size: test_case.content.len(),
            error_message: None,
        }
    }

    fn calculate_statistics(&self, durations: &[Duration]) -> Statistics {
        let mut sorted_durations = durations.to_vec();
        sorted_durations.sort();

        let mean = durations.iter().sum::<Duration>() / durations.len() as u32;
        let median = sorted_durations[sorted_durations.len() / 2];
        let p95 = sorted_durations[(sorted_durations.len() as f64 * 0.95) as usize];
        let p99 = sorted_durations[(sorted_durations.len() as f64 * 0.99) as usize];

        // Calculate standard deviation
        let mean_secs = mean.as_secs_f64();
        let variance = durations.iter()
            .map(|d| {
                let diff = d.as_secs_f64() - mean_secs;
                diff * diff
            })
            .sum::<f64>() / durations.len() as f64;
        
        let std_dev = Duration::from_secs_f64(variance.sqrt());

        Statistics {
            mean,
            median,
            p95,
            p99,
            std_dev,
        }
    }
}

#[derive(Debug, Serialize)]
pub struct PerformanceReport {
    pub results: Vec<TestResult>,
    pub generated_at: chrono::DateTime<chrono::Utc>,
}

impl PerformanceReport {
    pub fn new(results: Vec<TestResult>) -> Self {
        Self {
            results,
            generated_at: chrono::Utc::now(),
        }
    }
}
