// Integration tests for CLI testing framework
// Note: Full test execution requires system dependencies (pkg-config, glib-2.0)
// These tests verify module compilation and basic functionality

#[cfg(test)]
mod cli_testing_integration {
    use legacybridge::cli::testing::{TestDataManager, TestRunner, TestCase, TestType, SizeCategory};
    use legacybridge::cli::benchmarks::{BenchmarkSuite, BenchmarkConfig, BenchmarkType, create_default_benchmarks};
    use legacybridge::conversion::ConversionEngine;
    use tempfile::TempDir;
    use std::path::PathBuf;

    #[test]
    fn test_module_imports() {
        // This test verifies that all testing modules can be imported
        println!("Testing module imports...");
        
        // Test data module
        assert_eq!(SizeCategory::Small.as_str(), "small");
        
        // Test runner module
        let test_case = TestCase {
            id: "test".to_string(),
            name: "Test".to_string(),
            test_type: TestType::Unit,
            tags: vec![],
            timeout_ms: 5000,
            retry_count: 0,
            expected_outcome: None,
        };
        assert_eq!(test_case.id, "test");
        
        // Benchmark module
        let benchmarks = create_default_benchmarks();
        assert!(!benchmarks.is_empty());
    }

    #[test]
    fn test_test_data_manager_creation() {
        let temp_dir = TempDir::new().unwrap();
        let result = TestDataManager::new(&temp_dir.path());
        assert!(result.is_ok());
        
        // Verify directory structure
        assert!(temp_dir.path().join("samples").exists());
        assert!(temp_dir.path().join("baselines").exists());
        assert!(temp_dir.path().join("exports").exists());
    }

    #[test]
    fn test_size_category_ordering() {
        use std::cmp::Ordering;
        
        assert_eq!(SizeCategory::Tiny.cmp(&SizeCategory::Small), Ordering::Less);
        assert_eq!(SizeCategory::Small.cmp(&SizeCategory::Medium), Ordering::Less);
        assert_eq!(SizeCategory::Medium.cmp(&SizeCategory::Large), Ordering::Less);
        assert_eq!(SizeCategory::Large.cmp(&SizeCategory::ExtraLarge), Ordering::Less);
    }

    #[test]
    fn test_test_type_variants() {
        let test_types = vec![
            TestType::Unit,
            TestType::Integration,
            TestType::FormatValidation,
            TestType::PerformanceRegression,
            TestType::MemoryLeak,
            TestType::AccuracyValidation,
        ];
        
        // Verify all test types are distinct
        for (i, t1) in test_types.iter().enumerate() {
            for (j, t2) in test_types.iter().enumerate() {
                if i != j {
                    assert_ne!(format!("{:?}", t1), format!("{:?}", t2));
                }
            }
        }
    }

    #[test]
    fn test_benchmark_type_variants() {
        // Test that all benchmark types can be created
        let types = vec![
            BenchmarkType::ConversionSpeed {
                input_format: "rtf".to_string(),
                output_format: "md".to_string(),
                file_sizes: vec![SizeCategory::Small],
            },
            BenchmarkType::MemoryUsage {
                operation: "conversion".to_string(),
                iterations: 10,
            },
            BenchmarkType::Throughput {
                operation: "conversion".to_string(),
                duration_seconds: 10,
            },
            BenchmarkType::Latency {
                operation: "conversion".to_string(),
                percentiles: vec![50.0, 90.0, 99.0],
            },
            BenchmarkType::Concurrency {
                operation: "conversion".to_string(),
                thread_counts: vec![1, 2, 4],
            },
        ];
        
        assert_eq!(types.len(), 5);
    }

    #[test]
    fn test_benchmark_config_creation() {
        let config = BenchmarkConfig {
            name: "test_benchmark".to_string(),
            description: "Test benchmark".to_string(),
            benchmark_type: BenchmarkType::ConversionSpeed {
                input_format: "rtf".to_string(),
                output_format: "md".to_string(),
                file_sizes: vec![SizeCategory::Medium],
            },
            warmup_iterations: 3,
            measurement_iterations: 10,
            tags: vec!["test".to_string()],
        };
        
        assert_eq!(config.name, "test_benchmark");
        assert_eq!(config.warmup_iterations, 3);
        assert_eq!(config.measurement_iterations, 10);
    }

    #[test]
    fn test_default_benchmarks() {
        let benchmarks = create_default_benchmarks();
        
        // Verify we have the expected number of default benchmarks
        assert_eq!(benchmarks.len(), 5);
        
        // Verify benchmark names
        let names: Vec<&str> = benchmarks.iter().map(|b| b.name.as_str()).collect();
        assert!(names.contains(&"conversion_speed_rtf_to_md"));
        assert!(names.contains(&"memory_usage_conversion"));
        assert!(names.contains(&"throughput_batch_conversion"));
        assert!(names.contains(&"latency_single_file"));
        assert!(names.contains(&"concurrency_scaling"));
    }

    #[test]
    fn test_rtf_content_generation() {
        let content = TestDataManager::generate_rtf_content(100);
        
        // Basic RTF structure validation
        assert!(content.starts_with(r"{\rtf1"));
        assert!(content.ends_with("}"));
        assert!(content.contains(r"\ansi"));
        assert!(content.contains(r"\par"));
        
        // Should be larger than requested due to RTF overhead
        assert!(content.len() > 100);
    }

    #[test]
    fn test_markdown_content_generation() {
        let content = TestDataManager::generate_markdown_content(100);
        
        // Basic Markdown structure validation
        assert!(content.contains('#')); // Headers
        assert!(content.contains("Lorem ipsum")); // Text content
        
        // Should be at least the requested size
        assert!(content.len() >= 100);
    }
}