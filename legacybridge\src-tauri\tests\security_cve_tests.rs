// Security test suite for CVE-level fixes
//
// This module implements comprehensive tests for all 5 CVE-level security vulnerabilities
// addressed in CURSOR-07-CRITICAL-SECURITY-FIXES.MD

#[cfg(test)]
mod cve_security_tests {
    use legacybridge::conversion::{
        rtf_lexer::tokenize,
        security::{
            TextSizeTracker, RecursionTracker, SafeNumericParser, RtfSecurityFilter,
            SecurityValidation, RiskLevel
        },
        types::ConversionError,
    };
    use legacybridge::security::PathValidator;
    use std::time::{Duration, Instant};

    /// CVE-LEVEL-001: Test memory exhaustion prevention
    #[test]
    fn test_memory_exhaustion_prevention() {
        let mut tracker = TextSizeTracker::new(1024); // 1KB limit
        
        // Test normal operation
        assert!(tracker.check_and_add(512).is_ok());
        assert_eq!(tracker.current_size(), 512);
        
        // Test limit enforcement
        let result = tracker.check_and_add(600); // Would exceed 1KB
        assert!(result.is_err());
        
        match result.unwrap_err() {
            ConversionError::MemoryExhausted { requested, limit } => {
                assert_eq!(requested, 1112);
                assert_eq!(limit, 1024);
            },
            _ => panic!("Expected MemoryExhausted error"),
        }
    }

    #[test]
    fn test_rtf_lexer_memory_bounds() {
        // Test with large text that should trigger memory limits
        let attack_content = "a".repeat(20_000_000); // 20MB of 'a'
        let rtf_content = format!("{{\\rtf1 {}}}", attack_content);
        
        let result = tokenize(&rtf_content);
        
        // Should fail due to memory limits
        assert!(result.is_err());
        match result.unwrap_err() {
            ConversionError::MemoryExhausted { .. } => {}, // Expected
            ConversionError::ValidationError(_) => {}, // Also acceptable (input size limit)
            other => panic!("Unexpected error type: {:?}", other),
        }
    }

    /// CVE-LEVEL-002: Test recursion limit prevention
    #[test]
    fn test_recursion_limit_prevention() {
        let mut tracker = RecursionTracker::new(5); // Very low limit for testing
        
        // Test normal operation
        assert!(tracker.enter("test_function").is_ok());
        assert_eq!(tracker.current_depth(), 1);
        
        // Test nested calls
        for i in 2..=5 {
            assert!(tracker.enter(&format!("nested_{}", i)).is_ok());
            assert_eq!(tracker.current_depth(), i);
        }
        
        // Test limit enforcement
        let result = tracker.enter("too_deep");
        assert!(result.is_err());
        
        match result.unwrap_err() {
            ConversionError::RecursionLimitExceeded { current_depth, max_depth, call_stack } => {
                assert_eq!(current_depth, 5);
                assert_eq!(max_depth, 5);
                assert_eq!(call_stack.len(), 5);
            },
            _ => panic!("Expected RecursionLimitExceeded error"),
        }
    }

    #[test]
    fn test_deep_nesting_attack() {
        // Create deeply nested RTF content
        let open_braces = "{".repeat(1000);
        let close_braces = "}".repeat(1000);
        let attack_content = format!("{{\\rtf1 {}{}}}", open_braces, close_braces);
        
        let result = tokenize(&attack_content);
        
        // Should succeed at lexer level but fail at parser level due to recursion limits
        // For now, just ensure it doesn't crash
        match result {
            Ok(_) => {}, // Lexer succeeded
            Err(ConversionError::ValidationError(_)) => {}, // Size limit hit
            Err(other) => panic!("Unexpected error: {:?}", other),
        }
    }

    /// CVE-LEVEL-003: Test integer overflow protection
    #[test]
    fn test_integer_overflow_protection() {
        let parser = SafeNumericParser::new();
        
        // Test normal values
        assert_eq!(parser.parse_safe("123").unwrap(), 123);
        assert_eq!(parser.parse_safe("-456").unwrap(), -456);
        
        // Test boundary values
        assert_eq!(parser.parse_safe("1000000").unwrap(), 1000000);
        assert_eq!(parser.parse_safe("-1000000").unwrap(), -1000000);
        
        // Test overflow attempts
        let overflow_tests = vec![
            "1000001",      // Just over limit
            "-1000001",     // Just under limit
            "2147483648",   // i32::MAX + 1
            "-2147483649",  // i32::MIN - 1
            "12345678901234567890", // Very large number
        ];
        
        for test_input in overflow_tests {
            let result = parser.parse_safe(test_input);
            assert!(result.is_err(), "Should fail for input: {}", test_input);
        }
        
        // Test length limit
        let long_number = "1".repeat(20);
        let result = parser.parse_safe(&long_number);
        assert!(result.is_err());
        
        match result.unwrap_err() {
            ConversionError::NumericOverflow { input, max_length } => {
                assert_eq!(input, long_number);
                assert_eq!(max_length, 10);
            },
            _ => panic!("Expected NumericOverflow error"),
        }
    }

    /// CVE-LEVEL-004: Test path traversal prevention
    #[test]
    fn test_path_traversal_prevention() {
        let validator = PathValidator::new();
        
        let attack_paths = vec![
            "../../../etc/passwd",
            "..\\..\\Windows\\System32\\config\\sam",
            "/etc/shadow",
            "C:\\Windows\\System32\\config\\sam",
            "uploads/../../../etc/passwd",
            "~/.ssh/id_rsa",
            "/root/.bashrc",
            "C:\\Users\\<USER>\\Desktop\\secrets.txt",
        ];
        
        for path in attack_paths {
            let result = validator.validate_path(path);
            assert!(result.is_err(), "Path should be blocked: {}", path);
        }
    }

    #[test]
    fn test_symlink_attack_prevention() {
        let validator = PathValidator::new();
        
        // These would be symlink attacks in a real scenario
        let symlink_attacks = vec![
            "uploads/link_to_etc",
            "temp/../../etc/passwd",
            "output/../../../root/.ssh",
        ];
        
        for path in symlink_attacks {
            let result = validator.validate_path(path);
            // Should either be blocked or fail validation
            assert!(result.is_err(), "Symlink attack should be prevented: {}", path);
        }
    }

    /// CVE-LEVEL-005: Test dangerous control word filtering
    #[test]
    fn test_dangerous_control_words_blocked() {
        let filter = RtfSecurityFilter::new();
        
        let dangerous_words = vec![
            "object", "objdata", "objclass", "objname", "objsetsize", "objscalex", "objscaley",
            "field", "fldinst", "fldrslt", "fldlock", "fldpriv",
            "datafield", "do", "dptxbxtext",
            "fontemb", "fontfile", "template",
            "stylesheet", "info", "doccomm", "operator", "version"
        ];
        
        for word in dangerous_words {
            match filter.validate_control_word(word) {
                SecurityValidation::Blocked { reason, risk_level } => {
                    assert!(reason.contains(word));
                    match risk_level {
                        RiskLevel::Critical => {}, // Expected
                        _ => panic!("Expected Critical risk level for {}", word),
                    }
                },
                _ => panic!("Dangerous word {} not blocked", word),
            }
        }
    }

    #[test]
    fn test_safe_control_words_allowed() {
        let filter = RtfSecurityFilter::new();
        
        let safe_words = vec![
            "b", "i", "ul", "strike", "v", "f", "fs", "cf", "cb",
            "par", "pard", "plain", "tab", "line", "page",
            "sect", "sectd", "cols", "colsx", "column",
        ];
        
        for word in safe_words {
            match filter.validate_control_word(word) {
                SecurityValidation::Allowed => {}, // Expected
                other => panic!("Safe word {} should be allowed, got: {:?}", word, other),
            }
        }
    }

    #[test]
    fn test_unknown_control_words_flagged() {
        let filter = RtfSecurityFilter::new();
        
        let unknown_words = vec!["unknownword", "customcommand", "newfeature"];
        
        for word in unknown_words {
            match filter.validate_control_word(word) {
                SecurityValidation::Unknown { reason, risk_level } => {
                    assert!(reason.contains(word));
                    match risk_level {
                        RiskLevel::Low => {}, // Expected
                        _ => panic!("Expected Low risk level for unknown word {}", word),
                    }
                },
                other => panic!("Unknown word {} should be flagged as unknown, got: {:?}", word, other),
            }
        }
    }

    /// Performance impact validation
    #[test]
    fn test_security_performance_impact() {
        let start = Instant::now();
        
        // Test a reasonable RTF document
        let test_content = r#"{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}} \f0\fs24 Hello, World! \par This is a test document with \b bold \b0 and \i italic \i0 text.}"#;
        
        let result = tokenize(test_content);
        let duration = start.elapsed();
        
        // Should complete quickly (under 100ms for small document)
        assert!(duration < Duration::from_millis(100), "Security checks should not significantly impact performance");
        assert!(result.is_ok(), "Valid RTF should still parse correctly");
    }
}
