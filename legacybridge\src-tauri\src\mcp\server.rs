// LegacyBridge MCP Server - Complete Implementation
use crate::mcp::types::*;
use crate::conversion::{ConversionEngine, ConversionOptions as EngineOptions};
use crate::formats::{FormatManager, FormatDetection as EngineDetection, FormatType};
use crate::dll::{Dll<PERSON>uilder, BuildConfig};
use crate::dll::builder::{Architecture, OptimizationLevel};
use serde::{Serialize, Deserialize};
use serde_json::{json, Value as JsonValue};
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;
use chrono::Utc;
use std::collections::HashMap;
use std::time::Instant;

/// LegacyBridge MCP Server - Exposes document conversion capabilities to AI assistants
#[derive(Clone)]
pub struct LegacyBridgeMcpServer {
    /// Core conversion engine
    conversion_engine: Arc<ConversionEngine>,
    
    /// Format detection and registry
    format_manager: Arc<FormatManager>,
    
    /// Active conversion jobs
    active_jobs: Arc<RwLock<HashMap<String, ConversionJob>>>,
    
    /// Server statistics
    stats: Arc<RwLock<ServerStats>>,
    
    /// Configuration
    config: McpServerConfig,
    
    /// Server start time
    start_time: Instant,
}

impl LegacyBridgeMcpServer {
    pub fn new(config: McpServerConfig) -> Self {
        let format_manager = Arc::new(FormatManager::new());
        let conversion_engine = Arc::new(ConversionEngine::new());
        
        Self {
            conversion_engine,
            format_manager,
            active_jobs: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(ServerStats::default())),
            config,
            start_time: Instant::now(),
        }
    }
    
    /// Get server information
    pub fn get_server_info(&self) -> JsonValue {
        json!({
            "name": "legacybridge",
            "version": "2.0.0",
            "description": "Convert legacy document formats to modern standards",
            "capabilities": {
                "tools": true,
                "resources": true,
                "prompts": true,
                "websocket": self.config.enable_websocket,
                "batch_processing": true,
                "dll_building": self.config.enable_dll_building,
            }
        })
    }

    // ========================================
    // TOOL IMPLEMENTATIONS
    // ========================================
    
    pub async fn handle_convert_file(&self, args: JsonValue) -> Result<JsonValue, String> {
        let input_content = args["input_content"].as_str()
            .ok_or_else(|| "Missing input_content".to_string())?;
        
        let output_format = args["output_format"].as_str()
            .ok_or_else(|| "Missing output_format".to_string())?;
        
        let input_format = args["input_format"].as_str().unwrap_or("auto");
        
        // Decode content if base64
        let content = if input_format == "auto" || input_format.starts_with("binary") {
            base64::decode(input_content)
                .map_err(|e| format!("Base64 decode error: {}", e))?
        } else {
            input_content.as_bytes().to_vec()
        };
        
        // Detect format if auto
        let detected_format = if input_format == "auto" {
            let detection = self.format_manager.detect_format(&content)
                .map_err(|e| format!("Format detection failed: {}", e))?;
            format!("{:?}", detection.format_type).to_lowercase()
        } else {
            input_format.to_string()
        };
        
        // Parse conversion options
        let preserve_formatting = args["options"]["preserve_formatting"].as_bool().unwrap_or(true);
        let quality = args["options"]["quality"].as_u64().map(|q| q as u8).unwrap_or(8);
        
        // Map to internal conversion options
        let options = EngineOptions {
            preserve_formatting,
            extract_metadata: false,
            quality,
            custom: HashMap::new(),
        };
        
        // Perform conversion using the existing conversion module
        let start_time = Instant::now();
        let converted_content = match (&detected_format.as_str(), output_format) {
            ("rtf", "md") | ("rtf", "markdown") => {
                // Use existing RTF to Markdown conversion
                let rtf_str = String::from_utf8_lossy(&content);
                crate::conversion::rtf_to_markdown(&rtf_str)
                    .map_err(|e| format!("Conversion failed: {:?}", e))?
                    .into_bytes()
            },
            ("md" | "markdown", "rtf") => {
                // Use existing Markdown to RTF conversion
                let md_str = String::from_utf8_lossy(&content);
                crate::conversion::markdown_to_rtf(&md_str)
                    .map_err(|e| format!("Conversion failed: {:?}", e))?
                    .into_bytes()
            },
            (input, output) => {
                // Use conversion engine for other formats
                self.conversion_engine.convert(&content, input, output, options).await
                    .map_err(|e| format!("Conversion failed: {:?}", e))?
            }
        };
        
        let processing_time = start_time.elapsed();
        
        // Update statistics
        self.update_stats(1, content.len(), converted_content.len()).await;
        
        Ok(json!({
            "success": true,
            "output_content": String::from_utf8_lossy(&converted_content),
            "metadata": {
                "detected_format": detected_format,
                "output_format": output_format,
                "input_size": content.len(),
                "output_size": converted_content.len(),
                "processing_time_ms": processing_time.as_millis(),
                "conversion_quality": "high"
            }
        }))
    }
    
    pub async fn handle_rtf_to_markdown(&self, args: JsonValue) -> Result<JsonValue, String> {
        let rtf_content = args["rtf_content"].as_str()
            .ok_or_else(|| "Missing rtf_content".to_string())?;
        
        let preserve_formatting = args["preserve_formatting"].as_bool().unwrap_or(true);
        let include_metadata = args["include_metadata"].as_bool().unwrap_or(false);
        
        let start_time = Instant::now();
        
        // Use existing RTF to Markdown conversion
        let markdown_content = crate::conversion::rtf_to_markdown(rtf_content)
            .map_err(|e| format!("RTF conversion failed: {:?}", e))?;
        
        let processing_time = start_time.elapsed();
        
        self.update_stats(1, rtf_content.len(), markdown_content.len()).await;
        
        Ok(json!({
            "success": true,
            "markdown_content": markdown_content,
            "metadata": {
                "input_format": "rtf",
                "output_format": "markdown",
                "preserve_formatting": preserve_formatting,
                "include_metadata": include_metadata,
                "input_size": rtf_content.len(),
                "output_size": markdown_content.len(),
                "processing_time_ms": processing_time.as_millis()
            }
        }))
    }
    
    pub async fn handle_markdown_to_rtf(&self, args: JsonValue) -> Result<JsonValue, String> {
        let markdown_content = args["markdown_content"].as_str()
            .ok_or_else(|| "Missing markdown_content".to_string())?;
        
        let template = args["template"].as_str().unwrap_or("basic");
        let font_family = args["font_family"].as_str().unwrap_or("Times New Roman");
        let font_size = args["font_size"].as_u64().unwrap_or(12) as u8;
        
        let start_time = Instant::now();
        
        // Use existing Markdown to RTF conversion
        let rtf_content = crate::conversion::markdown_to_rtf(markdown_content)
            .map_err(|e| format!("Markdown conversion failed: {:?}", e))?;
        
        let processing_time = start_time.elapsed();
        
        self.update_stats(1, markdown_content.len(), rtf_content.len()).await;
        
        Ok(json!({
            "success": true,
            "rtf_content": rtf_content,
            "metadata": {
                "input_format": "markdown",
                "output_format": "rtf",
                "template": template,
                "font_family": font_family,
                "font_size": font_size,
                "input_size": markdown_content.len(),
                "output_size": rtf_content.len(),
                "processing_time_ms": processing_time.as_millis()
            }
        }))
    }
    
    pub async fn handle_convert_legacy_format(&self, args: JsonValue) -> Result<JsonValue, String> {
        let file_content = args["file_content"].as_str()
            .ok_or_else(|| "Missing file_content".to_string())?;
        
        let detected_format = args["detected_format"].as_str()
            .ok_or_else(|| "Missing detected_format".to_string())?;
        
        let output_format = args["output_format"].as_str().unwrap_or("md");
        let extraction_mode = args["extraction_mode"].as_str().unwrap_or("formatted");
        
        // Decode base64 content
        let content = base64::decode(file_content)
            .map_err(|e| format!("Base64 decode error: {}", e))?;
        
        let start_time = Instant::now();
        
        // Map format string to FormatType
        let format_type = match detected_format {
            "doc" => FormatType::Doc,
            "wordperfect" | "wpd" => FormatType::WordPerfect,
            "lotus123" | "123" | "wk1" => FormatType::Lotus123,
            "dbase" | "dbf" => FormatType::DBase,
            "wordstar" | "ws" => FormatType::WordStar,
            _ => return Err(format!("Unsupported format: {}", detected_format)),
        };
        
        // Convert based on output format
        let result = match output_format {
            "md" | "markdown" => {
                self.format_manager.convert_to_markdown(&content, &format_type)
                    .map_err(|e| format!("Legacy conversion failed: {:?}", e))?
            },
            "rtf" => {
                self.format_manager.convert_to_rtf(&content, &format_type)
                    .map_err(|e| format!("Legacy conversion failed: {:?}", e))?
            },
            _ => return Err(format!("Unsupported output format: {}", output_format)),
        };
        
        let processing_time = start_time.elapsed();
        
        self.update_stats(1, content.len(), result.content.len()).await;
        
        Ok(json!({
            "success": true,
            "output_content": result.content,
            "metadata": {
                "input_format": detected_format,
                "output_format": output_format,
                "extraction_mode": extraction_mode,
                "warnings": result.warnings,
                "processing_time_ms": processing_time.as_millis()
            }
        }))
    }
    
    pub async fn handle_detect_format(&self, args: JsonValue) -> Result<JsonValue, String> {
        let file_content = args["file_content"].as_str()
            .ok_or_else(|| "Missing file_content".to_string())?;
        
        let filename = args["filename"].as_str();
        let detailed_analysis = args["detailed_analysis"].as_bool().unwrap_or(false);
        
        // Decode content
        let content = base64::decode(file_content)
            .map_err(|e| format!("Base64 decode error: {}", e))?;
        
        // Try content-based detection
        let detection = self.format_manager.detect_format(&content)
            .map_err(|e| format!("Format detection failed: {:?}", e))?;
        
        // Build response
        let mut response = json!({
            "success": true,
            "format": format!("{:?}", detection.format_type).to_lowercase(),
            "confidence": detection.confidence,
            "version": detection.version,
        });
        
        if detailed_analysis {
            response["detailed_analysis"] = json!({
                "metadata": detection.metadata,
                "file_size": content.len(),
                "magic_bytes": format!("{:02X?}", &content[..std::cmp::min(16, content.len())]),
                "supported_conversions": match detection.format_type {
                    FormatType::Unknown => vec![],
                    _ => vec!["markdown", "rtf"],
                }
            });
        }
        
        Ok(response)
    }
    
    pub async fn handle_validate_file(&self, args: JsonValue) -> Result<JsonValue, String> {
        let file_content = args["file_content"].as_str()
            .ok_or_else(|| "Missing file_content".to_string())?;
        
        let expected_format = args["expected_format"].as_str();
        let repair_if_possible = args["repair_if_possible"].as_bool().unwrap_or(false);
        
        // For now, implement basic validation
        let content = if file_content.chars().all(|c| c.is_ascii()) {
            file_content.as_bytes().to_vec()
        } else {
            base64::decode(file_content)
                .map_err(|e| format!("Base64 decode error: {}", e))?
        };
        
        let detection = self.format_manager.detect_format(&content)
            .map_err(|e| format!("Format detection failed: {:?}", e))?;
        
        let is_valid = if let Some(expected) = expected_format {
            format!("{:?}", detection.format_type).to_lowercase() == expected.to_lowercase()
        } else {
            detection.format_type != FormatType::Unknown
        };
        
        Ok(json!({
            "success": true,
            "is_valid": is_valid,
            "detected_format": format!("{:?}", detection.format_type).to_lowercase(),
            "confidence": detection.confidence,
            "issues": if is_valid { vec![] } else { vec!["Format mismatch or unknown format"] },
            "repaired": false,
        }))
    }
    
    pub async fn handle_build_dll(&self, args: JsonValue) -> Result<JsonValue, String> {
        let architecture = args["architecture"].as_str().unwrap_or("x86");
        let included_formats = args["included_formats"].as_array()
            .map(|arr| arr.iter().filter_map(|v| v.as_str().map(String::from)).collect())
            .unwrap_or_else(|| vec!["rtf".to_string(), "doc".to_string()]);
        
        let optimization = args["optimization"].as_str().unwrap_or("release");
        let generate_wrappers = args["generate_wrappers"].as_bool().unwrap_or(true);
        
        // Map to internal types
        let arch = match architecture {
            "x86" => Architecture::X86,
            "x64" => Architecture::X64,
            "both" => Architecture::Both,
            _ => return Err("Invalid architecture".to_string()),
        };
        
        let opt_level = match optimization {
            "debug" => OptimizationLevel::Debug,
            "release" => OptimizationLevel::Release,
            "size" => OptimizationLevel::Size,
            _ => OptimizationLevel::Release,
        };
        
        // Create build configuration
        let build_config = BuildConfig {
            source_files: vec![], // Will be populated by builder
            output_name: "legacybridge".to_string(),
            architecture: arch,
            optimization: opt_level,
            debug_symbols: optimization == "debug",
            static_linking: true,
            export_definitions: None,
            include_dirs: vec![],
            library_dirs: vec![],
            libraries: vec![],
            defines: included_formats.iter().map(|f| format!("SUPPORT_{}", f.to_uppercase())).collect(),
            compiler_flags: vec![],
            linker_flags: vec![],
        };
        
        let builder = DllBuilder::new(build_config);
        let start_time = Instant::now();
        
        // Build DLL
        let result = builder.build().await
            .map_err(|e| format!("DLL build failed: {:?}", e))?;
        
        Ok(json!({
            "success": true,
            "build_id": Uuid::new_v4().to_string(),
            "output_files": vec![result.dll_path.to_string_lossy()],
            "architectures_built": vec![result.architecture],
            "warnings": Vec::<String>::new(),
            "errors": Vec::<String>::new(),
            "build_duration_ms": start_time.elapsed().as_millis()
        }))
    }
    
    pub async fn handle_batch_convert(&self, args: JsonValue) -> Result<JsonValue, String> {
        let files = args["files"].as_array()
            .ok_or_else(|| "Missing files array".to_string())?;
        
        let output_format = args["output_format"].as_str()
            .ok_or_else(|| "Missing output_format".to_string())?;
        
        let parallel_jobs = args["parallel_jobs"].as_u64().unwrap_or(4) as usize;
        let continue_on_error = args["continue_on_error"].as_bool().unwrap_or(true);
        
        // Create job
        let job_id = Uuid::new_v4().to_string();
        let job = ConversionJob {
            id: job_id.clone(),
            status: JobStatus::Processing,
            total_files: files.len(),
            processed_files: 0,
            successful_conversions: 0,
            failed_conversions: 0,
            start_time: Utc::now(),
            results: Vec::new(),
        };
        
        // Store job
        self.active_jobs.write().await.insert(job_id.clone(), job);
        
        // Clone necessary data for async processing
        let server = self.clone();
        let job_id_clone = job_id.clone();
        let files_clone = files.clone();
        let output_format_clone = output_format.to_string();
        
        // Process files asynchronously
        tokio::spawn(async move {
            server.process_batch_job(
                job_id_clone,
                files_clone,
                output_format_clone,
                parallel_jobs,
                continue_on_error
            ).await;
        });
        
        Ok(json!({
            "success": true,
            "job_id": job_id,
            "status": "processing",
            "total_files": files.len(),
            "message": format!("Started batch conversion of {} files", files.len())
        }))
    }
    
    pub async fn handle_get_job_status(&self, args: JsonValue) -> Result<JsonValue, String> {
        let job_id = args["job_id"].as_str()
            .ok_or_else(|| "Missing job_id".to_string())?;
        
        let jobs = self.active_jobs.read().await;
        let job = jobs.get(job_id)
            .ok_or_else(|| "Job not found".to_string())?;
        
        Ok(json!({
            "success": true,
            "job_id": job_id,
            "status": format!("{:?}", job.status),
            "progress": {
                "total_files": job.total_files,
                "processed_files": job.processed_files,
                "successful_conversions": job.successful_conversions,
                "failed_conversions": job.failed_conversions,
                "completion_percentage": (job.processed_files as f64 / job.total_files as f64 * 100.0)
            },
            "start_time": job.start_time,
            "results": job.results
        }))
    }
    
    // Helper methods
    async fn update_stats(&self, conversions: u64, input_bytes: usize, output_bytes: usize) {
        let mut stats = self.stats.write().await;
        stats.total_conversions += conversions;
        stats.total_input_bytes += input_bytes;
        stats.total_output_bytes += output_bytes;
        stats.last_updated = Utc::now();
        stats.uptime_seconds = self.start_time.elapsed().as_secs();
    }
    
    async fn process_batch_job(
        &self,
        job_id: String,
        files: Vec<JsonValue>,
        output_format: String,
        parallel_jobs: usize,
        continue_on_error: bool
    ) {
        use futures::stream::{self, StreamExt};
        
        let semaphore = Arc::new(tokio::sync::Semaphore::new(parallel_jobs));
        
        let results: Vec<_> = stream::iter(files.into_iter().enumerate())
            .map(|(index, file)| {
                let sem = semaphore.clone();
                let server = self.clone();
                let output_format = output_format.clone();
                let job_id = job_id.clone();
                
                async move {
                    let _permit = sem.acquire().await.unwrap();
                    
                    let filename = file["filename"].as_str().unwrap_or(&format!("file_{}", index));
                    let content = file["content"].as_str().unwrap_or("");
                    let format = file["format"].as_str();
                    
                    // Convert single file
                    let args = json!({
                        "input_content": content,
                        "input_format": format.unwrap_or("auto"),
                        "output_format": output_format,
                    });
                    
                    let start_time = Instant::now();
                    let result = match server.handle_convert_file(args).await {
                        Ok(response) => ConversionResult {
                            filename: filename.to_string(),
                            input_format: format.unwrap_or("auto").to_string(),
                            output_format: output_format.clone(),
                            success: true,
                            error_message: None,
                            output_size: response["metadata"]["output_size"].as_u64().unwrap_or(0) as usize,
                            processing_time_ms: start_time.elapsed().as_millis() as u64,
                        },
                        Err(e) => ConversionResult {
                            filename: filename.to_string(),
                            input_format: format.unwrap_or("auto").to_string(),
                            output_format: output_format.clone(),
                            success: false,
                            error_message: Some(e),
                            output_size: 0,
                            processing_time_ms: start_time.elapsed().as_millis() as u64,
                        },
                    };
                    
                    // Update job progress
                    server.update_job_progress(&job_id, &result).await;
                    
                    if !result.success && !continue_on_error {
                        Err(result)
                    } else {
                        Ok(result)
                    }
                }
            })
            .buffer_unordered(parallel_jobs)
            .collect()
            .await;
        
        // Update final job status
        self.finalize_job(&job_id, results).await;
    }
    
    async fn update_job_progress(&self, job_id: &str, result: &ConversionResult) {
        let mut jobs = self.active_jobs.write().await;
        if let Some(job) = jobs.get_mut(job_id) {
            job.processed_files += 1;
            if result.success {
                job.successful_conversions += 1;
            } else {
                job.failed_conversions += 1;
            }
            job.results.push(result.clone());
        }
    }
    
    async fn finalize_job(&self, job_id: &str, results: Vec<Result<ConversionResult, ConversionResult>>) {
        let mut jobs = self.active_jobs.write().await;
        if let Some(job) = jobs.get_mut(job_id) {
            job.status = if job.failed_conversions == 0 {
                JobStatus::Completed
            } else if job.successful_conversions == 0 {
                JobStatus::Failed
            } else {
                JobStatus::Completed // Partial success
            };
        }
    }

    // ========================================
    // MCP RESOURCES
    // ========================================
    
    pub async fn get_supported_formats(&self) -> JsonValue {
        let formats = self.format_manager.supported_formats();
        let format_definitions: Vec<FormatDefinition> = formats.into_iter().map(|f| {
            FormatDefinition {
                id: format!("{:?}", f).to_lowercase(),
                name: format!("{:?}", f),
                description: match f {
                    FormatType::Doc => "Microsoft Word 97-2003 format".to_string(),
                    FormatType::WordPerfect => "Corel WordPerfect document".to_string(),
                    FormatType::DBase => "dBase database file".to_string(),
                    FormatType::WordStar => "WordStar document".to_string(),
                    FormatType::Lotus123 => "Lotus 1-2-3 spreadsheet".to_string(),
                    FormatType::Unknown => "Unknown format".to_string(),
                },
                extensions: match f {
                    FormatType::Doc => vec!["doc".to_string()],
                    FormatType::WordPerfect => vec!["wpd".to_string(), "wp".to_string()],
                    FormatType::DBase => vec!["dbf".to_string(), "db3".to_string()],
                    FormatType::WordStar => vec!["ws".to_string(), "wsd".to_string()],
                    FormatType::Lotus123 => vec!["wk1".to_string(), "123".to_string()],
                    FormatType::Unknown => vec![],
                },
                mime_types: vec![],
                can_convert_to: vec!["markdown".to_string(), "rtf".to_string()],
                conversion_quality: HashMap::from([
                    ("markdown".to_string(), "excellent".to_string()),
                    ("rtf".to_string(), "good".to_string()),
                ]),
            }
        }).collect();
        
        json!(format_definitions)
    }
    
    pub async fn get_server_stats(&self) -> JsonValue {
        let stats = self.stats.read().await;
        json!({
            "total_conversions": stats.total_conversions,
            "total_input_bytes": stats.total_input_bytes,
            "total_output_bytes": stats.total_output_bytes,
            "active_jobs": stats.active_jobs,
            "uptime_seconds": self.start_time.elapsed().as_secs(),
            "last_updated": stats.last_updated,
        })
    }
    
    pub async fn get_active_jobs(&self) -> JsonValue {
        let jobs = self.active_jobs.read().await;
        let job_summaries: Vec<_> = jobs.values().map(|job| {
            json!({
                "id": job.id,
                "status": format!("{:?}", job.status),
                "progress": format!("{}/{}", job.processed_files, job.total_files),
                "start_time": job.start_time
            })
        }).collect();
        
        json!(job_summaries)
    }
}