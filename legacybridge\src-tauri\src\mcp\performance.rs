use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::sync::{<PERSON>, Mutex};
use std::time::{Duration, Instant};
use std::collections::HashMap;
use tokio::sync::Semaphore;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    pub thread_pool_size: usize,
    pub max_concurrent_requests: usize,
    pub request_timeout: u64, // seconds
    pub cache_size: usize, // MB
    pub cache_ttl: u64, // seconds
    pub enable_compression: bool,
    pub compression_level: u32,
    pub batch_processing: BatchConfig,
    pub memory_limits: MemoryLimits,
    pub cpu_limits: CpuLimits,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchConfig {
    pub enabled: bool,
    pub max_batch_size: usize,
    pub batch_timeout: u64, // milliseconds
    pub parallel_batches: usize,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MemoryLimits {
    pub max_heap_size: usize, // MB
    pub max_request_size: usize, // MB
    pub max_response_size: usize, // MB
    pub gc_threshold: f64, // percentage
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpuLimits {
    pub max_cpu_percent: f64,
    pub priority: ProcessPriority,
    pub affinity_mask: Option<Vec<usize>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum ProcessPriority {
    Low,
    Normal,
    High,
    Realtime,
}

#[derive(Debug, Clone)]
pub struct PerformanceMetrics {
    pub requests_per_second: f64,
    pub average_latency: f64,
    pub p95_latency: f64,
    pub p99_latency: f64,
    pub memory_usage: usize,
    pub cpu_usage: f64,
    pub cache_hit_rate: f64,
    pub error_rate: f64,
}

pub struct PerformanceManager {
    config: PerformanceConfig,
    semaphore: Arc<Semaphore>,
    metrics: Arc<Mutex<MetricsCollector>>,
    cache: Arc<Mutex<LruCache>>,
}

struct MetricsCollector {
    request_times: Vec<Duration>,
    request_count: u64,
    error_count: u64,
    cache_hits: u64,
    cache_misses: u64,
    start_time: Instant,
}

struct LruCache {
    capacity: usize,
    items: HashMap<String, CacheItem>,
    access_order: Vec<String>,
}

struct CacheItem {
    data: Vec<u8>,
    created_at: Instant,
    access_count: u64,
}

impl PerformanceManager {
    pub fn new(config: PerformanceConfig) -> Result<Self> {
        let semaphore = Arc::new(Semaphore::new(config.max_concurrent_requests));
        let metrics = Arc::new(Mutex::new(MetricsCollector::new()));
        let cache = Arc::new(Mutex::new(LruCache::new(config.cache_size)));

        Ok(Self {
            config,
            semaphore,
            metrics,
            cache,
        })
    }

    pub async fn acquire_permit(&self) -> Result<tokio::sync::SemaphorePermit<'_>> {
        self.semaphore.acquire().await
            .context("Failed to acquire semaphore permit")
    }

    pub fn record_request(&self, duration: Duration, success: bool) {
        if let Ok(mut metrics) = self.metrics.lock() {
            metrics.request_count += 1;
            metrics.request_times.push(duration);
            if !success {
                metrics.error_count += 1;
            }
            
            // Keep only last 1000 request times for metrics
            if metrics.request_times.len() > 1000 {
                metrics.request_times.remove(0);
            }
        }
    }

    pub fn get_from_cache(&self, key: &str) -> Option<Vec<u8>> {
        if let Ok(mut cache) = self.cache.lock() {
            if let Some(item) = cache.items.get_mut(key) {
                let age = item.created_at.elapsed().as_secs();
                if age < self.config.cache_ttl {
                    item.access_count += 1;
                    if let Ok(mut metrics) = self.metrics.lock() {
                        metrics.cache_hits += 1;
                    }
                    return Some(item.data.clone());
                } else {
                    // Expired, remove it
                    cache.items.remove(key);
                }
            }
        }
        
        if let Ok(mut metrics) = self.metrics.lock() {
            metrics.cache_misses += 1;
        }
        None
    }

    pub fn put_in_cache(&self, key: String, data: Vec<u8>) {
        if let Ok(mut cache) = self.cache.lock() {
            // Check size limit
            let size_mb = data.len() / (1024 * 1024);
            if size_mb > self.config.cache_size {
                return;
            }

            // Evict if necessary
            while cache.items.len() >= cache.capacity {
                if let Some(oldest_key) = cache.access_order.first().cloned() {
                    cache.items.remove(&oldest_key);
                    cache.access_order.remove(0);
                }
            }

            cache.items.insert(key.clone(), CacheItem {
                data,
                created_at: Instant::now(),
                access_count: 0,
            });
            cache.access_order.push(key);
        }
    }

    pub fn get_metrics(&self) -> PerformanceMetrics {
        let mut metrics = PerformanceMetrics {
            requests_per_second: 0.0,
            average_latency: 0.0,
            p95_latency: 0.0,
            p99_latency: 0.0,
            memory_usage: 0,
            cpu_usage: 0.0,
            cache_hit_rate: 0.0,
            error_rate: 0.0,
        };

        if let Ok(collector) = self.metrics.lock() {
            let elapsed = collector.start_time.elapsed().as_secs_f64();
            if elapsed > 0.0 {
                metrics.requests_per_second = collector.request_count as f64 / elapsed;
            }

            if !collector.request_times.is_empty() {
                let mut times: Vec<_> = collector.request_times.iter()
                    .map(|d| d.as_millis() as f64)
                    .collect();
                times.sort_by(|a, b| a.partial_cmp(b).unwrap());

                let sum: f64 = times.iter().sum();
                metrics.average_latency = sum / times.len() as f64;

                let p95_idx = (times.len() as f64 * 0.95) as usize;
                let p99_idx = (times.len() as f64 * 0.99) as usize;
                
                if p95_idx < times.len() {
                    metrics.p95_latency = times[p95_idx];
                }
                if p99_idx < times.len() {
                    metrics.p99_latency = times[p99_idx];
                }
            }

            let total_cache_accesses = collector.cache_hits + collector.cache_misses;
            if total_cache_accesses > 0 {
                metrics.cache_hit_rate = collector.cache_hits as f64 / total_cache_accesses as f64;
            }

            if collector.request_count > 0 {
                metrics.error_rate = collector.error_count as f64 / collector.request_count as f64;
            }
        }

        // Get actual memory usage
        metrics.memory_usage = self.get_memory_usage();
        metrics.cpu_usage = self.get_cpu_usage();

        metrics
    }

    fn get_memory_usage(&self) -> usize {
        // In a real implementation, this would use system APIs
        // For now, return a placeholder
        0
    }

    fn get_cpu_usage(&self) -> f64 {
        // In a real implementation, this would use system APIs
        // For now, return a placeholder
        0.0
    }

    pub fn should_compress(&self, data_size: usize) -> bool {
        self.config.enable_compression && data_size > 1024 // Compress if > 1KB
    }

    pub fn compress_data(&self, data: &[u8]) -> Result<Vec<u8>> {
        use flate2::write::GzEncoder;
        use flate2::Compression;
        use std::io::Write;

        let level = match self.config.compression_level {
            0..=3 => Compression::fast(),
            4..=6 => Compression::default(),
            _ => Compression::best(),
        };

        let mut encoder = GzEncoder::new(Vec::new(), level);
        encoder.write_all(data)?;
        encoder.finish().context("Failed to compress data")
    }

    pub fn decompress_data(&self, data: &[u8]) -> Result<Vec<u8>> {
        use flate2::read::GzDecoder;
        use std::io::Read;

        let mut decoder = GzDecoder::new(data);
        let mut result = Vec::new();
        decoder.read_to_end(&mut result)?;
        Ok(result)
    }
}

impl MetricsCollector {
    fn new() -> Self {
        Self {
            request_times: Vec::new(),
            request_count: 0,
            error_count: 0,
            cache_hits: 0,
            cache_misses: 0,
            start_time: Instant::now(),
        }
    }
}

impl LruCache {
    fn new(capacity_mb: usize) -> Self {
        // Convert MB to number of items (rough estimate)
        let capacity = capacity_mb * 100; // Assume ~10KB per item average
        
        Self {
            capacity,
            items: HashMap::new(),
            access_order: Vec::new(),
        }
    }
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            thread_pool_size: num_cpus::get(),
            max_concurrent_requests: 100,
            request_timeout: 30,
            cache_size: 100, // 100 MB
            cache_ttl: 3600, // 1 hour
            enable_compression: true,
            compression_level: 6,
            batch_processing: BatchConfig {
                enabled: false,
                max_batch_size: 100,
                batch_timeout: 100,
                parallel_batches: 4,
            },
            memory_limits: MemoryLimits {
                max_heap_size: 512, // 512 MB
                max_request_size: 10, // 10 MB
                max_response_size: 50, // 50 MB
                gc_threshold: 0.8,
            },
            cpu_limits: CpuLimits {
                max_cpu_percent: 80.0,
                priority: ProcessPriority::Normal,
                affinity_mask: None,
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_performance_config_default() {
        let config = PerformanceConfig::default();
        assert_eq!(config.thread_pool_size, num_cpus::get());
        assert_eq!(config.max_concurrent_requests, 100);
        assert_eq!(config.cache_size, 100);
        assert!(config.enable_compression);
    }

    #[tokio::test]
    async fn test_semaphore_limiting() {
        let config = PerformanceConfig {
            max_concurrent_requests: 2,
            ..Default::default()
        };
        
        let manager = PerformanceManager::new(config).unwrap();
        
        let _permit1 = manager.acquire_permit().await.unwrap();
        let _permit2 = manager.acquire_permit().await.unwrap();
        
        // Third permit should block
        let result = tokio::time::timeout(
            Duration::from_millis(100),
            manager.acquire_permit()
        ).await;
        
        assert!(result.is_err()); // Timeout
    }

    #[test]
    fn test_cache_operations() {
        let config = PerformanceConfig::default();
        let manager = PerformanceManager::new(config).unwrap();
        
        let key = "test_key";
        let data = vec![1, 2, 3, 4, 5];
        
        // Cache miss
        assert!(manager.get_from_cache(key).is_none());
        
        // Put in cache
        manager.put_in_cache(key.to_string(), data.clone());
        
        // Cache hit
        let cached = manager.get_from_cache(key);
        assert_eq!(cached, Some(data));
    }

    #[test]
    fn test_compression() {
        let config = PerformanceConfig::default();
        let manager = PerformanceManager::new(config).unwrap();
        
        let data = b"Hello, World! This is a test string for compression.";
        
        let compressed = manager.compress_data(data).unwrap();
        assert!(compressed.len() < data.len());
        
        let decompressed = manager.decompress_data(&compressed).unwrap();
        assert_eq!(decompressed, data);
    }
}