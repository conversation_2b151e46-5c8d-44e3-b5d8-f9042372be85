# Azure Infrastructure Deployment Guide

## Overview

This directory contains Azure Resource Manager (ARM) templates for deploying LegacyBridge on Azure with enterprise-grade infrastructure.

## Architecture

The infrastructure includes:
- **Virtual Network**: Multi-subnet VNet with service endpoints
- **AKS**: Managed Kubernetes cluster with auto-scaling
- **Azure Database for PostgreSQL**: Flexible Server with high availability
- **Azure Cache for Redis**: Managed Redis for caching
- **Storage Account**: Blob storage for files
- **Container Registry**: Azure Container Registry for Docker images
- **Key Vault**: Secure secret storage
- **Application Insights**: Application monitoring and diagnostics

## Prerequisites

1. Azure CLI installed and configured
2. kubectl installed
3. Sufficient Azure permissions (Contributor role)
4. Resource Group created

## Deployment Steps

### 1. Create Resource Group

```bash
# Set variables
RESOURCE_GROUP="legacybridge-production"
LOCATION="eastus"

# Create resource group
az group create \
  --name $RESOURCE_GROUP \
  --location $LOCATION
```

### 2. Deploy the ARM Template

```bash
# Deploy using the template and parameters
az deployment group create \
  --resource-group $RESOURCE_GROUP \
  --template-file legacybridge-infrastructure.json \
  --parameters @parameters.json \
  --parameters administratorLoginPassword="<SECURE_PASSWORD>"
```

### 3. Get AKS Credentials

```bash
# Get AKS cluster name
AKS_CLUSTER=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name legacybridge-infrastructure \
  --query properties.outputs.aksClusterName.value \
  --output tsv)

# Get AKS credentials
az aks get-credentials \
  --resource-group $RESOURCE_GROUP \
  --name $AKS_CLUSTER
```

### 4. Configure Container Registry

```bash
# Get ACR name
ACR_NAME=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name legacybridge-infrastructure \
  --query properties.outputs.containerRegistryName.value \
  --output tsv)

# Attach ACR to AKS
az aks update \
  --resource-group $RESOURCE_GROUP \
  --name $AKS_CLUSTER \
  --attach-acr $ACR_NAME
```

### 5. Deploy Kubernetes Resources

```bash
# Apply Kubernetes manifests
kubectl apply -f ../../k8s/
```

### 6. Configure Application Secrets

```bash
# Get connection strings
POSTGRES_FQDN=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name legacybridge-infrastructure \
  --query properties.outputs.postgresqlFQDN.value \
  --output tsv)

REDIS_HOST=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name legacybridge-infrastructure \
  --query properties.outputs.redisHostName.value \
  --output tsv)

REDIS_KEY=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name legacybridge-infrastructure \
  --query properties.outputs.redisPrimaryKey.value \
  --output tsv)

# Create Kubernetes secrets
kubectl create secret generic legacybridge-secrets \
  --from-literal=database-url="postgresql://legacyadmin:<PASSWORD>@${POSTGRES_FQDN}:5432/legacybridge?sslmode=require" \
  --from-literal=redis-url="rediss://:${REDIS_KEY}@${REDIS_HOST}:6380/0" \
  --namespace legacybridge
```

## Template Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| environment | production | Environment name |
| location | [resourceGroup().location] | Azure region |
| vnetAddressPrefix | 10.0.0.0/16 | Virtual network CIDR |
| aksNodeCount | 3 | Initial number of AKS nodes |
| aksNodeVMSize | Standard_DS2_v2 | VM size for AKS nodes |
| postgresqlVersion | 15 | PostgreSQL version |
| postgresqlSkuName | GP_Gen5_2 | PostgreSQL SKU |
| administratorLogin | legacyadmin | Database admin username |
| administratorLoginPassword | - | Database admin password (required) |

## Outputs

The ARM template provides the following outputs:

- `aksClusterName`: AKS cluster name
- `aksResourceId`: AKS resource ID
- `containerRegistryName`: ACR name
- `containerRegistryLoginServer`: ACR login server URL
- `postgresqlServerName`: PostgreSQL server name
- `postgresqlFQDN`: PostgreSQL fully qualified domain name
- `redisHostName`: Redis hostname
- `redisPrimaryKey`: Redis primary access key
- `storageAccountName`: Storage account name
- `storageAccountKey`: Storage account key
- `keyVaultName`: Key Vault name
- `appInsightsInstrumentationKey`: Application Insights key

## Cost Optimization

1. **Use Spot VMs**: Configure AKS node pools with spot instances
2. **Reserved Instances**: Purchase reservations for predictable workloads
3. **Auto-scaling**: Configure cluster autoscaler and VMSS
4. **Right-sizing**: Monitor and adjust VM sizes based on usage

## Security Best Practices

1. **Network Security**:
   - Service endpoints for secure connectivity
   - Network policies in AKS
   - Private endpoints for data services

2. **Identity & Access**:
   - Managed identities for Azure resources
   - Azure AD integration for AKS
   - RBAC for resource access

3. **Data Protection**:
   - Encryption at rest for all data services
   - SSL/TLS enforcement
   - Key Vault for secret management

## Monitoring

1. **Application Insights**: Application performance monitoring
2. **Azure Monitor**: Infrastructure monitoring
3. **Log Analytics**: Centralized logging
4. **Container Insights**: AKS-specific monitoring

## Troubleshooting

### Deployment Failures

```bash
# Check deployment status
az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name legacybridge-infrastructure

# View deployment operations
az deployment operation group list \
  --resource-group $RESOURCE_GROUP \
  --name legacybridge-infrastructure
```

### AKS Connection Issues

```bash
# Check AKS status
az aks show \
  --resource-group $RESOURCE_GROUP \
  --name $AKS_CLUSTER

# Get node status
kubectl get nodes
```

### Database Connection Issues

```bash
# Test PostgreSQL connection
kubectl run -it --rm debug --image=postgres:15 --restart=Never -- \
  psql "host=${POSTGRES_FQDN} port=5432 dbname=legacybridge user=legacyadmin sslmode=require"
```

## Cleanup

To delete all resources:

```bash
# Delete Kubernetes namespace
kubectl delete namespace legacybridge

# Delete resource group (this deletes all resources)
az group delete \
  --name $RESOURCE_GROUP \
  --yes
```

**WARNING**: This will delete all resources including databases and storage!