# Google Cloud Infrastructure Deployment Guide

## Overview

This directory contains Google Cloud Deployment Manager templates for deploying LegacyBridge on GCP with enterprise-grade infrastructure.

## Architecture

The infrastructure includes:
- **VPC Network**: Custom VPC with subnets for GKE
- **GKE**: Managed Kubernetes cluster with auto-scaling
- **Cloud SQL PostgreSQL**: High-availability PostgreSQL instance
- **Memorystore Redis**: Managed Redis for caching
- **Cloud Storage**: Object storage for files
- **Container Registry**: GCR for Docker images

## Prerequisites

1. Google Cloud SDK (gcloud) installed and configured
2. kubectl installed
3. Appropriate IAM permissions
4. APIs enabled: Compute, Container, SQL, Redis, Storage

## Deployment Steps

### 1. Set Project and Enable APIs

```bash
# Set your project
export PROJECT_ID="your-project-id"
export REGION="us-central1"
export ZONE="us-central1-a"

gcloud config set project $PROJECT_ID

# Enable required APIs
gcloud services enable \
  compute.googleapis.com \
  container.googleapis.com \
  sqladmin.googleapis.com \
  redis.googleapis.com \
  storage.googleapis.com \
  containerregistry.googleapis.com \
  deploymentmanager.googleapis.com
```

### 2. Deploy Infrastructure

```bash
# Create deployment
gcloud deployment-manager deployments create legacybridge-production \
  --config legacybridge-infrastructure.yaml
```

### 3. Get GKE Credentials

```bash
# Get cluster name
CLUSTER_NAME=$(gcloud deployment-manager deployments describe legacybridge-production \
  --format="value(outputs[?name=='gkeClusterName'].finalValue)")

# Get credentials
gcloud container clusters get-credentials $CLUSTER_NAME \
  --zone $ZONE
```

### 4. Configure Container Registry

```bash
# Configure Docker authentication
gcloud auth configure-docker

# Tag and push images
docker tag legacybridge/frontend:latest gcr.io/$PROJECT_ID/legacybridge-frontend:latest
docker tag legacybridge/backend:latest gcr.io/$PROJECT_ID/legacybridge-backend:latest

docker push gcr.io/$PROJECT_ID/legacybridge-frontend:latest
docker push gcr.io/$PROJECT_ID/legacybridge-backend:latest
```

### 5. Deploy Kubernetes Resources

```bash
# Update image references in Kubernetes manifests
sed -i "s|legacybridge/frontend:.*|gcr.io/$PROJECT_ID/legacybridge-frontend:latest|g" ../../k8s/deployment.yaml
sed -i "s|legacybridge/backend:.*|gcr.io/$PROJECT_ID/legacybridge-backend:latest|g" ../../k8s/deployment.yaml

# Apply manifests
kubectl apply -f ../../k8s/
```

### 6. Configure Application Secrets

```bash
# Get Cloud SQL connection details
SQL_INSTANCE=$(gcloud deployment-manager deployments describe legacybridge-production \
  --format="value(outputs[?name=='postgresInstanceName'].finalValue)")

SQL_CONNECTION=$(gcloud deployment-manager deployments describe legacybridge-production \
  --format="value(outputs[?name=='postgresConnectionName'].finalValue)")

# Get Redis details
REDIS_HOST=$(gcloud deployment-manager deployments describe legacybridge-production \
  --format="value(outputs[?name=='redisHost'].finalValue)")

# Create service account for Cloud SQL proxy
gcloud iam service-accounts create legacybridge-sql-proxy \
  --display-name="LegacyBridge SQL Proxy"

# Grant permissions
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:legacybridge-sql-proxy@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/cloudsql.client"

# Create key
gcloud iam service-accounts keys create sql-proxy-key.json \
  --iam-account=legacybridge-sql-proxy@$PROJECT_ID.iam.gserviceaccount.com

# Create Kubernetes secret for Cloud SQL proxy
kubectl create secret generic cloudsql-instance-credentials \
  --from-file=credentials.json=sql-proxy-key.json \
  --namespace legacybridge

# Create application secrets
kubectl create secret generic legacybridge-secrets \
  --from-literal=database-url="postgresql://legacybridge:PASSWORD@127.0.0.1:5432/legacybridge" \
  --from-literal=redis-url="redis://$REDIS_HOST:6379" \
  --from-literal=cloud-sql-instance=$SQL_CONNECTION \
  --namespace legacybridge
```

### 7. Deploy Cloud SQL Proxy Sidecar

Add this to your backend deployment:

```yaml
containers:
- name: cloud-sql-proxy
  image: gcr.io/cloudsql-docker/gce-proxy:latest
  command:
    - "/cloud_sql_proxy"
    - "-instances=$(CLOUD_SQL_INSTANCE)=tcp:5432"
    - "-credential_file=/secrets/cloudsql/credentials.json"
  env:
  - name: CLOUD_SQL_INSTANCE
    valueFrom:
      secretKeyRef:
        name: legacybridge-secrets
        key: cloud-sql-instance
  volumeMounts:
  - name: cloudsql-instance-credentials
    mountPath: /secrets/cloudsql
    readOnly: true
```

## Configuration Files

### Main Configuration (legacybridge-infrastructure.yaml)

The main configuration orchestrates all components and defines the infrastructure topology.

### Templates

- **network.jinja**: VPC, subnets, firewall rules
- **gke-cluster.jinja**: GKE cluster with node pools
- **cloud-sql.jinja**: PostgreSQL instance configuration
- **memorystore.jinja**: Redis instance configuration
- **storage.jinja**: Cloud Storage bucket
- **container-registry.jinja**: GCR setup

## Cost Optimization

1. **Preemptible VMs**: Add preemptible node pools for non-critical workloads
2. **Committed Use Discounts**: Purchase CUDs for predictable workloads
3. **Right-sizing**: Use GKE recommendations for node sizing
4. **Storage Classes**: Use appropriate storage classes (Nearline/Coldline)

## Security Best Practices

1. **Network Security**:
   - Private GKE nodes with Cloud NAT
   - VPC-native networking
   - Network policies enabled

2. **Identity & Access**:
   - Workload Identity for GKE
   - Service accounts with minimal permissions
   - Binary Authorization for container images

3. **Data Protection**:
   - Encryption at rest by default
   - Cloud SQL require SSL
   - Private service connections

## Monitoring

1. **Cloud Monitoring**: Metrics and dashboards
2. **Cloud Logging**: Centralized log management
3. **Cloud Trace**: Distributed tracing
4. **Cloud Profiler**: Application profiling

## Troubleshooting

### Deployment Issues

```bash
# Check deployment status
gcloud deployment-manager deployments describe legacybridge-production

# View deployment manifest
gcloud deployment-manager manifests describe \
  --deployment legacybridge-production \
  --format="value(config.content)"
```

### GKE Connection Issues

```bash
# Check cluster status
gcloud container clusters describe $CLUSTER_NAME --zone $ZONE

# Get node status
kubectl get nodes
```

### Cloud SQL Connection Issues

```bash
# Check Cloud SQL instance
gcloud sql instances describe $SQL_INSTANCE

# Test connection through proxy
kubectl run -it --rm debug --image=postgres:15 --restart=Never -- \
  psql "host=127.0.0.1 port=5432 dbname=legacybridge user=legacybridge"
```

## Cleanup

To delete all resources:

```bash
# Delete Kubernetes namespace
kubectl delete namespace legacybridge

# Delete deployment
gcloud deployment-manager deployments delete legacybridge-production --quiet
```

**WARNING**: This will delete all resources including databases and storage!