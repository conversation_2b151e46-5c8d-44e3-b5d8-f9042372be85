'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Build,
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  Download,
  RefreshCw,
  Terminal,
  Package,
  Cpu,
  HardDrive
} from 'lucide-react';

import { 
  BuildStatus, 
  DLLConfiguration,
  BuildStage,
  getBuildStageDisplay,
  getArchitectureDisplay
} from '@/lib/dll/dll-config';

interface BuildProgressProps {
  status: BuildStatus | null;
  config: DLLConfiguration;
  isBuilding: boolean;
  onRebuild: () => void;
}

export function BuildProgress({
  status,
  config,
  isBuilding,
  onRebuild
}: BuildProgressProps) {
  if (!status) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <Build className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Build Started</h3>
            <p className="text-muted-foreground mb-4">
              Configure your DLL settings and click "Build DLL" to start
            </p>
            <Button variant="outline" disabled>
              Waiting for Build
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const duration = status.endTime 
    ? new Date(status.endTime).getTime() - new Date(status.startTime).getTime()
    : Date.now() - new Date(status.startTime).getTime();

  const formatDuration = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return minutes > 0 
      ? `${minutes}m ${remainingSeconds}s`
      : `${seconds}s`;
  };

  return (
    <div className="space-y-6">
      {/* Build Status Summary */}
      <Card className={
        status.stage === 'completed' && status.success
          ? "border-green-200 bg-green-50 dark:bg-green-950/20"
          : status.stage === 'failed'
          ? "border-red-200 bg-red-50 dark:bg-red-950/20"
          : ""
      }>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              {status.stage === 'completed' && status.success ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : status.stage === 'failed' ? (
                <XCircle className="w-5 h-5 text-red-600" />
              ) : (
                <Clock className="w-5 h-5 text-blue-600 animate-pulse" />
              )}
              {getBuildStageDisplay(status.stage)}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                Build ID: {status.buildId.slice(0, 8)}
              </Badge>
              <Badge variant={status.stage === 'completed' && status.success ? 'success' : 
                            status.stage === 'failed' ? 'destructive' : 'default'}>
                {formatDuration(duration)}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Progress Bar */}
          {status.stage !== 'completed' && status.stage !== 'failed' && (
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">{status.currentStep}</span>
                <span className="text-sm text-muted-foreground">{status.progress}%</span>
              </div>
              <Progress value={status.progress} className="h-3" />
            </div>
          )}

          {/* Error Message */}
          {status.error && (
            <Alert variant="destructive" className="mb-4">
              <XCircle className="h-4 w-4" />
              <AlertDescription>{status.error}</AlertDescription>
            </Alert>
          )}

          {/* Success Info */}
          {status.stage === 'completed' && status.success && (
            <div className="space-y-4">
              <Alert className="border-green-200 bg-green-50 dark:bg-green-950/20">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800 dark:text-green-200">
                  DLL build completed successfully! Your libraries are ready for testing and deployment.
                </AlertDescription>
              </Alert>

              {/* Output Files */}
              <div>
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Package className="w-4 h-4" />
                  Generated Files
                </h4>
                <div className="space-y-2">
                  {status.outputFiles.map((file, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center justify-between p-3 bg-muted rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <FileText className="w-4 h-4 text-muted-foreground" />
                        <span className="font-mono text-sm">{file}</span>
                      </div>
                      <Button size="sm" variant="ghost">
                        <Download className="w-4 h-4" />
                      </Button>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Build Actions */}
          <div className="flex items-center justify-between mt-6">
            <Button
              variant="outline"
              onClick={onRebuild}
              disabled={isBuilding}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Rebuild
            </Button>
            
            {status.logs.length > 0 && (
              <BuildLogsDialog logs={status.logs} />
            )}
          </div>
        </CardContent>
      </Card>

      {/* Build Configuration Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Build Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <BuildConfigItem
              icon={Cpu}
              label="Architecture"
              value={config.architectures.map(getArchitectureDisplay).join(', ')}
            />
            <BuildConfigItem
              icon={Build}
              label="Optimization"
              value={config.optimization}
            />
            <BuildConfigItem
              icon={FileText}
              label="Formats"
              value={`${config.includedFormats.length} included`}
            />
            <BuildConfigItem
              icon={HardDrive}
              label="Static Linking"
              value={config.staticLinking ? 'Enabled' : 'Disabled'}
            />
          </div>
        </CardContent>
      </Card>

      {/* Build Stages Timeline */}
      {status.stage !== 'preparing' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Build Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <BuildTimeline status={status} />
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Supporting Components
interface BuildConfigItemProps {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  value: string;
}

function BuildConfigItem({ icon: Icon, label, value }: BuildConfigItemProps) {
  return (
    <div className="flex items-start gap-3">
      <Icon className="w-4 h-4 text-muted-foreground mt-0.5" />
      <div>
        <p className="text-sm text-muted-foreground">{label}</p>
        <p className="font-medium">{value}</p>
      </div>
    </div>
  );
}

interface BuildLogsDialogProps {
  logs: string[];
}

function BuildLogsDialog({ logs }: BuildLogsDialogProps) {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <>
      <Button
        size="sm"
        variant="outline"
        onClick={() => setIsOpen(true)}
      >
        <Terminal className="w-4 h-4 mr-1" />
        View Logs
      </Button>

      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
          <Card className="w-full max-w-3xl max-h-[80vh] flex flex-col">
            <CardHeader>
              <CardTitle>Build Logs</CardTitle>
            </CardHeader>
            <CardContent className="flex-1 overflow-auto">
              <pre className="font-mono text-xs whitespace-pre-wrap bg-muted p-4 rounded-lg">
                {logs.join('\n')}
              </pre>
            </CardContent>
            <div className="p-4 border-t">
              <Button onClick={() => setIsOpen(false)}>Close</Button>
            </div>
          </Card>
        </div>
      )}
    </>
  );
}

interface BuildTimelineProps {
  status: BuildStatus;
}

function BuildTimeline({ status }: BuildTimelineProps) {
  const stages: BuildStage[] = ['preparing', 'compiling', 'linking', 'packaging', 'completed'];
  const currentStageIndex = stages.indexOf(status.stage);

  return (
    <div className="relative">
      {stages.map((stage, index) => {
        const isCompleted = index < currentStageIndex || 
                          (stage === 'completed' && status.stage === 'completed');
        const isCurrent = stage === status.stage && status.stage !== 'failed';
        const isFailed = status.stage === 'failed' && index === currentStageIndex;

        return (
          <div key={stage} className="flex items-center mb-4 last:mb-0">
            <div className="relative">
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center
                ${isCompleted ? 'bg-green-600 text-white' :
                  isCurrent ? 'bg-blue-600 text-white animate-pulse' :
                  isFailed ? 'bg-red-600 text-white' :
                  'bg-muted text-muted-foreground'}
              `}>
                {isCompleted ? (
                  <CheckCircle className="w-4 h-4" />
                ) : isFailed ? (
                  <XCircle className="w-4 h-4" />
                ) : (
                  <span className="text-xs font-bold">{index + 1}</span>
                )}
              </div>
              {index < stages.length - 1 && (
                <div className={`
                  absolute top-8 left-4 w-0.5 h-8
                  ${isCompleted ? 'bg-green-600' : 'bg-muted'}
                `} />
              )}
            </div>
            <div className="ml-4">
              <p className={`font-medium ${isCurrent ? 'text-blue-600' : ''}`}>
                {getBuildStageDisplay(stage)}
              </p>
              {isCurrent && status.currentStep && (
                <p className="text-sm text-muted-foreground">{status.currentStep}</p>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}