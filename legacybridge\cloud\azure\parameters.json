{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"environment": {"value": "production"}, "location": {"value": "eastus"}, "vnetAddressPrefix": {"value": "10.0.0.0/16"}, "aksNodeCount": {"value": 3}, "aksNodeVMSize": {"value": "Standard_DS2_v2"}, "postgresqlVersion": {"value": "15"}, "postgresqlSkuName": {"value": "GP_Gen5_2"}, "administratorLogin": {"value": "<PERSON><PERSON><PERSON>"}, "administratorLoginPassword": {"value": "CHANGE_ME_TO_SECURE_PASSWORD"}}}