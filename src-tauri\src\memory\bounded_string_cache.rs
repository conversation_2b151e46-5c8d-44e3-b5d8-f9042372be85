// Bounded String Cache Implementation
// Based on CURSOR-08-PERFORMANCE-OPTIMIZATION.MD specifications

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};

#[derive(Clone)]
struct StringCacheEntry {
    value: Arc<String>,
    last_accessed: Instant,
    access_count: u64,
}

pub struct BoundedStringCache {
    cache: HashMap<String, StringCacheEntry>,
    max_size: usize,
    cleanup_threshold: Duration,
}

impl BoundedStringCache {
    pub fn new(max_size: usize) -> Self {
        Self {
            cache: HashMap::with_capacity(max_size),
            max_size,
            cleanup_threshold: Duration::from_secs(300), // 5 minutes
        }
    }

    pub fn get_or_insert(&mut self, key: &str) -> Arc<String> {
        // Cleanup old entries if needed
        if self.cache.len() >= self.max_size {
            self.cleanup_old_entries();
        }

        match self.cache.get_mut(key) {
            Some(entry) => {
                entry.last_accessed = Instant::now();
                entry.access_count += 1;
                entry.value.clone()
            }
            None => {
                let value = Arc::new(key.to_string());
                self.cache.insert(key.to_string(), StringCacheEntry {
                    value: value.clone(),
                    last_accessed: Instant::now(),
                    access_count: 1,
                });
                value
            }
        }
    }

    fn cleanup_old_entries(&mut self) {
        let now = Instant::now();
        let threshold = self.cleanup_threshold;

        self.cache.retain(|_, entry| {
            now.duration_since(entry.last_accessed) < threshold
        });

        // If still too large, remove least accessed
        while self.cache.len() >= self.max_size {
            let mut min_access_count = u64::MAX;
            let mut key_to_remove = None;

            for (key, entry) in &self.cache {
                if entry.access_count < min_access_count {
                    min_access_count = entry.access_count;
                    key_to_remove = Some(key.clone());
                }
            }

            if let Some(key) = key_to_remove {
                self.cache.remove(&key);
            } else {
                break;
            }
        }
    }

    pub fn get_stats(&self) -> CacheStats {
        let total_access_count: u64 = self.cache.values()
            .map(|entry| entry.access_count)
            .sum();
        
        let memory_usage = self.cache.iter()
            .map(|(key, entry)| key.len() + entry.value.len() + std::mem::size_of::<StringCacheEntry>())
            .sum();

        CacheStats {
            size: self.cache.len(),
            max_size: self.max_size,
            total_access_count,
            memory_usage,
        }
    }

    pub fn clear(&mut self) {
        self.cache.clear();
    }
}

#[derive(Debug)]
pub struct CacheStats {
    pub size: usize,
    pub max_size: usize,
    pub total_access_count: u64,
    pub memory_usage: usize,
}

// Global cache with proper cleanup
use once_cell::sync::Lazy;
use std::sync::Mutex;

static STRING_CACHE: Lazy<Mutex<BoundedStringCache>> = Lazy::new(|| {
    Mutex::new(BoundedStringCache::new(10000))
});

pub fn get_cached_string(key: &str) -> Arc<String> {
    STRING_CACHE.lock().unwrap().get_or_insert(key)
}

pub fn get_cache_stats() -> CacheStats {
    STRING_CACHE.lock().unwrap().get_stats()
}

pub fn clear_cache() {
    STRING_CACHE.lock().unwrap().clear();
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_bounded_cache_size() {
        let mut cache = BoundedStringCache::new(100);
        
        // Fill cache beyond capacity
        for i in 0..200 {
            let key = format!("test_string_{}", i);
            cache.get_or_insert(&key);
        }
        
        // Cache should not exceed max size
        assert!(cache.cache.len() <= 100);
    }

    #[test]
    fn test_cache_access_tracking() {
        let mut cache = BoundedStringCache::new(10);
        
        let key = "test_key";
        let value1 = cache.get_or_insert(key);
        let value2 = cache.get_or_insert(key);
        
        // Should return same Arc instance
        assert!(Arc::ptr_eq(&value1, &value2));
        
        // Access count should be tracked
        let entry = cache.cache.get(key).unwrap();
        assert_eq!(entry.access_count, 2);
    }

    #[test]
    fn test_lru_eviction() {
        let mut cache = BoundedStringCache::new(3);
        
        // Add entries
        cache.get_or_insert("key1");
        cache.get_or_insert("key2");
        cache.get_or_insert("key3");
        
        // Access key1 multiple times to increase its access count
        for _ in 0..5 {
            cache.get_or_insert("key1");
        }
        
        // Add new entry, should evict least accessed
        cache.get_or_insert("key4");
        
        // key1 should still be present due to high access count
        assert!(cache.cache.contains_key("key1"));
        assert_eq!(cache.cache.len(), 3);
    }
}
