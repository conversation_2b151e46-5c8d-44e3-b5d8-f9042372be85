use std::process::Command;

fn main() {
    println!("Testing LegacyBridge API Server...");
    
    // Test 1: Start the API server in background
    println!("Step 1: Building the API server...");
    let build_output = Command::new("cargo")
        .args(&["build", "--features", "api", "--no-default-features", "--bin", "api-server"])
        .output()
        .expect("Failed to execute cargo build");
    
    if !build_output.status.success() {
        println!("Build failed:");
        println!("{}", String::from_utf8_lossy(&build_output.stderr));
        return;
    }
    
    println!("✓ API server built successfully!");
    
    // Test 2: Try to run the API server for a few seconds
    println!("Step 2: Starting API server...");
    let mut child = Command::new("cargo")
        .args(&["run", "--features", "api", "--no-default-features", "--bin", "api-server"])
        .spawn()
        .expect("Failed to start API server");
    
    // Wait a moment for server to start
    std::thread::sleep(std::time::Duration::from_secs(3));
    
    // Test 3: Make HTTP requests to test endpoints
    println!("Step 3: Testing endpoints...");
    
    // Test health endpoint
    println!("Testing health endpoint...");
    match reqwest::blocking::get("http://localhost:8080/health") {
        Ok(response) => {
            println!("✓ Health endpoint responded with status: {}", response.status());
        }
        Err(e) => {
            println!("✗ Health endpoint failed: {}", e);
        }
    }
    
    // Test info endpoint
    println!("Testing info endpoint...");
    match reqwest::blocking::get("http://localhost:8080/info") {
        Ok(response) => {
            println!("✓ Info endpoint responded with status: {}", response.status());
        }
        Err(e) => {
            println!("✗ Info endpoint failed: {}", e);
        }
    }
    
    // Cleanup: kill the server
    let _ = child.kill();
    println!("API server test completed!");
}