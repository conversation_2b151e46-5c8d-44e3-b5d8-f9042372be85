// Quick-Data MCP Integration for LegacyBridge
// Provides advanced analytics on conversion data and document insights

use serde::{Serialize, Deserialize};
use crate::mcp::client::McpClient;
use crate::mcp::types::{IntegrationError, FormatDefinition};
use serde_json::{json, Value as JsonValue};

pub struct QuickDataIntegration {
    client: McpClient,
}

impl QuickDataIntegration {
    pub async fn new() -> Result<Self, IntegrationError> {
        let client = McpClient::connect("quick-data-mcp").await?;
        Ok(Self { client })
    }
    
    /// Analyze conversion patterns and performance
    pub async fn analyze_conversion_metrics(
        &self,
        conversion_data: Vec<ConversionMetric>,
    ) -> Result<ConversionAnalysis, IntegrationError> {
        // Convert conversion data to CSV format for analysis
        let csv_data = self.prepare_conversion_csv(&conversion_data)?;
        
        // Load data into Quick-Data
        let dataset_result = self.client.call_tool("load_csv_data", json!({
            "csv_content": csv_data,
            "dataset_name": "legacybridge_conversions",
            "has_headers": true
        })).await?;
        
        let dataset_id = dataset_result["dataset_id"].as_str()
            .ok_or_else(|| IntegrationError::InvalidResponse("Missing dataset_id".to_string()))?;
        
        // Perform comprehensive analysis
        let analysis_results = vec![
            // Format distribution analysis
            self.client.call_tool("analyze_categorical_distribution", json!({
                "dataset_id": dataset_id,
                "column": "input_format"
            })).await?,
            
            // Conversion time analysis
            self.client.call_tool("analyze_numerical_summary", json!({
                "dataset_id": dataset_id,
                "column": "conversion_time_ms"
            })).await?,
            
            // Success rate by format
            self.client.call_tool("analyze_correlation", json!({
                "dataset_id": dataset_id,
                "x_column": "input_format",
                "y_column": "success"
            })).await?,
            
            // File size impact on performance
            self.client.call_tool("create_scatter_plot", json!({
                "dataset_id": dataset_id,
                "x_column": "input_size_bytes",
                "y_column": "conversion_time_ms",
                "color_column": "input_format"
            })).await?,
        ];
        
        // Generate insights and recommendations
        let insights = self.client.call_tool("generate_insights", json!({
            "dataset_id": dataset_id,
            "focus_areas": ["performance", "quality", "success_rates"]
        })).await?;
        
        Ok(ConversionAnalysis {
            total_conversions: conversion_data.len(),
            format_distribution: analysis_results[0].clone(),
            performance_stats: analysis_results[1].clone(),
            success_rates: analysis_results[2].clone(),
            size_performance_correlation: analysis_results[3].clone(),
            insights: insights["insights"].as_array()
                .map(|arr| arr.to_vec())
                .unwrap_or_default(),
        })
    }
    
    /// Generate format compatibility report
    pub async fn generate_format_report(
        &self,
        supported_formats: Vec<FormatDefinition>,
    ) -> Result<FormatCompatibilityReport, IntegrationError> {
        // Create format compatibility matrix
        let compatibility_data = self.prepare_format_matrix(&supported_formats)?;
        
        let dataset_result = self.client.call_tool("load_csv_data", json!({
            "csv_content": compatibility_data,
            "dataset_name": "format_compatibility",
            "has_headers": true
        })).await?;
        
        let dataset_id = dataset_result["dataset_id"].as_str()
            .ok_or_else(|| IntegrationError::InvalidResponse("Missing dataset_id".to_string()))?;
        
        // Analyze conversion quality ratings
        let quality_analysis = self.client.call_tool("create_heatmap", json!({
            "dataset_id": dataset_id,
            "x_column": "input_format",
            "y_column": "output_format", 
            "value_column": "quality_score"
        })).await?;
        
        // Generate recommendations
        let recommendations = self.client.call_tool("generate_recommendations", json!({
            "dataset_id": dataset_id,
            "target": "optimal_conversion_paths"
        })).await?;
        
        Ok(FormatCompatibilityReport {
            total_formats: supported_formats.len(),
            quality_matrix: quality_analysis,
            recommendations: recommendations["recommendations"].as_array()
                .map(|arr| arr.to_vec())
                .unwrap_or_default(),
            best_practices: self.generate_best_practices(&supported_formats),
        })
    }
    
    /// Analyze batch conversion performance
    pub async fn analyze_batch_performance(
        &self,
        batch_metrics: Vec<BatchMetric>,
    ) -> Result<BatchPerformanceAnalysis, IntegrationError> {
        let csv_data = self.prepare_batch_csv(&batch_metrics)?;
        
        let dataset_result = self.client.call_tool("load_csv_data", json!({
            "csv_content": csv_data,
            "dataset_name": "batch_performance",
            "has_headers": true
        })).await?;
        
        let dataset_id = dataset_result["dataset_id"].as_str()
            .ok_or_else(|| IntegrationError::InvalidResponse("Missing dataset_id".to_string()))?;
        
        // Analyze optimal batch sizes
        let batch_size_analysis = self.client.call_tool("analyze_numerical_optimization", json!({
            "dataset_id": dataset_id,
            "optimize_column": "throughput_files_per_minute",
            "variable_column": "batch_size"
        })).await?;
        
        // Analyze parallel processing effectiveness
        let parallel_analysis = self.client.call_tool("create_line_chart", json!({
            "dataset_id": dataset_id,
            "x_column": "parallel_jobs",
            "y_column": "throughput_files_per_minute",
            "group_by": "average_file_size_category"
        })).await?;
        
        Ok(BatchPerformanceAnalysis {
            optimal_batch_size: batch_size_analysis["optimal_value"].as_u64().unwrap_or(10) as usize,
            optimal_parallel_jobs: parallel_analysis["optimal_x_value"].as_u64().unwrap_or(4) as usize,
            performance_insights: vec![
                format!("Optimal batch size: {} files", batch_size_analysis["optimal_value"]),
                format!("Best parallel job count: {}", parallel_analysis["optimal_x_value"]),
                "Small files (<1MB) benefit from higher parallelism".to_string(),
                "Large files (>10MB) should use lower parallelism to avoid memory issues".to_string(),
            ],
        })
    }
    
    /// Generate conversion quality report
    pub async fn generate_quality_report(
        &self,
        quality_metrics: Vec<QualityMetric>,
    ) -> Result<QualityReport, IntegrationError> {
        let csv_data = self.prepare_quality_csv(&quality_metrics)?;
        
        let dataset_result = self.client.call_tool("load_csv_data", json!({
            "csv_content": csv_data,
            "dataset_name": "conversion_quality",
            "has_headers": true
        })).await?;
        
        let dataset_id = dataset_result["dataset_id"].as_str()
            .ok_or_else(|| IntegrationError::InvalidResponse("Missing dataset_id".to_string()))?;
        
        // Analyze quality scores by format combination
        let quality_by_format = self.client.call_tool("create_grouped_bar_chart", json!({
            "dataset_id": dataset_id,
            "x_column": "input_format",
            "y_column": "quality_score",
            "group_column": "output_format"
        })).await?;
        
        // Identify problematic conversions
        let problem_conversions = self.client.call_tool("filter_and_analyze", json!({
            "dataset_id": dataset_id,
            "filter": "quality_score < 7",
            "analyze_columns": ["input_format", "output_format", "common_issues"]
        })).await?;
        
        Ok(QualityReport {
            average_quality_score: quality_metrics.iter()
                .map(|m| m.quality_score as f64)
                .sum::<f64>() / quality_metrics.len() as f64,
            quality_by_format_chart: quality_by_format,
            problem_conversions: problem_conversions["results"].as_array()
                .map(|arr| arr.to_vec())
                .unwrap_or_default(),
            improvement_recommendations: vec![
                "Enable preserve_formatting for RTF to Markdown conversions".to_string(),
                "Use legacy mode for WordPerfect files created before 1995".to_string(),
                "Convert DOC files to DOCX as intermediate step for better quality".to_string(),
            ],
        })
    }
    
    fn prepare_conversion_csv(&self, data: &[ConversionMetric]) -> Result<String, IntegrationError> {
        let mut csv = String::from("input_format,output_format,input_size_bytes,output_size_bytes,conversion_time_ms,success,confidence_score,quality_rating\n");
        
        for metric in data {
            csv.push_str(&format!(
                "{},{},{},{},{},{},{},{}\n",
                metric.input_format,
                metric.output_format,
                metric.input_size_bytes,
                metric.output_size_bytes,
                metric.conversion_time_ms,
                metric.success,
                metric.confidence_score,
                metric.quality_rating
            ));
        }
        
        Ok(csv)
    }
    
    fn prepare_format_matrix(&self, formats: &[FormatDefinition]) -> Result<String, IntegrationError> {
        let mut csv = String::from("input_format,output_format,quality_score,supported\n");
        
        for input_format in formats {
            for output_format in &input_format.can_convert_to {
                let quality_score = input_format.conversion_quality
                    .get(output_format)
                    .map(|q| match q.as_str() {
                        "excellent" => 10,
                        "good" => 8,
                        "fair" => 6,
                        "basic" => 4,
                        _ => 2,
                    })
                    .unwrap_or(0);
                
                csv.push_str(&format!(
                    "{},{},{},{}\n",
                    input_format.id,
                    output_format,
                    quality_score,
                    true
                ));
            }
        }
        
        Ok(csv)
    }
    
    fn prepare_batch_csv(&self, data: &[BatchMetric]) -> Result<String, IntegrationError> {
        let mut csv = String::from("batch_size,parallel_jobs,total_files,total_time_ms,throughput_files_per_minute,average_file_size_category\n");
        
        for metric in data {
            let throughput = (metric.total_files as f64 / metric.total_time_ms as f64) * 60000.0;
            let size_category = if metric.average_file_size < 1_000_000 {
                "small"
            } else if metric.average_file_size < 10_000_000 {
                "medium"
            } else {
                "large"
            };
            
            csv.push_str(&format!(
                "{},{},{},{},{},{}\n",
                metric.batch_size,
                metric.parallel_jobs,
                metric.total_files,
                metric.total_time_ms,
                throughput,
                size_category
            ));
        }
        
        Ok(csv)
    }
    
    fn prepare_quality_csv(&self, data: &[QualityMetric]) -> Result<String, IntegrationError> {
        let mut csv = String::from("input_format,output_format,quality_score,formatting_preserved,content_accuracy,metadata_preserved,common_issues\n");
        
        for metric in data {
            csv.push_str(&format!(
                "{},{},{},{},{},{},\"{}\"\n",
                metric.input_format,
                metric.output_format,
                metric.quality_score,
                metric.formatting_preserved,
                metric.content_accuracy,
                metric.metadata_preserved,
                metric.common_issues.join("; ")
            ));
        }
        
        Ok(csv)
    }
    
    fn generate_best_practices(&self, formats: &[FormatDefinition]) -> Vec<String> {
        vec![
            "Use RTF as intermediate format for legacy document chains".to_string(),
            "Convert DOC files to DOCX before final format conversion".to_string(),
            "Validate file integrity before processing large batches".to_string(),
            "Use parallel processing for files smaller than 10MB".to_string(),
            "Enable legacy mode for very old format versions".to_string(),
            "Preserve formatting option improves quality by 15-20%".to_string(),
            "Batch similar file types together for better performance".to_string(),
        ]
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ConversionMetric {
    pub input_format: String,
    pub output_format: String,
    pub input_size_bytes: usize,
    pub output_size_bytes: usize,
    pub conversion_time_ms: u64,
    pub success: bool,
    pub confidence_score: f64,
    pub quality_rating: u8,
}

#[derive(Debug)]
pub struct ConversionAnalysis {
    pub total_conversions: usize,
    pub format_distribution: JsonValue,
    pub performance_stats: JsonValue,
    pub success_rates: JsonValue,
    pub size_performance_correlation: JsonValue,
    pub insights: Vec<JsonValue>,
}

#[derive(Debug)]
pub struct FormatCompatibilityReport {
    pub total_formats: usize,
    pub quality_matrix: JsonValue,
    pub recommendations: Vec<JsonValue>,
    pub best_practices: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchMetric {
    pub batch_size: usize,
    pub parallel_jobs: usize,
    pub total_files: usize,
    pub total_time_ms: u64,
    pub average_file_size: usize,
}

#[derive(Debug)]
pub struct BatchPerformanceAnalysis {
    pub optimal_batch_size: usize,
    pub optimal_parallel_jobs: usize,
    pub performance_insights: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QualityMetric {
    pub input_format: String,
    pub output_format: String,
    pub quality_score: u8,
    pub formatting_preserved: bool,
    pub content_accuracy: f64,
    pub metadata_preserved: bool,
    pub common_issues: Vec<String>,
}

#[derive(Debug)]
pub struct QualityReport {
    pub average_quality_score: f64,
    pub quality_by_format_chart: JsonValue,
    pub problem_conversions: Vec<JsonValue>,
    pub improvement_recommendations: Vec<String>,
}