apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: legacybridge-network-policy
  namespace: legacybridge
  labels:
    app: legacybridge
spec:
  podSelector:
    matchLabels:
      app: legacybridge
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 3000
    - protocol: TCP
      port: 8080
  
  # Allow traffic between legacybridge pods
  - from:
    - namespaceSelector:
        matchLabels:
          name: legacybridge
      podSelector:
        matchLabels:
          app: legacybridge
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 3000
    - protocol: TCP
      port: 9090
    - protocol: TCP
      port: 8765
  
  # Allow Prometheus scraping
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
      podSelector:
        matchLabels:
          app: prometheus
    ports:
    - protocol: TCP
      port: 9090
  
  egress:
  # Allow DNS resolution
  - to:
    - namespaceSelector: {}
      podSelector:
        matchLabels:
          k8s-app: kube-dns
    ports:
    - protocol: UDP
      port: 53
  
  # Allow external HTTPS traffic
  - to:
    - ipBlock:
        cidr: 0.0.0.0/0
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
  
  # Allow database access
  - to:
    - namespaceSelector:
        matchLabels:
          name: legacybridge
      podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  
  # Allow Redis access
  - to:
    - namespaceSelector:
        matchLabels:
          name: legacybridge
      podSelector:
        matchLabels:
          app: redis
    ports:
    - protocol: TCP
      port: 6379
  
  # Allow internal pod communication
  - to:
    - namespaceSelector:
        matchLabels:
          name: legacybridge
      podSelector:
        matchLabels:
          app: legacybridge