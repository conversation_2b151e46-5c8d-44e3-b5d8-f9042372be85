-- Schema definition for LegacyBridge transformer plugin

local typedefs = require "kong.db.schema.typedefs"

return {
  name = "legacybridge-transformer",
  fields = {
    { consumer = typedefs.no_consumer },
    { protocols = typedefs.protocols_http },
    { config = {
        type = "record",
        fields = {
          {
            add_request_headers = {
              type = "map",
              keys = { type = "string" },
              values = { type = "string" },
              description = "Headers to add to requests"
            }
          },
          {
            remove_request_headers = {
              type = "array",
              elements = { type = "string" },
              default = {
                "X-Kong-Request-ID",
                "X-Kong-Proxy-Latency",
                "X-Kong-Upstream-Latency"
              },
              description = "Headers to remove from requests"
            }
          },
          {
            add_response_headers = {
              type = "map",
              keys = { type = "string" },
              values = { type = "string" },
              description = "Headers to add to responses"
            }
          },
          {
            remove_response_headers = {
              type = "array",
              elements = { type = "string" },
              default = {
                "Server",
                "X-Powered-By"
              },
              description = "Headers to remove from responses"
            }
          },
          {
            transform_request_body = {
              type = "boolean",
              default = false,
              description = "Enable request body transformation"
            }
          },
          {
            transform_response_body = {
              type = "boolean",
              default = false,
              description = "Enable response body transformation"
            }
          },
          {
            add_request_metadata = {
              type = "boolean",
              default = false,
              description = "Add metadata to request body"
            }
          },
          {
            add_response_metadata = {
              type = "boolean",
              default = true,
              description = "Add metadata to response body"
            }
          },
          {
            request_transformations = {
              type = "map",
              keys = { type = "string" },
              values = {
                type = "record",
                fields = {
                  {
                    action = {
                      type = "string",
                      one_of = { "add", "remove", "rename" },
                      description = "Transformation action"
                    }
                  },
                  {
                    value = {
                      type = "string",
                      description = "Value for add action"
                    }
                  },
                  {
                    new_name = {
                      type = "string",
                      description = "New field name for rename action"
                    }
                  }
                }
              },
              description = "Request body field transformations"
            }
          },
          {
            response_transformations = {
              type = "map",
              keys = { type = "string" },
              values = {
                type = "record",
                fields = {
                  {
                    action = {
                      type = "string",
                      one_of = { "add", "remove", "rename" },
                      description = "Transformation action"
                    }
                  },
                  {
                    value = {
                      type = "string",
                      description = "Value for add action"
                    }
                  },
                  {
                    new_name = {
                      type = "string",
                      description = "New field name for rename action"
                    }
                  }
                }
              },
              description = "Response body field transformations"
            }
          },
          {
            enable_cors = {
              type = "boolean",
              default = true,
              description = "Enable CORS headers"
            }
          },
          {
            cors_origin = {
              type = "string",
              default = "*",
              description = "CORS allowed origins"
            }
          },
          {
            cors_methods = {
              type = "string",
              default = "GET, POST, PUT, DELETE, OPTIONS",
              description = "CORS allowed methods"
            }
          },
          {
            cors_headers = {
              type = "string",
              default = "Content-Type, Authorization",
              description = "CORS allowed headers"
            }
          },
          {
            cors_max_age = {
              type = "number",
              default = 3600,
              description = "CORS max age in seconds"
            }
          }
        }
      }
    }
  }
}
